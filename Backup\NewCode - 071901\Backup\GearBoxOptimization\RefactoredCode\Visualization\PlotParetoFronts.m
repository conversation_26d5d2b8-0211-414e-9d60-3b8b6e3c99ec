function PlotParetoFronts(all_results, algorithm_names, save_plots)
% PlotParetoFronts 绘制多目标优化算法的Pareto前沿对比图
%
% 输入参数:
%   all_results - 包含所有算法结果的cell数组，每个元素是一个N×3的矩阵
%   algorithm_names - 算法名称的cell数组
%   save_plots - 是否保存图片（可选，默认为true）
%
% 输出:
%   无返回值，直接显示图形并可选保存

if nargin < 3
    save_plots = true;
end

% 验证输入参数
if length(all_results) ~= length(algorithm_names)
    error('算法结果数量与算法名称数量不匹配');
end

% 过滤空结果
valid_indices = [];
for i = 1:length(all_results)
    if ~isempty(all_results{i}) && size(all_results{i}, 2) >= 3
        valid_indices = [valid_indices, i];
    end
end

if isempty(valid_indices)
    fprintf('警告：没有有效的算法结果可以绘制\n');
    return;
end

% 只保留有效结果
all_results = all_results(valid_indices);
algorithm_names = algorithm_names(valid_indices);

% 设置颜色和标记
colors = [
    0.8500, 0.3250, 0.0980;  % 橙红色
    0.0000, 0.4470, 0.7410;  % 蓝色
    0.9290, 0.6940, 0.1250;  % 黄色
    0.4940, 0.1840, 0.5560;  % 紫色
    0.4660, 0.6740, 0.1880;  % 绿色
    0.3010, 0.7450, 0.9330;  % 青色
    0.6350, 0.0780, 0.1840;  % 深红色
    0.2500, 0.2500, 0.2500;  % 灰色
    0.8000, 0.4000, 0.8000;  % 粉色
    0.0000, 0.5000, 0.0000   % 深绿色
];

markers = {'o', 's', 'd', '^', 'v', '>', '<', 'p', 'h', '*'};
markerSizes = [60, 50, 55, 50, 50, 45, 45, 55, 50, 40];

% 创建边线颜色（比填充颜色更深）
edgeColors = colors * 0.7;

% 创建图形窗口
figure('Position', [100, 100, 1200, 800], 'Name', 'Pareto前沿对比', 'NumberTitle', 'off');

%% 绘制3D Pareto前沿
subplot(2, 2, [1, 2]);
hold on;
grid on;

legend_entries = {};
for i = 1:length(all_results)
    if ~isempty(all_results{i})
        % 处理算法名称中的特殊字符
        safe_alg_name = strrep(algorithm_names{i}, '/', '-');
        
        % 提取目标函数值
        f1 = all_results{i}(:, 1);        % 质量
        f2 = -all_results{i}(:, 2);       % 弯曲安全系数
        f3 = -all_results{i}(:, 3);       % 接触安全系数
        
        % 当前算法的颜色和标记
        colorIdx = mod(i-1, size(colors, 1))+1;
        markerIdx = mod(i-1, length(markers))+1;
        
        % 使用散点图绘制Pareto前沿
        scatter3(f2, f3, f1, markerSizes(colorIdx), ...
               'filled', ...
               markers{markerIdx}, ...
               'MarkerEdgeColor', edgeColors(colorIdx,:), ...
               'LineWidth', 1.2, ...
               'MarkerFaceColor', colors(colorIdx, :), ...
               'MarkerFaceAlpha', 0.8, ...
               'DisplayName', safe_alg_name);
        
        legend_entries{end+1} = safe_alg_name;
    end
end

% 设置坐标轴
xlabel('弯曲安全系数', 'FontSize', 12, 'FontWeight', 'bold');
ylabel('接触安全系数', 'FontSize', 12, 'FontWeight', 'bold');
zlabel('总质量 (kg)', 'FontSize', 12, 'FontWeight', 'bold');
title('三维Pareto前沿对比', 'FontSize', 14, 'FontWeight', 'bold');

% 添加图例
if ~isempty(legend_entries)
    legend(legend_entries, 'Location', 'best', 'FontSize', 10);
end

% 设置视角
view(45, 30);

%% 绘制2D投影图
% 质量 vs 弯曲安全系数
subplot(2, 2, 3);
hold on;
grid on;

for i = 1:length(all_results)
    if ~isempty(all_results{i})
        f1 = all_results{i}(:, 1);        % 质量
        f2 = -all_results{i}(:, 2);       % 弯曲安全系数
        
        colorIdx = mod(i-1, size(colors, 1))+1;
        markerIdx = mod(i-1, length(markers))+1;
        
        scatter(f2, f1, 40, colors(colorIdx, :), 'filled', markers{markerIdx}, ...
               'MarkerEdgeColor', edgeColors(colorIdx,:), 'LineWidth', 1);
    end
end

xlabel('弯曲安全系数', 'FontSize', 11, 'FontWeight', 'bold');
ylabel('总质量 (kg)', 'FontSize', 11, 'FontWeight', 'bold');
title('质量 vs 弯曲安全系数', 'FontSize', 12, 'FontWeight', 'bold');

% 质量 vs 接触安全系数
subplot(2, 2, 4);
hold on;
grid on;

for i = 1:length(all_results)
    if ~isempty(all_results{i})
        f1 = all_results{i}(:, 1);        % 质量
        f3 = -all_results{i}(:, 3);       % 接触安全系数
        
        colorIdx = mod(i-1, size(colors, 1))+1;
        markerIdx = mod(i-1, length(markers))+1;
        
        scatter(f3, f1, 40, colors(colorIdx, :), 'filled', markers{markerIdx}, ...
               'MarkerEdgeColor', edgeColors(colorIdx,:), 'LineWidth', 1);
    end
end

xlabel('接触安全系数', 'FontSize', 11, 'FontWeight', 'bold');
ylabel('总质量 (kg)', 'FontSize', 11, 'FontWeight', 'bold');
title('质量 vs 接触安全系数', 'FontSize', 12, 'FontWeight', 'bold');

% 调整子图间距
sgtitle('齿轮传动系统多目标优化Pareto前沿对比', 'FontSize', 16, 'FontWeight', 'bold');

%% 保存图片
if save_plots
    try
        % 确保Results目录存在
        results_dir = 'Results';
        if ~exist(results_dir, 'dir')
            mkdir(results_dir);
        end
        
        % 保存为PNG格式 - 使用print函数更适合批处理模式
        png_filename = fullfile(results_dir, 'Pareto前沿对比图.png');
        try
            print(gcf, png_filename, '-dpng', '-r300');
            fprintf('  PNG格式: %s\n', png_filename);
        catch
            % 备选方案
            saveas(gcf, png_filename, 'png');
            fprintf('  PNG格式: %s (备选方案)\n', png_filename);
        end

        % 保存为高质量的EPS格式
        eps_filename = fullfile(results_dir, 'Pareto前沿对比图.eps');
        try
            print(gcf, eps_filename, '-depsc', '-r300');
            fprintf('  EPS格式: %s\n', eps_filename);
        catch
            % 备选方案
            saveas(gcf, eps_filename, 'epsc');
            fprintf('  EPS格式: %s (备选方案)\n', eps_filename);
        end

        fprintf('Pareto前沿对比图已保存:\n');
        
    catch e
        fprintf('保存图片时出错: %s\n', e.message);
    end
end

hold off;

end
