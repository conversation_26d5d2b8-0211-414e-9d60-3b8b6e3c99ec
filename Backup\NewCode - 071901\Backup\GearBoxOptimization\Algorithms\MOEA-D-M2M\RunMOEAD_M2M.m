function [pop, F] = RunMOEAD_M2M(problem, params)
% RunMOEAD_M2M - 运行基于多目标到多子群的分解多目标进化算法 (MOEA/D-M2M)
%
% 输入:
%   problem - 包含问题信息的结构体
%   params  - 算法参数
%
% 输出:
%   pop - 最终种群
%   F   - 最终的非支配解集目标函数值
%
% 参考文献:
% <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, Decomposition of a multiobjective
% optimization problem into a number of simple multiobjective subproblems,
% IEEE Transactions on Evolutionary Computation, 2014, 18(3): 450-455.

%% 初始化参数
nVar = problem.nVar;
varSize = problem.varSize;
varMin = problem.varMin;
varMax = problem.varMax;
nPop = params.nPop;
maxIt = params.maxIt;
nObj = problem.nObj;
pCrossover = params.pCrossover;
pMutation = params.pMutation;

% 确保有评价次数计数器
if ~isfield(problem, 'FE')
    problem.FE = 0;
end

% MOEA/D-M2M特定参数
K = min(10, nPop/10);    % 子种群数量 (参考向量数量)
S = ceil(nPop/K);        % 每个子种群的大小

% 调整总种群大小以匹配子种群数量
nPop = S * K;

fprintf('开始运行MOEA/D-M2M算法...\n');

%% 初始化种群
empty_individual.Position = [];
empty_individual.Cost = [];
empty_individual.Rank = [];
empty_individual.SubpopID = [];

% 生成初始种群
pop = repmat(empty_individual, nPop, 1);

for i = 1:nPop
    % 生成随机解
    pop(i).Position = unifrnd(varMin, varMax, varSize);
    
    % 对离散变量特殊处理
    if isfield(problem, 'discreteVars')
        for j = 1:length(problem.discreteVars)
            idx = problem.discreteVars(j).idx;
            if problem.discreteVars(j).isInteger
                pop(i).Position(idx) = round(pop(i).Position(idx));
            else
                % 找到最接近的离散值
                values = problem.discreteVars(j).values;
                [~, closest_idx] = min(abs(pop(i).Position(idx) - values));
                pop(i).Position(idx) = values(closest_idx);
            end
        end
    end
    
    % 评估目标函数
    pop(i).Cost = problem.costFunction(pop(i).Position);
    problem.FE = problem.FE + 1;
end

% 生成权重向量
[W, ~] = GenerateReferencePoints(K, nObj);

% 分配个体到子种群
pop = AssociateToSubproblems(pop, W, S);

%% 主循环
for it = 1:maxIt
    % 计算当前迭代进度
    progress = it / maxIt;
    
    % 创建子代种群
    offspring = repmat(empty_individual, nPop, 1);
    
    % 创建局部和全局交配池
    MatingPoolLocal = zeros(S, K);
    for i = 1:K
        MatingPoolLocal(:,i) = randi(S, S, 1) + (i-1)*S;
    end
    MatingPoolLocal = MatingPoolLocal(:);
    
    MatingPoolGlobal = randi(nPop, 1, nPop);
    
    % 随机决定使用全局或局部交配
    rnd = rand(nPop, 1) < 0.7;
    MatingPool = MatingPoolLocal;
    MatingPool(rnd) = MatingPoolGlobal(rnd);
    
    % 生成子代
    for i = 1:nPop
        % 选择父代
        parent1_idx = i;
        parent2_idx = MatingPool(i);
        
        parent1 = pop(parent1_idx);
        parent2 = pop(parent2_idx);
        
        % 交叉操作 - 使用迭代进度而不是FE计数
        [child1, child2] = CrossoverOperator(parent1, parent2, pCrossover, progress);
        
        % 变异操作 - 使用迭代进度而不是FE计数
        child = MutationOperator(child1, pMutation, varMin, varMax, progress);
        
        % 对离散变量特殊处理
        if isfield(problem, 'discreteVars')
            for j = 1:length(problem.discreteVars)
                idx = problem.discreteVars(j).idx;
                if problem.discreteVars(j).isInteger
                    child.Position(idx) = round(child.Position(idx));
                else
                    % 找到最接近的离散值
                    values = problem.discreteVars(j).values;
                    [~, closest_idx] = min(abs(child.Position(idx) - values));
                    child.Position(idx) = values(closest_idx);
                end
            end
        end
        
        % 边界处理
        child.Position = max(child.Position, varMin);
        child.Position = min(child.Position, varMax);
        
        % 评估子代
        child.Cost = problem.costFunction(child.Position);
        problem.FE = problem.FE + 1;
        
        % 添加到子代种群
        offspring(i) = child;
    end
    
    % 合并种群
    combined_pop = [pop; offspring];
    
    % 分配到子种群
    pop = AssociateToSubproblems(combined_pop, W, S);
    
    % 显示迭代信息
    if mod(it, 10) == 0 || it == maxIt
        disp(['迭代 ' num2str(it) '/' num2str(maxIt) ', 非支配解数量 = ' num2str(length(GetNonDominatedSet(pop))) ', 评价次数 = ' num2str(problem.FE)]);
    end
end

% 提取非支配解
nonDominated = GetNonDominatedSet(pop);

% 修改代码，确保返回的是数值矩阵而非结构体数组
n_pareto = numel(nonDominated);
population = zeros(n_pareto, problem.nVar);
objectives = zeros(n_pareto, nObj);

for i = 1:n_pareto
    population(i, :) = nonDominated(i).Position;
    objectives(i, :) = nonDominated(i).Cost;
end

% 处理最大化目标
objectives(:, 2) = -objectives(:, 2);  % 取负值以便最大化弯曲安全系数
objectives(:, 3) = -objectives(:, 3);  % 取负值以便最大化接触安全系数

% 返回转换后的数值矩阵
pop = population;
F = objectives;

% 不再输出算法完成信息，由主程序统一处理
end

%% 辅助函数
function [W, indices] = GenerateReferencePoints(K, M)
% 生成参考点

if M == 2
    % 两个目标，均匀分布
    W = zeros(K, M);
    for i = 1:K
        W(i, 1) = (i-1)/(K-1);
        W(i, 2) = 1-W(i, 1);
    end
    indices = 1:K;
else
    % 三个或更多目标，使用Das和Dennis方法
    H = floor((K-1)^(1/(M-1)));
    
    % 生成权重向量
    W = [];
    for i = 0:H
        if M == 3
            for j = 0:H-i
                k = H - i - j;
                W = [W; i/H, j/H, k/H];
            end
        else
            % 对于更多目标，使用递归或随机采样
            for j = 1:K
                w = rand(1, M);
                W = [W; w / sum(w)];
            end
            W = unique(W, 'rows');
            if size(W, 1) > K
                W = W(1:K, :);
            end
        end
    end
    
    % 如果生成的权重向量少于需要的数量，随机生成补充
    while size(W, 1) < K
        w = rand(1, M);
        W = [W; w / sum(w)];
    end
    
    indices = 1:K;
end
end

function pop = AssociateToSubproblems(pop, W, S)
% 将解分配到子问题

K = size(W, 1);
N = length(pop);

% 提取所有解的目标函数值
objValues = reshape([pop.Cost], [], length(pop))';

% 计算余弦距离
cosine_distance = pdist2(objValues, W, 'cosine');
[~, transformation] = max(1-cosine_distance, [], 2);

% 每个子问题选择S个解
selected_pop = [];
for i = 1:K
    % 找到属于当前子问题的解
    current = find(transformation==i);
    
    if length(current) < S
        % 如果解不足，随机选择解补充
        additional = setdiff(1:N, current);
        if ~isempty(additional)
            selected = additional(randperm(length(additional), min(S-length(current), length(additional))));
            current = [current; selected(:)];
        end
    elseif length(current) > S
        % 如果解过多，通过非支配排序和拥挤度计算选择
        [FrontNo, MaxFNo] = NonDominatedSort(objValues(current,:), S);
        Last = find(FrontNo==MaxFNo);
        
        if ~isempty(Last)
            CrowdDis = CalculateCrowdingDistance(objValues(current(Last),:));
            [~, rank] = sort(CrowdDis);
            
            % 计算要保留的解数量
            remain = S - sum(FrontNo < MaxFNo);
            
            % 根据拥挤度选择解
            if remain > 0 && remain <= length(Last)
                selected = Last(rank(end-remain+1:end));
                selected_idx = find(FrontNo < MaxFNo);
                selected_idx = [selected_idx; current(selected)];
                current = current(selected_idx);
            else
                selected_idx = find(FrontNo < MaxFNo);
                current = current(selected_idx);
            end
        end
    end
    
    % 确保每个子问题有S个解
    if length(current) > S
        current = current(1:S);
    end
    
    % 标记子种群ID
    for j = 1:length(current)
        pop(current(j)).SubpopID = i;
    end
    
    % 添加到选中种群
    selected_pop = [selected_pop; pop(current)];
end

% 更新种群
pop = selected_pop;
end

function [child1, child2] = CrossoverOperator(parent1, parent2, pc, progress)
% 交叉操作

child1 = parent1;
child2 = parent2;

if rand > pc
    return;
end

% 使用MOEA/D-M2M中的自适应交叉操作
D = length(parent1.Position);
rc = (2*rand(1,1)-1)*(1-rand(1,1)^(-(1-progress)^0.7));
child1.Position = parent1.Position + rc*(parent1.Position-parent2.Position);
child2.Position = parent2.Position + rc*(parent2.Position-parent1.Position);
end

function child = MutationOperator(parent, pm, varMin, varMax, progress)
% 变异操作

child = parent;
D = length(parent.Position);

% 使用MOEA/D-M2M中的自适应变异操作
rm = 0.25*(2*rand(1,D)-1).*(1-rand(1,D).^(-(1-progress)^0.7));
Site = rand(1,D) < 1/D;

Lower = varMin;
Upper = varMax;

% 应用变异
child.Position(Site) = child.Position(Site) + rm(Site).*(Upper(Site)-Lower(Site));

% 边界处理
temp1 = child.Position < Lower;
temp2 = child.Position > Upper;
rnd = rand(1,D);
child.Position(temp1) = Lower(temp1) + 0.5*rnd(temp1).*(parent.Position(temp1)-Lower(temp1));
child.Position(temp2) = Upper(temp2) - 0.5*rnd(temp2).*(Upper(temp2)-parent.Position(temp2));
end

function [FrontNo, MaxFNo] = NonDominatedSort(objValues, nSelec)
% 非支配排序

N = size(objValues, 1);
Dominate = false(N);
for i = 1:N-1
    for j = i+1:N
        if all(objValues(i,:) <= objValues(j,:)) && any(objValues(i,:) < objValues(j,:))
            Dominate(i,j) = true;
        elseif all(objValues(j,:) <= objValues(i,:)) && any(objValues(j,:) < objValues(i,:))
            Dominate(j,i) = true;
        end
    end
end

% 统计支配数量
DominatedCount = sum(Dominate, 1)';
FrontNo = inf(N, 1);
MaxFNo = 0;
while sum(FrontNo < inf) < min(nSelec, N)
    MaxFNo = MaxFNo + 1;
    FrontNo(DominatedCount == 0 & FrontNo == inf) = MaxFNo;
    for i = find(FrontNo == MaxFNo)'
        DominatedCount(Dominate(i,:)) = DominatedCount(Dominate(i,:)) - 1;
    end
end
end

function CrowdDis = CalculateCrowdingDistance(objValues)
% 计算拥挤度

[N, M] = size(objValues);
CrowdDis = zeros(N, 1);

for i = 1:M
    [sorted, rank] = sort(objValues(:,i));
    CrowdDis(rank(1)) = inf;
    CrowdDis(rank(end)) = inf;
    
    % 计算中间点的拥挤度
    for j = 2:N-1
        if sorted(end) - sorted(1) > 0
            CrowdDis(rank(j)) = CrowdDis(rank(j)) + (sorted(j+1) - sorted(j-1))/(sorted(end) - sorted(1));
        end
    end
end
end

function nonDominated = GetNonDominatedSet(pop)
% 获取非支配解集

objValues = reshape([pop.Cost], [], length(pop))';
N = size(objValues, 1);
dominated = false(1, N);

for i = 1:N-1
    for j = i+1:N
        if ~dominated(i) && ~dominated(j)
            if all(objValues(i,:) <= objValues(j,:)) && any(objValues(i,:) < objValues(j,:))
                dominated(j) = true;
            elseif all(objValues(j,:) <= objValues(i,:)) && any(objValues(j,:) < objValues(i,:))
                dominated(i) = true;
            end
        end
    end
end

nonDominated = pop(~dominated);
end 