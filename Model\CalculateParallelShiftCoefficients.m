function [x1, x2] = CalculateParallelShiftCoefficients(z1, z2, m, alpha_deg, beta_deg, target_center_distance)
% CalculateParallelShiftCoefficients - 计算平行轴齿轮对的最佳变位系数
% 完全按照原有代码的计算方法
%
% 输入参数:
%   z1 - 小齿轮齿数
%   z2 - 大齿轮齿数
%   m - 模数 (mm)
%   alpha_deg - 压力角 (度)
%   beta_deg - 螺旋角 (度)
%   target_center_distance - 目标中心距 (mm)
%
% 输出参数:
%   x1 - 小齿轮变位系数
%   x2 - 大齿轮变位系数
%
% 此函数根据指定参数和目标中心距计算平行轴齿轮系统中齿轮对的最佳变位系数。
% 它分配总变位系数以平衡齿轮的滑动率。

% 将角度从度转换为弧度
alpha = alpha_deg * pi / 180;
beta = beta_deg * pi / 180;

% 1. 计算标准中心距 (a)
a_standard = m * (z1 + z2) / (2 * cos(beta));

% 2. 计算中心距系数
center_distance_factor = target_center_distance / a_standard;

% 3. 计算工作压力角 (alpha')
inv_alpha = tan(alpha) - alpha; % alpha的渐开线函数
cos_alpha_w = a_standard * cos(alpha) / target_center_distance;

% 将cos_alpha_w限制在有效范围内
cos_alpha_w = min(max(cos_alpha_w, -1), 1);
alpha_w = acos(cos_alpha_w);
inv_alpha_w = tan(alpha_w) - alpha_w;

% 4. 计算总变位系数
sum_x = (z1 + z2) * (inv_alpha_w - inv_alpha) / (2 * tan(alpha));

% 5. 检查sum_x是否在有效范围内 (0-1.0)
min_sum_x = 0.0;  % 总变位系数最小值
max_sum_x = 1.0;  % 总变位系数最大值

% 如果超出范围则调整sum_x
if sum_x < min_sum_x
    sum_x = min_sum_x;
elseif sum_x > max_sum_x
    sum_x = max_sum_x;
end

% 6. 计算传动比
u = z2 / z1;

% 7. 使用图片中的公式分配变位系数，以平衡滑动率
% 均衡滑动率的变位分配: x₁ = x_Σ/2 + ((z₂ - z₁)/(2(z₁ + z₂))) · x_Σ
x1 = sum_x/2 + (z2 - z1)/(2*(z1 + z2)) * sum_x;
x2 = sum_x - x1;

% 8. 检查防止根切的最小变位系数
% 计算避免根切所需的最小齿数
min_teeth_no_undercut = 2 / (sin(alpha)^2);

% 计算防止根切的最小变位系数
x1_min_undercut = (min_teeth_no_undercut - z1) / 2;
x2_min_undercut = (min_teeth_no_undercut - z2) / 2;

% 确保x1和x2不小于最小值
if x1 < x1_min_undercut
    x1 = x1_min_undercut;
    x2 = sum_x - x1;
end

if x2 < x2_min_undercut
    x2 = x2_min_undercut;
    x1 = sum_x - x2;
end

% 9. 最终检查变位系数范围
% 确保变位系数在合理范围内 (-0.5 到 1.0)
x1 = max(-0.5, min(1.0, x1));
x2 = max(-0.5, min(1.0, x2));

% 10. 验证计算结果
% 重新计算实际中心距以验证
actual_sum_x = x1 + x2;
inv_alpha_w_check = inv_alpha + 2 * actual_sum_x * tan(alpha) / (z1 + z2);
alpha_w_check = fzero(@(a) tan(a) - a - inv_alpha_w_check, alpha);
actual_center_distance = a_standard * cos(alpha) / cos(alpha_w_check);

% 如果中心距误差过大，进行微调
center_distance_error = abs(actual_center_distance - target_center_distance) / target_center_distance;
if center_distance_error > 0.001  % 0.1%的误差容限
    % 微调变位系数
    adjustment = (target_center_distance - actual_center_distance) / a_standard * 0.5;
    x1 = x1 + adjustment * (z2 - z1) / (z1 + z2);
    x2 = x2 - adjustment * (z2 - z1) / (z1 + z2);
    
    % 再次确保范围
    x1 = max(-0.5, min(1.0, x1));
    x2 = max(-0.5, min(1.0, x2));
end

end
