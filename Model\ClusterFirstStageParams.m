function clustered_params = ClusterFirstStageParams(first_stage_params)
% ClusterFirstStageParams 对一级平行轴系参数进行聚类
%   将相似参数（模数、齿数、螺旋角、压力角、齿宽系数相同，只有变位系数不同）归为一组，
%   每组只保留质量最小的那个解。
%
%   输入参数:
%   - first_stage_params: 一级平行轴系参数表格（从MAT文件加载的表格）
%
%   输出:
%   - clustered_params: 聚类后的参数表格，每组只保留质量最小的那个解

% 检查输入参数
if isempty(first_stage_params) || height(first_stage_params) == 0
    clustered_params = first_stage_params;
    fprintf('警告：输入参数为空，无法进行聚类\n');
    return;
end

% 获取核心参数列名
core_param_names = {'模数(mm)', '小齿轮齿数', '大齿轮齿数', '螺旋角(°)', '压力角(°)'};
mass_col_name = '总质量(kg)';

% 检查列名是否存在
for i = 1:length(core_param_names)
    if ~ismember(core_param_names{i}, first_stage_params.Properties.VariableNames)
        fprintf('警告：输入表格中缺少列 "%s"，无法进行聚类\n', core_param_names{i});
        clustered_params = first_stage_params;
        return;
    end
end

if ~ismember(mass_col_name, first_stage_params.Properties.VariableNames)
    fprintf('警告：输入表格中缺少列 "%s"，无法进行聚类\n', mass_col_name);
    clustered_params = first_stage_params;
    return;
end

% 提取核心参数
core_params = first_stage_params{:, core_param_names};

% 创建一个唯一标识符
% 将浮点数参数四舍五入到小数点后一位，以便更好地聚类
rounded_core_params = core_params;
rounded_core_params(:,1) = round(rounded_core_params(:,1) * 10) / 10; % 模数
rounded_core_params(:,4) = round(rounded_core_params(:,4) * 10) / 10; % 螺旋角
rounded_core_params(:,5) = round(rounded_core_params(:,5) * 10) / 10; % 压力角

% 创建唯一标识符字符串
identifiers = cell(height(rounded_core_params), 1);
for i = 1:height(rounded_core_params)
    identifiers{i} = sprintf('%.1f_%d_%d_%.1f_%.1f', ...
        rounded_core_params(i,1), ... % 模数
        rounded_core_params(i,2), ... % 小齿轮齿数
        rounded_core_params(i,3), ... % 大齿轮齿数
        rounded_core_params(i,4), ... % 螺旋角
        rounded_core_params(i,5));    % 压力角
end

% 获取唯一标识符
unique_identifiers = unique(identifiers);
fprintf('找到 %d 组不同的核心参数组合\n', length(unique_identifiers));

% 为每个唯一标识符找到质量最小的参数组
selected_indices = [];
for i = 1:length(unique_identifiers)
    % 找到具有相同标识符的所有行
    group_indices = find(strcmp(identifiers, unique_identifiers{i}));
    
    % 获取这些行的质量
    group_masses = first_stage_params{group_indices, mass_col_name};
    
    % 找到质量最小的行索引
    [~, min_idx] = min(group_masses);
    
    % 将该行添加到选定索引
    selected_indices = [selected_indices; group_indices(min_idx)];
end

% 按质量排序
[~, sort_idx] = sort(first_stage_params{selected_indices, mass_col_name});
selected_indices = selected_indices(sort_idx);

% 创建聚类后的参数表格
clustered_params = first_stage_params(selected_indices, :);

fprintf('聚类后保留 %d 组参数\n', height(clustered_params));

% 自动保存聚类结果到Results目录
try
    % 确保Results目录存在
    results_dir = 'Results';
    if ~exist(results_dir, 'dir')
        mkdir(results_dir);
        fprintf('创建结果文件夹: %s\n', results_dir);
    end
    
    % 对变位系数进行四位小数处理
    shift_coeff_columns = {'小齿轮变位系数', '大齿轮变位系数', '综合变位系数'};
    for i = 1:length(shift_coeff_columns)
        col_name = shift_coeff_columns{i};
        if ismember(col_name, clustered_params.Properties.VariableNames)
            clustered_params.(col_name) = round(clustered_params.(col_name), 4);
        end
    end

    % 对齿轮质量进行两位小数处理
    mass_columns = {'总质量(kg)', '小齿轮质量(kg)', '大齿轮质量(kg)'};
    for i = 1:length(mass_columns)
        col_name = mass_columns{i};
        if ismember(col_name, clustered_params.Properties.VariableNames)
            clustered_params.(col_name) = round(clustered_params.(col_name), 2);
        end
    end

    % 对安全系数进行三位小数处理
    safety_columns = {'小齿轮弯曲安全系数', '大齿轮弯曲安全系数', '接触安全系数'};
    for i = 1:length(safety_columns)
        col_name = safety_columns{i};
        if ismember(col_name, clustered_params.Properties.VariableNames)
            clustered_params.(col_name) = round(clustered_params.(col_name), 3);
        end
    end

    % 对传动比进行三位小数处理
    ratio_columns = {'传动比', '一级传动比'};
    for i = 1:length(ratio_columns)
        col_name = ratio_columns{i};
        if ismember(col_name, clustered_params.Properties.VariableNames)
            clustered_params.(col_name) = round(clustered_params.(col_name), 3);
        end
    end

    % 保存CSV文件
    clustered_file = fullfile(results_dir, '一级平行轴系聚类后参数.csv');
    writetable(clustered_params, clustered_file);
    fprintf('聚类结果已保存到: %s\n', clustered_file);
    
    % 同时保存为MAT文件，便于MATLAB直接读取
    clustered_mat_file = fullfile(results_dir, '一级平行轴系聚类后参数.mat');
    save(clustered_mat_file, 'clustered_params');
    fprintf('聚类结果已保存到: %s\n', clustered_mat_file);
catch ME
    fprintf('保存聚类结果时出错: %s\n', ME.message);
end

end
