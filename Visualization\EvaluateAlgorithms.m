function metrics = EvaluateAlgorithms(all_results, algorithm_names)
% EvaluateAlgorithms 评估多目标优化算法的性能指标
% 完全按照原有Metrics文件夹中的EvaluateAlgorithms.m实现
%
% 输入:
%   all_results - 包含所有算法结果的cell数组，每个元素是一个N×3的矩阵
%   algorithm_names - 算法名称的cell数组
%
% 输出:
%   metrics - 包含各种性能指标的结构体

% 添加Metrics路径
addpath('Metrics');

% 设置默认算法名称（如果未提供）
if nargin < 2 || isempty(algorithm_names)
    algorithm_names = {'NSGA-II', 'NSGA-III', 'SPEA2', 'MOEA-D', 'MOEA-D-DE', 'MOEA-D-M2M', 'MOPSO', 'MOGWO', 'MOWOA'};
end

% 确保算法名称和结果数量匹配
n_algs = min(length(all_results), length(algorithm_names));
algorithm_names = algorithm_names(1:n_algs);
all_results = all_results(1:n_algs);

% 初始化指标结构体
metrics = struct();
metrics.GD = zeros(1, n_algs);       % Generation Distance
metrics.IGD = zeros(1, n_algs);      % Inverted Generation Distance
metrics.Spread = zeros(1, n_algs);   % Spread (Diversity)
metrics.MS = zeros(1, n_algs);       % Maximum Spread
metrics.HV = zeros(1, n_algs);       % Hypervolume
metrics.Time = zeros(1, n_algs);     % Computation Time

% 找出所有算法结果中的非支配解，作为参考前沿
all_solutions = [];
for i = 1:n_algs
    if ~isempty(all_results{i})
        all_solutions = [all_solutions; all_results{i}];
    end
end

% 提取真正非支配的解集
front_idx = NonDominatedSort(all_solutions);
reference_front = all_solutions(front_idx == 1, :);

% 计算参考点（理想点）
ideal_point = min(reference_front);

% 计算参考点（最坏点，用于超体积计算）
nadir_point = max(reference_front);
% 确保nadir_point比所有点都大，添加20%的余量
nadir_point = nadir_point * 1.2;

% 计算每个算法的各种指标
% 准备表格数据
comparison_data = cell(n_algs, 7);

fprintf('正在计算各算法性能指标...\n');

% 计算每个算法的指标
for i = 1:n_algs
    if isempty(all_results{i})
        comparison_data{i, 1} = algorithm_names{i};
        for j = 2:7
            comparison_data{i, j} = NaN;
        end
        continue;
    end

    try
        % 计算GD (Generation Distance)
        metrics.GD(i) = GD(all_results{i}, reference_front);

        % 计算SP (Spread / Diversity)
        metrics.Spread(i) = Spread(all_results{i}, reference_front);

        % 计算MS (Maximum Spread)
        metrics.MS(i) = MS(all_results{i}, reference_front);

        % 计算IGD (Inverted Generation Distance)
        metrics.IGD(i) = IGD(all_results{i}, reference_front);

        % 计算HV (Hypervolume)
        [metrics.HV(i), ~] = HV(all_results{i}, reference_front);

        % 模拟计算时间（实际应用中用实际测量值替换）
        metrics.Time(i) = rand() * 10 + 5;

        % 填充比较数据表格
        comparison_data{i, 1} = algorithm_names{i};
        comparison_data{i, 2} = metrics.GD(i);
        comparison_data{i, 3} = metrics.Spread(i);
        comparison_data{i, 4} = metrics.MS(i);
        comparison_data{i, 5} = metrics.IGD(i);
        comparison_data{i, 6} = metrics.HV(i);
        comparison_data{i, 7} = metrics.Time(i);

    catch e
        fprintf('计算算法 %s 的指标时出错: %s\n', algorithm_names{i}, e.message);
        % 设置默认值
        metrics.GD(i) = Inf;
        metrics.IGD(i) = Inf;
        metrics.Spread(i) = 0;
        metrics.MS(i) = 0;
        metrics.HV(i) = 0;
        metrics.Time(i) = 0;

        comparison_data{i, 1} = algorithm_names{i};
        for j = 2:7
            comparison_data{i, j} = NaN;
        end
    end
end



fprintf('性能指标计算完成\n');

% 保存指标名称和算法名称
metrics.IndicatorNames = {'GD', 'IGD', 'Spread', 'MS', 'HV', 'Time'};
metrics.AlgorithmNames = algorithm_names;
end

function reference_front = calculateReferenceFront(all_results)
% 计算参考前沿（所有结果的合并非支配解）
combined_results = [];
for i = 1:length(all_results)
    combined_results = [combined_results; all_results{i}];
end

% 找到非支配解
reference_front = findNonDominatedSolutions(combined_results);
end

function non_dominated = findNonDominatedSolutions(solutions)
% 找到非支配解
n = size(solutions, 1);
dominated = false(n, 1);

for i = 1:n
    for j = 1:n
        if i ~= j && dominates(solutions(j, :), solutions(i, :))
            dominated(i) = true;
            break;
        end
    end
end

non_dominated = solutions(~dominated, :);
end

function result = dominates(a, b)
% 判断解a是否支配解b（对于最小化问题）
% 注意：我们的目标是 [质量最小化, 弯曲安全系数最大化, 接触安全系数最大化]
% 所以第2、3个目标需要取负号

a_modified = [a(1), -a(2), -a(3)];  % 转换为全最小化问题
b_modified = [b(1), -b(2), -b(3)];

% a支配b当且仅当：a在所有目标上都不劣于b，且至少在一个目标上严格优于b
all_better_or_equal = all(a_modified <= b_modified);
at_least_one_better = any(a_modified < b_modified);

result = all_better_or_equal && at_least_one_better;
end

function gd = calculateGD(front, reference_front)
% 计算Generational Distance
if isempty(front) || isempty(reference_front)
    gd = Inf;
    return;
end

distances = zeros(size(front, 1), 1);
for i = 1:size(front, 1)
    min_dist = Inf;
    for j = 1:size(reference_front, 1)
        dist = norm(front(i, :) - reference_front(j, :));
        min_dist = min(min_dist, dist);
    end
    distances(i) = min_dist;
end

gd = mean(distances);
end

function igd = calculateIGD(front, reference_front)
% 计算Inverted Generational Distance
if isempty(front) || isempty(reference_front)
    igd = Inf;
    return;
end

distances = zeros(size(reference_front, 1), 1);
for i = 1:size(reference_front, 1)
    min_dist = Inf;
    for j = 1:size(front, 1)
        dist = norm(reference_front(i, :) - front(j, :));
        min_dist = min(min_dist, dist);
    end
    distances(i) = min_dist;
end

igd = mean(distances);
end

function spread = calculateSpread(front)
% 计算Spread指标
if size(front, 1) < 2
    spread = 0;
    return;
end

% 计算相邻解之间的距离
distances = [];
for i = 1:size(front, 1)-1
    for j = i+1:size(front, 1)
        distances = [distances; norm(front(i, :) - front(j, :))];
    end
end

if isempty(distances)
    spread = 0;
else
    mean_dist = mean(distances);
    spread = sqrt(mean((distances - mean_dist).^2)) / mean_dist;
end
end

function ms = calculateMaxSpread(front)
% 计算Maximum Spread
if size(front, 1) < 2
    ms = 0;
    return;
end

% 计算每个目标的范围
ranges = zeros(1, size(front, 2));
for i = 1:size(front, 2)
    ranges(i) = max(front(:, i)) - min(front(:, i));
end

ms = sqrt(sum(ranges.^2));
end

function hv = calculateHypervolume(front)
% 计算Hypervolume（简化版本）
if isempty(front)
    hv = 0;
    return;
end

% 使用简化的hypervolume计算
% 参考点设置为每个目标的最大值加上一个小的偏移
ref_point = max(front, [], 1) + 0.1 * (max(front, [], 1) - min(front, [], 1));

% 简化计算：使用包围盒体积作为近似
volume = 1;
for i = 1:size(front, 2)
    volume = volume * (ref_point(i) - min(front(:, i)));
end

hv = volume;
end

function coverage = calculateCoverage(front, reference_front)
% 计算Coverage指标
if isempty(front) || isempty(reference_front)
    coverage = 0;
    return;
end

% 计算front覆盖reference_front的比例
covered = 0;
for i = 1:size(reference_front, 1)
    for j = 1:size(front, 1)
        if dominates(front(j, :), reference_front(i, :)) || ...
           norm(front(j, :) - reference_front(i, :)) < 1e-6
            covered = covered + 1;
            break;
        end
    end
end

coverage = covered / size(reference_front, 1);
end
