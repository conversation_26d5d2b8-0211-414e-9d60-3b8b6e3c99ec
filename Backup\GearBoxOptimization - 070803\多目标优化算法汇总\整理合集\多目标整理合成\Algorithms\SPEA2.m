function [PF, PSet] = SPEA2(CostFunction, nVar, VarMin, VarMax, MaxIt, nPop)
% SPEA2: 强度Pareto进化算法2 (Strength Pareto Evolutionary Algorithm 2)
%
% 输入参数:
%   CostFunction: 目标函数句柄
%   nVar: 决策变量数量
%   VarMin, VarMax: 决策变量的上下界
%   MaxIt: 最大迭代次数
%   nPop: 种群大小
%
% 输出参数:
%   PF: 非支配解的目标函数值（Pareto前沿）
%   PSet: 非支配解的决策变量（Pareto集）
%
% 参考文献:
%   <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, & <PERSON>, <PERSON> (2001). 
%   "SPEA2: Improving the strength Pareto evolutionary algorithm."
%   TIK-report, 103.

    % 默认参数值
    if nargin < 6
        nPop = 50;
    end
    if nargin < 5
        MaxIt = 200;
    end
    if nargin < 4
        VarMax = 1;
    end
    if nargin < 3
        VarMin = 0;
    end
    if nargin < 2
        nVar = 30;
    end
    
    % 算法参数
    nArchive = 50;                         % 存档大小
    K = round(sqrt(nPop + nArchive));      % KNN参数
    pCrossover = 0.7;                      % 交叉概率
    nCrossover = round(pCrossover * nPop / 2) * 2;
    pMutation = 1 - pCrossover;            % 变异概率
    nMutation = nPop - nCrossover;
    
    % 交叉参数
    crossover_params.gamma = 0.1;          % 交叉参数
    crossover_params.VarMin = VarMin;      % 下界
    crossover_params.VarMax = VarMax;      % 上界
    
    % 变异参数
    mutation_params.h = 0.2;               % 变异步长
    mutation_params.VarMin = VarMin;       % 下界
    mutation_params.VarMax = VarMax;       % 上界
    
    % 初始化个体结构体
    empty_individual.Position = [];
    empty_individual.Cost = [];
    empty_individual.S = [];               % 强度值
    empty_individual.R = [];               % 原始适应度
    empty_individual.sigma = [];           % 距离数组
    empty_individual.sigmaK = [];          % 第K近邻居的距离
    empty_individual.D = [];               % 密度估计
    empty_individual.F = [];               % 适应度
    
    % 初始化种群
    pop = repmat(empty_individual, nPop, 1);
    for i = 1:nPop
        pop(i).Position = unifrnd(VarMin, VarMax, [1, nVar]);
        pop(i).Cost = CostFunction(pop(i).Position);
    end
    
    % 初始化存档
    archive = [];
    
    % 主循环
    for it = 1:MaxIt
        % 合并种群和存档
        Q = [pop; archive];
        nQ = numel(Q);
        
        % 初始化支配计数
        dom = false(nQ, nQ);
        
        % 初始化强度值
        for i = 1:nQ
            Q(i).S = 0;
        end
        
        % 计算强度值
        for i = 1:nQ
            for j = i+1:nQ
                if Dominates(Q(i).Cost, Q(j).Cost)
                    Q(i).S = Q(i).S + 1;
                    dom(i, j) = true;
                elseif Dominates(Q(j).Cost, Q(i).Cost)
                    Q(j).S = Q(j).S + 1;
                    dom(j, i) = true;
                end
            end
        end
        
        % 计算原始适应度
        S = [Q.S];
        for i = 1:nQ
            Q(i).R = sum(S(dom(:, i)));
        end
        
        % 计算密度估计
        Z = [Q.Cost]';
        SIGMA = pdist2(Z', Z', 'seuclidean');
        SIGMA = sort(SIGMA);
        for i = 1:nQ
            Q(i).sigma = SIGMA(:, i);
            Q(i).sigmaK = Q(i).sigma(min(K, nQ));
            Q(i).D = 1 / (Q(i).sigmaK + 2);
            Q(i).F = Q(i).R + Q(i).D;
        end
        
        % 选择存档成员
        nND = sum([Q.R] == 0);
        if nND <= nArchive
            % 如果非支配解数量小于等于存档大小，按适应度排序
            F = [Q.F];
            [~, SO] = sort(F);
            Q = Q(SO);
            archive = Q(1:min(nArchive, nQ));
        else
            % 如果非支配解数量大于存档大小，按密度裁剪
            SIGMA = SIGMA(:, [Q.R] == 0);
            archive = Q([Q.R] == 0);
            
            % 裁剪存档
            k = 2;
            while numel(archive) > nArchive
                while min(SIGMA(k, :)) == max(SIGMA(k, :)) && k < size(SIGMA, 1)
                    k = k + 1;
                end
                
                [~, j] = min(SIGMA(k, :));
                
                % 删除距离最近的个体
                archive(j) = [];
                SIGMA(:, j) = [];
            end
        end
        
        % 获取近似Pareto前沿
        PF_temp = archive([archive.R] == 0);
        
        % 如果达到最大迭代次数，退出循环
        if it >= MaxIt
            break;
        end
        
        % 交叉操作
        popc = repmat(empty_individual, nCrossover/2, 2);
        for c = 1:nCrossover/2
            % 二元锦标赛选择
            p1 = BinaryTournamentSelection(archive, [archive.F]);
            p2 = BinaryTournamentSelection(archive, [archive.F]);
            
            % 交叉操作
            [popc(c, 1).Position, popc(c, 2).Position] = Crossover(p1.Position, p2.Position, crossover_params);
            
            % 评估子代
            popc(c, 1).Cost = CostFunction(popc(c, 1).Position);
            popc(c, 2).Cost = CostFunction(popc(c, 2).Position);
        end
        popc = popc(:);
        
        % 变异操作
        popm = repmat(empty_individual, nMutation, 1);
        for m = 1:nMutation
            % 二元锦标赛选择
            p = BinaryTournamentSelection(archive, [archive.F]);
            
            % 变异操作
            popm(m).Position = Mutate(p.Position, mutation_params);
            
            % 评估变异个体
            popm(m).Cost = CostFunction(popm(m).Position);
        end
        
        % 创建新种群
        pop = [popc; popm];
    end
    
    % 提取最终Pareto前沿
    PF_members = archive([archive.R] == 0);
    
    % 提取决策变量和目标函数值
    nPF = numel(PF_members);
    PSet = zeros(nPF, nVar);
    PF = zeros(nPF, size(PF_members(1).Cost, 2));
    
    for i = 1:nPF
        PSet(i, :) = PF_members(i).Position;
        PF(i, :) = PF_members(i).Cost;
    end
end

% 检查支配关系
function result = Dominates(x, y)
    % x支配y，如果x的所有目标都不劣于y，且至少有一个目标严格优于y
    result = all(x <= y) && any(x < y);
end

% 二元锦标赛选择
function p = BinaryTournamentSelection(pop, F)
    n = numel(pop);
    
    % 随机选择两个个体
    i1 = randi(n);
    i2 = randi(n);
    
    % 选择适应度更好的个体
    if F(i1) < F(i2)
        p = pop(i1);
    else
        p = pop(i2);
    end
end

% 模拟二进制交叉
function [y1, y2] = Crossover(x1, x2, params)
    gamma = params.gamma;
    VarMin = params.VarMin;
    VarMax = params.VarMax;
    
    % 判断是否为向量
    if isscalar(VarMin)
        VarMin = VarMin * ones(size(x1));
    end
    if isscalar(VarMax)
        VarMax = VarMax * ones(size(x1));
    end
    
    % 交叉操作
    alpha = rand(size(x1)) * (1 + 2 * gamma) - gamma;
    
    y1 = alpha .* x1 + (1 - alpha) .* x2;
    y2 = alpha .* x2 + (1 - alpha) .* x1;
    
    % 边界处理
    y1 = max(y1, VarMin);
    y1 = min(y1, VarMax);
    
    y2 = max(y2, VarMin);
    y2 = min(y2, VarMax);
end

% 多项式变异
function y = Mutate(x, params)
    h = params.h;
    VarMin = params.VarMin;
    VarMax = params.VarMax;
    
    % 判断是否为向量
    if isscalar(VarMin)
        VarMin = VarMin * ones(size(x));
    end
    if isscalar(VarMax)
        VarMax = VarMax * ones(size(x));
    end
    
    % 变异操作
    y = x;
    
    % 对每个决策变量随机变异
    j = randi(numel(x));
    
    sigma = h * (VarMax(j) - VarMin(j));
    
    y(j) = x(j) + sigma * randn;
    
    % 边界处理
    y = max(y, VarMin);
    y = min(y, VarMax);
end 