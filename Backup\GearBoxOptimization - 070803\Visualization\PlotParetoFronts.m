function PlotParetoFronts(optimization_results, metrics, figure_save_path)
% PLOTPARETOFRONTS 绘制并保存Pareto前沿图
%
% 输入:
%   optimization_results - 优化结果结构体
%   metrics - 算法评价指标结构体
%   figure_save_path - 图形保存路径

% 获取优化结果
all_results = optimization_results.all_results;
all_var_results = optimization_results.all_var_results;
alg_names = optimization_results.alg_names;
selected_vars = optimization_results.selected_vars;

% 确定目标数量（从第一个非空结果中获取）
n_objectives = 0;
for i = 1:length(all_results)
    if ~isempty(all_results{i})
        n_objectives = size(all_results{i}, 2);
        break;
    end
end

% 如果没有找到有效结果，使用默认值
if n_objectives == 0
    n_objectives = 3; % 默认为3目标问题
end

% 如果指标为空，初始化metrics结构体
if isempty(metrics)
    metrics = struct();
end

% 确保结果文件夹存在
if ~exist(figure_save_path, 'dir')
    mkdir(figure_save_path);
end

% 提取目标函数值
all_f1 = []; all_f2 = []; all_f3 = [];

for i = 1:length(all_results)
    if ~isempty(all_results{i})
        all_f1 = [all_f1; all_results{i}(:, 1)];
        all_f2 = [all_f2; -all_results{i}(:, 2)];
        if n_objectives >= 3
            all_f3 = [all_f3; -all_results{i}(:, 3)];
        end
    end
end

% 创建一个包含所有Pareto前沿图的单一图窗 - 使用适中的布局
fig_main = figure('Name', 'Pareto前沿图', 'NumberTitle', 'off', 'Color', 'white', 'Position', [100, 100, 1200, 450]);

% 设置默认字体为宋体，确保所有文字统一
set(0, 'DefaultAxesFontName', 'SimSun');
set(0, 'DefaultTextFontName', 'SimSun');
set(0, 'DefaultUicontrolFontName', 'SimSun');
set(0, 'DefaultAxesFontSize', 12);
set(0, 'DefaultTextFontSize', 12);

% 根据目标函数数量绘制不同维度的Pareto前沿
if n_objectives == 2
    % 二维情况：2个子图在同一行
    subplot(1, 2, 1);
    PlotMassVsBendingSafety(all_results, alg_names, all_f1, all_f2);
    title('质量vs弯曲安全系数', 'FontSize', 12, 'FontWeight', 'bold', 'FontName', 'SimSun');
    
    subplot(1, 2, 2);
    PlotMassVsContactSafety(all_results, alg_names, all_f1, all_f3);
    title('质量vs接触安全系数', 'FontSize', 12, 'FontWeight', 'bold', 'FontName', 'SimSun');
    
elseif n_objectives == 3
    % 三维情况：3个子图在同一行，增加间距
    subplot(1, 3, 1);
    Plot3DParetoFront(all_results, alg_names, all_f1, all_f2, all_f3, 'southeast'); % 图例位置设为右下角
    title('三维Pareto前沿', 'FontSize', 12, 'FontWeight', 'bold', 'FontName', 'SimSun');
    
    subplot(1, 3, 2);
    PlotMassVsBendingSafety(all_results, alg_names, all_f1, all_f2);
    title('质量vs弯曲安全系数', 'FontSize', 12, 'FontWeight', 'bold', 'FontName', 'SimSun');
    
    subplot(1, 3, 3);
    PlotMassVsContactSafety(all_results, alg_names, all_f1, all_f3);
    title('质量vs接触安全系数', 'FontSize', 12, 'FontWeight', 'bold', 'FontName', 'SimSun');
end

% 调整子图布局，增加间距
set(fig_main, 'Units', 'normalized');
if n_objectives == 3
    % 调整子图间距，确保标题不重叠
    p1 = get(subplot(1,3,1), 'Position');
    p2 = get(subplot(1,3,2), 'Position');
    p3 = get(subplot(1,3,3), 'Position');
    
    % 调整为合理的大小和位置
    p1(1) = 0.08;    % 左侧图向左移
    p1(2) = 0.20;    % 底部位置
    p1(3) = 0.24;    % 左侧图宽度
    p1(4) = 0.65;    % 图高度
    
    p2(1) = 0.38;    % 中间图位置
    p2(2) = 0.20;    % 底部位置
    p2(3) = 0.24;    % 中间图宽度
    p2(4) = 0.65;    % 图高度
    
    p3(1) = 0.68;    % 右侧图位置
    p3(2) = 0.20;    % 底部位置
    p3(3) = 0.24;    % 右侧图宽度
    p3(4) = 0.65;    % 图高度
    
    set(subplot(1,3,1), 'Position', p1);
    set(subplot(1,3,2), 'Position', p2);
    set(subplot(1,3,3), 'Position', p3);
end

% 添加总标题
sgtitle('多目标优化算法Pareto前沿比较', 'FontSize', 12, 'FontWeight', 'bold', 'FontName', 'SimSun');

% 保存前沿图
saveas(fig_main, fullfile(figure_save_path, 'pareto_fronts.png'));
fprintf('已保存图表: %s\n', fullfile(figure_save_path, 'pareto_fronts.png'));

fprintf('完成! 所有Pareto前沿图表已保存到%s文件夹\n', figure_save_path);
end

function [varargout] = Plot3DParetoFront(all_results, alg_names, f1_all, f2_all, f3_all, legend_position)
    % 绘制三维Pareto前沿
    % 如果未指定图例位置，默认为东北角
    if nargin < 6
        legend_position = 'northeast';
    end
    
    % 设置统一字体
    chineseFont = 'SimSun';
    fontSize = 12;
    
    % 定义更加鲜艳活泼的颜色方案
    colors = [
        [1.0000, 0.0000, 0.0000];  % 红色 - NSGA-II
        [0.0000, 0.0000, 1.0000];  % 蓝色 - NSGA-III
        [0.0000, 0.7000, 0.0000];  % 鲜绿色 - SPEA2
        [0.8000, 0.0000, 0.8000];  % 亮紫色 - MOEA-D
        [0.0000, 0.8000, 0.8000];  % 青绿色 - MOEA-D-DE
        [1.0000, 0.6000, 0.0000];  % 橙色 - MOEA-D-M2M
        [0.5000, 0.0000, 1.0000];  % 紫色 - MOPSO
        [1.0000, 1.0000, 0.0000];  % 鲜黄色 - MOGWO (改为大黄色)
        [1.0000, 0.0000, 0.5000];  % 品红色 - MOWOA (修改为品红色，避免与橙色太相似)
    ];
    
    % 定义边线颜色 - 增强立体感
    edgeColors = [
        [0.7000, 0.0000, 0.0000];  % 深红色边线
        [0.0000, 0.0000, 0.7000];  % 深蓝色边线
        [0.0000, 0.5000, 0.0000];  % 深绿色边线
        [0.6000, 0.0000, 0.6000];  % 深紫色边线
        [0.0000, 0.6000, 0.6000];  % 深青绿色边线
        [0.7000, 0.4000, 0.0000];  % 深橙色边线
        [0.3500, 0.0000, 0.7000];  % 深紫色边线
        [0.7500, 0.7500, 0.0000];  % 深黄色边线 - MOGWO (改为深黄色边线)
        [0.7000, 0.0000, 0.4000];  % 深品红色边线 (修改为深品红色)
    ];
    
    % 定义标记样式 - 使用更清晰区分的形状和大小
    markers = {'o', 's', 'd', '^', 'v', 'p', '>', 'h', '<'};
    markerSizes = [50, 55, 45, 60, 50, 55, 45, 60, 50]; % 不同大小的标记
    
    % 不需要在子图中创建新的figure，直接使用当前的axes
    % 获取当前的axes
    fig = gcf; % 获取当前figure
    
    % 创建轴并保持
    hold on;
    grid on;
    box on;
    
    % 图例条目
    legendEntries = {};
    
    % 绘制每个算法的Pareto前沿（仅使用散点）
    for i = 1:length(all_results)
        if isempty(all_results{i})
            continue;
        end
        
        % 提取目标函数值
        f1 = all_results{i}(:, 1);        % 质量
        f2 = -all_results{i}(:, 2);       % 弯曲安全系数
        f3 = -all_results{i}(:, 3);       % 接触安全系数
        
        % 当前算法的颜色和标记
        colorIdx = mod(i-1, size(colors, 1))+1;
        markerIdx = mod(i-1, length(markers))+1;
        
        % 使用散点图绘制Pareto前沿 - 修改坐标轴顺序，使z轴表示质量
        % 添加少量随机抖动以减少重叠
        jitter = 0.01; % 抖动量
        f2_jitter = f2 + (rand(size(f2))-0.5)*jitter*mean(f2);
        f3_jitter = f3 + (rand(size(f3))-0.5)*jitter*mean(f3);
        
        scatter3(f2_jitter, f3_jitter, f1, markerSizes(colorIdx), ...  % 使用不同大小的标记
               'filled', ...
               markers{markerIdx}, ...
               'MarkerEdgeColor', edgeColors(colorIdx,:), ...  % 使用更深的边线颜色增强立体感
               'LineWidth', 1.2, ...         % 增加线条粗细，增强立体感
               'MarkerFaceColor', colors(colorIdx, :), ...
               'MarkerFaceAlpha', 0.8, ... % 添加一些透明度
               'DisplayName', alg_names{i});
        
        legendEntries{end+1} = alg_names{i};
    end
    
    % 设置坐标轴范围
    xlim([min(f2_all)*0.95, max(f2_all)*1.05]);
    ylim([min(f3_all)*0.95, max(f3_all)*1.05]);
    zlim([min(f1_all)*0.95, max(f1_all)*1.05]);
    
    % 设置固定视角，调整为更好地查看
    view([-30, 25]);
    
    % 设置坐标轴标签 - 使用宋体
    xlabel('弯曲安全系数', 'FontName', chineseFont, 'FontSize', fontSize);
    ylabel('接触安全系数', 'FontName', chineseFont, 'FontSize', fontSize);
    zlabel('总质量 (kg)', 'FontName', chineseFont, 'FontSize', fontSize);
    
    % 美化坐标轴 - 学术论文风格
    ax = gca;
    ax.FontName = chineseFont;
    ax.FontSize = fontSize;
    ax.LineWidth = 1.0;         % 适中坐标轴线条粗细
    ax.GridLineStyle = ':';
    ax.GridAlpha = 0.1;         % 更低的网格线透明度，不干扰数据点
    ax.TickLength = [0.01 0.01];
    ax.TickDir = 'in';          % 内向刻度
    ax.XColor = [0.2 0.2 0.2];  % 深灰色坐标轴，更专业
    ax.YColor = [0.2 0.2 0.2];
    ax.ZColor = [0.2 0.2 0.2];
    % 设置背景为白色
    ax.Color = 'white';
    
    % 添加图例，放在指定位置，设置为学术论文风格
    lgd = legend(legendEntries, 'Location', legend_position, 'FontName', chineseFont, 'FontSize', fontSize-1);
    lgd.Box = 'on';
    lgd.LineWidth = 0.5;  % 更细的边框线条
    % 设置图例中的标记大小为适中值
    for i = 1:length(lgd.ItemTokenSize)
        lgd.ItemTokenSize(i) = 6;  % 减小图例中的标记
    end
    
    % 返回图形句柄
    if nargout > 0
        varargout{1} = fig;
    end
end

function [varargout] = PlotMassVsBendingSafety(all_results, alg_names, f1_all, f2_all)
    % 绘制质量vs弯曲安全系数图
    
    % 设置统一字体
    chineseFont = 'SimSun';
    fontSize = 12;
    
    % 定义更加鲜艳活泼的颜色方案
    colors = [
        [1.0000, 0.0000, 0.0000];  % 红色 - NSGA-II
        [0.0000, 0.0000, 1.0000];  % 蓝色 - NSGA-III
        [0.0000, 0.7000, 0.0000];  % 鲜绿色 - SPEA2
        [0.8000, 0.0000, 0.8000];  % 亮紫色 - MOEA-D
        [0.0000, 0.8000, 0.8000];  % 青绿色 - MOEA-D-DE
        [1.0000, 0.6000, 0.0000];  % 橙色 - MOEA-D-M2M
        [0.5000, 0.0000, 1.0000];  % 紫色 - MOPSO
        [1.0000, 1.0000, 0.0000];  % 鲜黄色 - MOGWO (改为大黄色)
        [1.0000, 0.0000, 0.5000];  % 品红色 - MOWOA
    ];
    
    % 定义边线颜色 - 增强立体感
    edgeColors = [
        [0.7000, 0.0000, 0.0000];  % 深红色边线
        [0.0000, 0.0000, 0.7000];  % 深蓝色边线
        [0.0000, 0.5000, 0.0000];  % 深绿色边线
        [0.6000, 0.0000, 0.6000];  % 深紫色边线
        [0.0000, 0.6000, 0.6000];  % 深青绿色边线
        [0.7000, 0.4000, 0.0000];  % 深橙色边线
        [0.3500, 0.0000, 0.7000];  % 深紫色边线
        [0.7500, 0.7500, 0.0000];  % 深黄色边线 - MOGWO (改为深黄色边线)
        [0.7000, 0.0000, 0.4000];  % 深品红色边线
    ];
    
    % 定义标记样式 - 使用更清晰区分的形状和大小
    markers = {'o', 's', 'd', '^', 'v', 'p', '>', 'h', '<'};
    markerSizes = [50, 55, 45, 60, 50, 55, 45, 60, 50]; % 不同大小的标记
    
    % 获取当前figure
    fig = gcf;
    
    % 创建轴并保持
    hold on;
    grid on;
    box on;
    
    % 图例条目
    legendEntries = {};
    
    % 绘制每个算法的Pareto前沿（仅使用散点）
    for i = 1:length(all_results)
        if isempty(all_results{i})
            continue;
        end
        
        % 提取目标函数值
        f1 = all_results{i}(:, 1);        % 质量
        f2 = -all_results{i}(:, 2);       % 弯曲安全系数
        
        % 当前算法的颜色和标记
        colorIdx = mod(i-1, size(colors, 1))+1;
        markerIdx = mod(i-1, length(markers))+1;
        
        % 添加少量随机抖动以减少重叠
        jitter = 0.01; % 抖动量
        f1_jitter = f1 + (rand(size(f1))-0.5)*jitter*mean(f1);
        f2_jitter = f2 + (rand(size(f2))-0.5)*jitter*mean(f2);
        
        % 使用散点图绘制Pareto前沿
        scatter(f1_jitter, f2_jitter, markerSizes(colorIdx), ...  % 使用不同大小的标记
               'filled', ...
               markers{markerIdx}, ...
               'MarkerEdgeColor', edgeColors(colorIdx,:), ...  % 使用更深的边线颜色增强立体感
               'LineWidth', 1.2, ...         % 增加线条粗细，增强立体感
               'MarkerFaceColor', colors(colorIdx, :), ...
               'MarkerFaceAlpha', 0.8, ... % 添加一些透明度
               'DisplayName', alg_names{i});
        
        legendEntries{end+1} = alg_names{i};
    end
    
    % 设置坐标轴范围
    xlim([min(f1_all)*0.95, max(f1_all)*1.05]);
    ylim([min(f2_all)*0.95, max(f2_all)*1.05]);
        
    % 设置坐标轴标签 - 使用宋体
    xlabel('总质量 (kg)', 'FontName', chineseFont, 'FontSize', fontSize);
    ylabel('弯曲安全系数', 'FontName', chineseFont, 'FontSize', fontSize);
    
    % 美化坐标轴 - 学术论文风格
    ax = gca;
    ax.FontName = chineseFont;
    ax.FontSize = fontSize;
    ax.LineWidth = 1.0;         % 适中坐标轴线条粗细
    ax.GridLineStyle = ':';
    ax.GridAlpha = 0.1;         % 更低的网格线透明度，不干扰数据点
    ax.TickLength = [0.01 0.01];
    ax.TickDir = 'in';          % 内向刻度
    ax.XColor = [0.2 0.2 0.2];  % 深灰色坐标轴，更专业
    ax.YColor = [0.2 0.2 0.2];
    % 设置背景为白色
    ax.Color = 'white';
    
    % 确保标签与坐标轴平行
    ax.XLabel.Rotation = 0;  % 确保X标签水平
    ax.YLabel.Rotation = 90; % 确保Y标签垂直
    
    % 添加图例，放在右下角，设置为学术论文风格
    lgd = legend(legendEntries, 'Location', 'southeast', 'FontName', chineseFont, 'FontSize', fontSize-1);
    lgd.Box = 'on';
    lgd.LineWidth = 0.5;  % 更细的边框线条
    % 设置图例中的标记大小为适中值
    for i = 1:length(lgd.ItemTokenSize)
        lgd.ItemTokenSize(i) = 6;  % 调整图例中的标记大小
    end
    
    % 返回图形句柄
    if nargout > 0
        varargout{1} = fig;
    end
end

function [varargout] = PlotMassVsContactSafety(all_results, alg_names, f1_all, f3_all)
    % 绘制质量vs接触安全系数图
    
    % 设置统一字体
    chineseFont = 'SimSun';
    fontSize = 12;
    
    % 定义更加鲜艳活泼的颜色方案
    colors = [
        [1.0000, 0.0000, 0.0000];  % 红色 - NSGA-II
        [0.0000, 0.0000, 1.0000];  % 蓝色 - NSGA-III
        [0.0000, 0.7000, 0.0000];  % 鲜绿色 - SPEA2
        [0.8000, 0.0000, 0.8000];  % 亮紫色 - MOEA-D
        [0.0000, 0.8000, 0.8000];  % 青绿色 - MOEA-D-DE
        [1.0000, 0.6000, 0.0000];  % 橙色 - MOEA-D-M2M
        [0.5000, 0.0000, 1.0000];  % 紫色 - MOPSO
        [1.0000, 1.0000, 0.0000];  % 鲜黄色 - MOGWO (改为大黄色)
        [1.0000, 0.0000, 0.5000];  % 品红色 - MOWOA
    ];
    
    % 定义边线颜色 - 增强立体感
    edgeColors = [
        [0.7000, 0.0000, 0.0000];  % 深红色边线
        [0.0000, 0.0000, 0.7000];  % 深蓝色边线
        [0.0000, 0.5000, 0.0000];  % 深绿色边线
        [0.6000, 0.0000, 0.6000];  % 深紫色边线
        [0.0000, 0.6000, 0.6000];  % 深青绿色边线
        [0.7000, 0.4000, 0.0000];  % 深橙色边线
        [0.3500, 0.0000, 0.7000];  % 深紫色边线
        [0.7500, 0.7500, 0.0000];  % 深黄色边线 - MOGWO (改为深黄色边线)
        [0.7000, 0.0000, 0.4000];  % 深品红色边线
    ];
    
    % 定义标记样式 - 使用更清晰区分的形状和大小
    markers = {'o', 's', 'd', '^', 'v', 'p', '>', 'h', '<'};
    markerSizes = [50, 55, 45, 60, 50, 55, 45, 60, 50]; % 不同大小的标记
    
    % 获取当前figure
    fig = gcf;
    
    % 创建轴并保持
    hold on;
    grid on;
    box on;
    
    % 图例条目
    legendEntries = {};
    
    % 绘制每个算法的Pareto前沿（仅使用散点）
    for i = 1:length(all_results)
        if isempty(all_results{i})
            continue;
        end
        
        % 提取目标函数值
        f1 = all_results{i}(:, 1);        % 质量
        f3 = -all_results{i}(:, 3);       % 接触安全系数
        
        % 当前算法的颜色和标记
        colorIdx = mod(i-1, size(colors, 1))+1;
        markerIdx = mod(i-1, length(markers))+1;
        
        % 添加少量随机抖动以减少重叠
        jitter = 0.01; % 抖动量
        f1_jitter = f1 + (rand(size(f1))-0.5)*jitter*mean(f1);
        f3_jitter = f3 + (rand(size(f3))-0.5)*jitter*mean(f3);
        
        % 使用散点图绘制Pareto前沿
        scatter(f1_jitter, f3_jitter, markerSizes(colorIdx), ...  % 使用不同大小的标记
               'filled', ...
               markers{markerIdx}, ...
               'MarkerEdgeColor', edgeColors(colorIdx,:), ...  % 使用更深的边线颜色增强立体感
               'LineWidth', 1.2, ...         % 增加线条粗细，增强立体感
               'MarkerFaceColor', colors(colorIdx, :), ...
               'MarkerFaceAlpha', 0.8, ... % 添加一些透明度
               'DisplayName', alg_names{i});
        
        legendEntries{end+1} = alg_names{i};
    end
    
    % 设置坐标轴范围
    xlim([min(f1_all)*0.95, max(f1_all)*1.05]);
    ylim([min(f3_all)*0.95, max(f3_all)*1.05]);
    
    % 设置坐标轴标签 - 使用宋体
    xlabel('总质量 (kg)', 'FontName', chineseFont, 'FontSize', fontSize);
    ylabel('接触安全系数', 'FontName', chineseFont, 'FontSize', fontSize);
    
    % 美化坐标轴 - 学术论文风格
    ax = gca;
    ax.FontName = chineseFont;
    ax.FontSize = fontSize;
    ax.LineWidth = 1.0;         % 适中坐标轴线条粗细
    ax.GridLineStyle = ':';
    ax.GridAlpha = 0.1;         % 更低的网格线透明度，不干扰数据点
    ax.TickLength = [0.01 0.01];
    ax.TickDir = 'in';          % 内向刻度
    ax.XColor = [0.2 0.2 0.2];  % 深灰色坐标轴，更专业
    ax.YColor = [0.2 0.2 0.2];
    % 设置背景为白色
    ax.Color = 'white';
    
    % 确保标签与坐标轴平行
    ax.XLabel.Rotation = 0;  % 确保X标签水平
    ax.YLabel.Rotation = 90; % 确保Y标签垂直
    
    % 添加图例，放在右下角，设置为学术论文风格
    lgd = legend(legendEntries, 'Location', 'southeast', 'FontName', chineseFont, 'FontSize', fontSize-1);
    lgd.Box = 'on';
    lgd.LineWidth = 0.5;  % 更细的边框线条
    % 设置图例中的标记大小为适中值
    for i = 1:length(lgd.ItemTokenSize)
        lgd.ItemTokenSize(i) = 6;  % 调整图例中的标记大小
    end
    
    % 返回图形句柄
    if nargout > 0
        varargout{1} = fig;
    end
end

function [varargout] = PlotVariableRelationships(all_var_results, alg_names, var_names)
    % 绘制决策变量关系图
    
    % 设置中文字体
    set(0, 'DefaultAxesFontName', 'Times New Roman');
    set(0, 'DefaultTextFontName', 'Times New Roman');
    
    % 定义更加鲜艳活泼的颜色方案
    colors = [
        [1.0000, 0.0000, 0.0000];  % 红色 - NSGA-II
        [0.0000, 0.0000, 1.0000];  % 蓝色 - NSGA-III
        [0.0000, 0.7000, 0.0000];  % 鲜绿色 - SPEA2
        [0.8000, 0.0000, 0.8000];  % 亮紫色 - MOEA-D
        [0.0000, 0.8000, 0.8000];  % 青绿色 - MOEA-D-DE
        [1.0000, 0.6000, 0.0000];  % 橙色 - MOEA-D-M2M
        [0.5000, 0.0000, 1.0000];  % 紫色 - MOPSO
        [1.0000, 1.0000, 0.0000];  % 鲜黄色 - MOGWO
        [1.0000, 0.0000, 0.5000];  % 品红色 - MOWOA
    ];
    
    % 定义边线颜色 - 增强立体感
    edgeColors = [
        [0.7000, 0.0000, 0.0000];  % 深红色边线
        [0.0000, 0.0000, 0.7000];  % 深蓝色边线
        [0.0000, 0.5000, 0.0000];  % 深绿色边线
        [0.6000, 0.0000, 0.6000];  % 深紫色边线
        [0.0000, 0.6000, 0.6000];  % 深青绿色边线
        [0.7000, 0.4000, 0.0000];  % 深橙色边线
        [0.3500, 0.0000, 0.7000];  % 深紫色边线
        [0.7500, 0.7500, 0.0000];  % 深黄色边线
        [0.7000, 0.0000, 0.4000];  % 深品红色边线
    ];
    
    % 定义标记样式 - 使用更清晰区分的形状和大小
    markers = {'o', 's', 'd', '^', 'v', 'p', '>', 'h', '<'};
    markerSizes = [50, 55, 45, 60, 50, 55, 45, 60, 50]; % 不同大小的标记
    
    % 设置字体和大小 - 专业学术论文规范
    englishFont = 'Times New Roman';  % 英文使用Times New Roman
    chineseFont = 'SimSun';           % 中文使用宋体
    fontSize = 14;                    % 基础字体大小
    axisFontSize = 12;                % 坐标轴字体大小
    titleFontSize = 14;               % 标题字体大小
    labelFontSize = 14;               % 标签字体大小
    
    % 获取屏幕尺寸
    screenSize = get(0, 'ScreenSize');
    screenWidth = screenSize(3);
    screenHeight = screenSize(4);
    
    % 创建一个居中的图窗
    figWidth = min(1000, screenWidth * 0.8);
    figHeight = min(800, screenHeight * 0.8);
    figLeft = (screenWidth - figWidth) / 2;
    figBottom = (screenHeight - figHeight) / 2;
    
    % 创建图
    fig = figure('Position', [figLeft, figBottom, figWidth, figHeight], 'Color', 'white', 'Visible', 'on');
    
    % 创建轴并保持
    ax = axes('Parent', fig);
    hold(ax, 'on');
    grid(ax, 'on');
    box(ax, 'on');
    
    % 图例条目
    legendEntries = {};
    
    % 提取每个算法的变量值
    n_vars = length(var_names);
    
    % 计算子图的行列数
    if n_vars <= 3
        rows = 1;
        cols = n_vars;
    else
        rows = ceil(n_vars / 3);
        cols = min(3, n_vars);
    end
    
    % 绘制每个变量的分布
    for v = 1:n_vars
        subplot(rows, cols, v);
        hold on;
        grid on;
        box on;
        
        var_values_all = [];
        
        % 绘制每个算法的变量分布
        for i = 1:length(all_var_results)
            if isempty(all_var_results{i})
                continue;
            end
            
            % 提取当前变量值
            var_values = all_var_results{i}(:, v);
            var_values_all = [var_values_all; var_values];
            
            % 当前算法的颜色和标记
            colorIdx = mod(i-1, size(colors, 1))+1;
            markerIdx = mod(i-1, length(markers))+1;
            
            % 使用散点图绘制变量分布
            scatter(ones(size(var_values))*i, var_values, markerSizes(colorIdx)/2, ...  % 使用不同大小的标记
                   'filled', ...
                   markers{markerIdx}, ...
                   'MarkerEdgeColor', edgeColors(colorIdx,:), ...  % 使用更深的边线颜色增强立体感
                   'LineWidth', 1.0, ...         % 增加线条粗细，增强立体感
                   'MarkerFaceColor', colors(colorIdx, :), ...
                   'MarkerFaceAlpha', 0.7, ... % 添加一些透明度
                   'DisplayName', alg_names{i});
            
            if v == 1
                legendEntries{end+1} = alg_names{i};
            end
        end
        
        % 设置坐标轴范围
        xlim([0.5, length(all_var_results)+0.5]);
        if ~isempty(var_values_all)
            ylim([min(var_values_all)*0.95, max(var_values_all)*1.05]);
        end
        
        % 设置x轴刻度
        xticks(1:length(all_var_results));
        xticklabels(alg_names);
        xtickangle(45);
        
        % 设置标题 - 使用宋体，使用格式化方式确保中文正确显示
        title(['{\fontname{', chineseFont, '}\fontsize{', num2str(titleFontSize-2), '}', var_names{v}, '}'], ...
            'FontName', englishFont, 'FontSize', titleFontSize-2, 'FontWeight', 'normal');
        
        % 美化坐标轴 - 学术论文风格
        ax = gca;
        ax.FontName = englishFont;
        ax.FontSize = axisFontSize-2;
        ax.LineWidth = 0.8;         % 适中坐标轴线条粗细
        ax.GridLineStyle = ':';
        ax.GridAlpha = 0.1;         % 更低的网格线透明度，不干扰数据点
        ax.TickLength = [0.01 0.01];
        ax.TickDir = 'in';          % 内向刻度
        ax.XColor = [0.2 0.2 0.2];  % 深灰色坐标轴，更专业
        ax.YColor = [0.2 0.2 0.2];
        % 设置背景为白色
        ax.Color = 'white';
        
        % 只在第一个子图显示图例
        if v == 1
            % 添加图例，放在右上角，设置为学术论文风格
            lgd = legend(legendEntries, 'Location', 'eastoutside', 'FontName', englishFont, 'FontSize', axisFontSize-2);
            lgd.Box = 'on';
            lgd.LineWidth = 0.5;  % 更细的边框线条
            % 设置图例中的标记大小为适中值
            for i = 1:length(lgd.ItemTokenSize)
                lgd.ItemTokenSize(i) = 8;  % 增大图例中的标记
            end
        end
    end
    
    % 调整子图布局
    sgtitle(['{\fontname{', chineseFont, '}\fontsize{', num2str(titleFontSize), '}决策变量分布}'], ...
        'FontName', englishFont, 'FontSize', titleFontSize, 'FontWeight', 'bold');
    
    % 返回图形句柄
    if nargout > 0
        varargout{1} = fig;
    end
end 