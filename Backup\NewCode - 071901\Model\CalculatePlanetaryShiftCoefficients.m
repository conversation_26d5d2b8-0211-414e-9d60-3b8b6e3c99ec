function [shift_ranges, optimal_shifts] = CalculatePlanetaryShiftCoefficients(gear_params)
% CalculatePlanetaryShiftCoefficients 计算行星轮系的科学变位系数范围和最优值
% 完全按照原有代码的计算方法
%
%   输入参数:
%   - gear_params: 齿轮几何参数结构体，包含字段:
%     - z1: 太阳轮齿数 (或外齿轮1)
%     - z2: 行星轮齿数 (或外齿轮2)
%     - zr: 内齿圈齿数 (可选，内啮合时需要)
%     - alpha: 压力角 (度)
%     - beta: 螺旋角 (度)
%     - module: 模数 (mm)
%     - center_distance: 中心距 (mm，可选)
%     - is_planetary: 是否为行星轮系 (布尔值)
%     - is_internal: 是否为内啮合 (布尔值)
%
%   输出:
%   - shift_ranges: 变位系数范围结构体，包含字段:
%     - x1_min, x1_max: 第一个齿轮变位系数范围
%     - x2_min, x2_max: 第二个齿轮变位系数范围
%     - xr_min, xr_max: 内齿圈变位系数范围 (内啮合时)
%     - sum_min, sum_max: 变位系数和的范围
%   - optimal_shifts: 最优变位系数结构体

% 提取参数
z1 = gear_params.z1;  % 太阳轮齿数 (或外齿轮1)
z2 = gear_params.z2;  % 行星轮齿数 (或外齿轮2)

% 检查是否有内齿圈参数
if isfield(gear_params, 'zr') && ~isempty(gear_params.zr)
    zr = gear_params.zr;  % 内齿圈齿数
    has_ring = true;
else
    zr = 0;
    has_ring = false;
end

% 其他参数
alpha_deg = gear_params.alpha;  % 压力角 (度)
beta_deg = gear_params.beta;    % 螺旋角 (度)
module = gear_params.module;    % 模数 (mm)

% 可选参数
if isfield(gear_params, 'is_planetary')
    is_planetary = gear_params.is_planetary;
else
    is_planetary = true;  % 默认为行星轮系
end

if isfield(gear_params, 'is_internal')
    is_internal = gear_params.is_internal;
else
    is_internal = has_ring;  % 有内齿圈时默认为内啮合
end

% 转换角度
alpha_t = alpha_deg * pi / 180;  % 端面压力角
beta = beta_deg * pi / 180;      % 螺旋角

% 初始化变位系数范围
shift_ranges = struct();
optimal_shifts = struct();

% 1. 根据齿数计算防止根切的最小变位系数
% 对于20度压力角，无变位时最小齿数为17，对于较小的齿数需要正变位
x1_min_undercut = max(0, (17 - z1) / 17);
x2_min_undercut = max(0, (17 - z2) / 17);

% 2. 根据齿顶厚度要求计算最大变位系数
% 齿顶厚度不小于0.4模数
x1_max_tip = calc_max_x_for_tip_thickness(z1, alpha_t);
x2_max_tip = calc_max_x_for_tip_thickness(z2, alpha_t);

% 3. 计算中心距
if isfield(gear_params, 'center_distance') && ~isempty(gear_params.center_distance)
    actual_center_distance = gear_params.center_distance;
    % 计算标准中心距用于后续计算
    normal_center_distance = module * (z1 + z2) / (2 * cos(beta));
else
    % 计算标准中心距
    normal_center_distance = module * (z1 + z2) / (2 * cos(beta));
    actual_center_distance = normal_center_distance;
end

% 4. 计算变位系数和的范围，以满足中心距要求
if ~is_internal
    % 外啮合
    center_sum = (z1 + z2)/2;  % 外啮合中心距
    % 计算啮合角余弦值
    cos_alpha_w = normal_center_distance * cos(alpha_t) / actual_center_distance;
    alpha_w = acos(cos_alpha_w);  % 啮合压力角
    
    % 计算渐开线函数
    inv_alpha = tan(alpha_t) - alpha_t;
    inv_alpha_w = tan(alpha_w) - alpha_w;
    
    % 计算满足中心距要求的变位系数和
    sum_x_sp = (z1 + z2) * (inv_alpha_w - inv_alpha) / (2 * tan(alpha_t));
    
    % 5. 计算平衡滑移变位系数分配
    u_sp = z2 / z1; % 太阳轮-行星轮传动比
    
    % 6. 考虑重合度要求，重合度不小于1.2
    % 为保证足够的重合度，计算最小变位系数
    [x1_min_eps, x2_min_eps] = get_min_shifts_for_contact_ratio(z1, z2, alpha_t, module, false);
    
    % 7. 考虑啮合干涉，计算干涉限制下的变位系数范围
    [x1_min_interf, x2_max_interf] = check_interference(z1, z2, alpha_t, false);
    
    % 8. 综合各种约束条件
    x1_min = max([x1_min_undercut, x1_min_eps, x1_min_interf, -0.5]);
    x1_max = min([x1_max_tip, 0.8]);
    
    x2_min = max([x2_min_undercut, x2_min_eps, -0.5]);
    x2_max = min([x2_max_tip, x2_max_interf, 0.8]);
    
    % 根据中心距约束调整范围
    x1_min = max(x1_min, sum_x_sp - x2_max);
    x1_max = min(x1_max, sum_x_sp - x2_min);
    x2_min = max(x2_min, sum_x_sp - x1_max);
    x2_max = min(x2_max, sum_x_sp - x1_min);
    
    sum_min = x1_min + x2_min;
    sum_max = x1_max + x2_max;
    
    % 保存外啮合范围
    shift_ranges.x1_min = x1_min;
    shift_ranges.x1_max = x1_max;
    shift_ranges.x2_min = x2_min;
    shift_ranges.x2_max = x2_max;
    shift_ranges.sum_min = sum_min;
    shift_ranges.sum_max = sum_max;
    
else
    % 内啮合（行星轮-内齿圈）
    if ~has_ring
        error('内啮合计算需要提供内齿圈齿数');
    end
    
    % 内啮合中，变位系数有特殊约束
    x1_min = max(x1_min_undercut, 0);  % 行星轮变位系数下限
    x1_max = min(x1_max_tip, 0.8);    % 行星轮变位系数上限
    
    x2_min = -0.5;  % 内齿圈变位系数下限
    x2_max = 0.3;   % 内齿圈变位系数上限
    
    % 根据中心距约束计算变位系数和
    % 对于内啮合：a = (z2 - z1) * m / 2
    if abs(actual_center_distance - module * (z2 - z1) / 2) > 0.01
        % 如果实际中心距与标准中心距不同，需要调整变位系数和
        % 计算内啮合的啮合压力角
        cos_alpha_w = module * (z2 - z1) * cos(alpha_t) / (2 * actual_center_distance);
        alpha_w = acos(cos_alpha_w);
        
        inv_alpha = tan(alpha_t) - alpha_t;
        inv_alpha_w = tan(alpha_w) - alpha_w;
        
        % 内啮合的变位系数和
        sum_x_pr = (z2 - z1) * (inv_alpha_w - inv_alpha) / (2 * tan(alpha_t));
        
        % 根据变位系数和调整范围
        x1_min = max(x1_min, sum_x_pr - x2_max);
        x1_max = min(x1_max, sum_x_pr - x2_min);
    end
    
    sum_min = x1_min + x2_min;
    sum_max = x1_max + x2_max;
    
    % 保存内啮合范围
    shift_ranges.x1_min = x1_min;
    shift_ranges.x1_max = x1_max;
    shift_ranges.x2_min = x2_min;
    shift_ranges.x2_max = x2_max;
    shift_ranges.xr_min = x2_min;  % 内齿圈
    shift_ranges.xr_max = x2_max;
    shift_ranges.sum_min = sum_min;
    shift_ranges.sum_max = sum_max;
end

%% 计算最优变位系数
if ~is_internal
    % 外啮合最优变位系数计算
    % 使用平衡滑移的分配方法
    sum_x_opt = sum_x_sp;
    
    % 确保在范围内
    sum_x_opt = max(sum_min, min(sum_max, sum_x_opt));
    
    % 分配变位系数 - 平衡滑移公式
    x1_opt = sum_x_opt/2 + (z2 - z1)/(2*(z1 + z2)) * sum_x_opt;
    x2_opt = sum_x_opt - x1_opt;
    
    % 确保在计算的范围内
    x1_opt = max(x1_min, min(x1_max, x1_opt));
    x2_opt = max(x2_min, min(x2_max, x2_opt));
    
    optimal_shifts.x1_opt = x1_opt;
    optimal_shifts.x2_opt = x2_opt;
    optimal_shifts.sum_opt = x1_opt + x2_opt;
    
    % 如果是行星轮系，计算内齿圈变位系数
    if is_planetary && has_ring
        % 行星轮系约束：x_s * z_s + x_p * z_p + x_r * z_r = 0
        optimal_shifts.xr_opt = -(x1_opt * z1 + x2_opt * z2) / zr;
        
        % 检查重合度
        eps_sp = calculate_contact_ratio(z1, z2, x1_opt, x2_opt, alpha_t, module, false);
        
        % 如果重合度不足，调整变位系数
        if eps_sp < 1.2
            % 可以增大变位系数和来提高重合度
            for test_sum = sum_x_opt:0.01:sum_max
                % 按比例调整两个齿轮的变位系数
                test_x1 = max(x1_min, min(x1_max, test_sum * (u_sp - 1) / (u_sp + 1)));
                test_x2 = test_sum - test_x1;
                
                % 检查内齿圈变位系数是否在合理范围内
                test_xr = -(test_x1 * z1 + test_x2 * z2) / zr;
                if test_xr < -0.5 || test_xr > 0.3
                    continue;  % 跳过不符合要求的变位系数组合
                end
                
                new_eps_sp = calculate_contact_ratio(z1, z2, test_x1, test_x2, alpha_t, module, false);
                
                if new_eps_sp >= 1.2
                    optimal_shifts.x1_opt = test_x1;
                    optimal_shifts.x2_opt = test_x2;
                    optimal_shifts.sum_opt = test_sum;
                    optimal_shifts.xr_opt = test_xr;
                    break;
                end
            end
        end
    end
else
    % 内啮合使用内啮合特定的最优变位系数计算
    % 对于内啮合，我们更关注避免干涉，而非平衡滑移
    u_pr = -z2 / z1; % 内啮合传动比(负值)
    
    % 计算中心距并确定工作压力角
    a_ac = module * (z2 - z1) / 2; % 行星轮与内齿圈的中心距
    
    % 计算满足中心距的变位系数和
    if abs(actual_center_distance - a_ac) > 0.01
        cos_alpha_w = a_ac * cos(alpha_t) / actual_center_distance;
        alpha_w = acos(cos_alpha_w);
        
        inv_alpha = tan(alpha_t) - alpha_t;
        inv_alpha_w = tan(alpha_w) - alpha_w;
        
        sum_x_opt = (z2 - z1) * (inv_alpha_w - inv_alpha) / (2 * tan(alpha_t));
    else
        sum_x_opt = 0;  % 标准中心距时
    end
    
    % 确保变位系数和在合理范围内
    sum_x_opt = max(sum_min, min(sum_max, sum_x_opt));
    
    % 分配变位系数 - 内啮合Maag公式
    if u_pr < -1 % 如果内齿圈齿数大于行星轮齿数的2倍以上
        x1_opt = sum_x_opt * (abs(u_pr) + 1) / (abs(u_pr) - 1);
    else
        x1_opt = sum_x_opt / 2; % 平均分配
    end
    x2_opt = sum_x_opt - x1_opt;
    
    % 确保在计算的范围内
    x1_opt = max(x1_min, min(x1_max, x1_opt));
    x2_opt = max(x2_min, min(x2_max, x2_opt));
    
    optimal_shifts.x1_opt = x1_opt;
    optimal_shifts.x2_opt = x2_opt;
    optimal_shifts.sum_opt = x1_opt + x2_opt;
    
    % 行星轮系中，二级内齿圈的变位系数也需要满足一级的约束
    if is_planetary && zr > 0
        optimal_shifts.xr_opt = -(optimal_shifts.x1_opt * z1 + optimal_shifts.x2_opt * z2) / zr;
    end
end

end

% 辅助函数
function x_max = calc_max_x_for_tip_thickness(z, alpha_t)
% 根据齿顶厚度要求计算最大变位系数
% 齿顶厚度应不小于0.4模数
x_max = 0.8 - 0.01 * max(0, 25 - z);  % 简化计算
end

function [x1_min, x2_min] = get_min_shifts_for_contact_ratio(z1, z2, alpha_t, module, is_internal)
% 计算满足重合度要求的最小变位系数
x1_min = max(0, (1.2 - 1.88 + 3.2/z1) * 0.1);
x2_min = max(0, (1.2 - 1.88 + 3.2/z2) * 0.1);
end

function [x1_min, x2_max] = check_interference(z1, z2, alpha_t, is_internal)
% 检查啮合干涉限制
x1_min = max(0, (17 - z1) / 17 * 0.5);
x2_max = 0.8;  % 简化
end

function eps = calculate_contact_ratio(z1, z2, x1, x2, alpha_t, module, is_internal)
% 计算重合度
eps = 1.88 - 3.2/z1 - 3.2/z2 + 0.1*(x1 + x2);
eps = max(eps, 1.0);
end
