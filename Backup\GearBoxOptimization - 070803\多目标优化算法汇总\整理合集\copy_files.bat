@echo off
cd "C:\Users\<USER>\Desktop\多目标优化算法汇总\多目标整理合成"

rem Copy algorithm files
copy NSGA2.m Algorithms\
copy NSGA3.m Algorithms\
copy MODA.m Algorithms\
copy MODE.m Algorithms\
copy MOEAD.m Algorithms\
copy MOGOA.m Algorithms\
copy MOGWO.m Algorithms\
copy MOMVO.m Algorithms\
copy MOPSO.m Algorithms\
copy MSSA.m Algorithms\
copy NSDBO.m Algorithms\
copy PESA2.m Algorithms\
copy SPEA2.m Algorithms\
copy NSWOA.m Algorithms\

rem Copy metrics
copy EvaluationMetrics.m Metrics\

rem Copy metrics from evaluation metrics folder
copy "C:\Users\<USER>\Desktop\多目标优化算法汇总\多目标优化的评价指标\GD.m" Metrics\
copy "C:\Users\<USER>\Desktop\多目标优化算法汇总\多目标优化的评价指标\IGD.m" Metrics\
copy "C:\Users\<USER>\Desktop\多目标优化算法汇总\多目标优化的评价指标\Spacing.m" Metrics\
copy "C:\Users\<USER>\Desktop\多目标优化算法汇总\多目标优化的评价指标\Spread.m" Metrics\
copy "C:\Users\<USER>\Desktop\多目标优化算法汇总\多目标优化的评价指标\HV.m" Metrics\

rem Copy test functions
copy TestProblems.m TestFunctions\

echo Files copied successfully! 