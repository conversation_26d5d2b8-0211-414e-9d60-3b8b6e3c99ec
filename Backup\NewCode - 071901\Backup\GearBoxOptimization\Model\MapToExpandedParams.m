function expanded_params = MapToExpandedParams(original_params, objectives, gear_materials, first_stage_params, input_power, input_speed, output_speed, service_life)
% MAPTOEXPANDEDPARAMS 将原始优化变量映射到扩展的参数集
% 用于将优化算法输出的原始变量转换为与三级齿轮系统表格格式匹配的扩展参数
%
% 输入:
%   original_params - 原始优化变量
%   objectives - 目标函数值 [总质量, 弯曲安全系数, 接触安全系数]
%   gear_materials - 齿轮材料参数结构体
%   first_stage_params - (可选) 一级齿轮参数结构体，用于多组一级参数的情况
%   input_power - (可选) 输入功率 (kW)
%   input_speed - (可选) 输入转速 (rpm)
%   output_speed - (可选) 输出转速 (rpm)
%   service_life - (可选) 设计寿命 (h)
%
% 输出:
%   expanded_params - 扩展的参数集，匹配表格格式

% 初始化输出参数表
expanded_params = zeros(1, 71); % 共71个参数，包括最后5列（TotalMass、SH、SF、TotalRatio、Error）

% 检查载荷参数并设置默认值
if nargin < 5 || isempty(input_power)
    input_power = 1000;  % 默认输入功率 (kW)
end
if nargin < 6 || isempty(input_speed)
    input_speed = 1490;  % 默认输入转速 (rpm) - 与主程序保持一致
end
if nargin < 7 || isempty(output_speed)
    output_speed = 15;   % 默认输出转速 (rpm)
end
if nargin < 8 || isempty(service_life)
    service_life = 20000;  % 默认设计寿命 (h)
end

% 检查是否提供了一级参数结构体（多组一级参数的情况）
if nargin >= 4 && ~isempty(first_stage_params) && isstruct(first_stage_params)
    % 多组一级参数的情况：从结构体中获取一级参数
    m1 = first_stage_params.m;
    z1 = first_stage_params.z1;
    z2 = first_stage_params.z2;
    helix_angle_1 = first_stage_params.beta;
    pressure_angle = first_stage_params.alpha;
    x1 = round(first_stage_params.x1, 4);
    x2 = round(first_stage_params.x2, 4);
    k_h1 = first_stage_params.k_h;

    % 从优化变量中提取二三级参数（跳过第一个变量，因为它是一级参数索引）
    if length(original_params) >= 19
        % 多组一级参数情况下的变量结构：[first_stage_idx, mn2, zs2, zp2, k_h2, mn3, zs3, zp3, k_h3, planets_count_2, planets_count_3,
        %                                    pressure_angle, helix_angle_2, helix_angle_3, xs2, xp2, xs3, xp3, pressure_angle_3_choice]
        mn2 = original_params(2);           % 二级模数
        zs2 = round(original_params(3));    % 二级太阳轮齿数
        zp2 = round(original_params(4));    % 二级行星轮齿数
        k_h2 = original_params(5);          % 二级齿宽系数
        mn3 = original_params(6);           % 三级模数
        zs3 = round(original_params(7));    % 三级太阳轮齿数
        zp3 = round(original_params(8));    % 三级行星轮齿数
        k_h3 = original_params(9);          % 三级齿宽系数
        planets_count_2 = round(original_params(10)); % 二级行星轮数量
        planets_count_3 = round(original_params(11)); % 三级行星轮数量
        % pressure_angle 已从first_stage_params获取
        helix_angle_2 = 0;          % 二级螺旋角强制设置为0°（直齿轮）
        helix_angle_3 = 0;          % 三级螺旋角强制设置为0°（直齿轮）
        xs2 = round(original_params(15), 4);          % 二级太阳轮变位系数
        xp2 = round(original_params(16), 4);          % 二级行星轮变位系数
        xs3 = round(original_params(17), 4);          % 三级太阳轮变位系数
        xp3 = round(original_params(18), 4);          % 三级行星轮变位系数
        pressure_angle_3_choice = round(original_params(19)); % 三级压力角选择
    else
        error('多组一级参数情况下，优化变量数量不足');
    end
else
    % 单组一级参数的情况：从优化变量中提取所有参数
    m1 = original_params(1);            % 一级模数
    z1 = round(original_params(2));     % 一级小齿轮齿数
    z2 = round(original_params(3));     % 一级大齿轮齿数
    mn2 = original_params(4);           % 二级模数
    zs2 = round(original_params(5));    % 二级太阳轮齿数
    zp2 = round(original_params(6));    % 二级行星轮齿数
    k_h2 = original_params(7);          % 二级齿宽系数
    mn3 = original_params(8);           % 三级模数
    zs3 = round(original_params(9));    % 三级太阳轮齿数
    zp3 = round(original_params(10));   % 三级行星轮齿数
    k_h3 = original_params(11);         % 三级齿宽系数
    planets_count_2 = round(original_params(12)); % 二级行星轮数量
    planets_count_3 = round(original_params(13)); % 三级行星轮数量
    pressure_angle = original_params(14);         % 一二级压力角
    helix_angle_1 = original_params(15);          % 一级螺旋角
    helix_angle_2 = 0;          % 二级螺旋角强制设置为0°（直齿轮）
    helix_angle_3 = 0;          % 三级螺旋角强制设置为0°（直齿轮）
    x1 = round(original_params(18), 4);           % 一级小齿轮变位系数
    x2 = round(original_params(19), 4);           % 一级大齿轮变位系数
    xs2 = round(original_params(20), 4);          % 二级太阳轮变位系数
    xp2 = round(original_params(21), 4);          % 二级行星轮变位系数
    xs3 = round(original_params(22), 4);          % 三级太阳轮变位系数
    xp3 = round(original_params(23), 4);          % 三级行星轮变位系数
    pressure_angle_3_choice = round(original_params(24)); % 三级压力角选择
    k_h1 = original_params(25);                   % 一级齿宽系数
end

% 计算内齿圈齿数
zr2 = zs2 + 2*zp2;                  % 二级内齿圈齿数
zr3 = zs3 + 2*zp3;                  % 三级内齿圈齿数

% 计算内齿圈变位系数（保留四位小数）
xr2 = round(-(xs2*zs2 + xp2*zp2) / zr2, 4);   % 二级内齿圈变位系数
xr3 = round(-(xs3*zs3 + xp3*zp3) / zr3, 4);   % 三级内齿圈变位系数

% 确定三级压力角
alpha3 = 20; % 默认值
if pressure_angle_3_choice >= 0.5
    alpha3 = 25; % 如果压力角选择为1，则为25度
end

% 计算中心距 - 使用精确的计算方法
% 一级平行轴齿轮中心距（考虑变位系数的精确计算）
alpha_rad = pressure_angle * pi / 180;
beta1_rad = helix_angle_1 * pi / 180;

if helix_angle_1 == 0
    % 直齿轮情况
    inv_alpha = tan(alpha_rad) - alpha_rad;
    inv_alpha_w = inv_alpha + 2 * (x1 + x2) * tan(alpha_rad) / (z1 + z2);
    alpha_w = fzero(@(a) tan(a) - a - inv_alpha_w, alpha_rad);
    a1 = m1 * (z1 + z2) * cos(alpha_rad) / (2 * cos(alpha_w));
else
    % 斜齿轮情况
    mt1 = m1 / cos(beta1_rad);  % 端面模数
    inv_alpha_t = tan(alpha_rad) - alpha_rad;
    inv_alpha_tw = inv_alpha_t + 2 * (x1 + x2) * tan(alpha_rad) / (z1 + z2);
    alpha_tw = fzero(@(a) tan(a) - a - inv_alpha_tw, alpha_rad);
    a1 = mt1 * (z1 + z2) * cos(alpha_rad) / (2 * cos(alpha_tw));
end

% 二级行星系中心距（太阳轮与行星轮中心距）- 考虑变位系数的精确计算
% 计算分度圆半径
rs2 = mn2 * zs2 / 2;  % 太阳轮分度圆半径
rp2 = mn2 * zp2 / 2;  % 行星轮分度圆半径

% 使用考虑变位系数的精确中心距计算公式
% a = (r1 + r2) * (1 + (x1 + x2) / (z1 + z2) * 2 * tan(alpha_t))
alpha2_rad = pressure_angle * pi / 180;  % 二级压力角（弧度）
beta2_rad = helix_angle_2 * pi / 180;    % 二级螺旋角（弧度）

% 计算端面压力角
if beta2_rad == 0
    alpha2_t = alpha2_rad;  % 直齿轮情况
else
    alpha2_t = atan(tan(alpha2_rad) / cos(beta2_rad));  % 斜齿轮端面压力角
end

% 二级太阳轮-行星轮中心距（外啮合）
a2 = (rs2 + rp2) * (1 + (xs2 + xp2) / (zs2 + zp2) * 2 * tan(alpha2_t));

% 三级行星系中心距（太阳轮与行星轮中心距）- 考虑变位系数的精确计算
% 计算分度圆半径
rs3 = mn3 * zs3 / 2;  % 太阳轮分度圆半径
rp3 = mn3 * zp3 / 2;  % 行星轮分度圆半径

% 使用考虑变位系数的精确中心距计算公式
alpha3_rad = alpha3 * pi / 180;  % 三级压力角（弧度）
beta3_rad = helix_angle_3 * pi / 180;    % 三级螺旋角（弧度）

% 计算端面压力角
if beta3_rad == 0
    alpha3_t = alpha3_rad;  % 直齿轮情况
else
    alpha3_t = atan(tan(alpha3_rad) / cos(beta3_rad));  % 斜齿轮端面压力角
end

% 三级太阳轮-行星轮中心距（外啮合）
a3 = (rs3 + rp3) * (1 + (xs3 + xp3) / (zs3 + zp3) * 2 * tan(alpha3_t));

% 计算各级传动比
i1 = z2 / z1;
i2 = (1 + zr2/zs2);
i3 = (1 + zr3/zs3);
% total_ratio和error_ratio变量已删除

% 计算综合变位系数（保留四位小数）
x_sum1 = round(x1 + x2, 4);          % 一级综合变位系数
x_sum2 = round(xs2 + xp2 + xr2, 4);  % 二级综合变位系数
x_sum3 = round(xs3 + xp3 + xr3, 4);  % 三级综合变位系数

% 计算齿宽
b1 = k_h1 * a1;
b2 = k_h2 * a2;
b3 = k_h3 * a3;

% 填充扩展参数
% 一级传动参数
expanded_params(1) = m1;          % 模数 m1
expanded_params(2) = z1;          % 小齿轮齿数 z1
expanded_params(3) = z2;          % 大齿轮齿数 z2(这里原来叫z3)
expanded_params(4) = k_h1;        % 齿宽系数 k_h1
expanded_params(5) = x1;          % 小齿轮变位系数 x1
expanded_params(6) = x2;          % 大齿轮变位系数 x2
expanded_params(7) = x_sum1;      % 一级综合变位系数
expanded_params(8) = helix_angle_1; % 螺旋角 beta1
expanded_params(9) = pressure_angle; % 压力角 alpha1
expanded_params(10) = round(a1, 2);         % 中心距 a1（保留两位小数）
expanded_params(11) = round(i1, 3);         % 传动比 i1（保留三位小数）
% 使用现有的GearOptObjectives函数计算安全系数和质量
% 构建完整的参数向量来调用GearOptObjectives
full_params = [m1, z1, z2, mn2, zs2, zp2, k_h2, mn3, zs3, zp3, k_h3, ...
               planets_count_2, planets_count_3, pressure_angle, helix_angle_1, helix_angle_2, helix_angle_3, ...
               x1, x2, xs2, xp2, xs3, xp3, alpha3, k_h1];

% 设置默认参数（载荷参数已在函数开头处理）
contact_safety_factor = 1.2;
bending_safety_factor = 1.5;
center_distance = 400;  % mm
quality_grade = 6;

% 使用默认材料参数（如果没有提供gear_materials或参数不完整）
if isempty(gear_materials) || ~isstruct(gear_materials)
    gear_material = struct();
    gear_material.density = 7850;  % kg/m³
    gear_material.bending_strength = 400e6;  % Pa
    gear_material.contact_strength = 1200e6;  % Pa
else
    gear_material = gear_materials;
    % 检查必要字段是否存在，如果不存在则使用默认值
    if ~isfield(gear_material, 'density')
        gear_material.density = 7850;  % kg/m³
    end
    if ~isfield(gear_material, 'bending_strength')
        gear_material.bending_strength = 400e6;  % Pa
    end
    if ~isfield(gear_material, 'contact_strength')
        gear_material.contact_strength = 1200e6;  % Pa
    end
end

try
    % 调用GearOptObjectives函数获取完整的计算结果
    [obj_values, constraints, is_valid] = GearOptObjectives(full_params, input_power, input_speed, output_speed, ...
                                      service_life, contact_safety_factor, bending_safety_factor, gear_material, ...
                                      planets_count_2, planets_count_3, gear_materials, quality_grade, ...
                                      pressure_angle, helix_angle_1, helix_angle_2, helix_angle_3, ...
                                      [x1, x2, xs2, xp2, xs3, xp3], alpha3, center_distance, k_h1);

    % 【重要】优先从GearOptObjectives.m的计算结果中获取质量值
    % 这是您算法中的标准计算方法，应该始终优先使用
    try
        M1 = evalin('base', 'M1_小齿轮质量');
        M2 = evalin('base', 'M2_大齿轮质量');

    catch
        try
            % 如果中文变量名有问题，使用英文变量名
            M1 = evalin('base', 'M1_gear1_mass');
            M2 = evalin('base', 'M2_gear2_mass');

        catch
            % 【警告】仅在GearOptObjectives函数完全失败时才使用备用计算
            % 这不是标准方法，仅作为最后的备用方案


            % 一级小齿轮体积计算（您的精确方法）
            % 计算实体部分体积
            V1_solid = (pi * b1 / 4) * (m1 / cos(helix_angle_1 * pi / 180) * z1)^2;

            % 计算齿高参数（完全按照您GearOptObjectives.m第521行）
            c_star = 2.25; % 标准齿高系数（与您的算法完全一致）
            h1 = c_star * m1 - 2 * x1 * m1; % 齿高计算

            % 计算有效长度
            l1 = b1 / cos(helix_angle_1 * pi / 180);

            % 计算齿宽系数sa
            r1 = m1 * z1 / 2; % 分度圆半径
            ra1 = r1 + m1 + x1 * m1; % 齿顶圆半径
            alpha_t1 = atan(tan(pressure_angle * pi / 180) / cos(helix_angle_1 * pi / 180)); % 端面压力角
            rb1 = r1 * cos(alpha_t1); % 基圆半径
            s1 = pi * m1 / 2; % 分度圆弧齿厚
            % 计算齿顶压力角
            alpha_a1 = acos(rb1 / ra1);
            inv_alpha_a1 = tan(alpha_a1) - alpha_a1;
            inv_alpha_t1 = tan(alpha_t1) - alpha_t1;
            sa1 = s1 * ra1 / r1 - 2 * ra1 * (inv_alpha_a1 - inv_alpha_t1);

            % 计算齿轮间隙体积
            V1_space = sa1 * h1 * l1 * z1;

            % 最终一级小齿轮体积
            V1 = V1_solid - V1_space;

            % 一级大齿轮体积计算（您的精确方法）
            % 计算实体部分体积
            V2_solid = (pi * b1 / 4) * (m1 / cos(helix_angle_1 * pi / 180) * z2)^2;

            % 计算齿高参数（完全按照您GearOptObjectives.m第551行）
            h2 = c_star * m1 - 2 * x2 * m1; % 齿高计算（与您的算法完全一致）

            % 计算有效长度
            l2 = b1 / cos(helix_angle_1 * pi / 180);

            % 计算齿宽系数sa
            r2 = m1 * z2 / 2; % 分度圆半径
            ra2 = r2 + m1 + x2 * m1; % 齿顶圆半径
            rb2 = r2 * cos(alpha_t1); % 基圆半径
            s2 = pi * m1 / 2; % 分度圆弧齿厚
            % 计算齿顶压力角
            alpha_a2 = acos(rb2 / ra2);
            inv_alpha_a2 = tan(alpha_a2) - alpha_a2;
            sa2 = s2 * ra2 / r2 - 2 * ra2 * (inv_alpha_a2 - inv_alpha_t1);

            % 计算齿轮间隙体积
            V2_space = sa2 * h2 * l2 * z2;

            % 最终一级大齿轮体积
            V2 = V2_solid - V2_space;

            % 计算质量（使用您的方法）
            M1 = gear_material.density * V1 / 1e9;  % 一级小齿轮质量(kg)
            M2 = gear_material.density * V2 / 1e9;  % 一级大齿轮质量(kg)
        end
    end

    % 从目标函数值中获取安全系数（注意：目标函数中安全系数是负值）
    % total_mass变量已删除
    min_bending_SF = -obj_values(2);  % 最小弯曲安全系数（转换为正值）
    min_contact_SF = -obj_values(3);  % 最小接触安全系数（转换为正值）

    % 使用与一级齿轮系参数网页完全相同的计算方法
    % 参考：GenerateValidFirstStageGears.m 第32行和第241-250行
    try
        % 获取材料参数
        material = MaterialManager('17CrNiMo6');

        % 使用传入的载荷参数（而不是硬编码的固定值）
        % 【关键】使用工程标准扭矩计算公式，与优化算法保持一致
        input_torque = 9550 * input_power / input_speed;

        % 【关键】使用CalculateParallelShiftCoefficients重新计算变位系数
        % 这与GenerateValidFirstStageGears.m第118行完全一致
        center_distance = 400;  % 假设中心距400mm
        [x1_calc, x2_calc] = CalculateParallelShiftCoefficients(z1, z2, m1, pressure_angle, helix_angle_1, center_distance);
        x1_calc = round(x1_calc, 4);
        x2_calc = round(x2_calc, 4);

        % 【关键】使用与网页相同的齿轮参数结构（GenerateValidFirstStageGears.m第230-238行）
        gear1Params = struct('m', m1, ...
                           'z', z1, ...
                           'alpha', pressure_angle, ...
                           'beta', helix_angle_1, ...
                           'b', b1, ...
                           'x', x1_calc, ...
                           'mating_z', z2, ...
                           'mating_x', x2_calc);

        % 【关键】使用与网页相同的载荷参数（GenerateValidFirstStageGears.m第241-244行）
        gear1LoadParams = struct('T', input_torque, ...
                               'n', input_speed, ...
                               'KA', 1.75, ...
                               'service_life', service_life);

        % 【关键】使用与网页相同的安全系数计算方法（GenerateValidFirstStageGears.m第250行）
        [SF1, SF2, SH1] = ParallelGearSafetyCalculator(gear1Params, gear1LoadParams, material, 'ISO6336');

        % 注意：网页中返回的是 [sf_bending1, sf_bending2, sf_contact]
        % 所以 SF1 = sf_bending1, SF2 = sf_bending2, SH1 = sf_contact

    catch
        % 如果计算失败，使用目标函数中的值
        SF1 = min_bending_SF;
        SF2 = min_bending_SF;
        SH1 = min_contact_SF;
    end

catch
    % 如果计算失败，使用目标函数中的最小安全系数值
    SF1 = min_bending_SF;
    SF2 = min_bending_SF;
    SH1 = min_contact_SF;
    M1 = 0;
    M2 = 0;
end

expanded_params(12) = round(SH1, 3);        % 接触安全系数 SH1（保留三位小数）
expanded_params(13) = round(SF1, 3);        % 弯曲安全系数(小齿轮) SF1（保留三位小数）
expanded_params(14) = round(SF2, 3);        % 弯曲安全系数(大齿轮) SF2（保留三位小数）
expanded_params(15) = round(M1, 2);         % 小齿轮质量 M1（保留两位小数）
expanded_params(16) = round(M2, 2);         % 大齿轮质量 M2（保留两位小数）

% 二级传动参数
expanded_params(17) = mn2;        % 模数 mn2
expanded_params(18) = zs2;        % 太阳轮齿数 zs2
expanded_params(19) = zp2;        % 行星轮齿数 zp2
expanded_params(20) = zr2;        % 内齿圈齿数 zr2
expanded_params(21) = planets_count_2; % 行星轮数量 n2
expanded_params(22) = k_h2;       % 齿宽系数 k_h2
expanded_params(23) = xs2;        % 太阳轮变位系数 xs2
expanded_params(24) = xp2;        % 行星轮变位系数 xp2
expanded_params(25) = xr2;        % 内齿圈变位系数 xr2
expanded_params(26) = x_sum2;     % 二级综合变位系数
expanded_params(27) = helix_angle_2; % 螺旋角 beta2
expanded_params(28) = pressure_angle; % 压力角 alpha2
expanded_params(29) = round(a2, 2);         % 中心距 a2（保留两位小数）
expanded_params(30) = round(i2, 3);         % 传动比 i2（保留三位小数）
% 【重要】优先从GearOptObjectives.m的计算结果中获取二级齿轮质量
% 这是您算法中的标准平均圆法计算，应该始终优先使用
try
    Ms2 = evalin('base', 'Ms2_二级太阳轮质量');
    Mp2_single = evalin('base', 'Mp2_二级单行星轮质量');
    Mr2 = evalin('base', 'Mr2_二级内齿圈质量');

catch
    try
        % 使用英文变量名
        Ms2 = evalin('base', 'Ms2_sun2_mass');
        Mp2_single = evalin('base', 'Mp2_planet2_mass');
        Mr2 = evalin('base', 'Mr2_ring2_mass');

    catch
        % 【警告】仅在GearOptObjectives函数完全失败时才使用备用计算


        % 【完全按照您GearOptObjectives.m第572-593行的方法】
        % 二级太阳轮体积计算（您的平均圆法）
        % 使用正确的二级齿数变量
        da_s2 = mn2 * zs2 + 2 * (1.0 + xs2) * mn2; % 太阳轮齿顶圆直径
        df_s2 = mn2 * zs2 - 2 * (1.25 - xs2) * mn2; % 太阳轮齿根圆直径
        dag_s2 = (da_s2 + df_s2) / 2; % 太阳轮平均圆直径
        Vs2 = (pi / 4) * dag_s2^2 * b2; % 二级太阳轮体积

        % 二级行星轮体积计算（您的平均圆法）
        % 使用正确的二级齿数变量
        da_p2 = mn2 * zp2 + 2 * (1.0 + xp2) * mn2; % 行星轮齿顶圆直径
        df_p2 = mn2 * zp2 - 2 * (1.25 - xp2) * mn2; % 行星轮齿根圆直径
        dag_p2 = (da_p2 + df_p2) / 2; % 行星轮平均圆直径
        Vp2 = (pi / 4) * dag_p2^2 * b2 * planets_count_2; % 所有二级行星轮体积

        % 二级内齿圈体积计算（您的方法）
        da_r2 = mn2 * zr2 - 2 * (1.0 + xr2) * mn2; % 内齿圈齿顶圆直径(内径)
        dr2_outer = da_r2 + 12 * mn2; % 内齿圈外径（齿顶圆直径+12个模数）
        Vr2 = (pi / 4) * (dr2_outer^2 - da_r2^2) * b2; % 二级内齿圈体积

        % 计算质量（使用您的方法）
        Ms2 = gear_material.density * Vs2 / 1e9;  % 二级太阳轮质量(kg)
        Mp2_total = gear_material.density * Vp2 / 1e9;  % 二级所有行星轮质量(kg)
        Mp2_single = Mp2_total / planets_count_2;  % 二级单个行星轮质量(kg)
        Mr2 = gear_material.density * Vr2 / 1e9;  % 二级内齿圈质量(kg)
    end
end
Mp2 = Mp2_single * planets_count_2;  % 总的行星轮质量

% 计算二级行星系实际安全系数
try
    % 设置二级行星系参数
    gear_params_planet2 = struct();
    gear_params_planet2.sun = struct('m', mn2, 'z', zs2, 'alpha', pressure_angle, 'beta', helix_angle_2, 'b', b2, 'x', xs2);
    gear_params_planet2.planet = struct('m', mn2, 'z', zp2, 'alpha', pressure_angle, 'beta', helix_angle_2, 'b', b2, 'x', xp2);
    gear_params_planet2.ring = struct('m', mn2, 'z', zr2, 'alpha', pressure_angle, 'beta', helix_angle_2, 'b', b2, 'x', xr2);
    gear_params_planet2.planets_count = planets_count_2;

    % 计算载荷参数
    T1 = 9550 * input_power / input_speed;  % 一级输入转矩 (N·m)
    T2 = T1 * i1;                           % 二级输入转矩 (N·m)
    n2 = input_speed / i1;                  % 二级输入转速 (rpm)

    planet2LoadParams = struct('T', T2, 'n', n2, 'KA', 1.75, 'service_life', service_life);

    % 获取材料参数
    [sun_material, ~] = MaterialManager('17CrNiMo6');      % 太阳轮材料
    [planet_material, ~] = MaterialManager('20CrNi2MoA');  % 行星轮材料
    [ring_material, ~] = MaterialManager('42CrMoA');       % 内齿圈材料
    planet2MaterialParams = struct('sun', sun_material, 'planet', planet_material, 'ring', ring_material);

    % 计算安全系数
    [~, ~, detailed_safety_2] = PlanetarySystemSafetyCalculator(gear_params_planet2, planet2LoadParams, planet2MaterialParams, 'ISO6336');

    % 使用实际计算的安全系数（保留三位小数）
    SHsps2 = round(detailed_safety_2.SHsps, 3);
    SHspp2 = round(detailed_safety_2.SHspp, 3);
    SFsps2 = round(detailed_safety_2.SFsps, 3);
    SFspp2 = round(detailed_safety_2.SFspp, 3);
    SHprr2 = round(detailed_safety_2.SHprr, 3);
    SHprp2 = round(detailed_safety_2.SHprp, 3);
    SFprr2 = round(detailed_safety_2.SFprr, 3);
    SFprp2 = round(detailed_safety_2.SFprp, 3);

catch ME
    % 如果计算失败，显示错误信息并使用实际计算的安全系数（不使用默认值）
    fprintf('警告：二级行星系安全系数计算失败: %s\n', ME.message);

    % 直接使用实际计算的安全系数，不管是0、负值还是其他异常值
    % 这样用户可以看到真实的计算结果
    if exist('detailed_safety_2', 'var') && isstruct(detailed_safety_2)
        SHsps2 = detailed_safety_2.SHsps;
        SHspp2 = detailed_safety_2.SHspp;
        SFsps2 = detailed_safety_2.SFsps;
        SFspp2 = detailed_safety_2.SFspp;
        SHprr2 = detailed_safety_2.SHprr;
        SHprp2 = detailed_safety_2.SHprp;
        SFprr2 = detailed_safety_2.SFprr;
        SFprp2 = detailed_safety_2.SFprp;
    else
        % 如果连detailed_safety_2都不存在，设为NaN表示计算完全失败
        SHsps2 = NaN; SHspp2 = NaN; SFsps2 = NaN; SFspp2 = NaN;
        SHprr2 = NaN; SHprp2 = NaN; SFprr2 = NaN; SFprp2 = NaN;
        fprintf('错误：二级行星系安全系数计算完全失败，设为NaN\n');
    end
end

expanded_params(31) = SHsps2;     % 太阳-行星接触安全系数 SHsps2
expanded_params(32) = SHspp2;     % 太阳-行星接触安全系数 SHspp2
expanded_params(33) = SFsps2;     % 太阳-行星弯曲安全系数 SFsps2
expanded_params(34) = SFspp2;     % 太阳-行星弯曲安全系数 SFspp2
expanded_params(35) = SHprr2;     % 行星-齿圈接触安全系数 SHprr2
expanded_params(36) = SHprp2;     % 行星-齿圈接触安全系数 SHprp2
expanded_params(37) = SFprr2;     % 行星-齿圈弯曲安全系数 SFprr2
expanded_params(38) = SFprp2;     % 行星-齿圈弯曲安全系数 SFprp2
expanded_params(39) = round(Ms2, 2);        % 太阳轮质量 Ms2（保留两位小数）
expanded_params(40) = round(Mp2, 2);        % 行星轮质量 Mp2（保留两位小数）
expanded_params(41) = round(Mr2, 2);        % 内齿圈质量 Mr2（保留两位小数）

% 三级传动参数
expanded_params(42) = mn3;        % 模数 mn3
expanded_params(43) = zs3;        % 太阳轮齿数 zs3
expanded_params(44) = zp3;        % 行星轮齿数 zp3
expanded_params(45) = zr3;        % 内齿圈齿数 zr3
expanded_params(46) = planets_count_3; % 行星轮数量 n3
expanded_params(47) = k_h3;       % 齿宽系数 k_h3
expanded_params(48) = xs3;        % 太阳轮变位系数 xs3
expanded_params(49) = xp3;        % 行星轮变位系数 xp3
expanded_params(50) = xr3;        % 内齿圈变位系数 xr3
expanded_params(51) = x_sum3;     % 三级综合变位系数
expanded_params(52) = helix_angle_3; % 螺旋角 beta3
expanded_params(53) = alpha3;     % 压力角 alpha3
expanded_params(54) = round(a3, 2);         % 中心距 a3（保留两位小数）
expanded_params(55) = round(i3, 3);         % 传动比 i3（保留三位小数）
% 【重要】优先从GearOptObjectives.m的计算结果中获取三级齿轮质量
% 这是您算法中的标准平均圆法计算，应该始终优先使用
try
    Ms3 = evalin('base', 'Ms3_三级太阳轮质量');
    Mp3_single = evalin('base', 'Mp3_三级单行星轮质量');
    Mr3 = evalin('base', 'Mr3_三级内齿圈质量');

catch
    try
        % 使用英文变量名
        Ms3 = evalin('base', 'Ms3_sun3_mass');
        Mp3_single = evalin('base', 'Mp3_planet3_mass');
        Mr3 = evalin('base', 'Mr3_ring3_mass');

    catch
        % 【警告】仅在GearOptObjectives函数完全失败时才使用备用计算


        % 【完全按照您GearOptObjectives.m第595-629行的方法】
        % 三级太阳轮体积计算（您的平均圆法）
        % 使用正确的三级齿数变量
        da_s3 = mn3 * zs3 + 2 * (1.0 + xs3) * mn3; % 太阳轮齿顶圆直径
        df_s3 = mn3 * zs3 - 2 * (1.25 - xs3) * mn3; % 太阳轮齿根圆直径
        dag_s3 = (da_s3 + df_s3) / 2; % 太阳轮平均圆直径
        Vs3 = (pi / 4) * dag_s3^2 * b3; % 三级太阳轮体积

        % 三级行星轮体积计算（您的平均圆法）
        % 使用正确的三级齿数变量
        da_p3 = mn3 * zp3 + 2 * (1.0 + xp3) * mn3; % 行星轮齿顶圆直径
        df_p3 = mn3 * zp3 - 2 * (1.25 - xp3) * mn3; % 行星轮齿根圆直径
        dag_p3 = (da_p3 + df_p3) / 2; % 行星轮平均圆直径
        Vp3 = (pi / 4) * dag_p3^2 * b3 * planets_count_3; % 所有三级行星轮体积

        % 三级内齿圈体积计算 - 考虑固定外径1442mm约束
        da_r3 = mn3 * zr3 - 2 * (1.0 + xr3) * mn3; % 内齿圈齿顶圆直径(内径)
        df_r3 = mn3 * zr3 + 2 * (1.25 - xr3) * mn3; % 内齿圈齿根圆直径
        dag_r3 = (da_r3 + df_r3) / 2; % 内齿圈平均圆直径
        dr3_outer_required = 1442; % 要求的三级内齿圈外径（固定值）

        % 检查内齿圈几何参数的合理性（不输出警告信息）
        if dag_r3 >= dr3_outer_required || da_r3 >= dr3_outer_required
            % 几何参数不合理，使用最小合理体积
            Vr3 = (pi / 4) * (dr3_outer_required^2 - (dr3_outer_required - 100)^2) * b3;
        else
            % 使用平均圆法计算体积（与GearOptObjectives.m保持一致）
            Vr3 = (pi / 4) * (dr3_outer_required^2 - dag_r3^2) * b3;
        end

        % 确保体积为正值
        if Vr3 <= 0
            Vr3 = (pi / 4) * (dr3_outer_required^2 - (dr3_outer_required - 100)^2) * b3;
        end

        % 计算质量（使用您的方法）
        Ms3 = gear_material.density * Vs3 / 1e9;  % 三级太阳轮质量(kg)
        Mp3_total = gear_material.density * Vp3 / 1e9;  % 三级所有行星轮质量(kg)
        Mp3_single = Mp3_total / planets_count_3;  % 三级单个行星轮质量(kg)
        Mr3 = gear_material.density * Vr3 / 1e9;  % 三级内齿圈质量(kg)
    end
end
Mp3 = Mp3_single * planets_count_3;  % 总的行星轮质量

% 计算三级行星系实际安全系数
try
    % 设置三级行星系参数
    gear_params_planet3 = struct();
    gear_params_planet3.sun = struct('m', mn3, 'z', zs3, 'alpha', alpha3, 'beta', helix_angle_3, 'b', b3, 'x', xs3);
    gear_params_planet3.planet = struct('m', mn3, 'z', zp3, 'alpha', alpha3, 'beta', helix_angle_3, 'b', b3, 'x', xp3);
    gear_params_planet3.ring = struct('m', mn3, 'z', zr3, 'alpha', alpha3, 'beta', helix_angle_3, 'b', b3, 'x', xr3);
    gear_params_planet3.planets_count = planets_count_3;

    % 计算载荷参数
    T1 = 9550 * input_power / input_speed;  % 一级输入转矩 (N·m)
    T2 = T1 * i1;                           % 二级输入转矩 (N·m)
    T3 = T2 * i2;                           % 三级输入转矩 (N·m)
    n3 = input_speed / (i1 * i2);           % 三级输入转速 (rpm)

    planet3LoadParams = struct('T', T3, 'n', n3, 'KA', 1.75, 'service_life', service_life);

    % 获取材料参数
    [sun_material, ~] = MaterialManager('17CrNiMo6');      % 太阳轮材料
    [planet_material, ~] = MaterialManager('20CrNi2MoA');  % 行星轮材料
    [ring_material, ~] = MaterialManager('42CrMoA');       % 内齿圈材料
    planet3MaterialParams = struct('sun', sun_material, 'planet', planet_material, 'ring', ring_material);

    % 计算安全系数
    [~, ~, detailed_safety_3] = PlanetarySystemSafetyCalculator(gear_params_planet3, planet3LoadParams, planet3MaterialParams, 'ISO6336');

    % 使用实际计算的安全系数（保留三位小数）
    SHsps3 = round(detailed_safety_3.SHsps, 3);
    SHspp3 = round(detailed_safety_3.SHspp, 3);
    SFsps3 = round(detailed_safety_3.SFsps, 3);
    SFspp3 = round(detailed_safety_3.SFspp, 3);
    SHprr3 = round(detailed_safety_3.SHprr, 3);
    SHprp3 = round(detailed_safety_3.SHprp, 3);
    SFprr3 = round(detailed_safety_3.SFprr, 3);
    SFprp3 = round(detailed_safety_3.SFprp, 3);

catch ME
    % 如果计算失败，显示错误信息并使用实际计算的安全系数（不使用默认值）
    fprintf('警告：三级行星系安全系数计算失败: %s\n', ME.message);

    % 直接使用实际计算的安全系数，不管是0、负值还是其他异常值
    % 这样用户可以看到真实的计算结果
    if exist('detailed_safety_3', 'var') && isstruct(detailed_safety_3)
        SHsps3 = detailed_safety_3.SHsps;
        SHspp3 = detailed_safety_3.SHspp;
        SFsps3 = detailed_safety_3.SFsps;
        SFspp3 = detailed_safety_3.SFspp;
        SHprr3 = detailed_safety_3.SHprr;
        SHprp3 = detailed_safety_3.SHprp;
        SFprr3 = detailed_safety_3.SFprr;
        SFprp3 = detailed_safety_3.SFprp;
    else
        % 如果连detailed_safety_3都不存在，设为NaN表示计算完全失败
        SHsps3 = NaN; SHspp3 = NaN; SFsps3 = NaN; SFspp3 = NaN;
        SHprr3 = NaN; SHprp3 = NaN; SFprr3 = NaN; SFprp3 = NaN;
        fprintf('错误：三级行星系安全系数计算完全失败，设为NaN\n');
    end
end

expanded_params(56) = SHsps3;     % 太阳-行星接触安全系数 SHsps3
expanded_params(57) = SHspp3;     % 太阳-行星接触安全系数 SHspp3
expanded_params(58) = SFsps3;     % 太阳-行星弯曲安全系数 SFsps3
expanded_params(59) = SFspp3;     % 太阳-行星弯曲安全系数 SFspp3
expanded_params(60) = SHprr3;     % 行星-齿圈接触安全系数 SHprr3
expanded_params(61) = SHprp3;     % 行星-齿圈接触安全系数 SHprp3
expanded_params(62) = SFprr3;     % 行星-齿圈弯曲安全系数 SFprr3
expanded_params(63) = SFprp3;     % 行星-齿圈弯曲安全系数 SFprp3
expanded_params(64) = round(Ms3, 2);        % 太阳轮质量 Ms3（保留两位小数）
expanded_params(65) = round(Mp3, 2);        % 行星轮质量 Mp3（保留两位小数）
expanded_params(66) = round(Mr3, 2);        % 内齿圈质量 Mr3（保留两位小数）

% 计算最后五列：TotalMass、SH、SF、TotalRatio、Error
% 这些值基于实际计算的齿轮参数，不使用任何默认值

% 1. TotalMass - 所有齿轮质量总和
total_mass = M1 + M2 + Ms2 + Mp2 + Mr2 + Ms3 + Mp3 + Mr3;

% 2. 计算所有安全系数并找出最小值
% 收集所有弯曲安全系数
all_bending_sf = [SH1, SF1, SF2, SFsps2, SFspp2, SFprr2, SFprp2, SFsps3, SFspp3, SFprr3, SFprp3];
% 收集所有接触安全系数
all_contact_sf = [SHsps2, SHspp2, SHprr2, SHprp2, SHsps3, SHspp3, SHprr3, SHprp3];

% 找出最小安全系数
min_contact_sf = min(all_contact_sf);  % SH - 最小接触安全系数
min_bending_sf = min(all_bending_sf);  % SF - 最小弯曲安全系数

% 3. TotalRatio - 总传动比
total_ratio = i1 * i2 * i3;

% 4. Error - 传动比误差百分比
% 计算目标传动比（基于输入输出转速）
target_ratio = input_speed / output_speed;
error_ratio = abs(total_ratio - target_ratio) / target_ratio * 100;

% 填充最后五列
expanded_params(67) = round(total_mass, 2);        % TotalMass（保留两位小数）
expanded_params(68) = round(min_contact_sf, 3);    % SH（保留三位小数）
expanded_params(69) = round(min_bending_sf, 3);    % SF（保留三位小数）
expanded_params(70) = round(total_ratio, 3);       % TotalRatio（保留三位小数）
expanded_params(71) = round(error_ratio, 2);       % Error（保留两位小数）

end