function [materials, gear_materials] = MaterialManager(material_name)
% MaterialManager 统一管理齿轮材料属性
%   该函数根据材料名称返回标准化的材料属性结构体
%   统一使用Config\MaterialProperties.m中的参数设置
%
%   输入参数:
%   - material_name: 材料名称，如'17CrNiMo6'、'20CrNi2MoA'、'42CrMoA'等
%                   如果为空，则返回所有材料组合的结构体
%
%   输出:
%   - materials: 包含材料属性的结构体
%   - gear_materials: (可选) 综合齿轮系统材料结构体，适用于优化计算

% 获取统一的材料配置
material_config = MaterialProperties();

% 如果指定了材料名称，返回该材料的属性
if nargin > 0 && ~isempty(material_name)
    % 使用MaterialProperties中的统一参数
    materials = material_config.getMaterialByName(material_name);
    
    % 如果需要第二个输出参数，创建综合齿轮材料结构体
    if nargout > 1
        gear_materials = material_config.gear_materials;
    end
else
    % 如果没有指定材料名称，返回所有材料和标准齿轮系统材料结构体
    materials = struct('material_17CrNiMo6', material_config.material_17CrNiMo6, ...
                      'material_20CrNi2MoA', material_config.material_20CrNi2MoA, ...
                      'material_42CrMoA', material_config.material_42CrMoA);
    
    % 创建三级减速机齿轮传动系统的标准材料配置
    gear_materials = material_config.gear_materials;
end

end
