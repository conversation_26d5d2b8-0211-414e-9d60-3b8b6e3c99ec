function [population, objectives] = RunMOEAD_DE(problem, params)
% RunMOEAD_DE - 运行MOEA/D-DE算法用于多目标优化
%
% 输入:
%   problem - 包含问题信息的结构体
%   params  - 算法参数
%
% 输出:
%   population - 最终种群
%   objectives - 最终的非支配解集目标函数值
%
% 参考文献:
% <PERSON><PERSON> and <PERSON><PERSON>, Multiobjective optimization problems with complicated
% Pareto sets, MOEA/D and NSGA-II, IEEE Transactions on Evolutionary
% Computation, 2009, 13(2): 284-302.

%% 初始化参数
nVar = problem.nVar;
varSize = [1, nVar];
varMin = problem.varMin;
varMax = problem.varMax;
nPop = params.nPop;
maxIt = params.maxIt;
nObj = problem.nObj;

% MOEA/D-DE特有参数
T = 20;         % 邻域大小
nr = 2;         % 最大替换数量
delta = 0.9;    % 选择概率
F = 0.5;        % DE缩放因子
CR = 1.0;       % DE交叉概率

% 确保有评价次数计数器
if ~isfield(problem, 'FE')
    problem.FE = 0;
end

%% 个体结构定义
empty_individual.Position = [];
empty_individual.Cost = [];

%% 生成权重向量
lambda = GenerateWeights(nPop, nObj);

%% 计算邻域
B = zeros(nPop, T);
for i = 1:nPop
    distances = sum((lambda - repmat(lambda(i, :), nPop, 1)).^2, 2);
    [~, indices] = sort(distances);
    B(i, :) = indices(1:T)';
end

%% 初始化种群
pop = repmat(empty_individual, nPop, 1);

for i = 1:nPop
    pop(i).Position = unifrnd(varMin, varMax, varSize);
    
    % 对离散变量特殊处理
    if isfield(problem, 'discreteVars')
        for j = 1:length(problem.discreteVars)
            idx = problem.discreteVars(j).idx;
            if problem.discreteVars(j).isInteger
                pop(i).Position(idx) = round(pop(i).Position(idx));
            else
                % 找到最接近的离散值
                values = problem.discreteVars(j).values;
                [~, closest_idx] = min(abs(pop(i).Position(idx) - values));
                pop(i).Position(idx) = values(closest_idx);
            end
        end
    end
    
    pop(i).Cost = problem.costFunction(pop(i).Position);
    problem.FE = problem.FE + 1;
end

%% 初始化理想点
z = min(vertcat(pop.Cost), [], 1);

%% 优化主循环
for it = 1:maxIt
    for i = 1:nPop
        % 选择父代
        if rand < delta
            % 从邻域中选择
            P = B(i, :);
        else
            % 从整个种群中选择
            P = 1:nPop;
        end
        
        % DE变异操作
        % 随机选择三个不同的个体
        candidates = P(randperm(length(P)));
        if length(candidates) >= 3
            r1 = candidates(1);
            r2 = candidates(2);
            r3 = candidates(3);
        else
            % 如果邻域太小，从整个种群选择
            all_candidates = randperm(nPop, 3);
            r1 = all_candidates(1);
            r2 = all_candidates(2);
            r3 = all_candidates(3);
        end
        
        % DE变异：V = X_r1 + F * (X_r2 - X_r3)
        v = pop(r1).Position + F * (pop(r2).Position - pop(r3).Position);
        
        % 边界处理
        v = max(v, varMin);
        v = min(v, varMax);
        
        % DE交叉
        y = pop(i).Position;
        jrand = randi(nVar);
        for j = 1:nVar
            if rand <= CR || j == jrand
                y(j) = v(j);
            end
        end
        
        % 对离散变量特殊处理
        if isfield(problem, 'discreteVars')
            for j = 1:length(problem.discreteVars)
                idx = problem.discreteVars(j).idx;
                if problem.discreteVars(j).isInteger
                    y(idx) = round(y(idx));
                else
                    values = problem.discreteVars(j).values;
                    [~, closest_idx] = min(abs(y(idx) - values));
                    y(idx) = values(closest_idx);
                end
            end
        end
        
        % 评估
        y_cost = problem.costFunction(y);
        problem.FE = problem.FE + 1;
        
        % 更新理想点
        z = min(z, y_cost);
        
        % 更新邻域解
        c = 0;
        for j = randperm(T)
            if c >= nr
                break;
            end
            
            k = B(i, j);
            
            % 计算Tchebycheff聚合函数值
            g_old = max(lambda(k, :) .* abs(pop(k).Cost - z));
            g_new = max(lambda(k, :) .* abs(y_cost - z));
            
            if g_new <= g_old
                pop(k).Position = y;
                pop(k).Cost = y_cost;
                c = c + 1;
            end
        end
    end
    
    % 显示迭代信息
    if mod(it, 10) == 0 || it == maxIt
        disp(['MOEA/D-DE: 迭代 ' num2str(it) '/' num2str(maxIt) ', 评价次数 = ' num2str(problem.FE)]);
    end
end

%% 提取最终结果
% 获取非支配解
[pop_nd, ~] = NonDominatedSorting(pop);
if ~isempty(pop_nd)
    % 将结构体数组转换为数值矩阵
    population = vertcat(pop_nd.Position);
    objectives = vertcat(pop_nd.Cost);
else
    population = [];
    objectives = [];
end

end

%% ========== 辅助函数 ==========

function lambda = GenerateWeights(N, M)
% 生成权重向量
if M == 2
    lambda = zeros(N, M);
    for i = 1:N
        lambda(i, 1) = (i-1) / (N-1);
        lambda(i, 2) = 1 - lambda(i, 1);
    end
elseif M == 3
    % 对于3目标，使用Das and Dennis方法
    H = floor((N^(1/M) - 1) * M);
    lambda = [];
    for i = 0:H
        for j = 0:H-i
            k = H - i - j;
            lambda = [lambda; i/H, j/H, k/H];
        end
    end
    
    % 如果生成的权重向量数量不够，随机生成补充
    if size(lambda, 1) < N
        additional = N - size(lambda, 1);
        rand_weights = rand(additional, M);
        rand_weights = rand_weights ./ repmat(sum(rand_weights, 2), 1, M);
        lambda = [lambda; rand_weights];
    end
    
    % 如果生成的权重向量数量过多，随机选择N个
    if size(lambda, 1) > N
        indices = randperm(size(lambda, 1), N);
        lambda = lambda(indices, :);
    end
else
    % 对于更高维度，使用随机权重
    lambda = rand(N, M);
    lambda = lambda ./ repmat(sum(lambda, 2), 1, M);
end
end

function [pop_nd, F] = NonDominatedSorting(pop)
% 非支配排序
nPop = numel(pop);

for i = 1:nPop
    pop(i).DominatedCount = 0;
    pop(i).DominationSet = [];
end

F{1} = [];

for i = 1:nPop
    for j = i+1:nPop
        p = pop(i);
        q = pop(j);
        
        if Dominates(p, q)
            p.DominationSet = [p.DominationSet j];
        elseif Dominates(q, p)
            p.DominatedCount = p.DominatedCount + 1;
        end
        
        pop(i) = p;
    end
    
    if pop(i).DominatedCount == 0
        F{1} = [F{1} i];
        pop(i).Rank = 1;
    end
end

% 返回第一前沿的解
pop_nd = pop(F{1});
end

function b = Dominates(p, q)
% 判断p是否支配q
b = all(p.Cost <= q.Cost) && any(p.Cost < q.Cost);
end
