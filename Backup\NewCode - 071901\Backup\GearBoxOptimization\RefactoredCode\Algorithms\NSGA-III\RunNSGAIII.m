function [population, objectives] = RunNSGAIII(problem, params)
% RunNSGAIII - 运行NSGA-III算法用于多目标优化
%
% 输入:
%   problem - 包含问题信息的结构体
%   params  - 算法参数
%
% 输出:
%   population - 最终种群
%   objectives - 最终的非支配解集目标函数值
%
% 参考文献:
% <PERSON><PERSON> and <PERSON><PERSON>, An evolutionary many-objective optimization algorithm
% using reference-point based non-dominated sorting approach, part I:
% Solving problems with box constraints, IEEE Transactions on Evolutionary
% Computation, 2014, 18(4): 577-601.

%% NSGA-III Parameters
nVar = problem.nVar;
varSize = [1, nVar];
varMin = problem.varMin;
varMax = problem.varMax;
nPop = params.nPop;
maxIt = params.maxIt;
nObj = problem.nObj;
pCrossover = params.pCrossover;
pMutation = params.pMutation;
nDivision = 12;  % 参考点划分数

% 确保有评价次数计数器
if ~isfield(problem, 'FE')
    problem.FE = 0;
end

%% 生成参考点
[W, nPop] = GenerateReferencePoints(nDivision, nObj);

%% 个体结构定义
empty_individual.Position = [];
empty_individual.Cost = [];
empty_individual.Rank = [];
empty_individual.DominatedCount = [];
empty_individual.DominationSet = [];

%% 初始化种群
pop = repmat(empty_individual, nPop, 1);

for i = 1:nPop
    pop(i).Position = unifrnd(varMin, varMax, varSize);
    
    % 对离散变量特殊处理
    if isfield(problem, 'discreteVars')
        for j = 1:length(problem.discreteVars)
            idx = problem.discreteVars(j).idx;
            if problem.discreteVars(j).isInteger
                pop(i).Position(idx) = round(pop(i).Position(idx));
            else
                % 找到最接近的离散值
                values = problem.discreteVars(j).values;
                [~, closest_idx] = min(abs(pop(i).Position(idx) - values));
                pop(i).Position(idx) = values(closest_idx);
            end
        end
    end
    
    pop(i).Cost = problem.costFunction(pop(i).Position);
    problem.FE = problem.FE + 1;
end

% 非支配排序
[pop, F] = NonDominatedSorting(pop);

%% 优化主循环
for it = 1:maxIt
    % 交叉
    popc = repmat(empty_individual, nPop, 1);
    for i = 1:nPop/2
        % 锦标赛选择
        p1 = TournamentSelection(pop);
        p2 = TournamentSelection(pop);
        
        % 交叉
        [popc(2*i-1).Position, popc(2*i).Position] = Crossover(p1.Position, p2.Position, pCrossover, varMin, varMax);
        
        % 变异
        popc(2*i-1).Position = Mutate(popc(2*i-1).Position, pMutation, varMin, varMax);
        popc(2*i).Position = Mutate(popc(2*i).Position, pMutation, varMin, varMax);
        
        % 离散变量处理
        if isfield(problem, 'discreteVars')
            for k = [2*i-1, 2*i]
                for j = 1:length(problem.discreteVars)
                    idx = problem.discreteVars(j).idx;
                    if problem.discreteVars(j).isInteger
                        popc(k).Position(idx) = round(popc(k).Position(idx));
                    else
                        values = problem.discreteVars(j).values;
                        [~, closest_idx] = min(abs(popc(k).Position(idx) - values));
                        popc(k).Position(idx) = values(closest_idx);
                    end
                end
            end
        end
        
        % 评估
        popc(2*i-1).Cost = problem.costFunction(popc(2*i-1).Position);
        popc(2*i).Cost = problem.costFunction(popc(2*i).Position);
        problem.FE = problem.FE + 2;
    end
    
    % 合并种群
    pop = [pop; popc];
    
    % 环境选择
    pop = EnvironmentalSelection(pop, nPop, W);
    
    % 显示迭代信息
    if mod(it, 10) == 0 || it == maxIt
        disp(['NSGA-III: 迭代 ' num2str(it) '/' num2str(maxIt) ', 评价次数 = ' num2str(problem.FE)]);
    end
end

%% 提取最终结果
% 获取非支配解
[~, F] = NonDominatedSorting(pop);
if ~isempty(F)
    population = pop(F{1});
    objectives = vertcat(population.Cost);
else
    population = pop;
    objectives = vertcat(pop.Cost);
end

end

%% ========== 辅助函数 ==========

function [W, N] = GenerateReferencePoints(p, M)
% 生成参考点
if M == 2
    W = [0:1/p:1; 1:-1/p:0]';
    W = W(2:end-1, :);
elseif M == 3
    W = [];
    for i = 0:p
        for j = 0:p-i
            k = p - i - j;
            W = [W; i/p, j/p, k/p];
        end
    end
else
    % 对于更高维度，使用简化的均匀分布
    W = rand(p*10, M);
    W = W ./ repmat(sum(W, 2), 1, M);
end
N = size(W, 1);
end

function [pop, F] = NonDominatedSorting(pop)
% 非支配排序（与NSGA-II相同）
nPop = numel(pop);

for i = 1:nPop
    pop(i).DominatedCount = 0;
    pop(i).DominationSet = [];
end

F{1} = [];

for i = 1:nPop
    for j = i+1:nPop
        p = pop(i);
        q = pop(j);
        
        if Dominates(p, q)
            p.DominationSet = [p.DominationSet j];
        elseif Dominates(q, p)
            p.DominatedCount = p.DominatedCount + 1;
        end
        
        pop(i) = p;
    end
    
    if pop(i).DominatedCount == 0
        F{1} = [F{1} i];
        pop(i).Rank = 1;
    end
end

k = 1;
while true
    Q = [];
    
    for i = F{k}
        p = pop(i);
        
        for j = p.DominationSet
            pop(j).DominatedCount = pop(j).DominatedCount - 1;

            if pop(j).DominatedCount == 0
                Q = [Q j];
                pop(j).Rank = k + 1;
            end
        end
    end
    
    if isempty(Q)
        break;
    end
    
    F{k+1} = Q;
    k = k + 1;
end
end

function pop = EnvironmentalSelection(pop, N, W)
% 环境选择
[pop, F] = NonDominatedSorting(pop);

% 选择前几个前沿
Selected = [];
k = 1;
while length(Selected) + length(F{k}) <= N
    Selected = [Selected, F{k}];
    k = k + 1;
end

% 如果需要从最后一个前沿中选择部分个体
if length(Selected) < N && k <= length(F)
    Last = F{k};
    K = N - length(Selected);
    
    if K > 0
        % 基于参考点的选择
        PopObj = vertcat(pop(Last).Cost);
        
        % 归一化
        zmin = min(PopObj, [], 1);
        zmax = max(PopObj, [], 1);
        PopObj = (PopObj - repmat(zmin, size(PopObj, 1), 1)) ./ repmat(zmax - zmin, size(PopObj, 1), 1);
        
        % 关联到参考点
        Distance = pdist2(PopObj, W);
        [~, Association] = min(Distance, [], 2);
        
        % 选择K个个体
        Chosen = zeros(1, K);
        for i = 1:K
            % 找到关联个体最少的参考点
            [~, rho] = min(accumarray(Association, 1, [size(W, 1), 1]));
            
            % 从该参考点关联的个体中选择距离最近的
            Associated = find(Association == rho);
            if ~isempty(Associated)
                [~, best] = min(Distance(Associated, rho));
                Chosen(i) = Last(Associated(best));
                Association(Associated(best)) = [];
                Distance(Associated(best), :) = [];
                Last(Associated(best)) = [];
            else
                % 如果没有关联个体，随机选择
                Chosen(i) = Last(1);
                Last(1) = [];
            end
        end
        
        Selected = [Selected, Chosen];
    end
end

pop = pop(Selected);
end

function i = TournamentSelection(pop)
% 锦标赛选择
nPop = numel(pop);
a = randi([1 nPop]);
b = randi([1 nPop]);

if pop(a).Rank < pop(b).Rank
    i = a;
else
    i = b;
end
end

function [y1, y2] = Crossover(x1, x2, pCrossover, varMin, varMax)
% SBX交叉
alpha = rand(size(x1));
y1 = alpha .* x1 + (1 - alpha) .* x2;
y2 = alpha .* x2 + (1 - alpha) .* x1;

% 边界处理
y1 = max(y1, varMin);
y1 = min(y1, varMax);
y2 = max(y2, varMin);
y2 = min(y2, varMax);
end

function y = Mutate(x, pMutation, varMin, varMax)
% 多项式变异
y = x;
flag = rand(size(x)) <= pMutation;
y(flag) = x(flag) + 0.1 * (varMax - varMin) .* randn(size(x(flag)));

% 边界处理
y = max(y, varMin);
y = min(y, varMax);
end

function b = Dominates(p, q)
% 判断p是否支配q
b = all(p.Cost <= q.Cost) && any(p.Cost < q.Cost);
end
