% Multi-objective optimization evaluation metrics
% Contains various metrics for evaluating the performance of MOO algorithms

% Generational Distance (GD) - Convergence metric
% Measures the average Euclidean distance from each solution to the nearest point in the true Pareto front
function Score = GD(PopObj, PF)
    Distance = min(pdist2(PopObj, PF), [], 2);
    Score = norm(Distance) / length(Distance);
end

% Inverted Generational Distance (IGD) - Convergence metric
% Measures the average Euclidean distance from each point in the true Pareto front to the nearest solution
function Score = IGD(PopObj, PF)
    Distance = min(pdist2(PF, PopObj), [], 2);
    Score = mean(Distance);
end

% Spacing - Diversity metric
% Measures the standard deviation of distances between solutions
function Score = Spacing(PopObj, PF)
    Distance = pdist2(PopObj, PopObj, 'cityblock');
    Distance(logical(eye(size(Distance, 1)))) = inf;
    Score = std(min(Distance, [], 2));
end

% Spread - Diversity metric
% Measures the extent of spread of the solutions
function Score = Spread(PopObj, PF)
    Dis1 = pdist2(<PERSON>Obj, PopObj);
    Dis1(logical(eye(size(Dis1, 1)))) = inf;
    [~, E] = max(PF, [], 1);
    Dis2 = pdist2(PF(E, :), PopObj);
    d1 = sum(min(Dis2, [], 2));
    d2 = mean(min(Dis1, [], 2));
    Score = (d1 + sum(abs(min(Dis1, [], 2) - d2))) / (d1 + (size(PopObj, 1) - size(PopObj, 2)) * d2);
end

% Hypervolume (HV) - Both convergence and diversity metric
% Measures the volume of the objective space dominated by the solutions and bounded by a reference point
function Score = HV(PopObj, PF, RefPoint)
    % Default reference point if not provided
    if nargin < 3
        RefPoint = max(PF, [], 1) * 1.1;
    end
    
    [N, M] = size(PopObj);
    % Normalize the population
    fmin = min(min(PopObj), min(PF));
    fmax = RefPoint;
    PopObj = (PopObj - repmat(fmin, N, 1)) ./ repmat(fmax - fmin, N, 1);
    
    % Sort the population
    PopObj = sortrows(PopObj);
    
    % Calculate HV recursively
    if M > 2
        % For more than 2 objectives, use recursive calculation
        Score = HVRecursive(PopObj, M, RefPoint);
    else
        % For 2 objectives, use direct calculation
        Score = HV2D(PopObj);
    end
end

% Calculate hypervolume for 2D case
function Score = HV2D(PopObj)
    [N, ~] = size(PopObj);
    % Sort by first objective (ascending)
    PopObj = sortrows(PopObj);
    
    % Calculate hypervolume
    Score = 0;
    for i = 1:N
        if i == 1
            Score = Score + (1 - PopObj(i, 1)) * (1 - PopObj(i, 2));
        else
            Score = Score + (1 - PopObj(i, 1)) * (PopObj(i-1, 2) - PopObj(i, 2));
        end
    end
end

% Recursive calculation of hypervolume
function Score = HVRecursive(PopObj, M, RefPoint)
    [N, ~] = size(PopObj);
    Score = 0;
    
    % Sort by last objective (ascending)
    [PopObj, rank] = sortrows(PopObj, M);
    
    % Calculate hypervolume
    for i = 1:N
        if i == 1
            Score = Score + (RefPoint(M) - PopObj(i, M)) * HVRecursive(PopObj(i, 1:M-1), M-1, RefPoint(1:M-1));
        else
            Score = Score + (PopObj(i, M) - PopObj(i-1, M)) * HVRecursive(PopObj(i, 1:M-1), M-1, RefPoint(1:M-1));
        end
    end
end 