function [Archive_X, Archive_F] = MSSA(CostFunction, nVar, VarMin, VarMax, MaxIt, nPop)
% MSSA: 多目标樽海鞘群算法 (Multi-objective Salp Swarm Algorithm)
%
% 输入参数:
%   CostFunction: 目标函数句柄
%   nVar: 决策变量数量
%   VarMin, VarMax: 决策变量的上下界
%   MaxIt: 最大迭代次数
%   nPop: 种群大小
%
% 输出参数:
%   Archive_X: 非支配解的决策变量
%   Archive_F: 非支配解的目标函数值
%
% 参考文献:
%   <PERSON><PERSON><PERSON>li, S., Gandomi, A. H., Mirjalili, S. Z., Saremi, S., Faris, H., & Mir<PERSON>lili, S. M. (2017).
%   "Salp Swarm Algorithm: A bio-inspired optimizer for engineering design problems."
%   Advances in Engineering Software, 114, 163-191.

    % 默认参数值
    if nargin < 6
        nPop = 200;
    end
    if nargin < 5
        MaxIt = 100;
    end
    if nargin < 4
        VarMax = 1;
    end
    if nargin < 3
        VarMin = 0;
    end
    if nargin < 2
        nVar = 5;
    end
    
    % 如果边界是标量，转换为向量
    if isscalar(VarMax)
        VarMax = ones(1, nVar) * VarMax;
    end
    if isscalar(VarMin)
        VarMin = ones(1, nVar) * VarMin;
    end
    
    % 算法参数
    ArchiveMaxSize = 100;                  % 存档最大容量
    
    % 初始化存档
    Archive_X = zeros(ArchiveMaxSize, nVar);
    
    % 计算目标函数数量
    sample = (VarMax - VarMin) .* rand(1, nVar) + VarMin;
    obj_values = CostFunction(sample);
    nObj = numel(obj_values);
    
    Archive_F = inf * ones(ArchiveMaxSize, nObj);
    Archive_member_no = 0;                 % 存档中的解数量
    
    % 初始化种群
    Salps_X = initialization(nPop, nVar, VarMax, VarMin);  % 樽海鞘位置
    Salps_fitness = zeros(nPop, nObj);     % 樽海鞘适应度
    
    % 初始化食物位置（目标）
    Food_position = zeros(nVar, 1);
    Food_fitness = inf * ones(1, nObj);
    
    % 初始化速度
    V = initialization(nPop, nVar, VarMax, VarMin);
    
    % 主循环
    for iter = 1:MaxIt
        % 更新c1参数（公式3.2）
        c1 = 2 * exp(-(4 * iter / MaxIt)^2);
        
        % 评估所有樽海鞘的适应度
        for i = 1:nPop
            Salps_fitness(i, :) = CostFunction(Salps_X(:, i)');
            
            % 如果当前樽海鞘支配食物，更新食物位置
            if dominates(Salps_fitness(i, :), Food_fitness)
                Food_fitness = Salps_fitness(i, :);
                Food_position = Salps_X(:, i);
            end
        end
        
        % 更新存档
        [Archive_X, Archive_F, Archive_member_no] = UpdateArchive(Archive_X, Archive_F, Salps_X', Salps_fitness, Archive_member_no);
        
        % 如果存档超过最大容量，根据拥挤度进行裁剪
        if Archive_member_no > ArchiveMaxSize
            Archive_mem_ranks = RankingProcess(Archive_F, ArchiveMaxSize, nObj);
            [Archive_X, Archive_F, Archive_mem_ranks, Archive_member_no] = HandleFullArchive(Archive_X, Archive_F, Archive_member_no, Archive_mem_ranks, ArchiveMaxSize);
        else
            Archive_mem_ranks = RankingProcess(Archive_F, ArchiveMaxSize, nObj);
        end
        
        % 重新计算拥挤度
        Archive_mem_ranks = RankingProcess(Archive_F, ArchiveMaxSize, nObj);
        
        % 从拥挤度较低的区域选择食物，以提高覆盖度
        index = RouletteWheelSelection(1./Archive_mem_ranks);
        if index == -1
            index = 1;
        end
        Food_fitness = Archive_F(index, :);
        Food_position = Archive_X(index, :)';
        
        % 更新樽海鞘位置
        for i = 1:nPop
            if i <= nPop/2 % 领导者
                for j = 1:nVar
                    c2 = rand();
                    c3 = rand();
                    
                    % 公式(3.1): 樽海鞘跟随者的位置更新
                    if c3 < 0.5
                        Salps_X(j, i) = Food_position(j) + c1 * ((VarMax(j) - VarMin(j)) * c2 + VarMin(j));
                    else
                        Salps_X(j, i) = Food_position(j) - c1 * ((VarMax(j) - VarMin(j)) * c2 + VarMin(j));
                    end
                end
            elseif i > nPop/2 && i < nPop+1 % 跟随者
                % 公式(3.4): 跟随者位置更新
                point1 = Salps_X(:, i-1);
                point2 = Salps_X(:, i);
                Salps_X(:, i) = (point2 + point1) / 2;
            end
            
            % 边界处理
            Flag4ub = Salps_X(:, i) > VarMax';
            Flag4lb = Salps_X(:, i) < VarMin';
            Salps_X(:, i) = (Salps_X(:, i) .* (~(Flag4ub + Flag4lb))) + VarMax' .* Flag4ub + VarMin' .* Flag4lb;
        end
    end
    
    % 返回最终的非支配解
    Archive_X = Archive_X(1:Archive_member_no, :);
    Archive_F = Archive_F(1:Archive_member_no, :);
end

% 初始化种群
function X = initialization(nPop, dim, ub, lb)
    X = zeros(dim, nPop);
    for i = 1:nPop
        X(:, i) = lb' + (ub' - lb') .* rand(dim, 1);
    end
end

% 判断支配关系
function result = dominates(x, y)
    % x支配y，如果x的所有目标都不劣于y，且至少有一个目标严格优于y
    result = all(x <= y) && any(x < y);
end

% 更新存档
function [Archive_X, Archive_F, Archive_member_no] = UpdateArchive(Archive_X, Archive_F, Salps_X, Salps_fitness, Archive_member_no)
    % 将当前种群中的非支配解添加到存档中
    for i = 1:size(Salps_X, 1)
        % 检查当前解是否被存档中的解支配
        dominated = false;
        
        % 检查与存档中的解的支配关系
        for j = 1:Archive_member_no
            if dominates(Archive_F(j, :), Salps_fitness(i, :))
                dominated = true;
                break;
            end
            
            % 如果当前解支配存档中的解，标记存档中的解为删除
            if dominates(Salps_fitness(i, :), Archive_F(j, :))
                Archive_F(j, :) = inf; % 标记为删除
            end
        end
        
        % 如果当前解不被任何存档中的解支配，添加到存档
        if ~dominated
            Archive_member_no = Archive_member_no + 1;
            Archive_X(Archive_member_no, :) = Salps_X(i, :);
            Archive_F(Archive_member_no, :) = Salps_fitness(i, :);
        end
    end
    
    % 删除标记为删除的解
    Archive_F_temp = Archive_F(1:Archive_member_no, :);
    Archive_X_temp = Archive_X(1:Archive_member_no, :);
    
    ind_valid = ~any(isinf(Archive_F_temp), 2);
    
    Archive_F(1:sum(ind_valid), :) = Archive_F_temp(ind_valid, :);
    Archive_X(1:sum(ind_valid), :) = Archive_X_temp(ind_valid, :);
    
    Archive_member_no = sum(ind_valid);
end

% 计算拥挤度
function ranks = RankingProcess(Archive_F, ArchiveMaxSize, M)
    [N, ~] = size(Archive_F);
    ranks = zeros(N, 1);
    
    % 计算每个维度的距离
    distances = zeros(N, N, M);
    for i = 1:M
        sorted_obj = sort(Archive_F(:, i));
        range_obj = sorted_obj(end) - sorted_obj(1);
        
        if range_obj == 0
            range_obj = 1;
        end
        
        for j = 1:N
            for k = 1:N
                distances(j, k, i) = abs(Archive_F(j, i) - Archive_F(k, i)) / range_obj;
            end
        end
    end
    
    % 计算欧几里得距离
    dist = zeros(N, N);
    for i = 1:N
        for j = 1:N
            dist(i, j) = sqrt(sum(squeeze(distances(i, j, :)).^2));
        end
    end
    
    % 对于每个解，计算到第k个最近邻居的距离
    k = max(2, ceil(sqrt(N)));
    for i = 1:N
        sorted_dist = sort(dist(i, :));
        ranks(i) = sorted_dist(min(k, N));
    end
end

% 处理存档满的情况
function [Archive_X, Archive_F, Archive_mem_ranks, Archive_member_no] = HandleFullArchive(Archive_X, Archive_F, Archive_member_no, Archive_mem_ranks, ArchiveMaxSize)
    % 根据拥挤度排序
    [~, indices] = sort(Archive_mem_ranks);
    
    % 保留拥挤度最大的ArchiveMaxSize个解
    Archive_X(1:ArchiveMaxSize, :) = Archive_X(indices(1:ArchiveMaxSize), :);
    Archive_F(1:ArchiveMaxSize, :) = Archive_F(indices(1:ArchiveMaxSize), :);
    Archive_mem_ranks = Archive_mem_ranks(indices(1:ArchiveMaxSize));
    Archive_member_no = ArchiveMaxSize;
    
    % 将多余的位置清零
    Archive_X(ArchiveMaxSize+1:end, :) = 0;
    Archive_F(ArchiveMaxSize+1:end, :) = inf;
end

% 轮盘赌选择
function index = RouletteWheelSelection(P)
    if isempty(P) || all(P <= 0)
        index = -1;
        return;
    end
    
    % 归一化概率
    P = P / sum(P);
    
    % 轮盘赌选择
    r = rand;
    c = cumsum(P);
    index = find(r <= c, 1, 'first');
    
    if isempty(index)
        index = length(P);
    end
end 