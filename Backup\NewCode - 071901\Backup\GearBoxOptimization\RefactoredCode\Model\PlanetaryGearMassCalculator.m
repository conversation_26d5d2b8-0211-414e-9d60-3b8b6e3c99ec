function [ms, mp, mr, total_mass] = PlanetaryGearMassCalculator(gear_params)
% PlanetaryGearMassCalculator 行星轮系质量计算器
% 严格按照GearOptObjectives.m中的原始计算方法，不做任何简化或改变
%
% 输入参数:
%   gear_params - 行星轮系几何参数结构体，包含：
%     .mn: 法向模数 (mm)
%     .z1: 太阳轮齿数
%     .z2: 行星轮齿数
%     .zr: 内齿圈齿数
%     .b: 齿宽 (mm)
%     .planets_count: 行星轮数量
%     .xs: 太阳轮变位系数
%     .xp: 行星轮变位系数
%     .xr: 内齿圈变位系数
%     .gear_materials: 材料参数结构体（可选）
%     .stage: 级数标识（2或3，用于材料选择）
%     .outer_diameter_constraint: 外径约束 (mm，可选，仅三级使用)
%
% 输出:
%   ms - 太阳轮质量 (kg)
%   mp - 所有行星轮总质量 (kg)
%   mr - 内齿圈质量 (kg)
%   total_mass - 总质量 (kg)

%% 提取参数（严格按照原始代码的变量名）
mn = gear_params.mn;
z1 = gear_params.z1;  % 太阳轮齿数
z2 = gear_params.z2;  % 行星轮齿数
zr = gear_params.zr;  % 内齿圈齿数
b = gear_params.b;    % 齿宽
planets_count = gear_params.planets_count;
xs = gear_params.xs;  % 太阳轮变位系数
xp = gear_params.xp;  % 行星轮变位系数
xr = gear_params.xr;  % 内齿圈变位系数

% 级数标识（用于材料选择）
if isfield(gear_params, 'stage')
    stage = gear_params.stage;
else
    stage = 2;  % 默认二级
end

%% 获取材料密度
if isfield(gear_params, 'gear_materials') && ~isempty(gear_params.gear_materials)
    gear_materials = gear_params.gear_materials;
    
    % 根据级数选择材料
    if stage == 2
        % 二级行星轮系材料
        density_sun = gear_materials.planet1_sun.density;      % 17CrNiMo6: 7850
        density_planet = gear_materials.planet1_planet.density; % 20CrNi2MoA: 7870
        density_ring = gear_materials.planet1_ring.density;    % 42CrMoA: 7800
    else
        % 三级行星轮系材料
        density_sun = gear_materials.planet2_sun.density;      % 17CrNiMo6: 7850
        density_planet = gear_materials.planet2_planet.density; % 20CrNi2MoA: 7870
        density_ring = gear_materials.planet2_ring.density;    % 42CrMoA: 7800
    end
else
    % 使用默认材料密度
    density_sun = 7850;     % 17CrNiMo6
    density_planet = 7870;  % 20CrNi2MoA
    density_ring = 7800;    % 42CrMoA
end

%% 使用平均圆法计算行星轮系齿轮体积
%% 太阳轮体积计算
% 计算太阳轮齿顶圆和齿根圆直径
da_s = mn * z1 + 2 * (1.0 + xs) * mn; % 太阳轮齿顶圆直径
df_s = mn * z1 - 2 * (1.25 - xs) * mn; % 太阳轮齿根圆直径
dag_s = (da_s + df_s) / 2; % 太阳轮平均圆直径
Vs = (pi / 4) * dag_s^2 * b; % 太阳轮体积

%% 行星轮体积计算
% 计算行星轮齿顶圆和齿根圆直径
da_p = mn * z2 + 2 * (1.0 + xp) * mn; % 行星轮齿顶圆直径
df_p = mn * z2 - 2 * (1.25 - xp) * mn; % 行星轮齿根圆直径
dag_p = (da_p + df_p) / 2; % 行星轮平均圆直径

% 计算行星轮轴孔直径（齿根圆直径减去6个模数）
d_hole_p = df_p - 6 * mn; % 行星轮轴孔直径

% 计算单个行星轮体积（实体体积减去轴孔体积）
V_solid_p = (pi / 4) * dag_p^2 * b; % 实体体积
V_hole_p = (pi / 4) * d_hole_p^2 * b; % 轴孔体积
V_single_p = V_solid_p - V_hole_p; % 单个行星轮净体积
Vp = V_single_p * planets_count; % 所有行星轮体积

%% 内齿圈体积计算
% 计算内齿圈齿顶圆和齿根圆直径
da_r = mn * zr - 2 * (1.0 + xr) * mn; % 内齿圈齿顶圆直径(内径)
df_r = mn * zr + 2 * (1.25 - xr) * mn; % 内齿圈齿根圆直径
dag_r = (da_r + df_r) / 2; % 内齿圈平均圆直径

% 检查是否有外径约束（仅三级使用）
if isfield(gear_params, 'outer_diameter_constraint') && ~isempty(gear_params.outer_diameter_constraint)
    % 三级内齿圈有固定外径约束
    dr_outer_required = gear_params.outer_diameter_constraint; % 固定外径1442mm
    dr_outer_min = da_r + 12 * mn; % 内齿圈最小外径（齿顶圆直径+12个模数）
    
    % 检查约束条件
    if dr_outer_min > dr_outer_required || dag_r >= dr_outer_required || da_r >= dr_outer_required
        % 内齿圈尺寸不合理，使用最小合理体积
        Vr = (pi / 4) * (dr_outer_required^2 - (dr_outer_required - 100)^2) * b; % 使用50mm壁厚
    else
        % 使用平均圆法计算三级内齿圈体积
        Vr = (pi / 4) * (dr_outer_required^2 - dag_r^2) * b;
        
        % 确保体积为正值
        if Vr <= 0
            Vr = (pi / 4) * (dr_outer_required^2 - (dr_outer_required - 100)^2) * b; % 使用50mm壁厚
        end
    end
else
    % 二级内齿圈或无外径约束的情况
    % 计算内齿圈外径 (齿顶圆直径基础上增加12个模数)
    dr_outer = da_r + 12 * mn;
    
    % 使用平均圆法计算内齿圈体积 (外径与平均圆之间的环形体积)
    Vr = (pi / 4) * (dr_outer^2 - dag_r^2) * b;
end

%% 质量计算
% 使用MaterialManager提供的标准材料密度计算质量
% 密度值：17CrNiMo6=7850 kg/m³, 20CrNi2MoA=7870 kg/m³, 42CrMoA=7800 kg/m³
ms = density_sun * Vs / 1e9;      % 太阳轮质量(kg) - 17CrNiMo6
mp = density_planet * Vp / 1e9;   % 行星轮质量(kg) - 20CrNi2MoA
mr = density_ring * Vr / 1e9;     % 内齿圈质量(kg) - 42CrMoA

% 总质量
total_mass = ms + mp + mr;

end
