function violations = evaluatePlanetaryConstraints(x)
% evaluatePlanetaryConstraints 评估行星轮系几何约束和装配条件
% 
% 输入参数:
%   x - 优化变量向量
%
% 输出参数:
%   violations - 约束违反值数组
%
% 验证的约束包括：
%   1. 邻接条件：相邻行星轮之间不能干涉
%   2. 安装条件：(zs + zr) / n_planets 必须为整数
%   3. 同心条件：角度变位同心条件
%   4. 重合度要求：重合度应≥1.2
%   5. 齿顶不变尖条件：齿顶厚度应≥0.4模数

violations = [];

try
    if length(x) < 18
        return; % 参数不足，跳过验证
    end
    
    % 提取参数
    mn2 = x(2);         % 二级模数
    zs2 = round(x(3));  % 二级太阳轮齿数
    zr2 = round(x(4));  % 二级内齿圈齿数
    zp2 = (zr2 - zs2) / 2; % 二级行星轮齿数
    
    mn3 = x(6);         % 三级模数
    zs3 = round(x(7));  % 三级太阳轮齿数
    zr3 = round(x(8));  % 三级内齿圈齿数
    zp3 = (zr3 - zs3) / 2; % 三级行星轮齿数
    
    % 行星轮数量
    n2 = round(x(10));  % 二级行星轮数量
    n3 = round(x(11));  % 三级行星轮数量
    
    % 变位系数
    xs2 = x(15); xp2 = x(16); % 二级变位系数
    xs3 = x(17); xp3 = x(18); % 三级变位系数
    
    % 计算内齿圈变位系数
    xr2 = -(xs2 * zs2 + xp2 * zp2) / zr2;
    xr3 = -(xs3 * zs3 + xp3 * zp3) / zr3;
    
    %% 1. 邻接条件验证
    violations = [violations, checkAdjacencyCondition(mn2, zs2, zp2, n2, xs2, xp2)];
    violations = [violations, checkAdjacencyCondition(mn3, zs3, zp3, n3, xs3, xp3)];
    
    %% 2. 安装条件验证
    violations = [violations, checkAssemblyCondition(zs2, zr2, n2)];
    violations = [violations, checkAssemblyCondition(zs3, zr3, n3)];
    
    %% 3. 同心条件验证
    violations = [violations, checkConcentricCondition(zs2, zp2, zr2, xs2, xp2, xr2, 20)];
    violations = [violations, checkConcentricCondition(zs3, zp3, zr3, xs3, xp3, xr3, 25)];
    
    %% 4. 重合度要求验证
    violations = [violations, checkContactRatio(zs2, zp2, xs2, xp2, mn2, 20)];
    violations = [violations, checkContactRatio(zs3, zp3, xs3, xp3, mn3, 25)];
    
    %% 5. 齿顶不变尖条件验证
    violations = [violations, checkTipThickness(zs2, xs2, mn2, 'sun')];
    violations = [violations, checkTipThickness(zp2, xp2, mn2, 'planet')];
    violations = [violations, checkTipThickness(zs3, xs3, mn3, 'sun')];
    violations = [violations, checkTipThickness(zp3, xp3, mn3, 'planet')];
    
catch ME
    % 如果计算出错，添加惩罚
    violations = [violations, 1.0];
    fprintf('行星轮系约束验证出错: %s\n', ME.message);
end

% 移除零值
violations = violations(violations > 0);
end

function violation = checkAdjacencyCondition(mn, zs, zp, n_planets, xs, xp)
% 检查邻接条件：相邻行星轮之间不能干涉
% 
% 邻接条件公式：
%   da_p < L_c
%   其中：da_p = 行星轮齿顶圆直径
%         L_c = 相邻行星轮中心距 = 2*a_sp*sin(π/n_planets)
%         a_sp = 太阳轮与行星轮的中心距

violation = 0;

try
    % 计算分度圆直径
    ds = mn * zs;  % 太阳轮分度圆直径
    dp = mn * zp;  % 行星轮分度圆直径
    
    % 计算齿顶圆半径（考虑变位系数）
    ha_star = 1.0;  % 齿顶高系数
    ra_p = dp/2 + mn * (ha_star + xp);  % 行星轮齿顶圆半径
    
    % 太阳轮与行星轮的中心距
    a_sp = (ds + dp) / 2;
    
    % 相邻行星轮中心距
    L_c = 2 * a_sp * sin(pi / n_planets);
    
    % 行星轮齿顶圆直径
    da_p = 2 * ra_p;
    
    % 邻接条件：da_p < L_c
    if da_p >= L_c
        violation = (da_p - L_c + 0.5 * mn) / mn;  % 归一化违反量
    end
    
    % 确保最小间隙≥0.5模数
    min_gap = L_c - da_p;
    if min_gap < 0.5 * mn
        violation = max(violation, (0.5 * mn - min_gap) / mn);
    end
    
catch
    violation = 1.0;  % 计算失败的惩罚
end
end

function violation = checkAssemblyCondition(zs, zr, n_planets)
% 检查安装条件：(zs + zr) / n_planets 必须为整数
% 
% 安装条件公式：
%   (zs + zr) / n_planets = 整数
%   这是行星轮系能够正确装配的必要条件

violation = 0;

try
    assembly_value = (zs + zr) / n_planets;
    integer_check = abs(round(assembly_value) - assembly_value);
    
    % 如果不是整数，违反安装条件
    if integer_check > 1e-6
        violation = integer_check * 10;  % 放大违反量
    end
    
catch
    violation = 1.0;
end
end

function violation = checkConcentricCondition(zs, zp, zr, xs, xp, xr, alpha_deg)
% 检查同心条件：角度变位同心条件
% 
% 同心条件公式：
%   (zs + zp) / cos(α_sp) = (zr - zp) / cos(α_pr)
%   其中：α_sp = 太阳轮-行星轮啮合压力角
%         α_pr = 行星轮-内齿圈啮合压力角

violation = 0;

try
    alpha_rad = alpha_deg * pi / 180;  % 压力角（弧度）
    
    % 计算渐开线函数
    inv_alpha = tan(alpha_rad) - alpha_rad;
    
    % 太阳轮-行星轮啮合压力角
    inv_alpha_sp = inv_alpha + 2 * tan(alpha_rad) * (xs + xp) / (zs + zp);
    alpha_sp = findPressureAngleFromInv(inv_alpha_sp);
    
    % 行星轮-内齿圈啮合压力角
    inv_alpha_pr = inv_alpha + 2 * tan(alpha_rad) * (xp + xr) / (zp + zr);
    alpha_pr = findPressureAngleFromInv(inv_alpha_pr);
    
    % 同心条件等式
    left_side = (zs + zp) / cos(alpha_sp);
    right_side = (zr - zp) / cos(alpha_pr);
    
    % 检查同心条件（允许1%误差）
    relative_error = abs(left_side - right_side) / left_side;
    if relative_error > 0.01
        violation = relative_error * 10;  % 放大违反量
    end
    
catch
    violation = 1.0;
end
end

function violation = checkContactRatio(zs, zp, xs, xp, mn, alpha_deg)
% 检查重合度要求：重合度应≥1.2
% 
% 重合度计算公式（简化）：
%   ε_α = 1.88 - 3.2/zs - 3.2/zp + 0.1*(xs + xp)

violation = 0;

try
    % 简化的重合度计算
    eps_alpha = 1.88 - 3.2/zs - 3.2/zp + 0.1*(xs + xp);
    
    % 重合度要求
    min_eps = 1.2;
    if eps_alpha < min_eps
        violation = (min_eps - eps_alpha) / min_eps;
    end
    
catch
    violation = 1.0;
end
end

function violation = checkTipThickness(z, x, mn, gear_type)
% 检查齿顶不变尖条件：齿顶厚度应≥0.4模数
% 
% 齿顶厚度计算公式（简化）：
%   sa ≈ (π/2 + 2*x*tan(α)) * mn / z - 2*(ha + x)*mn*tan(α)/z

violation = 0;

try
    % 简化的齿顶厚度计算
    alpha_rad = 20 * pi / 180;  % 假设20度压力角
    ha_star = 1.0;
    
    % 近似齿顶厚度
    sa = (pi/2 + 2*x*tan(alpha_rad)) * mn / z - 2*(ha_star + x)*mn*tan(alpha_rad)/z;
    
    % 齿顶厚度要求
    min_sa = 0.4 * mn;
    if sa < min_sa
        violation = (min_sa - sa) / min_sa;
    end
    
catch
    violation = 1.0;
end
end

function alpha = findPressureAngleFromInv(inv_alpha)
% 从渐开线函数值求解压力角（牛顿迭代法）
% 
% 求解方程：tan(α) - α = inv_α

try
    % 初始猜测
    alpha = 20 * pi / 180;
    
    % 牛顿迭代法求解
    for i = 1:10
        f = tan(alpha) - alpha - inv_alpha;
        df = 1/cos(alpha)^2 - 1;
        alpha_new = alpha - f/df;
        
        if abs(alpha_new - alpha) < 1e-8
            break;
        end
        alpha = alpha_new;
    end
    
    % 限制在合理范围内
    alpha = max(15*pi/180, min(30*pi/180, alpha));
    
catch
    alpha = 20 * pi / 180;  % 默认值
end
end
