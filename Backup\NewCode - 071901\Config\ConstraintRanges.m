function constraints = ConstraintRanges()
% ConstraintRanges 齿轮参数约束范围配置
% 定义所有优化变量的上下限约束
%
% 输出:
%   constraints - 约束范围结构体

%% 1. 完整优化模式约束（25个变量）
constraints.full_optimization = struct();

% 变量名称列表
constraints.full_optimization.variable_names = {
    'm1', 'z1', 'z2', 'mn2', 'zs2', 'zp2', 'k_h2', 'mn3', 'zs3', 'zp3', 'k_h3',
    'planets_count_2', 'planets_count_3', 'pressure_angle', 'helix_angle_1', 
    'helix_angle_2', 'helix_angle_3', 'x1', 'x2', 'xs2', 'xp2', 'xs3', 'xp3',
    'pressure_angle_3_choice', 'k_h1'
};

% 下限约束（严格按照原有代码default_lb）
constraints.full_optimization.lb = [
    7,      % m1 - 一级模数 (mm)
    17,     % z1 - 一级小齿轮齿数
    50,     % z2 - 一级大齿轮齿数
    5,      % mn2 - 二级法向模数 (mm)
    17,     % zs2 - 二级太阳轮齿数
    17,     % zp2 - 二级行星轮齿数
    0.6,    % k_h2 - 二级齿宽系数
    5,      % mn3 - 三级法向模数 (mm)
    17,     % zs3 - 三级太阳轮齿数
    17,     % zp3 - 三级行星轮齿数
    0.6,    % k_h3 - 三级齿宽系数
    3,      % planets_count_2 - 二级行星轮数量
    3,      % planets_count_3 - 三级行星轮数量
    20,     % pressure_angle - 压力角 (度)
    8,      % helix_angle_1 - 一级螺旋角 (度)
    0,      % helix_angle_2 - 二级螺旋角 (度)
    0,      % helix_angle_3 - 三级螺旋角 (度)
    0.1,    % x1 - 一级小齿轮变位系数
    0.1,    % x2 - 一级大齿轮变位系数
    0.1,    % xs2 - 二级太阳轮变位系数
    0.1,    % xp2 - 二级行星轮变位系数
    0.1,    % xs3 - 三级太阳轮变位系数
    0.1,    % xp3 - 三级行星轮变位系数
    0,      % pressure_angle_3_choice - 三级压力角选择
    0.28    % k_h1 - 一级齿宽系数
];

% 上限约束（严格按照原有代码default_ub）
constraints.full_optimization.ub = [
    13,     % m1 - 一级模数 (mm)
    30,     % z1 - 一级小齿轮齿数
    120,    % z2 - 一级大齿轮齿数
    20,     % mn2 - 二级法向模数 (mm)
    100,    % zs2 - 二级太阳轮齿数
    100,    % zp2 - 二级行星轮齿数
    1.0,    % k_h2 - 二级齿宽系数
    20,     % mn3 - 三级法向模数 (mm)
    100,    % zs3 - 三级太阳轮齿数
    100,    % zp3 - 三级行星轮齿数
    1.0,    % k_h3 - 三级齿宽系数
    8,      % planets_count_2 - 二级行星轮数量
    8,      % planets_count_3 - 三级行星轮数量
    20,     % pressure_angle - 压力角 (度)
    13,     % helix_angle_1 - 一级螺旋角 (度)
    0,      % helix_angle_2 - 二级螺旋角 (度)
    0,      % helix_angle_3 - 三级螺旋角 (度)
    1.0,    % x1 - 一级小齿轮变位系数
    1.0,    % x2 - 一级大齿轮变位系数
    1.0,    % xs2 - 二级太阳轮变位系数
    1.0,    % xp2 - 二级行星轮变位系数
    1.0,    % xs3 - 三级太阳轮变位系数
    1.0,    % xp3 - 三级行星轮变位系数
    1,      % pressure_angle_3_choice - 三级压力角选择
    0.4     % k_h1 - 一级齿宽系数
];

%% 2. 固定一级参数模式约束（18个变量）
constraints.fixed_first_stage = struct();

% 变量名称列表
constraints.fixed_first_stage.variable_names = {
    'mn2', 'zs2', 'zp2', 'k_h2', 'mn3', 'zs3', 'zp3', 'k_h3',
    'planets_count_2', 'planets_count_3', 'pressure_angle', 'helix_angle_2',
    'helix_angle_3', 'xs2', 'xp2', 'xs3', 'xp3', 'pressure_angle_3_choice'
};

% 下限约束（严格按照原有代码lb_fixed）
constraints.fixed_first_stage.lb = [
    5,      % mn2 - 二级法向模数 (mm)
    17,     % zs2 - 二级太阳轮齿数
    17,     % zp2 - 二级行星轮齿数
    0.6,    % k_h2 - 二级齿宽系数
    5,      % mn3 - 三级法向模数 (mm)
    17,     % zs3 - 三级太阳轮齿数
    17,     % zp3 - 三级行星轮齿数
    0.6,    % k_h3 - 三级齿宽系数
    3,      % planets_count_2 - 二级行星轮数量
    3,      % planets_count_3 - 三级行星轮数量
    20,     % pressure_angle - 压力角 (度)
    0,      % helix_angle_2 - 二级螺旋角 (度)
    0,      % helix_angle_3 - 三级螺旋角 (度)
    0.3,    % xs2 - 二级太阳轮变位系数
    0.2,    % xp2 - 二级行星轮变位系数
    0.3,    % xs3 - 三级太阳轮变位系数
    0.2,    % xp3 - 三级行星轮变位系数
    0       % pressure_angle_3_choice - 三级压力角选择
];

% 上限约束（严格按照原有代码ub_fixed）
constraints.fixed_first_stage.ub = [
    20,     % mn2 - 二级法向模数 (mm)
    100,    % zs2 - 二级太阳轮齿数
    100,    % zp2 - 二级行星轮齿数
    1.0,    % k_h2 - 二级齿宽系数
    20,     % mn3 - 三级法向模数 (mm)
    100,    % zs3 - 三级太阳轮齿数
    100,    % zp3 - 三级行星轮齿数
    1.0,    % k_h3 - 三级齿宽系数
    5,      % planets_count_2 - 二级行星轮数量
    5,      % planets_count_3 - 三级行星轮数量
    20,     % pressure_angle - 压力角 (度)
    0,      % helix_angle_2 - 二级螺旋角 (度)
    0,      % helix_angle_3 - 三级螺旋角 (度)
    0.6,    % xs2 - 二级太阳轮变位系数
    0.5,    % xp2 - 二级行星轮变位系数
    0.6,    % xs3 - 三级太阳轮变位系数
    0.5,    % xp3 - 三级行星轮变位系数
    1       % pressure_angle_3_choice - 三级压力角选择
];

%% 3. 工程约束
constraints.engineering = struct();

% 几何约束
constraints.engineering.geometry = struct();
constraints.engineering.geometry.min_tooth_count = 18;        % 最小齿数
constraints.engineering.geometry.max_tooth_count = 120;      % 最大齿数
constraints.engineering.geometry.min_module = 1.5;           % 最小模数 (mm)
constraints.engineering.geometry.max_module = 25;            % 最大模数 (mm)
constraints.engineering.geometry.min_width_coefficient = 0.2; % 最小齿宽系数
constraints.engineering.geometry.max_width_coefficient = 1.5; % 最大齿宽系数

% 传动比约束
constraints.engineering.transmission_ratio = struct();
constraints.engineering.transmission_ratio.min_stage_ratio = 2.0;   % 最小单级传动比
constraints.engineering.transmission_ratio.max_stage_ratio = 8.0;   % 最大单级传动比
constraints.engineering.transmission_ratio.target_total_ratio = 79.98; % 目标总传动比
constraints.engineering.transmission_ratio.ratio_tolerance = 0.05;  % 传动比容差

% 安全系数约束
constraints.engineering.safety_factors = struct();
constraints.engineering.safety_factors.min_contact_safety = 1.2;    % 最小接触安全系数
constraints.engineering.safety_factors.min_bending_safety = 1.2;    % 最小弯曲安全系数
constraints.engineering.safety_factors.max_contact_safety = 5.0;    % 最大接触安全系数
constraints.engineering.safety_factors.max_bending_safety = 5.0;    % 最大弯曲安全系数

% 中心距约束
constraints.engineering.center_distance = struct();
constraints.engineering.center_distance.stage1_fixed = 400;         % 一级中心距固定值 (mm)
constraints.engineering.center_distance.stage2_min = 50;            % 二级最小中心距 (mm)
constraints.engineering.center_distance.stage2_max = 300;           % 二级最大中心距 (mm)
constraints.engineering.center_distance.stage3_min = 50;            % 三级最小中心距 (mm)
constraints.engineering.center_distance.stage3_max = 400;           % 三级最大中心距 (mm)

%% 4. 变位系数约束
constraints.shift_coefficients = struct();

% 外齿轮变位系数
constraints.shift_coefficients.external_gear_min = 0.0;     % 外齿轮最小变位系数
constraints.shift_coefficients.external_gear_max = 1.0;     % 外齿轮最大变位系数

% 内齿轮变位系数
constraints.shift_coefficients.internal_gear_min = -0.8;    % 内齿轮最小变位系数
constraints.shift_coefficients.internal_gear_max = 0.3;     % 内齿轮最大变位系数

% 综合变位系数
constraints.shift_coefficients.sum_min = 0.6;               % 最小综合变位系数
constraints.shift_coefficients.sum_max = 1.2;               % 最大综合变位系数

%% 5. 离散变量定义（严格按照原有代码的problem.discreteVars）
constraints.discrete_variables = struct();

% 完整优化模式的离散变量（25个变量）
constraints.discrete_variables.full_optimization = [];

% 压力角选择（第24个变量）
constraints.discrete_variables.full_optimization(1).idx = 24;
constraints.discrete_variables.full_optimization(1).isInteger = true;
constraints.discrete_variables.full_optimization(1).values = [20, 25];

% 行星轮数量（第12、13个变量）
constraints.discrete_variables.full_optimization(2).idx = 12;
constraints.discrete_variables.full_optimization(2).isInteger = true;
constraints.discrete_variables.full_optimization(2).values = [3, 4, 5];

constraints.discrete_variables.full_optimization(3).idx = 13;
constraints.discrete_variables.full_optimization(3).isInteger = true;
constraints.discrete_variables.full_optimization(3).values = [3, 4, 5];

% 模数（第1、4、8个变量）
constraints.discrete_variables.full_optimization(4).idx = 1;
constraints.discrete_variables.full_optimization(4).isInteger = false;
constraints.discrete_variables.full_optimization(4).values = [5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 20];

constraints.discrete_variables.full_optimization(5).idx = 4;
constraints.discrete_variables.full_optimization(5).isInteger = false;
constraints.discrete_variables.full_optimization(5).values = [5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 20];

constraints.discrete_variables.full_optimization(6).idx = 8;
constraints.discrete_variables.full_optimization(6).isInteger = false;
constraints.discrete_variables.full_optimization(6).values = [5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 20];

% 齿数（第2、3、5、6、9、10个变量）
constraints.discrete_variables.full_optimization(7).idx = 2;
constraints.discrete_variables.full_optimization(7).isInteger = true;
constraints.discrete_variables.full_optimization(7).values = 17:30;

constraints.discrete_variables.full_optimization(8).idx = 3;
constraints.discrete_variables.full_optimization(8).isInteger = true;
constraints.discrete_variables.full_optimization(8).values = 50:120;

constraints.discrete_variables.full_optimization(9).idx = 5;
constraints.discrete_variables.full_optimization(9).isInteger = true;
constraints.discrete_variables.full_optimization(9).values = 17:100;

constraints.discrete_variables.full_optimization(10).idx = 6;
constraints.discrete_variables.full_optimization(10).isInteger = true;
constraints.discrete_variables.full_optimization(10).values = 17:100;

constraints.discrete_variables.full_optimization(11).idx = 9;
constraints.discrete_variables.full_optimization(11).isInteger = true;
constraints.discrete_variables.full_optimization(11).values = 17:100;

constraints.discrete_variables.full_optimization(12).idx = 10;
constraints.discrete_variables.full_optimization(12).isInteger = true;
constraints.discrete_variables.full_optimization(12).values = 17:100;

% 齿宽系数（第7、11、25个变量，0.01步长）
constraints.discrete_variables.full_optimization(13).idx = 7;
constraints.discrete_variables.full_optimization(13).isInteger = false;
constraints.discrete_variables.full_optimization(13).values = 0.6:0.01:1.0;

constraints.discrete_variables.full_optimization(14).idx = 11;
constraints.discrete_variables.full_optimization(14).isInteger = false;
constraints.discrete_variables.full_optimization(14).values = 0.6:0.01:1.0;

constraints.discrete_variables.full_optimization(15).idx = 25;
constraints.discrete_variables.full_optimization(15).isInteger = false;
constraints.discrete_variables.full_optimization(15).values = 0.28:0.01:0.4;

% 固定一级参数模式的离散变量（18个变量）
constraints.discrete_variables.fixed_first_stage = [];

% 压力角选择（第18个变量）
constraints.discrete_variables.fixed_first_stage(1).idx = 18;
constraints.discrete_variables.fixed_first_stage(1).isInteger = true;
constraints.discrete_variables.fixed_first_stage(1).values = [20, 25];

% 行星轮数量（第9、10个变量）
constraints.discrete_variables.fixed_first_stage(2).idx = 9;
constraints.discrete_variables.fixed_first_stage(2).isInteger = true;
constraints.discrete_variables.fixed_first_stage(2).values = [3, 4, 5];

constraints.discrete_variables.fixed_first_stage(3).idx = 10;
constraints.discrete_variables.fixed_first_stage(3).isInteger = true;
constraints.discrete_variables.fixed_first_stage(3).values = [3, 4, 5];

% 模数（第1、5个变量）
constraints.discrete_variables.fixed_first_stage(4).idx = 1;
constraints.discrete_variables.fixed_first_stage(4).isInteger = false;
constraints.discrete_variables.fixed_first_stage(4).values = [5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 20];

constraints.discrete_variables.fixed_first_stage(5).idx = 5;
constraints.discrete_variables.fixed_first_stage(5).isInteger = false;
constraints.discrete_variables.fixed_first_stage(5).values = [5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 20];

% 齿数（第2、3、6、7个变量）
constraints.discrete_variables.fixed_first_stage(6).idx = 2;
constraints.discrete_variables.fixed_first_stage(6).isInteger = true;
constraints.discrete_variables.fixed_first_stage(6).values = 17:100;

constraints.discrete_variables.fixed_first_stage(7).idx = 3;
constraints.discrete_variables.fixed_first_stage(7).isInteger = true;
constraints.discrete_variables.fixed_first_stage(7).values = 17:100;

constraints.discrete_variables.fixed_first_stage(8).idx = 6;
constraints.discrete_variables.fixed_first_stage(8).isInteger = true;
constraints.discrete_variables.fixed_first_stage(8).values = 17:100;

constraints.discrete_variables.fixed_first_stage(9).idx = 7;
constraints.discrete_variables.fixed_first_stage(9).isInteger = true;
constraints.discrete_variables.fixed_first_stage(9).values = 17:100;

% 齿宽系数（第4、8个变量，0.01步长）
constraints.discrete_variables.fixed_first_stage(10).idx = 4;
constraints.discrete_variables.fixed_first_stage(10).isInteger = false;
constraints.discrete_variables.fixed_first_stage(10).values = 0.6:0.01:1.0;

constraints.discrete_variables.fixed_first_stage(11).idx = 8;
constraints.discrete_variables.fixed_first_stage(11).isInteger = false;
constraints.discrete_variables.fixed_first_stage(11).values = 0.6:0.01:1.0;

%% 6. 质量约束
constraints.mass = struct();
constraints.mass.max_total_mass = 2000;                     % 最大总质量 (kg)
constraints.mass.max_gear_mass = 500;                       % 单个齿轮最大质量 (kg)

%% 7. 约束检查函数
constraints.checkConstraints = @(x, mode) checkAllConstraints(constraints, x, mode);
constraints.getConstraintViolation = @(x, mode) getConstraintViolation(constraints, x, mode);

end

%% ========== 辅助函数 ==========

function is_valid = checkAllConstraints(constraints, x, mode)
% 检查所有约束条件
is_valid = true;

try
    % 获取对应模式的约束
    if strcmp(mode, 'full')
        lb = constraints.full_optimization.lb;
        ub = constraints.full_optimization.ub;
    else
        lb = constraints.fixed_first_stage.lb;
        ub = constraints.fixed_first_stage.ub;
    end
    
    % 检查边界约束
    if any(x < lb) || any(x > ub)
        is_valid = false;
        return;
    end
    
    % 检查工程约束
    % 这里可以添加更多的工程约束检查
    
catch e
    fprintf('约束检查出错: %s\n', e.message);
    is_valid = false;
end
end

function violation = getConstraintViolation(constraints, x, mode)
% 计算约束违反程度
violation = 0;

try
    % 获取对应模式的约束
    if strcmp(mode, 'full')
        lb = constraints.full_optimization.lb;
        ub = constraints.full_optimization.ub;
    else
        lb = constraints.fixed_first_stage.lb;
        ub = constraints.fixed_first_stage.ub;
    end
    
    % 计算边界约束违反
    lower_violation = sum(max(0, lb - x));
    upper_violation = sum(max(0, x - ub));
    violation = lower_violation + upper_violation;
    
catch e
    fprintf('约束违反计算出错: %s\n', e.message);
    violation = Inf;
end
end
