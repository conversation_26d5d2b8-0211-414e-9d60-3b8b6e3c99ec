function ms = MS(approximation_front, reference_front)
% MS - Maximum Spread 最大扩展度指标
% 计算近似前沿在各目标上的覆盖范围相对于参考前沿的比率
%
% 输入:
%   approximation_front - 近似前沿解集
%   reference_front - 参考前沿解集
%
% 输出:
%   ms - 最大扩展度值

if isempty(approximation_front) || isempty(reference_front)
    ms = 0;
    return;
end

m = size(approximation_front, 2); % 目标数量

% 计算近似前沿和参考前沿在各目标上的极值
min_approx = min(approximation_front);
max_approx = max(approximation_front);
min_ref = min(reference_front);
max_ref = max(reference_front);

% 计算各目标上的归一化扩展度
spread_sum = 0;
for i = 1:m
    range_approx = max_approx(i) - min_approx(i);
    range_ref = max_ref(i) - min_ref(i);
    if range_ref == 0
        normalized_spread = 1;
    else
        normalized_spread = range_approx / range_ref;
    end
    spread_sum = spread_sum + normalized_spread^2;
end

ms = sqrt(spread_sum / m);
end
