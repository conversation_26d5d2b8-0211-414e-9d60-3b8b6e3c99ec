function html_output = GenerateFirstStageHTML(gear_combinations, sf_combinations, contact_safety_factor, bending_safety_factor)
% GenerateFirstStageHTML 生成一级平行轴系组合的HTML表格
%   输入: 
%   - gear_combinations: 一级平行轴系所有组合参数表格
%   - sf_combinations: 满足安全系数要求的组合参数表格
%   - contact_safety_factor: 接触安全系数要求
%   - bending_safety_factor: 弯曲安全系数要求
%   输出:
%   - html_output: 包含表格的HTML文本

% 处理可选参数
if nargin < 2
    sf_combinations = [];
end
if nargin < 3
    contact_safety_factor = 1.2;
end
if nargin < 4
    bending_safety_factor = 1.2;
end

% 检查输入是否为空
if isempty(gear_combinations) || height(gear_combinations) == 0
    html_output = '<div class="alert alert-warning">未找到满足约束的一级平行轴系组合</div>';
    return;
end

% 开始生成HTML - 使用cell数组存储HTML片段，避免'\n'字符问题
html = {};
html{end+1} = '<!DOCTYPE html>';
html{end+1} = '<html lang="zh-CN">';
html{end+1} = '<head>';
html{end+1} = '    <meta charset="UTF-8">';
html{end+1} = '    <meta name="viewport" content="width=device-width, initial-scale=1.0">';
html{end+1} = '    <title>一级平行轴系参数组合报告</title>';

% 引入外部库
html{end+1} = '    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.6/css/jquery.dataTables.min.css">';
html{end+1} = '    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">';
html{end+1} = '    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>';
html{end+1} = '    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>';

% CSS样式，采用CreateSummaryTables.m的风格
html{end+1} = '    <style>';
html{end+1} = '        @import url("https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap");';
html{end+1} = '        body { font-family: "Microsoft YaHei", "Roboto", Arial, sans-serif; margin: 0; color: #333; background-color: #f5f7fa; line-height: 1.5; }';
html{end+1} = '        h1, h2, h3 { color: #2c3e50; margin-top: 0.5em; margin-bottom: 0.5em; }';
html{end+1} = '        h1 { text-align: center; padding: 15px 10px; background: linear-gradient(135deg, #3498db, #2c3e50); color: white; font-weight: 500; letter-spacing: 1px; box-shadow: 0 2px 6px rgba(0,0,0,0.1); }';
html{end+1} = '        h2 { border-bottom: 2px solid #3498db; padding-bottom: 5px; font-weight: 500; }';
html{end+1} = '        .main-container { max-width: 1200px; margin: 0 auto; padding: 20px; padding-bottom: 40px; }';
html{end+1} = '        .container { background-color: white; padding: 20px; border-radius: 5px; box-shadow: 0 1px 3px rgba(0,0,0,0.08); margin-bottom: 25px; }';
html{end+1} = '        table.dataTable { width: 100%; border-collapse: collapse; margin: 15px 0; font-size: 14px; }';
html{end+1} = '        table.dataTable thead th { background: linear-gradient(to bottom, #5a7b9c, #7b96ae); color: white; text-align: center; padding: 10px 5px; vertical-align: middle; font-weight: 500; border: 1px solid #4a6b8c; }';
html{end+1} = '        table.dataTable tbody td { border: 1px solid #e0e6ed; text-align: center; padding: 8px 5px; }';
html{end+1} = '        table.dataTable tbody tr:nth-child(even) { background-color: #f8fafc; }';
html{end+1} = '        table.dataTable tbody tr:hover { background-color: #eef7fb !important; transition: all 0.2s ease; }';
html{end+1} = '        .highlight-row { background-color: #c3e6cb !important; }';
html{end+1} = '        .highlight-row:hover { background-color: #b1dfbb !important; }';
html{end+1} = '        /* 参数分组颜色 - 更鲜明的浅色系 */';
html{end+1} = '        .group-geometry { background-color: #e6f2ff; }'; % 几何参数 - 更鲜明的浅蓝色
html{end+1} = '        .group-shift { background-color: #fff0e6; }';    % 变位系数 - 浅橙色
html{end+1} = '        .group-result { background-color: #f0f0f0; }';    % 结果参数 - 浅灰色
html{end+1} = '        .group-mass { background-color: #f0f0f0; }';     % 质量参数 - 浅灰色（与结果参数相同）
html{end+1} = '        .group-safety { background-color: #e6ffec; }';    % 安全系数 - 浅绿色
html{end+1} = '        .summary { margin: 15px 0; padding: 15px; background-color: #edf7ff; border-radius: 5px; border-left: 5px solid #3498db; }';
html{end+1} = '        .dataTables_wrapper .dataTables_filter input { margin-left: 10px; padding: 5px; border: 1px solid #ddd; border-radius: 4px; }';
html{end+1} = '        .dataTables_wrapper .dataTables_length select { padding: 5px; margin: 0 5px; border: 1px solid #ddd; border-radius: 4px; }';
html{end+1} = '        .dataTables_info, .dataTables_paginate { margin-top: 15px; }';
html{end+1} = '        .dataTables_paginate .paginate_button { padding: 5px 10px; margin: 0 2px; border: 1px solid #ddd; border-radius: 4px; cursor: pointer; }';
html{end+1} = '        .dataTables_paginate .paginate_button.current { background: #3498db; color: white !important; border-color: #3498db; }';
html{end+1} = '        .explanation { background-color: white; border-left: 4px solid #3498db; padding: 15px; margin: 15px 0; }';
html{end+1} = '        .explanation h4 { margin-top: 0; color: #2980b9; font-weight: 500; }';
html{end+1} = '        .footnote { font-size: 13px; color: #666; margin-top: 30px; margin-bottom: 20px; padding: 15px 0; text-align: center; border-top: 1px solid #e0e6ed; }';
html{end+1} = '        /* 颜色图例 */';
html{end+1} = '        .color-legend { display: flex; flex-wrap: wrap; gap: 15px; margin: 15px 0; }';
html{end+1} = '        .color-item { display: flex; align-items: center; }';
html{end+1} = '        .color-box { width: 20px; height: 20px; margin-right: 8px; border: 1px solid #ddd; }';
html{end+1} = '        .recommended-box { background-color: #c3e6cb; }';
html{end+1} = '        .geometry-box { background-color: #e6f2ff; }';
html{end+1} = '        .shift-box { background-color: #fff0e6; }';
html{end+1} = '        .result-box { background-color: #f0f0f0; }';
html{end+1} = '        .safety-box { background-color: #e6ffec; }';
html{end+1} = '        /* 导航栏 */';
html{end+1} = '        .navbar { background-color: #f8f9fa; padding: 10px 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.05); position: sticky; top: 0; z-index: 1000; display: flex; justify-content: center; }';
html{end+1} = '        .navbar a { margin: 0 15px; padding: 8px 15px; color: #3498db; text-decoration: none; border-radius: 4px; font-weight: 500; }';
html{end+1} = '        .navbar a:hover { background-color: #eef7fb; }';
html{end+1} = '        .section-title { padding: 25px 0 0; margin: 45px 0 25px; border-top: 1px solid #e0e6ed; text-align: center; color: #2c3e50; }';
html{end+1} = '        .section-title h2 { display: inline-block; margin-top: 0; padding: 0 20px; }';
html{end+1} = '    </style>';
html{end+1} = '</head>';
html{end+1} = '<body>';

html{end+1} = sprintf('<h1 style="position: relative;">一级平行轴系参数组合报告<span style="position: absolute; right: 20px; bottom: 15px; font-size: 14px; font-weight: normal; opacity: 0.9;">生成时间: %s</span></h1>', string(datetime("now", "Format", "yyyy-MM-dd HH:mm:ss")));

% 添加导航栏
html{end+1} = '<div class="navbar">';
html{end+1} = '    <a href="#summary">概览信息</a>';
html{end+1} = '    <a href="#clustered-table">聚类后参数表格</a>';
if ~isempty(sf_combinations) && height(sf_combinations) > 0
    html{end+1} = '    <a href="#safety-table">满足安全系数表格</a>';
end
html{end+1} = '    <a href="#all-table">所有参数表格</a>';
html{end+1} = '</div>';

html{end+1} = '<div class="main-container">';

% 添加概览信息部分
html{end+1} = '    <div class="container" id="summary">';
html{end+1} = '        <h2 style="margin-top:0;">参数组合概览</h2>';
html{end+1} = '        <div class="summary">';
html{end+1} = sprintf('            <p><i class="fas fa-info-circle"></i> <strong>找到符合条件的组合数量: </strong>%s</p>', num2str(height(gear_combinations)));
if ~isempty(sf_combinations) && height(sf_combinations) > 0
    html{end+1} = sprintf('            <p><i class="fas fa-info-circle"></i> <strong>满足安全系数要求的组合数量: </strong>%s</p>', num2str(height(sf_combinations)));
    
    % 尝试读取聚类后的参数数量
    try
        clustered_file = fullfile('Results', '一级平行轴系聚类后参数.csv');
        clustered_params = readtable(clustered_file, 'VariableNamingRule', 'preserve');
        html{end+1} = sprintf('            <p><i class="fas fa-info-circle"></i> <strong>聚类后保留的组合数量: </strong>%s</p>', num2str(height(clustered_params)));
    catch
        % 如果无法读取聚类文件，不显示此信息
    end
end
html{end+1} = '            <p><i class="fas fa-ruler"></i> <strong>目标中心距: </strong>400 mm (误差控制在±0.05%以内)</p>';
html{end+1} = sprintf('            <p><i class="fas fa-shield-alt"></i> <strong>接触安全系数要求: </strong>≥ %.2f</p>', contact_safety_factor);
html{end+1} = sprintf('            <p><i class="fas fa-shield-alt"></i> <strong>弯曲安全系数要求: </strong>≥ %.2f</p>', bending_safety_factor);

html{end+1} = '            <h4 style="margin-top:15px;">约束条件:</h4>';
html{end+1} = '            <ul>';
html{end+1} = '                <li><strong>模数:</strong> 限制在离散值: 7, 8, 9, 10, 11, 12, 13 mm</li>';
html{end+1} = '                <li><strong>齿数:</strong> 不小于17，不大于100</li>';
html{end+1} = '                <li><strong>变位系数:</strong>';
html{end+1} = '                    <ul>';
html{end+1} = '                        <li>综合变位系数: 0.0-1.0</li>';
html{end+1} = '                    </ul>';
html{end+1} = '                </li>';
html{end+1} = '                <li><strong>螺旋角:</strong> 8°-13°</li>';
html{end+1} = '                <li><strong>压力角:</strong> 20°</li>';
html{end+1} = '                <li><strong>安全系数要求:</strong>';
html{end+1} = '                    <ul>';
html{end+1} = sprintf('                        <li>接触安全系数: ≥ %.2f</li>', contact_safety_factor);
html{end+1} = sprintf('                        <li>弯曲安全系数: ≥ %.2f</li>', bending_safety_factor);
html{end+1} = '                    </ul>';
html{end+1} = '                </li>';
html{end+1} = '            </ul>';
html{end+1} = '        </div>';

% 添加颜色图例
html{end+1} = '        <div class="explanation">';
html{end+1} = '            <h4>表格颜色说明</h4>';
html{end+1} = '            <div class="color-legend">';
html{end+1} = '                <div class="color-item"><div class="color-box recommended-box"></div><span>推荐配置</span></div>';
html{end+1} = '                <div class="color-item"><div class="color-box geometry-box"></div><span>几何参数</span></div>';
html{end+1} = '                <div class="color-item"><div class="color-box shift-box"></div><span>变位系数</span></div>';
html{end+1} = '                <div class="color-item"><div class="color-box result-box"></div><span>结果参数</span></div>';
html{end+1} = '                <div class="color-item"><div class="color-box result-box"></div><span>质量参数</span></div>';
html{end+1} = '                <div class="color-item"><div class="color-box safety-box"></div><span>安全系数</span></div>';
html{end+1} = '            </div>';
html{end+1} = '        </div>';
html{end+1} = '    </div>';

% 添加聚类后参数表格部分 - 始终显示此部分，并放在满足安全系数表格之前
html{end+1} = '    <div class="section-title" id="clustered-table">';
html{end+1} = '        <h2>聚类后参数组合</h2>';
html{end+1} = '    </div>';
html{end+1} = '    <div class="container">';

% 尝试读取聚类后的参数文件
clustered_file = fullfile('Results', '一级平行轴系聚类后参数.csv');
try
    clustered_params = readtable(clustered_file, 'VariableNamingRule', 'preserve');
    html{end+1} = '        <h3>聚类后保留的不同核心参数组合</h3>';
    html{end+1} = '        <div class="table-responsive">';
    html{end+1} = '            <table id="clusteredTable" class="display compact stripe hover">';
    html = [html, generateTableHeaders()];
    html = [html, generateTableBody(clustered_params, true, contact_safety_factor, bending_safety_factor)];
    html{end+1} = '            </table>';
    html{end+1} = '        </div>';

    % 添加聚类说明信息
    html{end+1} = '        <div class="explanation">';
    html{end+1} = '            <h4>聚类说明</h4>';
    html{end+1} = '            <ul>';
    html{end+1} = '                <li><strong>聚类方法:</strong> 将核心参数（模数、齿数、螺旋角、压力角）相同的组合归为一组</li>';
    html{end+1} = '                <li><strong>选择标准:</strong> 每组只保留质量最小的那个解（通常是齿宽系数或变位系数最优的组合）</li>';
    if ~isempty(sf_combinations)
        html{end+1} = sprintf('                <li><strong>聚类结果:</strong> 从原始的%d组参数中提取出%d组不同核心参数组合</li>', height(sf_combinations), height(clustered_params));
    else
        html{end+1} = sprintf('                <li><strong>聚类结果:</strong> 提取出%d组不同核心参数组合</li>', height(clustered_params));
    end
    html{end+1} = '                <li><strong>优化策略:</strong> 这些聚类后的参数将用于二三级参数优化，可以提高优化效率并保持参数多样性</li>';
    html{end+1} = '            </ul>';
    html{end+1} = '        </div>';
catch
    % 如果无法读取聚类文件，显示提示信息
    html{end+1} = '        <div class="alert alert-warning">';
    html{end+1} = '            <p><i class="fas fa-exclamation-triangle"></i> <strong>未找到聚类后的参数文件</strong></p>';
    html{end+1} = '            <p>聚类后的参数文件将在优化过程中自动生成。请先运行完整优化过程。</p>';
    html{end+1} = '        </div>';
end

html{end+1} = '    </div>';

% 判断是否需要显示满足安全系数的表格
if ~isempty(sf_combinations) && height(sf_combinations) > 0
    % 生成满足安全系数要求的表格
    html{end+1} = '    <div class="section-title" id="safety-table">';
    html{end+1} = '        <h2>满足安全系数要求的组合</h2>';
    html{end+1} = '    </div>';
    html{end+1} = '    <div class="container">';
    html{end+1} = sprintf('        <h3>满足接触安全系数 ≥ %.2f 和弯曲安全系数 ≥ %.2f 的参数组合</h3>', contact_safety_factor, bending_safety_factor);
    html{end+1} = '        <div class="table-responsive">';
    html{end+1} = '            <table id="safetyTable" class="display compact stripe hover">';
    html = [html, generateTableHeaders()];
    html = [html, generateTableBody(sf_combinations, true, contact_safety_factor, bending_safety_factor)];
    html{end+1} = '            </table>';
    html{end+1} = '        </div>';

    % 添加说明信息
    html{end+1} = '        <div class="explanation">';
    html{end+1} = '            <h4>参数说明</h4>';
    html{end+1} = '            <ul>';
    html{end+1} = sprintf('                <li><strong>绿色背景的行</strong>为推荐配置 (安全系数高于要求值的%.0f%%)</li>', (1.2-1)*100);
    html{end+1} = sprintf('                <li><strong>安全系数筛选条件:</strong> 接触安全系数≥%.2f，小齿轮和大齿轮弯曲安全系数均≥%.2f</li>', contact_safety_factor, bending_safety_factor);
    html{end+1} = '                <li><strong>默认排序:</strong> 按总质量从低到高排序，可通过点击表头切换排序方式</li>';
    html{end+1} = '            </ul>';
    html{end+1} = '        </div>';
    html{end+1} = '    </div>';
end

% 生成所有参数的表格
html{end+1} = '    <div class="section-title" id="all-table">';
html{end+1} = '        <h2>所有有效参数组合</h2>';
html{end+1} = '    </div>';
html{end+1} = '    <div class="container">';
html{end+1} = '        <h3>所有满足中心距和变位约束的参数组合</h3>';
html{end+1} = '        <div class="table-responsive">';
html{end+1} = '            <table id="allTable" class="display compact stripe hover">';
html = [html, generateTableHeaders()];
html = [html, generateTableBody(gear_combinations, false, contact_safety_factor, bending_safety_factor)];
html{end+1} = '            </table>';
html{end+1} = '        </div>';

% 添加说明信息
html{end+1} = '        <div class="explanation">';
html{end+1} = '            <h4>参数说明</h4>';
html{end+1} = '            <ul>';
html{end+1} = '                <li><strong>绿色背景的行</strong>为推荐配置 (质量较轻且安全系数较高)</li>';
html{end+1} = '                <li><strong>中心距:</strong> 实际中心距误差控制在±0.05%以内，确保与目标值(400 mm)精确匹配</li>';
html{end+1} = '                <li><strong>变位系数:</strong> 精确到小数点后4位，综合变位系数为小齿轮和大齿轮变位系数之和</li>';
html{end+1} = '                <li><strong>安全系数:</strong> 包括接触安全系数和弯曲安全系数，计算方法与优化算法保持一致</li>';
html{end+1} = '                <li><strong>接触安全系数:</strong> 反映齿面接触疲劳强度裕度，防止齿面点蚀</li>';
html{end+1} = '                <li><strong>弯曲安全系数:</strong> 分别显示小齿轮和大齿轮的齿根弯曲疲劳强度裕度，防止齿根断裂</li>';
html{end+1} = '                <li><strong>压力角:</strong> 标准压力角固定为20°，用于计算齿轮几何参数和啮合特性</li>';
html{end+1} = '            </ul>';
html{end+1} = '        </div>';
html{end+1} = '    </div>';

% 添加页脚信息
html{end+1} = '    <div class="footnote">';
html{end+1} = sprintf('        生成时间: %s | 三级减速机齿轮传动系统优化', string(datetime("now", "Format", "yyyy-MM-dd HH:mm:ss")));
html{end+1} = '    </div>';
html{end+1} = '</div>';

% DataTables初始化脚本
html{end+1} = '<script>';
html{end+1} = '    $(document).ready(function() {';

% 满足安全系数的表格初始化
if ~isempty(sf_combinations) && height(sf_combinations) > 0
    html{end+1} = '        var safetyTable = $("#safetyTable").DataTable({';
    html{end+1} = '            "language": {';
    html{end+1} = '                "search": "搜索:",';
    html{end+1} = '                "lengthMenu": "每页显示 _MENU_ 条记录",';
    html{end+1} = '                "zeroRecords": "没有找到匹配的记录",';
    html{end+1} = '                "info": "显示第 _START_ 至 _END_ 条记录，共 _TOTAL_ 条",';
    html{end+1} = '                "infoEmpty": "显示第 0 至 0 条记录，共 0 条",';
    html{end+1} = '                "infoFiltered": "(从 _MAX_ 条记录过滤)",';
    html{end+1} = '                "paginate": {';
    html{end+1} = '                    "first": "首页",';
    html{end+1} = '                    "last": "末页",';
    html{end+1} = '                    "next": "下一页",';
    html{end+1} = '                    "previous": "上一页"';
    html{end+1} = '                }';
    html{end+1} = '            },';
    html{end+1} = '            "pageLength": 10,';
    html{end+1} = '            "lengthMenu": [[10, 25, 50, 100, -1], [10, 25, 50, 100, "全部"]],';
    html{end+1} = '            "order": [[13, "asc"]], // 默认按质量排序';
    html{end+1} = '            "responsive": true,';
    html{end+1} = '            "columnDefs": [';
    html{end+1} = '                { className: "group-geometry", targets: [1, 2, 3, 4, 5, 6, 7] },';
    html{end+1} = '                { className: "group-shift", targets: [8, 9, 10] },';
    html{end+1} = '                { className: "group-result", targets: [11, 12, 13] },';
    html{end+1} = '                { className: "group-mass", targets: [14, 15] },';
    html{end+1} = '                { className: "group-safety", targets: [16, 17, 18] }';
    html{end+1} = '            ]';
    html{end+1} = '        });';
end

% 聚类表格初始化
html{end+1} = '        // 初始化聚类表格';
html{end+1} = '        if($("#clusteredTable").length > 0) {';
html{end+1} = '            var clusteredTable = $("#clusteredTable").DataTable({';
html{end+1} = '                "language": {';
html{end+1} = '                    "search": "搜索:",';
html{end+1} = '                    "lengthMenu": "每页显示 _MENU_ 条记录",';
html{end+1} = '                    "zeroRecords": "没有找到匹配的记录",';
html{end+1} = '                    "info": "显示第 _START_ 至 _END_ 条记录，共 _TOTAL_ 条",';
html{end+1} = '                    "infoEmpty": "显示第 0 至 0 条记录，共 0 条",';
html{end+1} = '                    "infoFiltered": "(从 _MAX_ 条记录过滤)",';
html{end+1} = '                    "paginate": {';
html{end+1} = '                        "first": "首页",';
html{end+1} = '                        "last": "末页",';
html{end+1} = '                        "next": "下一页",';
html{end+1} = '                        "previous": "上一页"';
html{end+1} = '                    }';
html{end+1} = '                },';
html{end+1} = '                "pageLength": 10,';
html{end+1} = '                "lengthMenu": [[10, 25, 50, 100, -1], [10, 25, 50, 100, "全部"]],';
html{end+1} = '                "order": [[13, "asc"]], // 默认按质量排序';
html{end+1} = '                "responsive": true,';
html{end+1} = '                "columnDefs": [';
html{end+1} = '                    { className: "group-geometry", targets: [1, 2, 3, 4, 5, 6, 7] },';
html{end+1} = '                    { className: "group-shift", targets: [8, 9, 10] },';
html{end+1} = '                    { className: "group-result", targets: [11, 12, 13] },';
html{end+1} = '                    { className: "group-mass", targets: [14, 15] },';
html{end+1} = '                    { className: "group-safety", targets: [16, 17, 18] }';
html{end+1} = '                ]';
html{end+1} = '            });';
html{end+1} = '        }';

% 所有参数表格初始化
html{end+1} = '        var allTable = $("#allTable").DataTable({';
html{end+1} = '            "language": {';
html{end+1} = '                "search": "搜索:",';
html{end+1} = '                "lengthMenu": "每页显示 _MENU_ 条记录",';
html{end+1} = '                "zeroRecords": "没有找到匹配的记录",';
html{end+1} = '                "info": "显示第 _START_ 至 _END_ 条记录，共 _TOTAL_ 条",';
html{end+1} = '                "infoEmpty": "显示第 0 至 0 条记录，共 0 条",';
html{end+1} = '                "infoFiltered": "(从 _MAX_ 条记录过滤)",';
html{end+1} = '                "paginate": {';
html{end+1} = '                    "first": "首页",';
html{end+1} = '                    "last": "末页",';
html{end+1} = '                    "next": "下一页",';
html{end+1} = '                    "previous": "上一页"';
html{end+1} = '                }';
html{end+1} = '            },';
html{end+1} = '            "pageLength": 25,';
html{end+1} = '            "lengthMenu": [[10, 25, 50, 100, -1], [10, 25, 50, 100, "全部"]],';
html{end+1} = '            "order": [[13, "asc"]], // 默认按质量排序';
html{end+1} = '            "responsive": true,';
html{end+1} = '            "columnDefs": [';
html{end+1} = '                { className: "group-geometry", targets: [1, 2, 3, 4, 5, 6, 7] },';
html{end+1} = '                { className: "group-shift", targets: [8, 9, 10] },';
html{end+1} = '                { className: "group-result", targets: [11, 12, 13] },';
html{end+1} = '                { className: "group-mass", targets: [14, 15] },';
html{end+1} = '                { className: "group-safety", targets: [16, 17, 18] }';
html{end+1} = '            ]';
html{end+1} = '        });';

% 平滑滚动处理
html{end+1} = '        // 平滑滚动到锚点';
html{end+1} = '        $(".navbar a").on("click", function(e) {';
html{end+1} = '            e.preventDefault();';
html{end+1} = '            var target = $(this).attr("href");';
html{end+1} = '            $("html, body").animate({';
html{end+1} = '                scrollTop: $(target).offset().top - 70';
html{end+1} = '            }, 600);';
html{end+1} = '        });';

html{end+1} = '    });';
html{end+1} = '</script>';
html{end+1} = '</body>';
html{end+1} = '</html>';

% 将cell数组连接为字符串，使用换行符连接
html_output = strjoin(html, '\n');

% 如果需要，可以在这里保存文件（但保持与原有调用方式的兼容性）
% 原有的调用方式是返回HTML内容，由调用者保存文件
end

function html = generateTableHeaders()
    % 生成表头的函数
    html = {};
    html{end+1} = '                <thead>';
    html{end+1} = '                    <tr>';
    html{end+1} = '                        <th>序号</th>';
    html{end+1} = '                        <th class="group-geometry">模数<br>(mm)</th>';
    html{end+1} = '                        <th class="group-geometry">小齿轮<br>齿数</th>';
    html{end+1} = '                        <th class="group-geometry">大齿轮<br>齿数</th>';
    html{end+1} = '                        <th class="group-geometry">传动比</th>';
    html{end+1} = '                        <th class="group-geometry">螺旋角<br>(°)</th>';
    html{end+1} = '                        <th class="group-geometry">压力角<br>(°)</th>';
    html{end+1} = '                        <th class="group-geometry">齿宽系数</th>';
    html{end+1} = '                        <th class="group-shift">小齿轮<br>变位系数</th>';
    html{end+1} = '                        <th class="group-shift">大齿轮<br>变位系数</th>';
    html{end+1} = '                        <th class="group-shift">综合<br>变位系数</th>';
    html{end+1} = '                        <th class="group-result">实际中心距<br>(mm)</th>';
    html{end+1} = '                        <th class="group-result">齿宽<br>(mm)</th>';
    html{end+1} = '                        <th class="group-result">总质量<br>(kg)</th>';
    html{end+1} = '                        <th class="group-mass">小齿轮质量<br>(kg)</th>';
    html{end+1} = '                        <th class="group-mass">大齿轮质量<br>(kg)</th>';
    html{end+1} = '                        <th class="group-safety">接触<br>安全系数</th>';
    html{end+1} = '                        <th class="group-safety">小齿轮<br>弯曲安全系数</th>';
    html{end+1} = '                        <th class="group-safety">大齿轮<br>弯曲安全系数</th>';
    html{end+1} = '                    </tr>';
    html{end+1} = '                </thead>';
    html{end+1} = '                <tbody>';
end

function html = generateTableBody(data, is_safety_factor_table, contact_safety_factor, bending_safety_factor)
    % 生成表格主体的函数
    html = {};

    % 获取表格列名
    col_names = data.Properties.VariableNames;

    % 添加数据行
    for i = 1:height(data)
        row = data(i,:);

        % 检查接触安全系数和弯曲安全系数的列索引
        contact_sf_col = find(strcmp(col_names, '接触安全系数'), 1);
        small_bending_sf_col = find(strcmp(col_names, '小齿轮弯曲安全系数'), 1);
        large_bending_sf_col = find(strcmp(col_names, '大齿轮弯曲安全系数'), 1);

        if isempty(contact_sf_col) || isempty(small_bending_sf_col) || isempty(large_bending_sf_col)
            % 如果找不到列名，使用位置索引
            contact_sf_col = 18;  % 接触安全系数
            small_bending_sf_col = 16;  % 小齿轮弯曲安全系数
            large_bending_sf_col = 17;  % 大齿轮弯曲安全系数
        end

        % 确定是否为推荐行
        is_recommended = false;
        if i <= 10
            if is_safety_factor_table
                % 对于安全系数表格，如果满足较高的安全系数，则推荐
                if row{1, contact_sf_col} >= contact_safety_factor * 1.2 && ...
                   row{1, small_bending_sf_col} >= bending_safety_factor * 1.2 && ...
                   row{1, large_bending_sf_col} >= bending_safety_factor * 1.2
                    is_recommended = true;
                end
            else
                % 对于所有参数表格，如果安全系数较高，则推荐
                if row{1, contact_sf_col} >= 1.5 && row{1, small_bending_sf_col} >= 1.2 && row{1, large_bending_sf_col} >= 1.2
                    is_recommended = true;
                end
            end
        end

        % 设置行样式
        if is_recommended
            html{end+1} = '                    <tr class="highlight-row">';
        else
            html{end+1} = '                    <tr>';
        end

        % 添加序号列
        html{end+1} = sprintf('                        <td>%d</td>', i);

        % 添加数据列 - 直接使用列索引，确保数据正确显示
        % 几何参数组
        html{end+1} = sprintf('                        <td class="group-geometry">%.1f</td>', row{1, 1});  % 模数
        html{end+1} = sprintf('                        <td class="group-geometry">%d</td>', row{1, 2});    % 小齿轮齿数
        html{end+1} = sprintf('                        <td class="group-geometry">%d</td>', row{1, 3});    % 大齿轮齿数
        html{end+1} = sprintf('                        <td class="group-geometry">%.3f</td>', row{1, 4});  % 传动比
        html{end+1} = sprintf('                        <td class="group-geometry">%.1f</td>', row{1, 5});  % 螺旋角
        html{end+1} = sprintf('                        <td class="group-geometry">%.1f</td>', row{1, 6});  % 压力角
        html{end+1} = sprintf('                        <td class="group-geometry">%.3f</td>', row{1, 7});  % 齿宽系数

        % 变位系数组 - 精确到4位小数
        html{end+1} = sprintf('                        <td class="group-shift">%.4f</td>', row{1, 8});     % 小齿轮变位系数
        html{end+1} = sprintf('                        <td class="group-shift">%.4f</td>', row{1, 9});     % 大齿轮变位系数
        html{end+1} = sprintf('                        <td class="group-shift">%.4f</td>', row{1, 10});    % 综合变位系数

        % 结果参数组
        html{end+1} = sprintf('                        <td class="group-result">%.2f</td>', row{1, 11});   % 实际中心距
        html{end+1} = sprintf('                        <td class="group-result">%.2f</td>', row{1, 12});   % 齿宽
        html{end+1} = sprintf('                        <td class="group-result">%.2f</td>', row{1, 13});   % 总质量
        html{end+1} = sprintf('                        <td class="group-mass">%.2f</td>', row{1, 14});   % 小齿轮质量
        html{end+1} = sprintf('                        <td class="group-mass">%.2f</td>', row{1, 15});   % 大齿轮质量

        % 安全系数组 - 接触安全系数和两个弯曲安全系数
        html{end+1} = sprintf('                        <td class="group-safety">%.3f</td>', row{1, contact_sf_col});   % 接触安全系数
        html{end+1} = sprintf('                        <td class="group-safety">%.3f</td>', row{1, small_bending_sf_col});   % 小齿轮弯曲安全系数
        html{end+1} = sprintf('                        <td class="group-safety">%.3f</td>', row{1, large_bending_sf_col});   % 大齿轮弯曲安全系数

        html{end+1} = '                    </tr>';
    end

    html{end+1} = '                </tbody>';
end
