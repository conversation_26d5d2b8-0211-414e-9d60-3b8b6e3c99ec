% Multi-objective optimization test problems
% Contains various benchmark problems for testing multi-objective algorithms

function [f, PF] = TestProblems(name, x)
    switch upper(name)
        case 'ZDT1'
            f = ZDT1(x);
            if nargout > 1
                PF = ZDT1_PF();
            end
        case 'ZDT2'
            f = ZDT2(x);
            if nargout > 1
                PF = ZDT2_PF();
            end
        case 'ZDT3'
            f = ZDT3(x);
            if nargout > 1
                PF = ZDT3_PF();
            end
        case 'ZDT4'
            f = ZDT4(x);
            if nargout > 1
                PF = ZDT4_PF();
            end
        case 'ZDT6'
            f = ZDT6(x);
            if nargout > 1
                PF = ZDT6_PF();
            end
        otherwise
            error('Unknown test problem');
    end
end

% ZDT1 Test Problem
function f = ZDT1(x)
    f = zeros(1, 2);
    dim = length(x);
    g = 1 + 9*sum(x(2:dim))/(dim-1);
    f(1) = x(1);
    f(2) = g*(1-sqrt(x(1)/g));
end

function PF = ZDT1_PF()
    x = linspace(0, 1, 100);
    PF = [x; 1-sqrt(x)]';
end

% ZDT2 Test Problem
function f = ZDT2(x)
    f = zeros(1, 2);
    dim = length(x);
    g = 1 + 9*sum(x(2:dim))/(dim-1);
    f(1) = x(1);
    f(2) = g*(1-(x(1)/g)^2);
end

function PF = ZDT2_PF()
    x = linspace(0, 1, 100);
    PF = [x; 1-x.^2]';
end

% ZDT3 Test Problem
function f = ZDT3(x)
    f = zeros(1, 2);
    dim = length(x);
    g = 1 + 9*sum(x(2:dim))/(dim-1);
    f(1) = x(1);
    f(2) = g*(1-sqrt(x(1)/g)-(x(1)/g)*sin(10*pi*x(1)));
end

function PF = ZDT3_PF()
    % The Pareto front for ZDT3 is discontinuous
    x1 = linspace(0, 0.0830, 20);
    x2 = linspace(0.1822, 0.2577, 20);
    x3 = linspace(0.4093, 0.4538, 20);
    x4 = linspace(0.6183, 0.6525, 20);
    x5 = linspace(0.8233, 0.8518, 20);
    
    x = [x1, x2, x3, x4, x5];
    y = zeros(size(x));
    for i = 1:length(x)
        y(i) = 1 - sqrt(x(i)) - x(i)*sin(10*pi*x(i));
    end
    
    PF = [x; y]';
end

% ZDT4 Test Problem
function f = ZDT4(x)
    f = zeros(1, 2);
    dim = length(x);
    g = 1 + 10*(dim-1) + sum((x(2:dim).^2 - 10*cos(4*pi*x(2:dim))));
    f(1) = x(1);
    f(2) = g*(1-sqrt(x(1)/g));
end

function PF = ZDT4_PF()
    x = linspace(0, 1, 100);
    PF = [x; 1-sqrt(x)]';
end

% ZDT6 Test Problem
function f = ZDT6(x)
    f = zeros(1, 2);
    dim = length(x);
    g = 1 + 9*(sum(x(2:dim))/(dim-1))^0.25;
    f(1) = 1 - exp(-4*x(1))*sin(6*pi*x(1))^6;
    f(2) = g*(1-(f(1)/g)^2);
end

function PF = ZDT6_PF()
    x = linspace(0.28, 1, 100);
    f1 = 1 - exp(-4*x).*sin(6*pi*x).^6;
    f2 = 1 - f1.^2;
    PF = [f1; f2]';
end 