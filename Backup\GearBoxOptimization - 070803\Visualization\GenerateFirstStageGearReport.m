function GenerateFirstStageGearReport(center_distance, input_power, input_speed, service_life, contact_safety_factor, bending_safety_factor, gear_material)
% GenerateFirstStageGearReport 生成一级平行轴系齿轮参数组合报告并保存为HTML
%
%   输入参数:
%   - center_distance: 目标中心距 (mm)
%   - input_power: 输入功率 (kW)
%   - input_speed: 输入转速 (rpm)
%   - service_life: 设计寿命 (h)
%   - contact_safety_factor: 接触安全系数
%   - bending_safety_factor: 弯曲安全系数
%   - gear_material: 齿轮材料参数
%
%   输出:
%   - 无返回值，直接生成HTML报告文件

% 创建结果文件夹
results_dir = 'Results';
if ~exist(results_dir, 'dir')
    mkdir(results_dir);
    fprintf('创建结果文件夹: %s\n', results_dir);
end

% 螺旋角范围（度）- 按照原代码要求为8°-13°
helix_angle_range = [8, 13];

% 生成有效的齿轮组合
fprintf('开始生成一级平行轴系有效参数组合...\n');
fprintf('目标中心距: %.2f mm (误差范围：±0.01%%)\n', center_distance);
fprintf('输入功率: %.2f kW\n', input_power);
fprintf('输入转速: %.2f rpm\n', input_speed);
fprintf('螺旋角范围: %.1f°-%.1f°\n', helix_angle_range(1), helix_angle_range(2));
fprintf('变位系数范围: 主动轮(0.3-0.8), 从动轮(0-0.5), 总和(0.4-1.0)\n');

% 调用生成函数
gear_combinations = GenerateValidFirstStageGears(center_distance, input_power, input_speed, service_life, contact_safety_factor, bending_safety_factor, gear_material, helix_angle_range);

% 检查是否找到有效组合
if isempty(gear_combinations) || height(gear_combinations) == 0
    fprintf('警告：未找到满足约束的有效齿轮组合\n');
else
    % 这里不再重复输出组合数量，因为GenerateValidFirstStageGears已经输出了
    
    % 显示前5个组合（如果有）
    rows_to_show = min(5, height(gear_combinations));
    fprintf('\n前 %d 个组合 (按质量排序):\n', rows_to_show);
    fprintf('%-6s %-8s %-8s %-8s %-12s %-10s %-10s %-10s\n', ...
            '模数', '小齿数', '大齿数', '螺旋角', '中心距', '齿宽', '质量', '安全系数(接触/弯曲)');
    
    for i = 1:rows_to_show
        fprintf('%-6.1f %-8d %-8d %-8.1f %-12.2f %-10.2f %-10.2f %-5.2f/%-5.2f\n', ...
                gear_combinations{i, 1}, ... % 模数
                gear_combinations{i, 2}, ... % 小齿轮齿数
                gear_combinations{i, 3}, ... % 大齿轮齿数
                gear_combinations{i, 5}, ... % 螺旋角
                gear_combinations{i, 11}, ... % 实际中心距
                gear_combinations{i, 12}, ... % 齿宽
                gear_combinations{i, 13}, ... % 估计质量
                gear_combinations{i, 15}, gear_combinations{i, 14}); % 安全系数（接触/弯曲）
    end
end

% 生成HTML报告
fprintf('\n生成HTML报告...\n');
html_content = GenerateFirstStageHTML(gear_combinations);

% 保存HTML文件
html_filename = fullfile(results_dir, '一级平行轴系有效参数组合.html');
fid = fopen(html_filename, 'w', 'n', 'utf-8');
if fid ~= -1
    fwrite(fid, html_content, 'char');
    fclose(fid);
    fprintf('HTML报告已保存至: %s\n', html_filename);
    
    % 尝试在浏览器中打开报告
    try
        web(html_filename, '-browser');
        fprintf('HTML报告已在浏览器中打开\n');
    catch
        fprintf('无法自动打开HTML报告，请手动打开文件\n');
    end
else
    fprintf('错误：无法创建HTML报告文件\n');
end

end 