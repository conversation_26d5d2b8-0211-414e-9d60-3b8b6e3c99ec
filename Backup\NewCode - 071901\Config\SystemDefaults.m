function defaults = SystemDefaults()
% SystemDefaults 系统默认参数配置
% 定义齿轮传动系统的默认工作参数和设计要求
%
% 输出:
%   defaults - 系统默认参数结构体

%% 1. 基本工作参数（用于用户输入的默认值）
defaults.operating_conditions = struct();
defaults.operating_conditions.input_power = 1170.24;        % 输入功率 (kW)
defaults.operating_conditions.input_speed = 1490;           % 输入转速 (rpm)
defaults.operating_conditions.output_speed = 18.63;         % 输出转速 (rpm)
defaults.operating_conditions.input_torque = 7500;          % 输入转矩 (Nm)
defaults.operating_conditions.service_life = 50000;         % 设计寿命 (h)

%% 2. 安全系数要求
defaults.safety_factors = struct();
defaults.safety_factors.contact_safety_factor = 1.2;        % 接触安全系数要求
defaults.safety_factors.bending_safety_factor = 1.2;        % 弯曲安全系数要求

%% 3. 几何约束参数
defaults.geometric_constraints = struct();
defaults.geometric_constraints.center_distance = 400;       % 一级中心距约束 (mm)
defaults.geometric_constraints.max_outer_diameter = 1442;   % 三级内齿圈外径约束 (mm)

%% 4. 齿轮精度等级
defaults.quality = struct();
defaults.quality.grade = 6;                                 % 齿轮精度等级 (ISO 1328)

%% 5. 计算参数
defaults.calculation = struct();
defaults.calculation.pressure_angle = 20;                   % 标准压力角 (度)

end
