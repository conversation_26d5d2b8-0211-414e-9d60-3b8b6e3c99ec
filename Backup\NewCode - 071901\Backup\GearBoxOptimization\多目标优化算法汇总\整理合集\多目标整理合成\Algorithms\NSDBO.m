function [Pop_X, Pop_F] = NSDBO(CostFunction, nVar, VarMin, VarMax, MaxIt, nPop)
% NSDBO: 多目标非支配排序蜣螂优化算法 (Non-dominated Sorting Dung Beetle Optimizer)
%
% 输入参数:
%   CostFunction: 目标函数句柄
%   nVar: 决策变量数量
%   VarMin, VarMax: 决策变量的上下界
%   MaxIt: 最大迭代次数
%   nPop: 种群大小
%
% 输出参数:
%   Pop_X: 非支配解的决策变量
%   Pop_F: 非支配解的目标函数值
%
% 参考文献:
%   <PERSON><PERSON> and <PERSON><PERSON>, "Spotted hyena optimizer: A novel bio-inspired 
%   based metaheuristic technique for engineering applications", 
%   Advances in Engineering Software, vol. 114, pp. 48-70, 2017.

    % 默认参数值
    if nargin < 6
        nPop = 100;
    end
    if nargin < 5
        MaxIt = 100;
    end
    if nargin < 4
        VarMax = 1;
    end
    if nargin < 3
        VarMin = 0;
    end
    if nargin < 2
        nVar = 5;
    end
    
    % 算法参数
    nArchive = 100;    % 存档大小
    
    % 获取目标函数数量
    sample = unifrnd(VarMin, VarMax, [1, nVar]);
    obj_values = CostFunction(sample);
    nObj = numel(obj_values);
    
    % 初始化种群
    K = nVar + nObj;  % 位置 + 目标函数值
    POS = zeros(nPop, K + 1);  % 最后一列用于标记非支配排序的等级
    
    % 随机初始化位置
    for i = 1:nPop
        POS(i, 1:nVar) = unifrnd(VarMin, VarMax, [1, nVar]);
        POS(i, nVar+1:K) = CostFunction(POS(i, 1:nVar));
    end
    
    % 初始化辅助位置数组
    POS_ad = zeros(nPop, K);
    
    % 非支配排序并初始化存档
    DOMINATED = checkDomination(POS(:, nVar+1:nVar+nObj));
    ERP = POS(~DOMINATED, 1:K+1);  % 存档，用于保存非支配解
    
    % 主循环
    for iter = 1:MaxIt
        % 初始化不同群体的大小
        pNum1 = floor(nPop * 0.2);
        pNum2 = floor(nPop * 0.4);
        pNum3 = floor(nPop * 0.63);
        
        % 新位置数组
        newPOS = zeros(nPop, K);
        
        % 存档大小
        leng = size(ERP, 1);
        
        % 第一组蜣螂：滚动粪球
        r2 = rand;
        for i = 1:pNum1
            if r2 < 0.9
                r1 = rand;
                a = rand;
                if a > 0.1
                    a = 1;
                else
                    a = -1;
                end
                % 随机选择一个"差"解
                worse = ERP(randi(leng), 1:nVar);
                % 公式(1)：与差解的交互更新
                newPOS(i, 1:nVar) = POS(i, 1:nVar) + 0.3 * abs(POS(i, 1:nVar) - worse) + a * 0.1 * POS_ad(i, 1:nVar);
            else
                % 随机方向变化
                aaa = randi(180);
                if aaa == 0 || aaa == 90 || aaa == 180
                    newPOS(i, 1:nVar) = POS(i, 1:nVar);
                else
                    theta = aaa * pi / 180;
                    % 公式(2)：使用三角函数进行方向变化
                    newPOS(i, 1:nVar) = POS(i, 1:nVar) + tan(theta) .* abs(POS(i, 1:nVar) - POS_ad(i, 1:nVar));
                end
            end
        end
        
        % 搜索阶段的控制参数
        R = 1 - iter / MaxIt;
        
        % 从存档中随机选择一个解作为最佳解
        bestXX = ERP(randi(leng), 1:nVar);
        
        % 公式(3)：基于R因子的搜索范围控制
        Xnew1 = bestXX .* (1 - R);
        Xnew2 = bestXX .* (1 + R);
        
        % 边界检查
        Xnew1 = bound(Xnew1, VarMax, VarMin);
        Xnew2 = bound(Xnew2, VarMax, VarMin);
        
        % 再选择一个最佳解
        bestX = ERP(randi(leng), 1:nVar);
        
        % 公式(5)：另一种搜索范围控制
        Xnew11 = bestX .* (1 - R);
        Xnew22 = bestX .* (1 + R);
        
        % 边界检查
        Xnew11 = bound(Xnew11, VarMax, VarMin);
        Xnew22 = bound(Xnew22, VarMax, VarMin);
        
        % 第二组蜣螂：基于公式(4)的位置更新
        for i = (pNum1 + 1):pNum2
            % 公式(4)：结合最佳解和搜索范围
            newPOS(i, 1:nVar) = bestXX + ((rand(1, nVar)) .* (POS(i, 1:nVar) - Xnew1) + (rand(1, nVar)) .* (POS(i, 1:nVar) - Xnew2));
        end
        
        % 第三组蜣螂：基于公式(6)的位置更新
        for i = (pNum2 + 1):pNum3
            % 公式(6)：使用高斯随机数的位置更新
            newPOS(i, 1:nVar) = POS(i, 1:nVar) + ((randn()) .* (POS(i, 1:nVar) - Xnew11) + ((rand(1, nVar)) .* (POS(i, 1:nVar) - Xnew22)));
        end
        
        % 第四组蜣螂：基于公式(7)的位置更新
        for i = (pNum3 + 1):nPop
            % 公式(7)：基于两个最佳解的位置更新
            newPOS(i, 1:nVar) = bestX + randn(1, nVar) .* ((abs((POS(i, 1:nVar) - bestXX))) + (abs((POS(i, 1:nVar) - bestX)))) ./ 2;
        end
        
        % 评估所有新位置的目标函数值并更新
        for i = 1:nPop
            % 边界检查
            newPOS(i, 1:nVar) = bound(newPOS(i, 1:nVar), VarMax, VarMin);
            
            % 计算新位置的目标函数值
            newPOS(i, nVar+1:K) = CostFunction(newPOS(i, 1:nVar));
            
            % 支配关系比较
            dom_less = 0;
            dom_equal = 0;
            dom_more = 0;
            
            % 比较新位置和当前位置的目标函数值
            for k = 1:nObj
                if newPOS(i, nVar+k) < POS(i, nVar+k)
                    dom_less = dom_less + 1;
                elseif newPOS(i, nVar+k) == POS(i, nVar+k)
                    dom_equal = dom_equal + 1;
                else
                    dom_more = dom_more + 1;
                end
            end
            
            % 如果新位置支配当前位置
            if dom_more == 0 && dom_equal ~= nObj
                % 保存当前位置到辅助数组
                POS_ad(i, 1:K) = POS(i, 1:K);
                % 更新当前位置为新位置
                POS(i, 1:K) = newPOS(i, 1:K);
            else
                % 保存新位置到辅助数组
                POS_ad(i, 1:K) = newPOS(i, 1:K);
            end
        end
        
        % 合并当前位置和辅助位置
        pos_com = [POS(:, 1:K); POS_ad];
        
        % 非支配排序
        intermediate_pos = non_domination_sort_mod(pos_com, nObj, nVar);
        
        % 选择下一代种群
        POS = replace_chromosome(intermediate_pos, nObj, nVar, nArchive);
        
        % 更新存档
        DOMINATED = checkDomination(POS(:, nVar+1:nVar+nObj));
        ERP = POS(~DOMINATED, 1:K+1);
    end
    
    % 返回最终的非支配解
    Pop_X = ERP(:, 1:nVar);
    Pop_F = ERP(:, nVar+1:nVar+nObj);
end

% 边界处理函数
function a = bound(a, ub, lb)
    % 如果标量边界，转换为向量
    if isscalar(ub)
        ub = ones(size(a)) * ub;
    end
    if isscalar(lb)
        lb = ones(size(a)) * lb;
    end
    
    % 边界检查
    a(a > ub) = ub(a > ub);
    a(a < lb) = lb(a < lb);
end

% 支配关系检查
function d = dominates(x, y)
    % x支配y，如果x的所有目标都不劣于y，且至少有一个目标严格优于y
    d = all(x <= y, 2) & any(x < y, 2);
end

% 非支配解检查
function dom_vector = checkDomination(fitness)
    Np = size(fitness, 1);
    dom_vector = zeros(Np, 1);
    all_perm = nchoosek(1:Np, 2);
    all_perm = [all_perm; [all_perm(:, 2) all_perm(:, 1)]];
    
    d = dominates(fitness(all_perm(:, 1), :), fitness(all_perm(:, 2), :));
    dominated_particles = unique(all_perm(d == 1, 2));
    dom_vector(dominated_particles) = 1;
end

% 非支配排序函数
function sorted_chromosome = non_domination_sort_mod(chromosome, M, V)
    [N, ~] = size(chromosome);
    
    % 初始化排序结果
    sorted_chromosome = chromosome;
    
    % 初始化等级
    for i = 1:N
        sorted_chromosome(i, V+M+1) = 0;
    end
    
    % 计算每个个体的支配数和被支配个体集合
    dominate_count = zeros(N, 1);
    dominated_by = cell(N, 1);
    
    for i = 1:N
        for j = i+1:N
            if dominates_check(sorted_chromosome(i, V+1:V+M), sorted_chromosome(j, V+1:V+M))
                dominated_by{i} = [dominated_by{i}, j];
                dominate_count(j) = dominate_count(j) + 1;
            elseif dominates_check(sorted_chromosome(j, V+1:V+M), sorted_chromosome(i, V+1:V+M))
                dominated_by{j} = [dominated_by{j}, i];
                dominate_count(i) = dominate_count(i) + 1;
            end
        end
    end
    
    % 等级分配
    current_front = find(dominate_count == 0)';
    front_num = 1;
    
    while ~isempty(current_front)
        for i = 1:length(current_front)
            sorted_chromosome(current_front(i), V+M+1) = front_num;
            for j = 1:length(dominated_by{current_front(i)})
                dominate_count(dominated_by{current_front(i)}(j)) = dominate_count(dominated_by{current_front(i)}(j)) - 1;
            end
        end
        
        front_num = front_num + 1;
        current_front = find(dominate_count == 0 & sorted_chromosome(:, V+M+1) == 0)';
    end
end

% 支配检查辅助函数
function result = dominates_check(x, y)
    result = all(x <= y) && any(x < y);
end

% 替换染色体函数
function new_chromosome = replace_chromosome(sorted_chromosome, M, V, N)
    [~, idx] = sortrows(sorted_chromosome(:, V+M+1));
    sorted_chromosome = sorted_chromosome(idx, :);
    
    max_rank = max(sorted_chromosome(:, V+M+1));
    new_chromosome = [];
    
    rank = 1;
    while size(new_chromosome, 1) < N && rank <= max_rank
        current_front = sorted_chromosome(sorted_chromosome(:, V+M+1) == rank, :);
        
        if size(new_chromosome, 1) + size(current_front, 1) <= N
            new_chromosome = [new_chromosome; current_front];
        else
            % 剩余可以添加的个体数量
            remaining = N - size(new_chromosome, 1);
            
            % 计算拥挤度
            crowding_distance = compute_crowding_distance(current_front, M, V);
            
            % 根据拥挤度排序
            [~, idx] = sort(crowding_distance, 'descend');
            current_front = current_front(idx, :);
            
            % 添加拥挤度最大的个体
            new_chromosome = [new_chromosome; current_front(1:remaining, :)];
        end
        
        rank = rank + 1;
    end
end

% 计算拥挤度
function crowding_distance = compute_crowding_distance(front, M, V)
    [N, ~] = size(front);
    crowding_distance = zeros(N, 1);
    
    for m = 1:M
        [~, idx] = sort(front(:, V+m));
        
        % 边界点拥挤度设为无穷大
        crowding_distance(idx(1)) = inf;
        crowding_distance(idx(N)) = inf;
        
        f_max = max(front(:, V+m));
        f_min = min(front(:, V+m));
        
        if f_max > f_min
            for i = 2:N-1
                crowding_distance(idx(i)) = crowding_distance(idx(i)) + ...
                    (front(idx(i+1), V+m) - front(idx(i-1), V+m)) / (f_max - f_min);
            end
        end
    end
end 