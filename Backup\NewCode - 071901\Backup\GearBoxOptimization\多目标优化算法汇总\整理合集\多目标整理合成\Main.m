%% 多目标优化算法比较主程序
% 本程序用于比较多种多目标优化算法在测试问题上的性能

%% 清除环境
clc;
clear;
close all;

%% 添加路径
addpath(genpath('Algorithms'));    % 算法文件夹
addpath(genpath('Metrics'));       % 评价指标文件夹
addpath(genpath('TestFunctions')); % 测试函数文件夹
addpath(genpath('Visualization')); % 可视化文件夹

%% 参数设置
% 问题参数
nVar = 10;                % 决策变量数量
VarMin = 0;               % 变量下界
VarMax = 1;               % 变量上界
MaxIt = 100;              % 最大迭代次数
nPop = 100;               % 种群大小
nRuns = 3;                % 独立运行次数

% 选择要比较的算法
algorithms = {
    'NSGA2'  % 非支配排序遗传算法II
    'NSGA3'  % 非支配排序遗传算法III
    'MODA'   % 多目标蜻蜓算法
    'MODE'   % 多目标差分进化算法
    'MOEAD'  % 基于分解的多目标进化算法
    'MOGOA'  % 多目标蝗虫优化算法
    'MOGWO'  % 多目标灰狼优化算法
    'MOMVO'  % 多目标多元宇宙优化算法
    'MOPSO'  % 多目标粒子群优化算法
    'MSSA'   % 多目标樽海鞘群算法
    'NSDBO'  % 多目标非支配排序蜣螂优化算法
    'PESA2'  % 基于范围选择的Pareto进化算法II
    'SPEA2'  % 强度Pareto进化算法2
    'NSWOA'  % 多目标非支配排序鲸鱼优化算法
};

% 可选择特定算法运行，例如：
selected_algorithms = {'NSGA2', 'MOPSO', 'MOGWO'};
% 如果要运行所有算法，注释上面一行并取消下面一行的注释：
% selected_algorithms = algorithms;

% 选择测试问题
test_problems = {'ZDT1', 'ZDT2', 'ZDT3'};
selected_problems = {'ZDT1'};  % 可以选择特定问题
% selected_problems = test_problems;  % 运行所有测试问题

% 评价指标
metrics = {'GD', 'IGD', 'Spacing', 'Spread', 'HV'};

%% 结果存储
results = struct();

% 初始化结果结构
for p = 1:length(selected_problems)
    problem = selected_problems{p};
    results.(problem) = struct();
    
    for a = 1:length(selected_algorithms)
        alg = selected_algorithms{a};
        results.(problem).(alg) = struct();
        
        for m = 1:length(metrics)
            metric = metrics{m};
            results.(problem).(alg).(metric) = zeros(nRuns, 1);
        end
        
        results.(problem).(alg).Time = zeros(nRuns, 1);
        results.(problem).(alg).BestF = cell(nRuns, 1);
        results.(problem).(alg).BestX = cell(nRuns, 1);
    end
end

%% 运行算法
for p = 1:length(selected_problems)
    problem = selected_problems{p};
    fprintf('问题: %s\n', problem);
    
    % 创建目标函数句柄
    CostFunction = @(x) TestProblems(problem, x);
    
    % 获取真实的Pareto前沿作为参考
    [~, PF] = TestProblems(problem, []);
    
    for a = 1:length(selected_algorithms)
        alg = selected_algorithms{a};
        fprintf('  算法: %s\n', alg);
        
        for run = 1:nRuns
            fprintf('    运行: %d/%d\n', run, nRuns);
            
            % 运行算法
            tic;
            switch alg
                case 'NSGA2'
                    [pop, F1] = NSGA2(CostFunction, nVar, VarMin, VarMax, MaxIt, nPop);
                    % 提取解决方案和目标值
                    X = zeros(length(F1), nVar);
                    F = zeros(length(F1), size(F1(1).Cost, 2));
                    for i = 1:length(F1)
                        X(i, :) = F1(i).Position;
                        F(i, :) = F1(i).Cost;
                    end
                
                case 'MODA'
                    [X, F] = MODA(CostFunction, nVar, VarMin, VarMax, MaxIt, nPop);
                
                case 'NSGA3'
                    [pop, F1] = NSGA3(CostFunction, nVar, VarMin, VarMax, MaxIt, nPop);
                    % 提取解决方案和目标值
                    X = zeros(length(F1), nVar);
                    F = zeros(length(F1), size(F1(1).Cost, 2));
                    for i = 1:length(F1)
                        X(i, :) = F1(i).Position;
                        F(i, :) = F1(i).Cost;
                    end
                
                case 'MODE'
                    OUT = MODE(CostFunction, nVar, VarMin, VarMax, MaxIt, nPop);
                    X = OUT.PSet;
                    F = OUT.PFront;
                
                case 'MOEAD'
                    EP = MOEAD(CostFunction, nVar, VarMin, VarMax, MaxIt, nPop);
                    X = zeros(length(EP), nVar);
                    F = zeros(length(EP), size(EP(1).Cost, 2));
                    for i = 1:length(EP)
                        X(i, :) = EP(i).Position;
                        F(i, :) = EP(i).Cost;
                    end
                
                case 'MOGOA'
                    [X, F] = MOGOA(CostFunction, nVar, VarMin, VarMax, MaxIt, nPop);
                
                case 'MOGWO'
                    [X, F] = MOGWO(CostFunction, nVar, VarMin, VarMax, MaxIt, nPop);
                
                case 'MOMVO'
                    [X, F] = MOMVO(CostFunction, nVar, VarMin, VarMax, MaxIt, nPop);
                
                case 'MOPSO'
                    [X, F] = MOPSO(CostFunction, nVar, VarMin, VarMax, MaxIt, nPop);
                    
                case 'MSSA'
                    [X, F] = MSSA(CostFunction, nVar, VarMin, VarMax, MaxIt, nPop);
                    
                case 'NSDBO'
                    [X, F] = NSDBO(CostFunction, nVar, VarMin, VarMax, MaxIt, nPop);
                    
                case 'PESA2'
                    [X, F] = PESA2(CostFunction, nVar, VarMin, VarMax, MaxIt, nPop);
                    
                case 'SPEA2'
                    [X, F] = SPEA2(CostFunction, nVar, VarMin, VarMax, MaxIt, nPop);
                    
                case 'NSWOA'
                    [X, F] = NSWOA(CostFunction, nVar, VarMin, VarMax, MaxIt, nPop);
                
                otherwise
                    error(['未知算法: ' alg]);
            end
            time = toc;
            
            % 存储结果
            results.(problem).(alg).Time(run) = time;
            results.(problem).(alg).BestF{run} = F;
            results.(problem).(alg).BestX{run} = X;
            
            % 计算评价指标
            results.(problem).(alg).GD(run) = GD(F, PF);
            results.(problem).(alg).IGD(run) = IGD(F, PF);
            results.(problem).(alg).Spacing(run) = Spacing(F, PF);
            results.(problem).(alg).Spread(run) = Spread(F, PF);
            results.(problem).(alg).HV(run) = HV(F, PF);
        end
    end
end

%% 展示结果
disp('结果总结:');

for p = 1:length(selected_problems)
    problem = selected_problems{p};
    disp(['问题: ' problem]);
    
    % 准备表格数据和可视化数据
    tableData = cell(length(selected_algorithms), length(metrics)+1);
    rowNames = cell(length(selected_algorithms), 1);
    metrics_data = zeros(length(selected_algorithms), length(metrics));
    algorithm_fronts = cell(length(selected_algorithms), 1);
    
    for a = 1:length(selected_algorithms)
        alg = selected_algorithms{a};
        rowNames{a} = alg;
        
        % 平均执行时间
        tableData{a, 1} = mean(results.(problem).(alg).Time);
        
        % 平均评价指标
        for m = 1:length(metrics)
            metric = metrics{m};
            metric_value = mean(results.(problem).(alg).(metric));
            tableData{a, m+1} = metric_value;
            metrics_data(a, m) = metric_value;
        end
        
        % 获取最后一次运行的Pareto前沿
        algorithm_fronts{a} = results.(problem).(alg).BestF{end};
    end
    
    % 创建并显示表格
    colNames = ['Time', metrics];
    T = cell2table(tableData, 'RowNames', rowNames, 'VariableNames', colNames);
    disp(T);
    
    % 使用可视化模块绘制Pareto前沿
    PlotParetoFront(PF, algorithm_fronts, selected_algorithms, problem, 1);
    
    % 使用可视化模块绘制指标对比图 (排除Time列)
    PlotMetricsComparison(metrics_data, selected_algorithms, metrics, problem, 2);
    
    % 保存图形
    saveas(1, [problem '_ParetoFront.fig']);
    saveas(1, [problem '_ParetoFront.png']);
    saveas(2, [problem '_Metrics.fig']);
    saveas(2, [problem '_Metrics.png']);
end

% 保存结果
save('MOO_Results.mat', 'results');
disp('结果已保存到MOO_Results.mat');

% 完成
disp('算法比较完成!'); 