function [objectives, constraints, is_valid] = GearOptObjectives(x, input_power, input_speed, output_speed, ...
                                      service_life, contact_safety_factor, bending_safety_factor, gear_material, ...
                                      planets_count_2, planets_count_3, gear_materials, quality_grade, ...
                                      pressure_angle, helix_angle_1, helix_angle_2, helix_angle_3, profile_shifts, pressure_angle_3, center_distance, k_h1)
% GEAROPTOBJECTIVES 计算三级减速机齿轮系统的多目标优化函数值
%   目标：1. 系统轻量化（总质量最小）
%         2. 齿轮弯曲应力安全系数最大（最小值）
%         3. 齿轮接触应力安全系数最大（最小值）
%
% 输入:
%   x - 设计变量向量 [m1, z1, z2, mn2, zs2, zp2, k_h2, mn3, zs3, zp3, k_h3]
%   input_power - 输入功率(kW)
%   input_speed - 输入转速(rpm)
%   output_speed - 输出转速(rpm)
%   service_life - 设计寿命(h)
%   contact_safety_factor - 接触安全系数
%   bending_safety_factor - 弯曲安全系数
%   gear_material - 齿轮材料属性结构体(兼容旧代码)
%   planets_count_2 - 二级行星齿轮数量
%   planets_count_3 - 三级行星齿轮数量
%   gear_materials - 不同齿轮的材料属性结构体(新增)
%   quality_grade - 齿轮精度等级 (ISO 1328), 5-12 (新增)
%   pressure_angle - 一二级压力角 (度) (新增)
%   helix_angle_1 - 一级传动螺旋角 (度) (新增)
%   helix_angle_2 - 二级行星系螺旋角 (度) (新增)
%   helix_angle_3 - 三级行星系螺旋角 (度) (新增)
%   profile_shifts - 变位系数 [x1, x2, xs2, xp2, xs3, xp3] (新增)
%   pressure_angle_3 - 三级行星系压力角 (度) (新增)
%   center_distance - 一级平行轴齿轮系中心距 (mm) (新增)
%   k_h1 - 一级齿宽系数 (新增)
%
% 输出:
%   objectives - [总质量, -最小弯曲安全系数, -最小接触安全系数]
%   constraints - 约束条件违反值(正值表示违反约束)
%   is_valid - 结果是否有效的标志(0或1)

% 默认为有效
is_valid = true;
constraints = 0;

% 设置默认精度等级
if nargin < 11 || isempty(quality_grade)
    quality_grade = 6;  % 默认6级精度
end

% 设置默认压力角
if nargin < 12 || isempty(pressure_angle)
    pressure_angle = 20;  % 默认压力角20度
end

% 设置默认螺旋角
if nargin < 13 || isempty(helix_angle_1)
    helix_angle_1 = 0;  % 默认直齿轮
end

if nargin < 14 || isempty(helix_angle_2)
    helix_angle_2 = 0;  % 默认直齿轮
end

if nargin < 15 || isempty(helix_angle_3)
    helix_angle_3 = 0;  % 默认直齿轮
end

% 设置默认变位系数
if nargin < 16 || isempty(profile_shifts)
    profile_shifts = zeros(1, 6);  % 默认无变位
end

% 设置默认三级压力角
if nargin < 17 || isempty(pressure_angle_3)
    pressure_angle_3 = pressure_angle;  % 默认与一二级相同
end

% 设置默认一级齿轮中心距
if nargin < 18 || isempty(center_distance)
    center_distance = 400;  % 默认中心距为400mm
end

% 设置默认一级齿宽系数
if nargin < 19 || isempty(k_h1)
    k_h1 = 10;  % 默认一级齿宽系数为10
end

% 提取变位系数
x1 = profile_shifts(1);   % 一级小齿轮变位系数
x2 = profile_shifts(2);   % 一级大齿轮变位系数
xs2 = profile_shifts(3);  % 二级太阳轮变位系数
xp2 = profile_shifts(4);  % 二级行星轮变位系数
xs3 = profile_shifts(5);  % 三级太阳轮变位系数
xp3 = profile_shifts(6);  % 三级行星轮变位系数

% 检查是否存在传动比约束
try
    ratio_constraints_file = 'ratio_constraints.mat';
    if exist(ratio_constraints_file, 'file')
        load(ratio_constraints_file, 'ratio_constraints');
    else
        ratio_constraints.enabled = false;
    end
catch
    ratio_constraints.enabled = false;
end

% 验证输入变量
if any(isnan(x)) || any(isinf(x))
    is_valid = false;
    objectives = [1000, -0.1, -0.1];  % 使用合理的默认值，便于算法收敛
    constraints = 100;
    return;
end

% 将设计变量四舍五入到合理的值
x_rounded = x;
x_rounded(2:3) = round(x(2:3));    % 一级齿轮齿数取整
x_rounded(5:6) = round(x(5:6));    % 二级行星系齿数取整
x_rounded(9:10) = round(x(9:10));  % 三级行星系齿数取整

% 提取设计变量
m1 = x_rounded(1);       % 一级模数(mm)
z1 = x_rounded(2);       % 一级小齿轮齿数
z2 = x_rounded(3);       % 一级大齿轮齿数
mn2 = x_rounded(4);      % 二级模数(mm)
zs2 = x_rounded(5);      % 二级太阳轮齿数
zp2 = x_rounded(6);      % 二级行星轮齿数
k_h2 = x_rounded(7);     % 二级齿宽系数
mn3 = x_rounded(8);      % 三级模数(mm)
zs3 = x_rounded(9);      % 三级太阳轮齿数
zp3 = x_rounded(10);     % 三级行星轮齿数
k_h3 = x_rounded(11);    % 三级齿宽系数

% 确保齿宽系数在合理范围内
% 二级和三级齿宽系数在0.6-1.0之间，避免异常值
k_h2 = max(0.6, min(1.0, k_h2));
k_h3 = max(0.6, min(1.0, k_h3));

% 一级齿宽系数在0.28-0.4之间
if exist('k_h1', 'var')
    k_h1 = max(0.28, min(0.4, k_h1));
end

% 计算内齿圈变位系数
% 内齿圈变位系数可为负值，但与啮合齿轮的变位系数和满足特定关系
% 对于行星轮系，通常要满足：xs + xp + xr = 0（确保中心距不变）
% 由于我们已经约束了xs + xp >= 0.6 且 <= 1.1，所以xr在-1.1到-0.6之间
xr2 = -(xs2 + xp2);  % 二级内齿圈变位系数
xr3 = -(xs3 + xp3);  % 三级内齿圈变位系数

% 确保内齿圈变位系数不小于-0.8，符合工程实践
xr2 = max(-0.8, xr2);
xr3 = max(-0.8, xr3);

% 检查内齿圈变位系数是否在合理范围内
if xr2 < -0.8 || xr3 < -0.8
    is_valid = false;
    objectives = [700, -0.6, -0.6];
    constraints = 30;
    return;
end

% 基本验证：齿数必须大于等于最小值
if z1 < 17 || z2 < 17 || zs2 < 17 || zp2 < 17 || zs3 < 17 || zp3 < 17
    is_valid = false;
    objectives = [800, -0.5, -0.5];  % 使用合理的默认值，便于算法收敛
    constraints = 50;
    return;
end

% 基本验证：大齿轮和内齿圈齿数不超过上限
zr2 = zs2 + 2*zp2;  % 二级齿圈齿数
zr3 = zs3 + 2*zp3;  % 三级齿圈齿数
if z2 > 100 || zr2 > 120 || zr3 > 120
    is_valid = false;
    objectives = [800, -0.5, -0.5];  % 使用合理的默认值，便于算法收敛
    constraints = 50;
    return;
end

% ===== 【新增】提前计算一级平行轴齿轮传动的实际中心距并检查约束 =====
% 计算实际中心距
if helix_angle_1 == 0
    % 直齿轮情况
    inv_alpha = tan(pressure_angle * pi / 180) - (pressure_angle * pi / 180); % 渐开线函数
    inv_alpha_w = inv_alpha + 2 * (x1 + x2) * tan(pressure_angle * pi / 180) / (z1 + z2);
    alpha_w = fzero(@(a) tan(a) - a - inv_alpha_w, pressure_angle * pi / 180);
    actual_center_distance = m1 * (z1 + z2) * cos(pressure_angle * pi / 180) / (2 * cos(alpha_w));
else
    % 斜齿轮情况
    mt1 = m1 / cos(helix_angle_1 * pi / 180);  % 端面模数
    inv_alpha_t = tan(pressure_angle * pi / 180) - (pressure_angle * pi / 180);
    inv_alpha_tw = inv_alpha_t + 2 * (x1 + x2) * tan(pressure_angle * pi / 180) / (z1 + z2);
    alpha_tw = fzero(@(a) tan(a) - a - inv_alpha_tw, pressure_angle * pi / 180);
    actual_center_distance = mt1 * (z1 + z2) * cos(pressure_angle * pi / 180) / (2 * cos(alpha_tw));
end

% 检查实际中心距是否满足要求，允许±0.01%的误差 - 提高优先级
center_distance_tolerance = 0.0001;  % 0.01%的误差容忍度
center_distance_error = abs(actual_center_distance - center_distance) / center_distance;

if center_distance_error > center_distance_tolerance
    is_valid = false;
    objectives = [600, -0.7, -0.7];  % 使用合理的默认值，便于算法收敛
    constraints = 100 * center_distance_error;  % 增加约束惩罚
    return;
end

% 计算各级传动比
i1 = z2 / z1;  % 一级传动比
zr2 = zs2 + 2*zp2;  % 二级齿圈齿数
i2 = 1 + zr2/zs2;   % 二级传动比
zr3 = zs3 + 2*zp3;  % 三级齿圈齿数
i3 = 1 + zr3/zs3;   % 三级传动比

% 检查总传动比是否满足要求
total_i = i1 * i2 * i3;
required_i = input_speed / output_speed;

% 检查传动比约束
if exist('ratio_constraints', 'var') && ratio_constraints.enabled
    % 1. 首先检查总传动比是否满足要求
    if abs(total_i - ratio_constraints.total_ratio) / ratio_constraints.total_ratio > ratio_constraints.tolerance
        % 传动比不满足要求，这是一个硬约束，直接返回无效解
        is_valid = false;
        objectives = [500, -0.8, -0.8]; % 使用合理的默认值，便于算法收敛
        constraints = 100 * abs(total_i - ratio_constraints.total_ratio) / ratio_constraints.total_ratio;
        return;
    end
    
    % 2. 检查一级传动比范围约束 (2.5-3.5)
    if isfield(ratio_constraints, 'i1_min') && isfield(ratio_constraints, 'i1_max')
        if i1 < ratio_constraints.i1_min || i1 > ratio_constraints.i1_max
            is_valid = false;
            objectives = [500, -0.8, -0.8];
            constraints = 50 + 10 * max(abs(i1 - ratio_constraints.i1_min), abs(i1 - ratio_constraints.i1_max)) / ratio_constraints.i1_min;
            return;
        end
    end
    
    % 3. 检查二级传动比范围约束 (3-8)
    if isfield(ratio_constraints, 'i2_min') && isfield(ratio_constraints, 'i2_max')
        if i2 < ratio_constraints.i2_min || i2 > ratio_constraints.i2_max
            is_valid = false;
            objectives = [500, -0.8, -0.8];
            constraints = 50 + 10 * max(abs(i2 - ratio_constraints.i2_min), abs(i2 - ratio_constraints.i2_max)) / ratio_constraints.i2_min;
            return;
        end
    end
    
    % 4. 检查三级传动比范围约束 (3-8)
    if isfield(ratio_constraints, 'i3_min') && isfield(ratio_constraints, 'i3_max')
        if i3 < ratio_constraints.i3_min || i3 > ratio_constraints.i3_max
            is_valid = false;
            objectives = [500, -0.8, -0.8];
            constraints = 50 + 10 * max(abs(i3 - ratio_constraints.i3_min), abs(i3 - ratio_constraints.i3_max)) / ratio_constraints.i3_min;
            return;
        end
    end
    
    % 5. 检查三级传动比是否不大于二级传动比
    if isfield(ratio_constraints, 'i3_leq_i2') && ratio_constraints.i3_leq_i2
        if i3 > i2
            is_valid = false;
            objectives = [500, -0.8, -0.8];
            constraints = 50 + 10 * (i3 - i2) / i2;
            return;
        end
    end
    
    % 6. 如果还有具体的目标传动比，检查偏差
    if isfield(ratio_constraints, 'i1') && isfield(ratio_constraints, 'i2') && isfield(ratio_constraints, 'i3')
        i1_dev = abs(i1 - ratio_constraints.i1) / ratio_constraints.i1;
        i2_dev = abs(i2 - ratio_constraints.i2) / ratio_constraints.i2;
        i3_dev = abs(i3 - ratio_constraints.i3) / ratio_constraints.i3;
        
        ratio_tolerance = 0.15; % 允许15%的偏差
        
        if i1_dev > ratio_tolerance || i2_dev > ratio_tolerance || i3_dev > ratio_tolerance
            constraints = constraints + 10 * max([i1_dev, i2_dev, i3_dev]);
        end
    end
else
    % 如果没有启用比例约束，使用旧的检查方法
    % 对于大传动比，允许更大的误差范围
    if required_i > 50
        tolerance = 0.10; % 对于大传动比(>50)，允许10%的误差
    else
        tolerance = 0.05; % 对于小传动比，保持5%的误差
    end
    
    % 检查总传动比
    if abs(total_i - required_i) / required_i > tolerance
        objectives = [500, -0.8, -0.8]; % 使用合理的默认值，便于算法收敛
        constraints = 20;
        is_valid = false;
        return;
    end
end

% 计算各级转矩和转速
T_in = 9550 * input_power / input_speed;  % 输入轴转矩(N.m)
omega_in = input_speed * pi / 30;         % 输入轴角速度(rad/s)

% 第一级
omega1 = omega_in;               % 一级输入角速度
T1 = T_in;                       % 一级输入转矩
omega2 = omega1 / i1;            % 一级输出角速度
T2 = T1 * i1 * 0.98;             % 一级输出转矩(考虑效率)

% 第二级
omega_s2 = omega2;               % 二级太阳轮角速度
T_s2 = T2;                       % 二级太阳轮转矩
omega_out2 = omega_s2 / i2;      % 二级输出角速度
T_out2 = T_s2 * i2 * 0.98;       % 二级输出转矩(考虑效率)

% 第三级
omega_s3 = omega_out2;           % 三级太阳轮角速度
T_s3 = T_out2;                   % 三级太阳轮转矩
omega_out3 = omega_s3 / i3;      % 三级输出角速度
T_out3 = T_s3 * i3 * 0.98;       % 三级输出转矩(考虑效率)

% 各级齿轮参数计算
% 计算分度圆直径
d1 = m1 * z1;                     % 一级小齿轮分度圆直径(mm)
d2 = m1 * z2;                     % 一级大齿轮分度圆直径(mm)
ds2 = mn2 * zs2;                  % 二级太阳轮分度圆直径(mm)
dp2 = mn2 * zp2;                  % 二级行星轮分度圆直径(mm)
dr2 = mn2 * zr2;                  % 二级齿圈分度圆直径(mm)
ds3 = mn3 * zs3;                  % 三级太阳轮分度圆直径(mm)
dp3 = mn3 * zp3;                  % 三级行星轮分度圆直径(mm)
dr3 = mn3 * zr3;                  % 三级齿圈分度圆直径(mm)

% 计算实际中心距（考虑变位系数）
% 一级平行轴齿轮中心距
if helix_angle_1 == 0
    % 直齿轮情况
    inv_alpha = tan(pressure_angle * pi / 180) - (pressure_angle * pi / 180); % 渐开线函数
    inv_alpha_w = inv_alpha + 2 * (x1 + x2) * tan(pressure_angle * pi / 180) / (z1 + z2);
    alpha_w = fzero(@(a) tan(a) - a - inv_alpha_w, pressure_angle * pi / 180);
    a1 = m1 * (z1 + z2) * cos(pressure_angle * pi / 180) / (2 * cos(alpha_w));  % 实际中心距
else
    % 斜齿轮情况
    mt1 = m1 / cos(helix_angle_1 * pi / 180);  % 端面模数
    inv_alpha_t = tan(pressure_angle * pi / 180) - (pressure_angle * pi / 180);
    inv_alpha_tw = inv_alpha_t + 2 * (x1 + x2) * tan(pressure_angle * pi / 180) / (z1 + z2);
    alpha_tw = fzero(@(a) tan(a) - a - inv_alpha_tw, pressure_angle * pi / 180);
    a1 = mt1 * (z1 + z2) * cos(pressure_angle * pi / 180) / (2 * cos(alpha_tw));  % 实际中心距
end

% 二级行星系中心距
a2 = (ds2 + dp2) / 2;             % 二级太阳轮与行星轮中心距(mm)

% 三级行星系中心距
a3 = (ds3 + dp3) / 2;             % 三级太阳轮与行星轮中心距(mm)

% 使用中心距和齿宽系数计算齿宽
% 一级传动使用实际中心距计算齿宽
b1 = k_h1 * a1;                 % 一级齿宽(mm)，使用实际中心距计算

% 二级行星系使用中心距计算齿宽
b2 = k_h2 * a2;                 % 二级齿宽(mm)，使用中心距计算

% 三级行星系使用中心距计算齿宽
b3 = k_h3 * a3;                 % 三级齿宽(mm)，使用中心距计算

%% 邻接条件检查 - 按照图3-1的邻接条件要求实现
% 计算二级行星系的邻接条件
r_ac2 = dp2/2 + mn2; % 二级行星轮齿顶圆半径 (加上1个模数作为齿顶高度)
a_ac2 = (ds2 + dp2) / 2; % 二级太阳轮与行星轮的中心距
L_c2 = 2 * a_ac2 * sin(pi/planets_count_2); % 相邻行星轮中心距
d_ac2 = 2 * r_ac2; % 行星轮齿顶圆直径

% 检查二级行星轮系的邻接条件
adjacency_condition2_1 = (2*r_ac2 < L_c2);
adjacency_condition2_2 = (d_ac2 < 2*a_ac2*sin(pi/planets_count_2));

% 确保最小间隙≥0.5模数
min_gap2 = L_c2 - d_ac2;
gap_condition2 = (min_gap2 >= 0.5 * mn2);

% 计算三级行星系的邻接条件
r_ac3 = dp3/2 + mn3; % 三级行星轮齿顶圆半径
a_ac3 = (ds3 + dp3) / 2; % 三级太阳轮与行星轮的中心距
L_c3 = 2 * a_ac3 * sin(pi/planets_count_3); % 相邻行星轮中心距
d_ac3 = 2 * r_ac3; % 行星轮齿顶圆直径

% 检查三级行星轮系的邻接条件
adjacency_condition3_1 = (2*r_ac3 < L_c3);
adjacency_condition3_2 = (d_ac3 < 2*a_ac3*sin(pi/planets_count_3));

% 确保最小间隙≥0.5模数
min_gap3 = L_c3 - d_ac3;
gap_condition3 = (min_gap3 >= 0.5 * mn3);

%% 安装条件检查 - 按照公式(1.5)的安装条件要求实现
% 二级行星系安装条件：(zs + zr) / np 必须为整数
assembly_value2 = (zs2 + zr2) / planets_count_2;
assembly_condition2 = abs(round(assembly_value2) - assembly_value2) < 1e-6; % 检查是否为整数

% 三级行星系安装条件：(zs + zr) / np 必须为整数
assembly_value3 = (zs3 + zr3) / planets_count_3;
assembly_condition3 = abs(round(assembly_value3) - assembly_value3) < 1e-6; % 检查是否为整数

% 如果任一邻接条件或安装条件不满足，设置无效解
if ~adjacency_condition2_1 || ~adjacency_condition2_2 || ~gap_condition2 || ...
   ~adjacency_condition3_1 || ~adjacency_condition3_2 || ~gap_condition3 || ...
   ~assembly_condition2 || ~assembly_condition3
    is_valid = false;
    objectives = [400, -1.0, -1.0]; % 设置默认目标值
    constraints = constraints + 50; % 增加约束惩罚
    return;
end

%% 同心条件检查 - 按照公式(3-10)的角度变位同心条件实现
% 计算标准压力角（弧度）
alpha_rad = pressure_angle * pi / 180;

% 二级行星轮系角度变位同心条件计算
% 计算变位后的实际啮合压力角
% 太阳轮-行星轮啮合压力角 alpha_ac
inv_alpha = tan(alpha_rad) - alpha_rad; % 渐开线函数
inv_alpha_ac2 = inv_alpha + 2 * tan(alpha_rad) * (xs2 + xp2) / (zs2 + zp2);
alpha_ac2 = findPressureAngle(inv_alpha_ac2); % 求解方程获得实际压力角

% 行星轮-内齿圈啮合压力角 alpha_bc
inv_alpha_bc2 = inv_alpha + 2 * tan(alpha_rad) * (xp2 + xr2) / (zp2 + zr2);
alpha_bc2 = findPressureAngle(inv_alpha_bc2);

% 计算同心条件等式左右两边的值
left_side2 = (zs2 + zp2) / cos(alpha_ac2);
right_side2 = (zr2 - zp2) / cos(alpha_bc2);

% 检查同心条件是否满足（允许小误差）
concentric_tolerance = 0.01; % 允许1%的误差
concentric_condition2 = abs(left_side2 - right_side2) / left_side2 < concentric_tolerance;

% 三级行星轮系角度变位同心条件计算
% 计算变位后的压力角
alpha3_rad = pressure_angle_3 * pi / 180; % 三级行星系可能有不同的压力角
inv_alpha3 = tan(alpha3_rad) - alpha3_rad;
inv_alpha_ac3 = inv_alpha3 + 2 * tan(alpha3_rad) * (xs3 + xp3) / (zs3 + zp3);
alpha_ac3 = findPressureAngle(inv_alpha_ac3);

inv_alpha_bc3 = inv_alpha3 + 2 * tan(alpha3_rad) * (xp3 + xr3) / (zp3 + zr3);
alpha_bc3 = findPressureAngle(inv_alpha_bc3);

left_side3 = (zs3 + zp3) / cos(alpha_ac3);
right_side3 = (zr3 - zp3) / cos(alpha_bc3);
concentric_condition3 = abs(left_side3 - right_side3) / left_side3 < concentric_tolerance;

% 如果同心条件不满足，设置无效解
if ~concentric_condition2 || ~concentric_condition3
    is_valid = false;
    objectives = [450, -0.9, -0.9]; % 设置默认目标值
    constraints = constraints + 60; % 增加约束惩罚
    return;
end

% 定义内部函数：求解渐开线方程得到压力角
function alpha = findPressureAngle(inv_alpha)
    % 使用简单的牛顿法求解方程 tan(alpha) - alpha = inv_alpha
    alpha = pi/4; % 初始猜测值
    max_iter = 10;
    tolerance = 1e-6;
    
    for i = 1:max_iter
        f = tan(alpha) - alpha - inv_alpha;
        if abs(f) < tolerance
            break;
        end
        df = 1/cos(alpha)^2 - 1;
        alpha = alpha - f/df;
    end
end

% 质量计算（简化模型）
% 齿轮体积估算(mm^3)
V1 = pi * (d1/2)^2 * b1 * 0.6;  % 一级小齿轮体积(简化为圆柱的60%)
V2 = pi * (d2/2)^2 * b2 * 0.6;  % 一级大齿轮体积(简化为圆柱的60%)
Vs2 = pi * (ds2/2)^2 * b2 * 0.6;  % 二级太阳轮体积
Vp2 = pi * (dp2/2)^2 * b2 * 0.6 * planets_count_2;  % 所有二级行星轮体积
Vr2 = pi * ((dr2+20)/2)^2 * b2 * 0.3;  % 二级齿圈体积(简化)
Vs3 = pi * (ds3/2)^2 * b3 * 0.6;  % 三级太阳轮体积
Vp3 = pi * (dp3/2)^2 * b3 * 0.6 * planets_count_3;  % 所有三级行星轮体积
Vr3 = pi * ((dr3+20)/2)^2 * b3 * 0.3;  % 三级齿圈体积(简化)

% 检查是否提供了详细材料参数
if exist('gear_materials', 'var') && ~isempty(gear_materials)
    % 使用不同材料的密度计算质量
    m1 = gear_materials.parallel.density * V1 / 1e9;  % 一级小齿轮质量(kg)
    m2 = gear_materials.parallel.density * V2 / 1e9;  % 一级大齿轮质量(kg)
    ms2 = gear_materials.planet1_sun.density * Vs2 / 1e9;  % 二级太阳轮质量(kg)
    mp2 = gear_materials.planet1_planet.density * Vp2 / 1e9;  % 二级行星轮质量(kg)
    mr2 = gear_materials.planet1_ring.density * Vr2 / 1e9;  % 二级齿圈质量(kg)
    ms3 = gear_materials.planet2_sun.density * Vs3 / 1e9;  % 三级太阳轮质量(kg)
    mp3 = gear_materials.planet2_planet.density * Vp3 / 1e9;  % 三级行星轮质量(kg)
    mr3 = gear_materials.planet2_ring.density * Vr3 / 1e9;  % 三级齿圈质量(kg)
else
    % 使用统一材料密度（向后兼容）
    m1 = gear_material.density * V1 / 1e9;  % 一级小齿轮质量(kg)
    m2 = gear_material.density * V2 / 1e9;  % 一级大齿轮质量(kg)
    ms2 = gear_material.density * Vs2 / 1e9;  % 二级太阳轮质量(kg)
    mp2 = gear_material.density * Vp2 / 1e9;  % 二级行星轮质量(kg)
    mr2 = gear_material.density * Vr2 / 1e9;  % 二级齿圈质量(kg)
    ms3 = gear_material.density * Vs3 / 1e9;  % 三级太阳轮质量(kg)
    mp3 = gear_material.density * Vp3 / 1e9;  % 三级行星轮质量(kg)
    mr3 = gear_material.density * Vr3 / 1e9;  % 三级齿圈质量(kg)
end

% 总质量(kg)
total_mass = m1 + m2 + ms2 + mp2 + mr2 + ms3 + mp3 + mr3;

% 使用ISO 6336标准计算安全系数
try
    % 获取材料参数
    material = MaterialManager('17CrNiMo6');
    
    % 计算一级齿轮安全系数
    gear1Params = struct('m', m1, 'z', z1, 'alpha', pressure_angle, 'beta', helix_angle_1, ...
                       'b', b1, 'x', x1, 'mating_z', z2, 'mating_x', x2);
    gear1LoadParams = struct('T', T1, 'n', omega1 * 30 / pi, 'KA', 1.75, 'service_life', service_life);
    [SF1_pinion, SF1_gear, SH1] = ParallelGearSafetyCalculator(gear1Params, gear1LoadParams, material, 'ISO6336');
    
    % 取最小的弯曲安全系数作为一级传动的弯曲安全系数
    SF1 = min(SF1_pinion, SF1_gear);
    
    gear2Params = struct('m', m1, 'z', z2, 'alpha', pressure_angle, 'beta', helix_angle_1, ...
                       'b', b1, 'x', x2, 'mating_z', z1, 'mating_x', x1);
    gear2LoadParams = struct('T', T2, 'n', omega2 * 30 / pi, 'KA', 1.75, 'service_life', service_life);
    [SF2_pinion, SF2_gear, SH2] = ParallelGearSafetyCalculator(gear2Params, gear2LoadParams, material, 'ISO6336');
    
    % 取最小的弯曲安全系数作为一级传动的弯曲安全系数（虽然这里的计算是多余的，保留以确保一致性）
    SF2 = min(SF2_pinion, SF2_gear);
    
    % 计算二级行星系安全系数
    sun2Params = struct('m', mn2, 'z', zs2, 'alpha', pressure_angle, 'beta', helix_angle_2, ...
                      'b', b2, 'x', xs2);
    planet2Params = struct('z', zp2, 'x', xp2);
    ring2Params = struct('z', zr2, 'x', -xs2-xp2);
    planet2LoadParams = struct('T', T_s2, 'n', omega_s2 * 30 / pi, 'KA', 1.75, ... % 应用系数，调整为1.75
                             'service_life', service_life, 'planets_count', planets_count_2);
    planet2MaterialParams = struct('sun', material, 'planet', material, 'ring', material);
    
    % 创建二级行星齿轮系统参数结构体
    gear_params_planet2 = struct();
    % 太阳轮参数
    gear_params_planet2.sun = struct('m', mn2, 'z', zs2, 'alpha', pressure_angle, ...
                                   'beta', helix_angle_2, 'b', b2, 'x', xs2);
    % 行星轮参数
    gear_params_planet2.planet = struct('m', mn2, 'z', zp2, 'alpha', pressure_angle, ...
                                      'beta', helix_angle_2, 'b', b2, 'x', xp2);
    % 内齿圈参数
    gear_params_planet2.ring = struct('m', mn2, 'z', zr2, 'alpha', pressure_angle, ...
                                    'beta', helix_angle_2, 'b', b2, 'x', -xs2-xp2);
    % 行星轮数量
    gear_params_planet2.planets_count = planets_count_2;
    
    % 二级行星系载荷参数
    planet2LoadParams = struct('T', T_s2, 'n', omega_s2 * 30 / pi, 'KA', 1.75, ... % 应用系数，调整为1.75
                             'service_life', service_life);
    
    [SF_planet2, SH_planet2] = PlanetarySystemSafetyCalculator(gear_params_planet2, planet2LoadParams, planet2MaterialParams, 'ISO6336');
    
    % 计算三级行星系安全系数
    sun3Params = struct('m', mn3, 'z', zs3, 'alpha', pressure_angle_3, 'beta', helix_angle_3, ...
                      'b', b3, 'x', xs3);
    planet3Params = struct('z', zp3, 'x', xp3);
    ring3Params = struct('z', zr3, 'x', -xs3-xp3);
    planet3LoadParams = struct('T', T_s3, 'n', omega_s3 * 30 / pi, 'KA', 1.75, ... % 应用系数，调整为1.75
                             'service_life', service_life, 'planets_count', planets_count_3);
    planet3MaterialParams = struct('sun', material, 'planet', material, 'ring', material);
    
    % 创建三级行星齿轮系统参数结构体
    gear_params_planet3 = struct();
    % 太阳轮参数
    gear_params_planet3.sun = struct('m', mn3, 'z', zs3, 'alpha', pressure_angle_3, ...
                                   'beta', helix_angle_3, 'b', b3, 'x', xs3);
    % 行星轮参数
    gear_params_planet3.planet = struct('m', mn3, 'z', zp3, 'alpha', pressure_angle_3, ...
                                      'beta', helix_angle_3, 'b', b3, 'x', xp3);
    % 内齿圈参数
    gear_params_planet3.ring = struct('m', mn3, 'z', zr3, 'alpha', pressure_angle_3, ...
                                    'beta', helix_angle_3, 'b', b3, 'x', -xs3-xp3);
    % 行星轮数量
    gear_params_planet3.planets_count = planets_count_3;
    
    % 三级行星系载荷参数
    planet3LoadParams = struct('T', T_s3, 'n', omega_s3 * 30 / pi, 'KA', 1.75, ... % 应用系数，调整为1.75
                             'service_life', service_life);
    
    [SF_planet3, SH_planet3] = PlanetarySystemSafetyCalculator(gear_params_planet3, planet3LoadParams, planet3MaterialParams, 'ISO6336');
    
    % 取最小安全系数
    min_bending_SF = min([SF1, SF2, SF_planet2, SF_planet3]);
    min_contact_SF = min([SH1, SH2, SH_planet2, SH_planet3]);
    
catch
    % 如果ISO 6336计算出错，回退到简化模型
    try
        % 获取材料参数
        material = MaterialManager('17CrNiMo6');
        
        % 计算一级齿轮安全系数
        gear1Params = struct('m', m1, 'z', z1, 'alpha', pressure_angle, 'beta', helix_angle_1, ...
                           'b', b1, 'x', x1, 'mating_z', z2);
        gear1LoadParams = struct('T', T1, 'n', omega1 * 30 / pi, 'KA', 1.75, 'service_life', service_life);
        [SF1_pinion, SF1_gear, SH1] = ParallelGearSafetyCalculator(gear1Params, gear1LoadParams, material, 'ISO6336');
        
        % 取最小的弯曲安全系数作为一级传动的弯曲安全系数
        SF1 = min(SF1_pinion, SF1_gear);
        
        gear2Params = struct('m', m1, 'z', z2, 'alpha', pressure_angle, 'beta', helix_angle_1, ...
                           'b', b1, 'x', x2, 'mating_z', z1);
        gear2LoadParams = struct('T', T2, 'n', omega2 * 30 / pi, 'KA', 1.75, 'service_life', service_life);
        [SF2_pinion, SF2_gear, SH2] = ParallelGearSafetyCalculator(gear2Params, gear2LoadParams, material, 'ISO6336');
        
        % 取最小的弯曲安全系数作为一级传动的弯曲安全系数
        SF2 = min(SF2_pinion, SF2_gear);
        
        % 计算二级行星系安全系数
        sun2Params = struct('m', mn2, 'z', zs2, 'alpha', pressure_angle, 'beta', helix_angle_2, ...
                          'b', b2, 'x', xs2);
        planet2Params = struct('z', zp2, 'x', xp2);
        ring2Params = struct('z', zr2, 'x', -xs2-xp2);
        planet2LoadParams = struct('T', T_s2, 'n', omega_s2 * 30 / pi, 'KA', 1.75, ...
                                 'service_life', service_life, 'planets_count', planets_count_2);
        planet2MaterialParams = struct('sun', material, 'planet', material, 'ring', material);
        
        % 创建二级行星齿轮系统参数结构体
        gear_params_planet2 = struct();
        % 太阳轮参数
        gear_params_planet2.sun = struct('m', mn2, 'z', zs2, 'alpha', pressure_angle, ...
                                       'beta', helix_angle_2, 'b', b2, 'x', xs2);
        % 行星轮参数
        gear_params_planet2.planet = struct('m', mn2, 'z', zp2, 'alpha', pressure_angle, ...
                                          'beta', helix_angle_2, 'b', b2, 'x', xp2);
        % 内齿圈参数
        gear_params_planet2.ring = struct('m', mn2, 'z', zr2, 'alpha', pressure_angle, ...
                                        'beta', helix_angle_2, 'b', b2, 'x', -xs2-xp2);
        % 行星轮数量
        gear_params_planet2.planets_count = planets_count_2;
        
        % 二级行星系载荷参数
        planet2LoadParams = struct('T', T_s2, 'n', omega_s2 * 30 / pi, 'KA', 1.75, ...
                                 'service_life', service_life);
        
        [SF_planet2, SH_planet2] = PlanetarySystemSafetyCalculator(gear_params_planet2, planet2LoadParams, planet2MaterialParams, 'ISO6336');
        
        % 计算三级行星系安全系数
        sun3Params = struct('m', mn3, 'z', zs3, 'alpha', pressure_angle_3, 'beta', helix_angle_3, ...
                          'b', b3, 'x', xs3);
        planet3Params = struct('z', zp3, 'x', xp3);
        ring3Params = struct('z', zr3, 'x', -xs3-xp3);
        planet3LoadParams = struct('T', T_s3, 'n', omega_s3 * 30 / pi, 'KA', 1.75, ...
                                 'service_life', service_life, 'planets_count', planets_count_3);
        planet3MaterialParams = struct('sun', material, 'planet', material, 'ring', material);
        
        % 创建三级行星齿轮系统参数结构体
        gear_params_planet3 = struct();
        % 太阳轮参数
        gear_params_planet3.sun = struct('m', mn3, 'z', zs3, 'alpha', pressure_angle_3, ...
                                       'beta', helix_angle_3, 'b', b3, 'x', xs3);
        % 行星轮参数
        gear_params_planet3.planet = struct('m', mn3, 'z', zp3, 'alpha', pressure_angle_3, ...
                                          'beta', helix_angle_3, 'b', b3, 'x', xp3);
        % 内齿圈参数
        gear_params_planet3.ring = struct('m', mn3, 'z', zr3, 'alpha', pressure_angle_3, ...
                                        'beta', helix_angle_3, 'b', b3, 'x', -xs3-xp3);
        % 行星轮数量
        gear_params_planet3.planets_count = planets_count_3;
        
        % 三级行星系载荷参数
        planet3LoadParams = struct('T', T_s3, 'n', omega_s3 * 30 / pi, 'KA', 1.75, ...
                                 'service_life', service_life);
        
        [SF_planet3, SH_planet3] = PlanetarySystemSafetyCalculator(gear_params_planet3, planet3LoadParams, planet3MaterialParams, 'ISO6336');
        
        % 取最小安全系数
        min_bending_SF = min([SF1, SF2, SF_planet2, SF_planet3]);
        min_contact_SF = min([SH1, SH2, SH_planet2, SH_planet3]);
        
    catch
        % 如果简化模型也失败，使用最基本的计算方法
        % 弯曲应力计算简化
        sigma_F1 = 2000 * T1 / (m1^2 * z1 * b1);  % 一级小齿轮弯曲应力(MPa)
        sigma_F2 = 2000 * T2 / (m1^2 * z2 * b1);  % 一级大齿轮弯曲应力
        sigma_Fs2 = 2000 * T_s2 / (mn2^2 * zs2 * b2);  % 二级太阳轮弯曲应力
        sigma_Fs3 = 2000 * T_s3 / (mn3^2 * zs3 * b3);  % 三级太阳轮弯曲应力

        % 接触应力计算简化
        sigma_H1 = 1000 * sqrt(T1 * (z1+z2) / (z1 * b1 * d1^2));  % 一级接触应力(MPa)
        sigma_Hs2 = 1000 * sqrt(T_s2 * (zs2+zp2) / (zs2 * b2 * ds2^2));  % 二级接触应力
        sigma_Hs3 = 1000 * sqrt(T_s3 * (zs3+zp3) / (zs3 * b3 * ds3^2));  % 三级接触应力

        % 安全系数计算
        SF1 = gear_material.bending_strength / 1e6 / sigma_F1;  % 一级小齿轮弯曲安全系数
        SF2 = gear_material.bending_strength / 1e6 / sigma_F2;  % 一级大齿轮弯曲安全系数
        SFs2 = gear_material.bending_strength / 1e6 / sigma_Fs2;  % 二级太阳轮弯曲安全系数
        SFs3 = gear_material.bending_strength / 1e6 / sigma_Fs3;  % 三级太阳轮弯曲安全系数

        SH1 = gear_material.contact_strength / 1e6 / sigma_H1;  % 一级接触安全系数
        SHs2 = gear_material.contact_strength / 1e6 / sigma_Hs2;  % 二级接触安全系数
        SHs3 = gear_material.contact_strength / 1e6 / sigma_Hs3;  % 三级接触安全系数
        
        % 检查结果是否包含NaN或Inf
        if any(isnan([SF1, SF2, SFs2, SFs3, SH1, SHs2, SHs3])) || ...
           any(isinf([SF1, SF2, SFs2, SFs3, SH1, SHs2, SHs3]))
            objectives = [350, -1.2, -1.2]; % 使用合理的默认值，便于算法收敛
            constraints = 5;
            is_valid = false;
            return;
        end
        
        % 取最小安全系数
        min_bending_SF = min([SF1, SF2, SFs2, SFs3]);
        min_contact_SF = min([SH1, SHs2, SHs3]);
    end
end

% 检查安全系数是否为负值或过小
if min_bending_SF <= 0.1 || min_contact_SF <= 0.1
    objectives = [300, -1.5, -1.5]; % 使用合理的默认值，便于算法收敛
    constraints = 2;
    is_valid = false;
    return;
end

% 目标函数值 (最小化质量, 最大化安全系数)
objectives = [total_mass, -min_bending_SF, -min_contact_SF];

% 约束条件 (所有安全系数必须大于要求的安全系数)
g1 = bending_safety_factor - min_bending_SF;
g2 = contact_safety_factor - min_contact_SF;
constraints = max([0, g1, g2]);  % 取违反的最大值

% 检查目标函数值是否合理
if total_mass <= 0 || total_mass > 1000 || min_bending_SF <= 0 || min_contact_SF <= 0
    is_valid = false;
    objectives = [250, -1.8, -1.8]; % 使用合理的默认值，便于算法收敛
    constraints = 1;
end
end 