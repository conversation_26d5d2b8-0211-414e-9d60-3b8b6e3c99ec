# 多目标优化算法集合

本项目包含多种多目标优化算法的MATLAB实现，使用统一的接口格式，便于对比和使用。

## 项目结构

项目组织成以下几个主要模块:

- **Algorithms/** - 包含所有算法实现
- **Metrics/** - 包含评价指标函数
- **TestFunctions/** - 包含测试问题函数
- **Visualization/** - 包含可视化工具函数
- **Main.m** - 主程序，用于运行和比较算法

## 已实现算法

1. **NSGA-II** - 非支配排序遗传算法II (Non-dominated Sorting Genetic Algorithm II)
2. **NSGA-III** - 非支配排序遗传算法III (Non-dominated Sorting Genetic Algorithm III)
3. **MODA** - 多目标蜻蜓算法 (Multi-objective Dragonfly Algorithm)
4. **MODE** - 多目标差分进化算法 (Multi-objective Differential Evolution)
5. **MOEA/D** - 基于分解的多目标进化算法 (Multi-objective Evolutionary Algorithm based on Decomposition)
6. **MOGOA** - 多目标蝗虫优化算法 (Multi-objective Grasshopper Optimization Algorithm)
7. **MOGWO** - 多目标灰狼优化算法 (Multi-objective Grey Wolf Optimizer)
8. **MOMVO** - 多目标多元宇宙优化算法 (Multi-objective Multi-Verse Optimizer)
9. **MOPSO** - 多目标粒子群优化算法 (Multi-objective Particle Swarm Optimization)
10. **SPEA2** - 强度Pareto进化算法2 (Strength Pareto Evolutionary Algorithm 2)
11. **PESA2** - 基于范围选择的Pareto进化算法II (Pareto Envelope-based Selection Algorithm II)
12. **NSDBO** - 多目标非支配排序蜣螂优化算法 (Non-dominated Sorting Dung Beetle Optimizer)
13. **MSSA** - 多目标樽海鞘群算法 (Multi-objective Salp Swarm Algorithm)
14. **NSWOA** - 多目标非支配排序鲸鱼优化算法 (Non-dominated Sorting Whale Optimization Algorithm)

## 测试问题

`TestFunctions/TestProblems.m` 文件包含多个标准测试问题:
- ZDT系列问题
- 可扩展添加其他测试问题

## 评价指标

`Metrics/` 目录包含多种评价指标:
- 代际距离 (Generational Distance, GD)
- 反向代际距离 (Inverted Generational Distance, IGD)
- 间隔度量 (Spacing)
- 扩展度 (Spread)
- 超体积 (Hypervolume, HV)

## 可视化工具

`Visualization/` 目录包含可视化工具:
- `PlotParetoFront.m` - 绘制Pareto前沿
- `PlotMetricsComparison.m` - 绘制评价指标对比图

## 使用方法

1. 打开 MATLAB
2. 进入项目根目录
3. 编辑 `Main.m` 文件选择要比较的算法和问题 
4. 运行 `Main.m`

### 主程序参数设置

在 `Main.m` 中，你可以设置以下参数:
- `nVar`: 决策变量数量
- `VarMin`, `VarMax`: 决策变量上下界
- `MaxIt`: 最大迭代次数
- `nPop`: 种群大小
- `nRuns`: 独立运行次数
- `selected_algorithms`: 选择要运行的算法
- `selected_problems`: 选择要测试的问题

## 算法接口格式

所有算法遵循统一的接口格式:
```matlab
[PF, PSet] = Algorithm(CostFunction, nVar, VarMin, VarMax, MaxIt, nPop)
```

其中:
- `CostFunction`: 目标函数句柄
- `nVar`: 决策变量数量
- `VarMin`, `VarMax`: 决策变量上下界
- `MaxIt`: 最大迭代次数
- `nPop`: 种群大小
- `PF`: 返回的Pareto前沿（目标值）
- `PSet`: 返回的Pareto集（决策变量值）

## 结果分析

运行主程序后，你将获得:
1. 控制台输出的结果表格
2. Pareto前沿的可视化图
3. 评价指标对比图
4. 保存的.mat结果文件和图像文件

## 参考文献
1. Deb, K., Pratap, A., Agarwal, S., & Meyarivan, T. A. M. T. (2002). A fast and elitist multiobjective genetic algorithm: NSGA-II. IEEE Transactions on Evolutionary Computation, 6(2), 182-197.
2. Deb, K., & Jain, H. (2013). An evolutionary many-objective optimization algorithm using reference-point-based nondominated sorting approach, part I: solving problems with box constraints. IEEE Transactions on Evolutionary Computation, 18(4), 577-601.
3. Mirjalili, S. (2016). Dragonfly algorithm: a new meta-heuristic optimization technique for solving single-objective, discrete, and multi-objective problems. Neural Computing and Applications, 27(4), 1053-1073. 
4. Mirjalili, S., Saremi, S., Mirjalili, S. M., & Coelho, L. D. S. (2016). Multi-objective grey wolf optimizer: a novel algorithm for multi-criterion optimization. Expert Systems with Applications, 47, 106-119.
5. Zitzler, E., Laumanns, M., & Thiele, L. (2001). SPEA2: Improving the strength Pareto evolutionary algorithm. TIK-report, 103. 