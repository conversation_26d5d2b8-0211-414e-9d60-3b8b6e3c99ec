function [EP] = MOEAD(CostFunction, nVar, VarMin, VarMax, MaxIt, nPop)
% MOEAD: 基于分解的多目标进化算法 (Multi-objective Evolutionary Algorithm based on Decomposition)
%
% 输入参数:
%   CostFunction: 目标函数句柄
%   nVar: 决策变量数量
%   VarMin, VarMax: 决策变量的上下界
%   MaxIt: 最大迭代次数
%   nPop: 种群大小（子问题数量）
%
% 输出参数:
%   EP: 最终的非支配解集
%
% 参考文献:
%   <PERSON><PERSON> and <PERSON><PERSON>, "MOEA/D: A Multiobjective Evolutionary Algorithm Based on Decomposition," 
%   IEEE Transactions on Evolutionary Computation, vol. 11, no. 6, pp. 712-731, 2007.

    % 默认参数值
    if nargin < 6
        nPop = 50;
    end
    if nargin < 5
        MaxIt = 100;
    end
    if nargin < 4
        VarMax = 1;
    end
    if nargin < 3
        VarMin = 0;
    end
    if nargin < 2
        nVar = 3;
    end
    
    % 决策变量矩阵大小
    VarSize = [nVar 1];
    
    % 获取目标函数数量
    nObj = numel(CostFunction(unifrnd(VarMin, VarMax, VarSize)));
    
    % MOEA/D 参数设置
    nArchive = 50;                    % 存档大小
    T = max(ceil(0.15 * nPop), 2);    % 邻居数量
    T = min(max(T, 2), 15);
    
    % 交叉参数
    crossover_params.gamma = 0.5;
    crossover_params.VarMin = VarMin;
    crossover_params.VarMax = VarMax;
    
    % 创建子问题（基于分解）
    sp = CreateSubProblems(nObj, nPop, T);
    
    % 初始化个体结构体
    empty_individual.Position = [];
    empty_individual.Cost = [];
    empty_individual.g = [];
    empty_individual.IsDominated = [];
    
    % 初始化理想点
    z = zeros(nObj, 1);
    
    % 初始化种群
    pop = repmat(empty_individual, nPop, 1);
    for i = 1:nPop
        % 随机生成位置
        pop(i).Position = unifrnd(VarMin, VarMax, VarSize);
        % 计算目标函数值
        pop(i).Cost = CostFunction(pop(i).Position);
        % 更新理想点
        z = min(z, pop(i).Cost);
    end
    
    % 计算分解目标函数值
    for i = 1:nPop
        pop(i).g = DecomposedCost(pop(i), z, sp(i).lambda);
    end
    
    % 确定种群支配状态
    pop = DetermineDomination(pop);
    
    % 初始化估计的Pareto前沿
    EP = pop(~[pop.IsDominated]);
    
    % 主循环
    for it = 1:MaxIt
        % 对每个子问题
        for i = 1:nPop
            % 繁殖（交叉）
            % 随机选择两个邻居
            K = randsample(T, 2);
            
            j1 = sp(i).Neighbors(K(1));
            p1 = pop(j1);
            
            j2 = sp(i).Neighbors(K(2));
            p2 = pop(j2);
            
            % 生成新解
            y = empty_individual;
            y.Position = Crossover(p1.Position, p2.Position, crossover_params);
            y.Cost = CostFunction(y.Position);
            
            % 更新理想点
            z = min(z, y.Cost);
            
            % 更新邻居解
            for j = sp(i).Neighbors
                % 计算新解在当前子问题上的分解目标函数值
                y.g = DecomposedCost(y, z, sp(j).lambda);
                
                % 如果新解更好，则替换
                if y.g <= pop(j).g
                    pop(j) = y;
                end
            end
        end
        
        % 确定种群支配状态
        pop = DetermineDomination(pop);
        
        % 获取当前种群中的非支配解
        ndpop = pop(~[pop.IsDominated]);
        
        % 更新外部存档
        EP = [EP; ndpop];
        
        % 确保存档中只包含非支配解
        EP = DetermineDomination(EP);
        EP = EP(~[EP.IsDominated]);
        
        % 如果存档太大，则随机删除多余的解
        if numel(EP) > nArchive
            Extra = numel(EP) - nArchive;
            ToBeDeleted = randsample(numel(EP), Extra);
            EP(ToBeDeleted) = [];
        end
    end
end

% 创建子问题
function sp = CreateSubProblems(nObj, nPop, T)
    % 初始化子问题结构体
    sp = cell(nPop, 1);
    
    if nObj == 2
        % 2目标情况：均匀分布的权重向量
        for i = 1:nPop
            lambda = [(i-1)/(nPop-1) 1-(i-1)/(nPop-1)];
            
            sp{i}.lambda = lambda;
        end
    else
        % 多目标情况：随机生成权重向量
        for i = 1:nPop
            lambda = rand(1, nObj);
            lambda = lambda / sum(lambda);
            
            sp{i}.lambda = lambda;
        end
    end
    
    % 计算子问题之间的距离
    SIGMA = zeros(nPop, nPop);
    for i = 1:nPop
        for j = i:nPop
            SIGMA(i, j) = norm(sp{i}.lambda - sp{j}.lambda);
            SIGMA(j, i) = SIGMA(i, j);
        end
    end
    
    % 确定T个最近的邻居
    for i = 1:nPop
        [~, so] = sort(SIGMA(i, :));
        sp{i}.Neighbors = so(1:T);
    end
    
    % 转换为结构体数组
    SP = repmat(struct('lambda', [], 'Neighbors', []), nPop, 1);
    for i = 1:nPop
        SP(i).lambda = sp{i}.lambda;
        SP(i).Neighbors = sp{i}.Neighbors;
    end
    
    sp = SP;
end

% 计算分解目标函数值（切比雪夫方法）
function g = DecomposedCost(ind, z, lambda)
    % 计算切比雪夫分解的目标函数值
    % g = max_i {lambda_i * |f_i(x) - z_i|}
    
    g = max(lambda .* abs(ind.Cost - z'));
end

% 确定支配状态
function pop = DetermineDomination(pop)
    nPop = numel(pop);
    
    % 初始化支配状态
    for i = 1:nPop
        pop(i).IsDominated = false;
    end
    
    for i = 1:nPop-1
        for j = i+1:nPop
            if Dominates(pop(i).Cost, pop(j).Cost)
                pop(j).IsDominated = true;
            elseif Dominates(pop(j).Cost, pop(i).Cost)
                pop(i).IsDominated = true;
            end
        end
    end
end

% 检查支配关系
function d = Dominates(x, y)
    % x支配y，如果x的所有目标都不劣于y，且至少有一个目标严格优于y
    d = all(x <= y) && any(x < y);
end

% 交叉操作
function y = Crossover(x1, x2, params)
    gamma = params.gamma;
    VarMin = params.VarMin;
    VarMax = params.VarMax;
    
    % 模拟二进制交叉（SBX）的简化版本
    alpha = rand(size(x1)) * (1 + 2*gamma) - gamma;
    
    y = alpha.*x1 + (1-alpha).*x2;
    
    % 边界检查
    y = max(y, VarMin);
    y = min(y, VarMax);
end 