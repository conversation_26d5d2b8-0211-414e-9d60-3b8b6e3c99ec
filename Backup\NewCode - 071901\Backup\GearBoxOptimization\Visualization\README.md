# 可视化模块

本文件夹包含齿轮传动系统多目标优化的可视化相关功能，主要用于结果展示和数据可视化。

## 文件说明

- `DisplayParetoTable.m`: 显示代表性Pareto最优解参数表的弹出窗口
- `DisplayAllParetoTable.m`: 显示所有Pareto最优解参数表的弹出窗口
- `DisplayComparisonTable.m`: 显示算法性能比较结果表的弹出窗口
- `PlotParetoFronts.m`: 绘制多目标优化算法的Pareto前沿
- `VisualizationManager.m`: 可视化管理函数，提供可视化功能的统一入口

## 使用方法

### 代表性最优解表格显示

```matlab
DisplayParetoTable(variables, objective_values, solution_types, param_names, title_text);
```

参数说明：
- `variables`: 变量值矩阵
- `objective_values`: 目标函数值矩阵
- `solution_types`: 解类型描述的单元数组
- `param_names`: 参数名称的单元数组
- `title_text`: 窗口标题

### 所有最优解表格显示

```matlab
DisplayAllParetoTable(variables, objective_values, param_names, sort_idx, ...
                     min_mass_idx, max_bend_idx, max_contact_idx, ...
                     title_text, n_pareto);
```

参数说明：
- `variables`: 变量值矩阵
- `objective_values`: 目标函数值矩阵
- `param_names`: 参数名称的单元数组
- `sort_idx`: 按质量排序的索引
- `min_mass_idx`, `max_bend_idx`, `max_contact_idx`: 代表性解的索引
- `title_text`: 窗口标题
- `n_pareto`: Pareto解的数量

### 算法比较表格显示

```matlab
DisplayComparisonTable(data, metric_names, title_text);
```

参数说明：
- `data`: 算法性能数据矩阵(cell数组)
- `metric_names`: 指标名称的单元数组
- `title_text`: 窗口标题

### Pareto前沿绘制

```matlab
PlotParetoFronts(all_results, algorithm_names);
```

参数说明：
- `all_results`: 所有算法的结果cell数组
- `algorithm_names`: 算法名称的cell数组

## 功能特点

- 所有表格都支持导出为Excel文件
- 代表性解在表格中有特殊标记
- 表格列宽自适应
- 支持多种可视化方式 