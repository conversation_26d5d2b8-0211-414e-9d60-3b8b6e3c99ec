function [pop, F] = RunMOGWO(problem, params)
% RunMOGWO - 运行多目标灰狼优化算法 (Multi-Objective Grey Wolf Optimizer)
%
% 输入:
%   problem - 包含问题信息的结构体
%   params  - 算法参数
%
% 输出:
%   pop - 最终种群
%   F   - 最终的非支配解集目标函数值
%
% 参考文献:
% <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> <PERSON>, <PERSON><PERSON>,
% Multi-objective grey wolf optimizer: A novel algorithm for
% multi-criterion optimization, Expert Systems with Applications,
% DOI: http://dx.doi.org/10.1016/j.eswa.2015.10.039

%% 初始化参数
nVar = problem.nVar;
varSize = problem.varSize;
varMin = problem.varMin;
varMax = problem.varMax;
nPop = params.nPop;
maxIt = params.maxIt;
nObj = problem.nObj;

% 确保有评价次数计数器
if ~isfield(problem, 'FE')
    problem.FE = 0;
end

% MOGWO特定参数
nArchive = 200;                 % 增大档案大小以保存更多非支配解
alpha = 0.1;                    % 网格膨胀率
nGrid = 10;                     % 每个维度上的网格数
beta = 4;                       % 领导者选择压力参数
gamma = 2;                      % 档案成员删除的额外选择压力

% 不再输出开始信息，由主程序统一处理
% fprintf('开始运行MOGWO算法...\n');

%% 初始化种群
empty_wolf.Position = [];
empty_wolf.Cost = [];
empty_wolf.Dominated = false;
empty_wolf.GridIndex = [];
empty_wolf.GridSubIndex = [];

% 初始化狼群
wolves = repmat(empty_wolf, nPop, 1);

% 随机生成初始狼群位置
for i = 1:nPop
    wolves(i).Position = unifrnd(varMin, varMax, varSize);
    
    % 对离散变量特殊处理
    if isfield(problem, 'discreteVars')
        for j = 1:length(problem.discreteVars)
            idx = problem.discreteVars(j).idx;
            if problem.discreteVars(j).isInteger
                wolves(i).Position(idx) = round(wolves(i).Position(idx));
            else
                % 找到最接近的离散值
                values = problem.discreteVars(j).values;
                [~, closest_idx] = min(abs(wolves(i).Position(idx) - values));
                wolves(i).Position(idx) = values(closest_idx);
            end
        end
    end
    
    wolves(i).Cost = problem.costFunction(wolves(i).Position);
    problem.FE = problem.FE + 1;
end

% 进行非支配排序，确定首领狼
wolves = DetermineDomination(wolves);
non_dominated_wolves = wolves(~[wolves.Dominated]);

% 初始化外部档案（存储非支配解）
archive = non_dominated_wolves;

% 创建超立方体网格
G = CreateHypercubes(archive, nGrid, alpha, nObj);

% 更新档案中每个解的网格索引
for i = 1:numel(archive)
    [archive(i).GridIndex, archive(i).GridSubIndex] = GetGridIndex(archive(i), G);
end

%% 主循环
for it = 1:maxIt
    
    % 更新首领狼的影响参数a（随迭代线性减小）
    a = 2 - it*(2/maxIt);
    
    % 更新狼群位置
    for i = 1:nPop
        
        % 选择Alpha、Beta、Delta狼（确保不从同一网格选择）
        if numel(archive) >= 3
            [Alpha, Beta, Delta] = SelectDistinctLeaders(archive, beta);
        elseif numel(archive) == 2
            % 只有两个非支配解时
            [Alpha, Beta] = SelectDistinctLeaders(archive, beta);
            Delta = Beta; % Delta用Beta代替
        elseif numel(archive) == 1
            % 只有一个非支配解时
            Alpha = archive(1);
            Beta = Alpha;
            Delta = Alpha;
        else
            % 如果档案为空（异常情况），随机选择
            rand_idx = randperm(nPop, 3);
            Alpha = wolves(rand_idx(1));
            Beta = wolves(rand_idx(2));
            Delta = wolves(rand_idx(3));
        end
        
        % 对每个决策变量更新位置
        for j = 1:nVar
            % 计算与Alpha狼的距离和接近向量
            c1 = 2*rand(); % 随机系数
            d_alpha = abs(c1*Alpha.Position(j) - wolves(i).Position(j));
            A1 = 2*a*rand() - a;
            X1 = Alpha.Position(j) - A1*d_alpha;
            
            % 计算与Beta狼的距离和接近向量
            c2 = 2*rand();
            d_beta = abs(c2*Beta.Position(j) - wolves(i).Position(j));
            A2 = 2*a*rand() - a;
            X2 = Beta.Position(j) - A2*d_beta;
            
            % 计算与Delta狼的距离和接近向量
            c3 = 2*rand();
            d_delta = abs(c3*Delta.Position(j) - wolves(i).Position(j));
            A3 = 2*a*rand() - a;
            X3 = Delta.Position(j) - A3*d_delta;
            
            % 更新位置（取三狼引导的平均位置）
            wolves(i).Position(j) = (X1 + X2 + X3)/3;
        end
        
        % 对离散变量特殊处理
        if isfield(problem, 'discreteVars')
            for j = 1:length(problem.discreteVars)
                idx = problem.discreteVars(j).idx;
                if problem.discreteVars(j).isInteger
                    wolves(i).Position(idx) = round(wolves(i).Position(idx));
                else
                    % 找到最接近的离散值
                    values = problem.discreteVars(j).values;
                    [~, closest_idx] = min(abs(wolves(i).Position(idx) - values));
                    wolves(i).Position(idx) = values(closest_idx);
                end
            end
        end
        
        % 边界处理
        wolves(i).Position = max(wolves(i).Position, varMin);
        wolves(i).Position = min(wolves(i).Position, varMax);
        
        % 计算新位置的目标函数值
        wolves(i).Cost = problem.costFunction(wolves(i).Position);
        problem.FE = problem.FE + 1;
    end
    
    % 更新狼群的支配状态
    wolves = DetermineDomination(wolves);
    
    % 更新档案（存储非支配解）
    non_dominated_wolves = wolves(~[wolves.Dominated]);
    
    % 合并现有档案和新的非支配解
    archive = [archive; non_dominated_wolves];
    archive = DetermineDomination(archive);
    archive = archive(~[archive.Dominated]);
    
    % 更新网格
    G = CreateHypercubes(archive, nGrid, alpha, nObj);
    
    % 更新档案中每个解的网格索引
    for i = 1:numel(archive)
        [archive(i).GridIndex, archive(i).GridSubIndex] = GetGridIndex(archive(i), G);
    end
    
    % 如果档案超出大小，进行删减
    if numel(archive) > nArchive
        archive = DeleteFromRep(archive, numel(archive) - nArchive, gamma);
    end
    
    % 显示迭代信息
    if mod(it, 10) == 0 || it == maxIt
        disp(['迭代 ' num2str(it) '/' num2str(maxIt) ', 非支配解数量 = ' num2str(numel(archive)) ', 评价次数 = ' num2str(problem.FE)]);
    end
    
end

% 返回非支配解
% 修改代码，确保返回的是数值矩阵而非结构体数组
n_pareto = numel(archive);
population = zeros(n_pareto, problem.nVar);
objectives = zeros(n_pareto, nObj);

for i = 1:n_pareto
    population(i, :) = archive(i).Position;
    objectives(i, :) = archive(i).Cost;
end

% 处理最大化目标
objectives(:, 2) = -objectives(:, 2);  % 取负值以便最大化弯曲安全系数
objectives(:, 3) = -objectives(:, 3);  % 取负值以便最大化接触安全系数

% 返回转换后的数值矩阵
pop = population;
F = objectives;

% 不再输出算法完成信息，由主程序统一处理
end

%% 辅助函数

function pop = DetermineDomination(pop)
% 确定种群中解的支配关系
n = numel(pop);

for i = 1:n
    pop(i).Dominated = false;
end

for i = 1:n
    if ~pop(i).Dominated
        for j = i+1:n
            if ~pop(j).Dominated
                p = pop(i).Cost;
                q = pop(j).Cost;
                
                if dominates(p, q)
                    % i 支配 j
                    pop(j).Dominated = true;
                elseif dominates(q, p)
                    % j 支配 i
                    pop(i).Dominated = true;
                    break;
                end
            end
        end
    end
end

end

function res = dominates(x, y)
% 确定x是否支配y
res = all(x <= y) && any(x < y);
end

function G = CreateHypercubes(archive, nGrid, alpha, nObj)
% 创建超立方体网格，与原始论文实现一致

if isempty(archive)
    % 如果档案为空，返回空网格
    empty_grid.Lower = [];
    empty_grid.Upper = [];
    G = repmat(empty_grid, nObj, 1);
    return;
end

% 提取所有档案的目标函数值
costs = reshape([archive.Cost], nObj, []);

empty_grid.Lower = [];
empty_grid.Upper = [];
G = repmat(empty_grid, nObj, 1);

% 计算每个维度上的网格边界
for j = 1:nObj
    
    min_cj = min(costs(j, :));
    max_cj = max(costs(j, :));
    
    % 扩大网格边界
    dcj = alpha*(max_cj - min_cj);
    
    min_cj = min_cj - dcj;
    max_cj = max_cj + dcj;
    
    % 生成等分网格
    gx = linspace(min_cj, max_cj, nGrid+1);
    
    G(j).Lower = gx(1:end-1);
    G(j).Upper = gx(2:end);
end

end

function [GridIndex, GridSubIndex] = GetGridIndex(particle, G)
% 计算粒子在网格中的索引
nObj = numel(particle.Cost);
GridIndex = zeros(1, nObj);

for j = 1:nObj
    GridIndex(j) = 0;
    for k = 1:numel(G(j).Lower)
        if particle.Cost(j) >= G(j).Lower(k) && particle.Cost(j) < G(j).Upper(k)
            GridIndex(j) = k;
            break;
        end
    end
    
    % 处理边界情况
    if GridIndex(j) == 0
        GridIndex(j) = numel(G(j).Lower);
    end
end

% 计算子网格索引（用于基于拥挤度的选择）
nGridPerDim = zeros(1, nObj);
for j = 1:nObj
    nGridPerDim(j) = numel(G(j).Lower);
end

GridSubIndex = 0;
for j = 1:nObj
    GridSubIndex = GridSubIndex * nGridPerDim(j) + (GridIndex(j) - 1);
end
GridSubIndex = GridSubIndex + 1;
end

function [occ_cell_index, occ_cell_member_count] = GetOccupiedCells(archive)
% 获取被占据的网格单元及其拥挤度
n = numel(archive);

GridIndices = zeros(n, 1);
for i = 1:n
    GridIndices(i) = archive(i).GridSubIndex;
end

occ_cell_index = unique(GridIndices);
occ_cell_member_count = zeros(size(occ_cell_index));

for i = 1:numel(occ_cell_index)
    occ_cell_member_count(i) = sum(GridIndices == occ_cell_index(i));
end
end

function selected = RouletteWheelSelection(p)
% 轮盘赌选择
cumsum_p = cumsum(p);
r = rand();
selected = find(r <= cumsum_p, 1, 'first');
end

function leader = SelectLeader(archive, beta)
% 选择一个领导者（基于拥挤度）
% 获取网格单元及其拥挤度
[occ_cell_index, occ_cell_member_count] = GetOccupiedCells(archive);

% 计算选择概率（拥挤度越低，概率越高）
p = occ_cell_member_count.^(-beta);
p = p / sum(p);

% 轮盘赌选择一个网格单元
selected_cell_idx = occ_cell_index(RouletteWheelSelection(p));

% 找到该网格中的所有解
GridIndices = zeros(numel(archive), 1);
for i = 1:numel(archive)
    GridIndices(i) = archive(i).GridSubIndex;
end
selected_cell_members = find(GridIndices == selected_cell_idx);

% 从该网格中随机选择一个解
n = numel(selected_cell_members);
selected_member_idx = randi([1 n]);
leader_idx = selected_cell_members(selected_member_idx);

leader = archive(leader_idx);
end

function [Alpha, Beta, Delta] = SelectDistinctLeaders(archive, beta)
% 选择三个不同的领导者，确保多样性
% 先选择Alpha
Alpha = SelectLeader(archive, beta);

% 创建一个不包含Alpha所在网格的临时档案
temp_archive_1 = archive;
alpha_grid = Alpha.GridSubIndex;
alpha_pos = Alpha.Position;

% 找出与Alpha不同网格或不同位置的解
valid_indices = [];
for i = 1:numel(archive)
    if archive(i).GridSubIndex ~= alpha_grid || ~isequal(archive(i).Position, alpha_pos)
        valid_indices = [valid_indices i];
    end
end

% 如果没有足够的不同网格，保留所有不同位置的解
if isempty(valid_indices) && numel(archive) > 1
    valid_indices = 1:numel(archive);
    valid_indices(valid_indices == find(arrayfun(@(x) isequal(x.Position, alpha_pos), archive))) = [];
end

% 如果有效的解，从中选择Beta
if ~isempty(valid_indices)
    temp_archive_1 = archive(valid_indices);
    Beta = SelectLeader(temp_archive_1, beta);
    
    % 创建不包含Alpha和Beta所在网格的临时档案
    beta_grid = Beta.GridSubIndex;
    beta_pos = Beta.Position;
    
    % 找出与Alpha和Beta不同网格或不同位置的解
    valid_indices_2 = [];
    for i = 1:numel(temp_archive_1)
        if temp_archive_1(i).GridSubIndex ~= beta_grid || ~isequal(temp_archive_1(i).Position, beta_pos)
            valid_indices_2 = [valid_indices_2 i];
        end
    end
    
    % 如果有效的解，从中选择Delta
    if ~isempty(valid_indices_2)
        temp_archive_2 = temp_archive_1(valid_indices_2);
        Delta = SelectLeader(temp_archive_2, beta);
    else
        Delta = Beta; % 如果没有更多有效解，Delta等于Beta
    end
else
    Beta = Alpha; % 如果没有有效解，Beta等于Alpha
    Delta = Alpha; % Delta也等于Alpha
end
end

function archive = DeleteFromRep(archive, extra, gamma)
% 从存储库中删除extra个解，与原始论文实现一致
for k = 1:extra
    % 获取当前被占据的网格及其拥挤度
    [occ_cell_index, occ_cell_member_count] = GetOccupiedCells(archive);
    
    % 计算删除概率（拥挤度越高，概率越高）
    p = occ_cell_member_count.^gamma;
    p = p / sum(p);
    
    % 选择一个网格
    selected_cell_idx = occ_cell_index(RouletteWheelSelection(p));
    
    % 找到该网格中的所有解
    GridIndices = zeros(numel(archive), 1);
    for i = 1:numel(archive)
        GridIndices(i) = archive(i).GridSubIndex;
    end
    cell_members = find(GridIndices == selected_cell_idx);
    
    % 从该网格中随机选择一个解删除
    n = numel(cell_members);
    to_remove = cell_members(randi([1 n]));
    
    archive(to_remove) = [];
    
    % 如果已删除足够的解，结束循环
    if numel(archive) <= extra
        break;
    end
end
end 