function gear_combinations = GenerateValidFirstStageGears(center_distance, input_power, input_speed, service_life, contact_safety_factor, bending_safety_factor, gear_material, helix_angle_range)
% GenerateValidFirstStageGears 生成满足一级平行轴系约束的齿轮参数组合
%   该函数生成满足以下约束的一级平行轴齿轮系参数组合：
%   1. 模数限制在离散值：7, 8, 9, 10, 11, 12, 13
%   2. 齿数不小于17，不大于100
%   3. 综合变位系数控制在0.0-1.0之间
%   4. 螺旋角范围按照原有代码要求
%
%   输入参数:
%   - center_distance: 目标中心距 (mm)
%   - input_power: 输入功率 (kW)
%   - input_speed: 输入转速 (rpm)
%   - service_life: 设计寿命 (h)
%   - contact_safety_factor: 接触安全系数
%   - bending_safety_factor: 弯曲安全系数
%   - gear_material: 齿轮材料参数
%   - helix_angle_range: 螺旋角范围 [min, max]
%
%   输出:
%   - gear_combinations: 包含有效齿轮组合的表格

% 添加Model目录到路径，确保可以找到CalculatePlanetaryShiftCoefficients函数
addpath('Model');

% 定义模数的离散值
modules = [7, 8, 9, 10, 11, 12, 13];

% 初始化结果数组
combinations = [];

% 计算输入扭矩 (Nm) - 使用工程标准公式与优化算法保持一致
input_torque = 9550 * input_power / input_speed;

% 压力角设置为20度（按原代码要求）
pressure_angle_deg = 20;
pressure_angle = pressure_angle_deg * pi / 180;

% 定义k_h1范围（齿宽系数，按原代码为0.28-0.4）
k_h1_min = 0.28;
k_h1_max = 0.4;
k_h1_values = k_h1_min:0.01:k_h1_max; % 使用0.01作为步长进行穷举计算

% 定义螺旋角范围（角度）
beta_min = helix_angle_range(1);
beta_max = helix_angle_range(2);
beta_values = linspace(beta_min, beta_max, 6); % 增加到6个值进行计算

% 定义齿数范围
min_teeth = 18;
max_teeth = 100;

% 定义变位系数范围 - 这些将作为参考，实际会使用CalculatePlanetaryShiftCoefficients计算
x1_min = 0.3; % 主动轮变位系数下限
x1_max = 0.8; % 主动轮变位系数上限
x2_min = 0.0; % 从动轮变位系数下限
x2_max = 0.5; % 从动轮变位系数上限
sum_x_min = 0.0; % 综合变位系数下限
sum_x_max = 1.0; % 综合变位系数上限

% 质量、弯曲安全系数和接触安全系数的估计值范围
mass_range = [50, 500]; % kg，估计值
sf_bending_range = [1.0, 3.0]; % 估计值
sf_contact_range = [1.0, 3.0]; % 估计值

% 定义传动比范围（作为参考，不是硬性约束）
ratio_min = 1.5;
ratio_max = 10;
ratio_step = 0.1;  % 更细的传动比搜索步长

% 计数器和进度显示
total_combinations = 0;
valid_combinations = 0;
max_combinations_to_find = 5000; % 增加最大组合数量上限到5000
max_combinations_per_module = 1000; % 每个模数最多找1000个组合
fprintf('正在生成满足一级平行轴系约束的齿轮参数组合...\n');
fprintf('中心距误差控制在±0.05%%以内\n');
fprintf('传动比范围限制在2.5-3.5之间\n');
fprintf('使用CalculateParallelShiftCoefficients函数优化计算变位系数\n');

% 开始组合计算
for m_idx = 1:length(modules)
    m = modules(m_idx);
    fprintf('----- 模数 %d -----\n', m);
    
    % 每个模数的有效组合计数
    module_valid_combinations = 0;
    
    % 对于当前模数，计算可能的齿数范围
    for z1 = min_teeth:min(60, max_teeth)  % 扩大小齿轮齿数搜索范围
        for ratio = ratio_min:ratio_step:ratio_max  % 使用更小的步长遍历传动比
            z2_exact = z1 * ratio;
            z2 = round(z2_exact);  % 四舍五入到最接近的整数
            
            % 检查z2是否在允许范围内
            if z2 < min_teeth || z2 > max_teeth
                continue;
            end
            
            % 计算实际传动比并检查误差
            actual_ratio = z2 / z1;
            
            % 检查传动比是否在2.5-3.5范围内
            if actual_ratio < 2.5 || actual_ratio > 3.5
                continue;
            end
            
            ratio_error = abs(actual_ratio - ratio) / ratio;
            if ratio_error > 0.05  % 传动比误差超过5%，跳过
                continue;
            end
            
            % 对于每个螺旋角，计算实际中心距
            for beta_idx = 1:length(beta_values)
                beta_deg = beta_values(beta_idx);
                beta = beta_deg * pi / 180;
                
                % 使用直接计算函数计算变位系数
                [x1, x2] = CalculateParallelShiftCoefficients(z1, z2, m, pressure_angle_deg, beta_deg, center_distance);
                
                % 添加额外的安全检查，确保变位系数在合理范围内
                if x1 > 1.0 || x1 < -0.5 || x2 > 1.0 || x2 < -0.5
                    fprintf('警告: 计算出的变位系数超出合理范围: z1=%d, z2=%d, m=%.1f, x1=%.4f, x2=%.4f\n', z1, z2, m, x1, x2);
                    continue; % 跳过这个组合
                end
                
                % 计算变位系数和
                sum_x = x1 + x2;
                
                % 对于每个齿宽系数，计算齿宽
                for k_h1_idx = 1:length(k_h1_values)
                    k_h1 = k_h1_values(k_h1_idx);
                    
                    total_combinations = total_combinations + 1;
                    
                    % 计算基圆节距
                    p_b = pi * m * cos(pressure_angle);
                    
                    % 计算分度圆直径
                    d1 = m * z1 / cos(beta);
                    d2 = m * z2 / cos(beta);
                    
                    % 计算基圆直径
                    db1 = d1 * cos(pressure_angle);
                    db2 = d2 * cos(pressure_angle);
                    
                    % 计算实际压力角（考虑变位后）
                    inv_alpha = tan(pressure_angle) - pressure_angle;
                    inv_alpha_w = inv_alpha + 2 * (x1 + x2) * tan(pressure_angle) / (z1 + z2);
                    
                    % 使用二分法求解压力角
                    alpha_w = solveInvoluteFunction(inv_alpha_w);
                    
                    % 计算实际中心距
                    a_w = 0.5 * (d1 + d2) * cos(pressure_angle) / cos(alpha_w);
                    
                    % 检查中心距是否满足要求（允许±0.05%的误差）
                    center_distance_error = abs(a_w - center_distance) / center_distance;
                    if center_distance_error > 0.0005
                        continue;
                    end
                            
                    % 计算齿宽（使用实际中心距）
                    b1 = k_h1 * a_w;
                    
                    % 计算估计质量（使用与GearOptObjectives相同的方法）
                    gear_density = gear_material.density;  % kg/m^3
                    
                    % 从GearOptObjectives使用相同的计算方法
                    % 首先计算实体部分体积
                    V1_solid = (pi * b1 / 4) * (m / cos(beta) * z1)^2;
                    
                    % 计算齿顶圆和齿根圆参数
                    c_star = 2.25; % 标准齿高系数
                    
                    % 计算压力角
                    alpha_t1 = pressure_angle;
                    inv_alpha_t1 = tan(alpha_t1) - alpha_t1;
                    
                    % 计算齿高
                    h1 = c_star * m - 2 * x1 * m;
                    
                    % 计算有效长度
                    l1 = b1 / cos(beta);
                    
                    % 计算分度圆半径和齿顶圆半径
                    r1 = m * z1 / 2;
                    ra1 = r1 + m + x1 * m;
                    rb1 = r1 * cos(alpha_t1);
                    
                    % 计算齿顶压力角
                    alpha_a1 = acos(rb1 / ra1);
                    inv_alpha_a1 = tan(alpha_a1) - alpha_a1;
                    
                    % 计算齿宽系数sa
                    s1 = pi * m / 2;
                    sa1 = s1 * ra1 / r1 - 2 * ra1 * (inv_alpha_a1 - inv_alpha_t1);
                    
                    % 计算齿轮间隙体积
                    V1_space = sa1 * h1 * l1 * z1;
                    
                    % 最终小齿轮体积
                    V1 = V1_solid - V1_space;
                    
                    % 对大齿轮进行相同的计算
                    V2_solid = (pi * b1 / 4) * (m / cos(beta) * z2)^2;
                    
                    h2 = c_star * m - 2 * x2 * m;
                    l2 = b1 / cos(beta);
                    
                    r2 = m * z2 / 2;
                    ra2 = r2 + m + x2 * m;
                    rb2 = r2 * cos(alpha_t1);
                    
                    alpha_a2 = acos(rb2 / ra2);
                    inv_alpha_a2 = tan(alpha_a2) - alpha_a2;
                    
                    s2 = pi * m / 2;
                    sa2 = s2 * ra2 / r2 - 2 * ra2 * (inv_alpha_a2 - inv_alpha_t1);
                    
                    V2_space = sa2 * h2 * l2 * z2;
                    
                    V2 = V2_solid - V2_space;
                    
                    % 计算质量
                    mass1 = gear_density * V1 / 1e9;  % kg，除以1e9将mm³转换为m³
                    mass2 = gear_density * V2 / 1e9;  % kg
                    total_mass = mass1 + mass2;
                    
                    % 安全系数计算 - 使用统一的安全系数计算函数
                    % 准备齿轮参数
                    gear1Params = struct('m', m, ...
                                       'z', z1, ...
                                       'alpha', pressure_angle_deg, ...
                                       'beta', beta_deg, ...
                                       'b', b1, ...
                                       'x', x1, ...
                                       'mating_z', z2, ...
                                       'mating_x', x2);  % 添加啮合齿轮变位系数
                    
                    % 准备载荷参数
                    gear1LoadParams = struct('T', input_torque, ...
                                          'n', input_speed, ...
                                          'KA', 1.75, ... % 使用与ParallelGearSafetyCalculator.m中相同的应用系数
                                          'service_life', service_life);
                    
                    % 获取材料参数 - 使用传入的gear_material参数
                    material = gear_material;
                    
                    % 计算安全系数
                    [sf_bending1, sf_bending2, sf_contact] = ParallelGearSafetyCalculator(gear1Params, gear1LoadParams, material, 'ISO6336');
                    
                    % 添加到结果数组
                    valid_combinations = valid_combinations + 1;
                    module_valid_combinations = module_valid_combinations + 1;
                    
                    % 添加压力角到结果中，包括两个齿轮的弯曲安全系数和各自质量
                    % 对变位系数保留四位小数
                    x1_rounded = round(x1, 4);
                    x2_rounded = round(x2, 4);
                    sum_x_rounded = round(sum_x, 4);
                    % 对质量保留两位小数，传动比保留三位小数，中心距保留两位小数
                    actual_ratio_rounded = round(actual_ratio, 3);
                    a_w_rounded = round(a_w, 2);
                    total_mass_rounded = round(total_mass, 2);
                    mass1_rounded = round(mass1, 2);
                    mass2_rounded = round(mass2, 2);
                    new_row = [m, z1, z2, actual_ratio_rounded, beta_deg, pressure_angle_deg, k_h1, x1_rounded, x2_rounded, sum_x_rounded, a_w_rounded, b1, total_mass_rounded, mass1_rounded, mass2_rounded, sf_bending1, sf_bending2, sf_contact];
                    combinations = [combinations; new_row];
                    
                    % 如果当前模数找到足够多的组合，跳到下一个模数
                    if module_valid_combinations >= max_combinations_per_module
                        break;
                    end
                    
                    % 如果总共找到足够多的组合，可以提前结束整个过程
                    if valid_combinations >= max_combinations_to_find
                        break;
                    end
                end
                
                if module_valid_combinations >= max_combinations_per_module || valid_combinations >= max_combinations_to_find
                    break;
                end
            end
            
            if module_valid_combinations >= max_combinations_per_module || valid_combinations >= max_combinations_to_find
                break;
            end
        end
        
        if module_valid_combinations >= max_combinations_per_module || valid_combinations >= max_combinations_to_find
            break;
        end
    end
    
    % 显示每个模数找到的组合数
    fprintf('%d 个有效组合\n', module_valid_combinations);
    
    % 如果总共找到足够多的组合，可以提前结束整个过程
    if valid_combinations >= max_combinations_to_find
        break;
    end
end

fprintf('\n========================\n总计: %d 个有效齿轮组合\n========================\n', valid_combinations);

% 创建结果表格
if ~isempty(combinations)
    var_names = {'模数(mm)', '小齿轮齿数', '大齿轮齿数', '传动比', '螺旋角(°)', '压力角(°)', '齿宽系数', ...
                '小齿轮变位系数', '大齿轮变位系数', '综合变位系数', '实际中心距(mm)', ...
                '齿宽(mm)', '总质量(kg)', '小齿轮质量(kg)', '大齿轮质量(kg)', '小齿轮弯曲安全系数', '大齿轮弯曲安全系数', '接触安全系数'};
    gear_combinations = array2table(combinations, 'VariableNames', var_names);
    
    % 按照质量排序
    gear_combinations = sortrows(gear_combinations, 13);  % 按估计质量排序
else
    % 如果没有找到有效组合，创建空表格
    gear_combinations = table();
    fprintf('警告：未找到满足所有约束的有效齿轮组合！\n');
end
end

function alpha_w = solveInvoluteFunction(inv_alpha_w)
% 用二分法求解渐开线函数的反函数
% 输入: inv_alpha_w - 目标渐开线函数值
% 输出: alpha_w - 对应的角度值

% 初始化上下界（弧度）
alpha_lower = 0;
alpha_upper = pi/4;  % 45度足够大了

% 设置收敛阈值
tol = 1e-6;
max_iter = 100;

for i = 1:max_iter
    % 二分法
    alpha_mid = (alpha_lower + alpha_upper) / 2;
    
    % 计算中点的渐开线函数值
    inv_alpha_mid = tan(alpha_mid) - alpha_mid;
    
    % 检查是否收敛
    if abs(inv_alpha_mid - inv_alpha_w) < tol
        alpha_w = alpha_mid;
        return;
    end
    
    % 更新区间
    if inv_alpha_mid < inv_alpha_w
        alpha_lower = alpha_mid;
    else
        alpha_upper = alpha_mid;
    end
end

% 如果达到最大迭代次数仍未收敛，返回最后的近似值
alpha_w = (alpha_lower + alpha_upper) / 2;
end 