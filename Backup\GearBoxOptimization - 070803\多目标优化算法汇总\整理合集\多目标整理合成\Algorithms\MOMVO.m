function [Archive_X, Archive_F] = MOMVO(CostFunction, nVar, VarMin, VarMax, MaxIt, nPop)
% MOMVO: 多目标多元宇宙优化算法 (Multi-objective Multi-Verse Optimizer)
%
% 输入参数:
%   CostFunction: 目标函数句柄
%   nVar: 决策变量数量
%   VarMin, VarMax: 决策变量的上下界
%   MaxIt: 最大迭代次数
%   nPop: 种群大小
%
% 输出参数:
%   Archive_X: 非支配解的决策变量
%   Archive_F: 非支配解的目标函数值
%
% 参考文献:
%   <PERSON><PERSON><PERSON>, S., Jangir, P., & <PERSON>remi, S. (2017). 
%   "Multi-objective ant lion optimizer: a multi-objective optimization algorithm for solving 
%   engineering problems", Applied Intelligence, 46(1), 79-95.

    % 默认参数值
    if nargin < 6
        nPop = 50;
    end
    if nargin < 5
        MaxIt = 100;
    end
    if nargin < 4
        VarMax = 1;
    end
    if nargin < 3
        VarMin = 0;
    end
    if nargin < 2
        nVar = 5;
    end
    
    % 变量区间向量化
    if isscalar(VarMin)
        VarMin = VarMin * ones(1, nVar);
    end
    if isscalar(VarMax)
        VarMax = VarMax * ones(1, nVar);
    end
    
    % 算法参数
    ArchiveMaxSize = 100;        % 存档最大大小
    
    % 初始化存档
    Archive_X = zeros(ArchiveMaxSize, nVar);
    Archive_F = ones(ArchiveMaxSize, 2) * inf;
    Archive_member_no = 0;
    
    % 初始化最佳宇宙
    Best_universe = zeros(1, nVar);
    
    % 获取目标函数数量
    sample = unifrnd(VarMin, VarMax, [1, nVar]);
    obj_values = CostFunction(sample);
    obj_no = numel(obj_values);
    
    % 更新目标函数值数组大小
    Archive_F = ones(ArchiveMaxSize, obj_no) * inf;
    Best_universe_Inflation_rate = inf * ones(1, obj_no);
    
    % 虫洞存在概率的最小值和最大值 (Eq.(3.3))
    WEP_Max = 1;
    WEP_Min = 0.2;
    
    % 初始化宇宙位置
    Universes = initialization(nPop, nVar, VarMax, VarMin);
    
    % 主循环
    for Time = 1:MaxIt
        % 虫洞存在概率 (Eq. (3.3))
        WEP = WEP_Min + Time * ((WEP_Max - WEP_Min) / MaxIt);
        
        % 行进距离率 (Eq. (3.4))
        TDR = 1 - ((Time)^(1/6) / (MaxIt)^(1/6));
        
        % 计算每个宇宙的膨胀率（适应度值）
        Inflation_rates = zeros(nPop, obj_no);
        for i = 1:nPop
            % 边界检查
            Flag4ub = Universes(i, :) > VarMax;
            Flag4lb = Universes(i, :) < VarMin;
            Universes(i, :) = (Universes(i, :) .* (~(Flag4ub + Flag4lb))) + VarMax .* Flag4ub + VarMin .* Flag4lb;
            
            % 计算膨胀率
            Inflation_rates(i, :) = CostFunction(Universes(i, :));
            
            % 精英保留
            if dominates(Inflation_rates(i, :), Best_universe_Inflation_rate)
                Best_universe_Inflation_rate = Inflation_rates(i, :);
                Best_universe = Universes(i, :);
            end
        end
        
        % 基于膨胀率排序
        [sorted_Inflation_rates, sorted_indexes] = sort(Inflation_rates(:, 1));
        
        % 创建排序后的宇宙
        Sorted_universes = zeros(nPop, nVar);
        for i = 1:nPop
            Sorted_universes(i, :) = Universes(sorted_indexes(i), :);
        end
        
        % 归一化排序后的膨胀率 (Eq. (3.1))
        % 使用行归一化
        normalized_sorted_Inflation_rates = zeros(size(sorted_Inflation_rates));
        sum_sorted_rates = sum(sorted_Inflation_rates);
        if sum_sorted_rates ~= 0
            normalized_sorted_Inflation_rates = sorted_Inflation_rates / sum_sorted_rates;
        else
            normalized_sorted_Inflation_rates = ones(size(sorted_Inflation_rates)) / numel(sorted_Inflation_rates);
        end
        
        % 保留最佳宇宙
        Universes(1, :) = Sorted_universes(1, :);
        
        % 更新存档
        [Archive_X, Archive_F, Archive_member_no] = UpdateArchive(Archive_X, Archive_F, Universes, Inflation_rates, Archive_member_no);
        
        % 处理存档满的情况
        if Archive_member_no > ArchiveMaxSize
            Archive_mem_ranks = RankingProcess(Archive_F, ArchiveMaxSize, obj_no);
            [Archive_X, Archive_F, Archive_mem_ranks, Archive_member_no] = HandleFullArchive(Archive_X, Archive_F, Archive_member_no, Archive_mem_ranks, ArchiveMaxSize);
        else
            Archive_mem_ranks = RankingProcess(Archive_F, ArchiveMaxSize, obj_no);
        end
        
        % 选择存档中的一个解作为最佳宇宙
        index = RouletteWheelSelection(1./Archive_mem_ranks);
        if index == -1
            index = 1;
        end
        Best_universe_Inflation_rate = Archive_F(index, :);
        Best_universe = Archive_X(index, :);
        
        % 更新宇宙位置
        for i = 2:nPop % 从第2个开始，因为第1个是精英
            Black_hole_index = i;
            for j = 1:nVar
                r1 = rand();
                % 白洞机制 (Eq. (3.1))
                if r1 < normalized_sorted_Inflation_rates(i)
                    White_hole_index = RouletteWheelSelection(-sorted_Inflation_rates);
                    if White_hole_index == -1
                        White_hole_index = 1;
                    end
                    Universes(Black_hole_index, j) = Sorted_universes(White_hole_index, j);
                end
                
                % 虫洞机制 (Eq. (3.2))
                r2 = rand();
                if r2 < WEP
                    r3 = rand();
                    if r3 < 0.5
                        Universes(i, j) = Best_universe(j) + TDR * ((VarMax(j) - VarMin(j)) * rand + VarMin(j));
                    else
                        Universes(i, j) = Best_universe(j) - TDR * ((VarMax(j) - VarMin(j)) * rand + VarMin(j));
                    end
                end
            end
        end
    end
    
    % 只返回存档中的成员
    Archive_X = Archive_X(1:Archive_member_no, :);
    Archive_F = Archive_F(1:Archive_member_no, :);
end

% 初始化宇宙位置
function Positions = initialization(pop_size, dim, ub, lb)
    Positions = zeros(pop_size, dim);
    for i = 1:dim
        Positions(:, i) = rand(pop_size, 1) .* (ub(i) - lb(i)) + lb(i);
    end
end

% 支配关系检查
function dom = dominates(x, y)
    % x支配y，如果x的所有目标都不劣于y，且至少有一个目标严格优于y
    dom = all(x <= y) && any(x < y);
end

% 更新存档
function [Archive_X, Archive_F, Archive_member_no] = UpdateArchive(Archive_X, Archive_F, Universes, Inflation_rates, Archive_member_no)
    % 输入:
    %   Archive_X, Archive_F: 当前存档
    %   Universes, Inflation_rates: 当前宇宙位置和膨胀率
    %   Archive_member_no: 当前存档成员数量
    
    [nPop, nVar] = size(Universes);
    
    for i = 1:nPop
        % 获取当前宇宙的位置和膨胀率
        universe_position = Universes(i, :);
        universe_inflation = Inflation_rates(i, :);
        
        if Archive_member_no == 0
            % 如果存档为空，直接添加
            Archive_X(Archive_member_no + 1, :) = universe_position;
            Archive_F(Archive_member_no + 1, :) = universe_inflation;
            Archive_member_no = Archive_member_no + 1;
        else
            dominated = false;
            should_add = true;
            
            j = 1;
            while j <= Archive_member_no && ~dominated
                if dominates(Archive_F(j, :), universe_inflation)
                    dominated = true;
                    should_add = false;
                elseif dominates(universe_inflation, Archive_F(j, :))
                    % 移除被支配的存档成员
                    Archive_X(j:Archive_member_no-1, :) = Archive_X(j+1:Archive_member_no, :);
                    Archive_F(j:Archive_member_no-1, :) = Archive_F(j+1:Archive_member_no, :);
                    Archive_member_no = Archive_member_no - 1;
                    j = j - 1;
                end
                j = j + 1;
            end
            
            if should_add
                Archive_X(Archive_member_no + 1, :) = universe_position;
                Archive_F(Archive_member_no + 1, :) = universe_inflation;
                Archive_member_no = Archive_member_no + 1;
            end
        end
    end
end

% 对存档成员进行排名
function ranks = RankingProcess(Archive_F, ArchiveMaxSize, obj_no)
    [Archive_member_no, ~] = size(Archive_F);
    
    % 计算拥挤距离
    distance_matrix = zeros(Archive_member_no, Archive_member_no);
    for i = 1:Archive_member_no
        for j = i:Archive_member_no
            distance_matrix(i, j) = sqrt(sum((Archive_F(i, :) - Archive_F(j, :)).^2));
            distance_matrix(j, i) = distance_matrix(i, j);
        end
    end
    
    % 计算拥挤度
    crowding = zeros(Archive_member_no, 1);
    for i = 1:Archive_member_no
        sorted_dist = sort(distance_matrix(i, :));
        % 求k个最近邻居的距离之和
        k = min(3, Archive_member_no-1);
        if k > 0
            crowding(i) = sum(sorted_dist(2:k+1));
        end
    end
    
    % 归一化拥挤度
    if max(crowding) ~= min(crowding) && max(crowding) > 0
        crowding = (crowding - min(crowding)) / (max(crowding) - min(crowding));
    else
        crowding = ones(Archive_member_no, 1);
    end
    
    ranks = crowding;
end

% 处理存档满的情况
function [Archive_X, Archive_F, Archive_mem_ranks, Archive_member_no] = HandleFullArchive(Archive_X, Archive_F, Archive_member_no, Archive_mem_ranks, ArchiveMaxSize)
    % 根据拥挤度排序
    [~, indices] = sort(Archive_mem_ranks);
    
    % 保留排名靠前的解
    Archive_X = Archive_X(indices(1:ArchiveMaxSize), :);
    Archive_F = Archive_F(indices(1:ArchiveMaxSize), :);
    Archive_mem_ranks = Archive_mem_ranks(indices(1:ArchiveMaxSize));
    Archive_member_no = ArchiveMaxSize;
end

% 轮盘赌选择
function index = RouletteWheelSelection(weights)
    if sum(weights) == 0
        index = -1;
        return;
    end
    
    accumulation = cumsum(weights);
    p = rand() * accumulation(end);
    chosen_index = -1;
    for i = 1:length(accumulation)
        if accumulation(i) > p
            chosen_index = i;
            break;
        end
    end
    index = chosen_index;
end 