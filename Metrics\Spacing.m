function Score = Spacing(PopObj)
% Spacing - 分布间距指标
% 根据Schott的标准定义：SP = sqrt(1/(|S|-1) * Σ(di - d̄)²)
%
% 输入:
%   PopObj - 解集矩阵，每行代表一个解
%
% 输出:
%   Score - Spacing值，越小越好（0表示完美均匀分布）
%
% 公式说明:
% - di = min_{j≠i} ||si - sj|| 是第i个解到最近邻解的距离
% - d̄ 是所有距离的均值
% - 计算所有距离的标准差
%
% 参考文献:
% Schott, J. R<PERSON> (1995). Fault tolerant design using single and
% multicriteria genetic algorithm optimization.

if size(PopObj, 1) < 2
    Score = 0;
    return;
end

N = size(PopObj, 1);

% 计算每个解到其最近邻解的欧几里得距离
distances = zeros(N, 1);
for i = 1:N
    min_dist = inf;
    for j = 1:N
        if i ~= j
            dist = norm(PopObj(i, :) - PopObj(j, :));
            min_dist = min(min_dist, dist);
        end
    end
    distances(i) = min_dist;
end

% 计算平均距离
mean_distance = mean(distances);

% 计算Spacing指标（标准差的定义）
Score = sqrt(sum((distances - mean_distance).^2) / (N - 1));
end