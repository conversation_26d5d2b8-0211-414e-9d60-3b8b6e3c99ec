function GenerateThreeStageHTML(selected_algorithms, system_params, optimization_results, first_stage_params, metrics)
% GenerateThreeStageHTML 生成三级齿轮参数显示网页
% 融合版本：专门用于参数传输显示，不进行计算
% 支持从优化结果和CSV文件读取数据进行展示
%
% 输入参数:
%   selected_algorithms - 选择的算法名称列表
%   system_params - 系统参数结构体
%   optimization_results - 优化结果结构体（可选）
%   first_stage_params - 一级参数表格（可选）
%   metrics - 性能指标（可选）

fprintf('正在生成三级齿轮参数显示网页...\n');

% 提取系统参数
input_speed = getFieldValue(system_params, 'input_speed', 1490);
output_speed = getFieldValue(system_params, 'output_speed', 18.62);
input_torque = getFieldValue(system_params, 'input_torque', 7500);
contact_safety_factor = getFieldValue(system_params, 'contact_safety_factor', 1.2);
bending_safety_factor = getFieldValue(system_params, 'bending_safety_factor', 1.2);

% 设置结果目录
results_dir = 'Results';
if ~exist(results_dir, 'dir')
    mkdir(results_dir);
end

% 从多个数据源读取参数数据
algorithm_data = cell(length(selected_algorithms), 1);
pareto_variables = cell(length(selected_algorithms), 1);
pareto_solutions = cell(length(selected_algorithms), 1);

for i = 1:length(selected_algorithms)
    algorithm_name = selected_algorithms{i};
    field_name = convertToValidFieldName(algorithm_name);
    safe_algorithm_name = createSafeFileName(algorithm_name);
    csv_filename = fullfile(results_dir, sprintf('%s_参数表格.csv', safe_algorithm_name));

    % 优先从CSV文件读取
    if exist(csv_filename, 'file')
        try
            csv_data = readtable(csv_filename);
            algorithm_data{i} = csv_data;
            % 转换为数值矩阵用于后续处理
            pareto_variables{i} = table2array(csv_data);
            % 目标函数值在第67-69列
            if size(pareto_variables{i}, 2) >= 69
                pareto_solutions{i} = pareto_variables{i}(:, [67, 69, 68]);
                pareto_solutions{i}(:, 2) = -1 ./ pareto_solutions{i}(:, 2);
                pareto_solutions{i}(:, 3) = -1 ./ pareto_solutions{i}(:, 3);
            end
            fprintf('成功读取 %s 算法参数表格\n', algorithm_name);
        catch ME
            fprintf('读取 %s 算法参数表格失败: %s\n', algorithm_name, ME.message);
            algorithm_data{i} = [];
        end
    % 备选：从optimization_results读取
    elseif nargin >= 3 && ~isempty(optimization_results) && isfield(optimization_results, field_name)
        result = optimization_results.(field_name);
        if isfield(result, 'variables') && isfield(result, 'objectives')
            pareto_variables{i} = result.variables;
            pareto_solutions{i} = result.objectives;
            fprintf('从优化结果读取 %s 算法数据\n', algorithm_name);
        end
    else
        fprintf('未找到 %s 算法数据\n', algorithm_name);
        algorithm_data{i} = [];
    end
end

% 创建HTML文件
html_file = fullfile(results_dir, '三级齿轮参数综合显示.html');
fid = fopen(html_file, 'w');

if fid == -1
    error('无法创建HTML文件: %s', html_file);
end

% 写入HTML头部 - 融合两个函数的样式
fprintf(fid, '<!DOCTYPE html>\n');
fprintf(fid, '<html lang="zh-CN">\n');
fprintf(fid, '<head>\n');
fprintf(fid, '<meta charset="UTF-8">\n');
fprintf(fid, '<meta name="viewport" content="width=device-width, initial-scale=1.0">\n');
fprintf(fid, '<title>三级齿轮传动系统参数综合显示</title>\n');
fprintf(fid, '<style>\n');
fprintf(fid, 'body { font-family: "Microsoft YaHei", Arial, sans-serif; margin: 0; color: #333; background-color: #f5f7fa; line-height: 1.5; }\n');
fprintf(fid, 'h1 { text-align: center; padding: 15px 10px; background: linear-gradient(135deg, #3498db, #2c3e50); color: white; margin: 0; font-weight: 500; letter-spacing: 1px; box-shadow: 0 2px 6px rgba(0,0,0,0.1); }\n');
fprintf(fid, 'h2 { color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 5px; font-weight: 500; }\n');
fprintf(fid, 'h3 { border-bottom: 1px solid #bdc3c7; padding-bottom: 3px; }\n');
fprintf(fid, '.container { max-width: 1200px; margin: 0 auto; padding: 20px; }\n');
fprintf(fid, 'table { border-collapse: collapse; width: 100%%; margin-bottom: 20px; background: white; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }\n');
fprintf(fid, 'th, td { padding: 6px 8px; text-align: center; border: 1px solid #e0e6ed; font-size: 0.9em; }\n');
fprintf(fid, 'th { background: linear-gradient(to bottom, #5a7b9c, #7b96ae); color: white; font-weight: 500; }\n');
fprintf(fid, 'tr:nth-child(even) { background-color: #f8fafc; }\n');
fprintf(fid, 'tr:hover { background-color: #eef7fb; transition: all 0.2s ease; }\n');
fprintf(fid, '.algorithm-section { margin-bottom: 30px; background: white; padding: 15px; border-radius: 5px; box-shadow: 0 1px 3px rgba(0,0,0,0.08); }\n');
fprintf(fid, '.param-info { background: #e8f4f8; padding: 15px; border-radius: 5px; margin-bottom: 20px; border-left: 4px solid #3498db; }\n');
fprintf(fid, '.best-solution { background: #e8f5e8; border-left: 4px solid #27ae60; padding: 15px; border-radius: 5px; margin-bottom: 20px; }\n');
fprintf(fid, '.data-card { background: white; padding: 10px; border-radius: 5px; margin: 5px; text-align: center; box-shadow: 0 1px 3px rgba(0,0,0,0.1); }\n');
fprintf(fid, '.card-label { font-size: 0.9em; color: #666; margin-bottom: 5px; }\n');
fprintf(fid, '.card-value { font-size: 1.2em; font-weight: bold; color: #2c3e50; }\n');
fprintf(fid, '.flex-container { display: flex; flex-wrap: wrap; gap: 10px; margin: 15px 0; }\n');
fprintf(fid, '.flex-item { flex: 1; min-width: 200px; }\n');
fprintf(fid, '</style>\n');
fprintf(fid, '</head>\n');
fprintf(fid, '<body>\n');
fprintf(fid, '<h1>三级齿轮传动系统参数综合显示<span style="position: absolute; right: 20px; bottom: 15px; font-size: 14px; font-weight: normal; opacity: 0.9;">生成时间: %s</span></h1>\n', string(datetime('now', 'Format', 'yyyy-MM-dd HH:mm:ss')));
fprintf(fid, '<div class="container">\n');

% 显示系统参数信息 - 使用更现代的卡片式布局
fprintf(fid, '<h2>系统基本参数</h2>\n');
fprintf(fid, '<div class="flex-container">\n');

% 输入参数卡片
fprintf(fid, '<div class="flex-item data-card">\n');
fprintf(fid, '<div class="card-label">输入转速</div>\n');
fprintf(fid, '<div class="card-value">%.1f rpm</div>\n', input_speed);
fprintf(fid, '</div>\n');

fprintf(fid, '<div class="flex-item data-card">\n');
fprintf(fid, '<div class="card-label">输出转速</div>\n');
fprintf(fid, '<div class="card-value">%.2f rpm</div>\n', output_speed);
fprintf(fid, '</div>\n');

fprintf(fid, '<div class="flex-item data-card">\n');
fprintf(fid, '<div class="card-label">输入扭矩</div>\n');
fprintf(fid, '<div class="card-value">%.0f Nm</div>\n', input_torque);
fprintf(fid, '</div>\n');

fprintf(fid, '<div class="flex-item data-card">\n');
fprintf(fid, '<div class="card-label">目标传动比</div>\n');
fprintf(fid, '<div class="card-value">%.3f</div>\n', input_speed / output_speed);
fprintf(fid, '</div>\n');

fprintf(fid, '<div class="flex-item data-card">\n');
fprintf(fid, '<div class="card-label">安全系数要求</div>\n');
fprintf(fid, '<div class="card-value">接触≥%.1f 弯曲≥%.1f</div>\n', contact_safety_factor, bending_safety_factor);
fprintf(fid, '</div>\n');

fprintf(fid, '</div>\n');

% 参数说明
fprintf(fid, '<div class="param-info">\n');
fprintf(fid, '<h3>参数说明</h3>\n');
fprintf(fid, '<p>本页面展示三级齿轮传动系统的优化参数，包括：</p>\n');
fprintf(fid, '<ul>\n');
fprintf(fid, '<li><strong>一级传动参数</strong>：模数(m₁)、齿数(z₁,z₂)、螺旋角(β₁)、变位系数(x₁,x₂)等</li>\n');
fprintf(fid, '<li><strong>二级传动参数</strong>：太阳轮齿数(z_{s2})、行星轮齿数(z_{p2})、模数(m₂)、变位系数(x_{s2},x_{p2})等</li>\n');
fprintf(fid, '<li><strong>三级传动参数</strong>：太阳轮齿数(z_{s3})、行星轮齿数(z_{p3})、模数(m₃)、变位系数(x_{s3},x_{p3})等</li>\n');
fprintf(fid, '<li><strong>性能指标</strong>：总质量、接触安全系数、弯曲安全系数</li>\n');
fprintf(fid, '</ul>\n');
fprintf(fid, '</div>\n');

% 为每个算法显示参数表格
fprintf(fid, '<h2>算法优化参数表格</h2>\n');

for i = 1:length(selected_algorithms)
    if isempty(algorithm_data{i}) && (isempty(pareto_variables{i}) || isempty(pareto_solutions{i}))
        continue;
    end

    algorithm_name = selected_algorithms{i};

    fprintf(fid, '<div class="algorithm-section">\n');
    fprintf(fid, '<h3>%s 算法参数</h3>\n', algorithm_name);

    % 显示表格 - 优先使用algorithm_data（表格格式）
    if ~isempty(algorithm_data{i})
        data_table = algorithm_data{i};

        fprintf(fid, '<div style="overflow-x: auto;">\n');
        fprintf(fid, '<table>\n');

        % 表头
        fprintf(fid, '<thead><tr>\n');
        column_names = data_table.Properties.VariableNames;
        for j = 1:length(column_names)
            fprintf(fid, '<th>%s</th>\n', column_names{j});
        end
        fprintf(fid, '</tr></thead>\n');

        % 表格数据 - 只显示前10行以避免页面过长
        fprintf(fid, '<tbody>\n');
        max_rows = min(10, height(data_table));
        for row = 1:max_rows
            fprintf(fid, '<tr>\n');
            for col = 1:width(data_table)
                value = data_table{row, col};
                if isnumeric(value)
                    if col >= 18 && col <= 23  % 变位系数，保留四位小数
                        fprintf(fid, '<td>%.4f</td>\n', value);
                    elseif abs(value) < 0.001 && value ~= 0
                        fprintf(fid, '<td>%.6f</td>\n', value);
                    elseif abs(value) < 1
                        fprintf(fid, '<td>%.4f</td>\n', value);
                    elseif abs(value) < 100
                        fprintf(fid, '<td>%.2f</td>\n', value);
                    else
                        fprintf(fid, '<td>%.0f</td>\n', value);
                    end
                else
                    fprintf(fid, '<td>%s</td>\n', string(value));
                end
            end
            fprintf(fid, '</tr>\n');
        end
        fprintf(fid, '</tbody>\n');
        fprintf(fid, '</table>\n');
        fprintf(fid, '</div>\n');

        if height(data_table) > 10
            fprintf(fid, '<p><em>注: 仅显示前10行数据，完整数据请查看CSV文件</em></p>\n');
        end
    % 备选：使用pareto_variables数据
    elseif ~isempty(pareto_variables{i})
        variables = pareto_variables{i};
        solutions = pareto_solutions{i};

        fprintf(fid, '<div style="overflow-x: auto;">\n');
        fprintf(fid, '<table>\n');

        % 创建简化表头
        fprintf(fid, '<thead><tr>\n');
        fprintf(fid, '<th>序号</th>\n');
        fprintf(fid, '<th>m₁</th><th>z₁</th><th>z₂</th><th>β₁</th>\n');
        fprintf(fid, '<th>z_{s2}</th><th>z_{p2}</th><th>k_{h2}</th><th>m₂</th>\n');
        fprintf(fid, '<th>z_{s3}</th><th>z_{p3}</th><th>k_{h3}</th><th>m₃</th>\n');
        fprintf(fid, '<th>总质量(kg)</th><th>接触安全系数</th><th>弯曲安全系数</th>\n');
        fprintf(fid, '</tr></thead>\n');

        % 表格数据 - 只显示前10行
        fprintf(fid, '<tbody>\n');
        max_rows = min(10, size(variables, 1));
        for row = 1:max_rows
            fprintf(fid, '<tr>\n');
            fprintf(fid, '<td>%d</td>\n', row);

            % 显示主要参数 - 区分71列表格数据和原始优化变量
            if size(variables, 2) >= 71
                % 这是从CSV读取的71列完整表格数据
                % 一级参数 (根据column_names: m1,z1,z2,k_h1,x1,x2,x_sum1,beta1,alpha1,a1,i1...)
                fprintf(fid, '<td>%.2f</td>\n', variables(row, 1)); % m₁ (列1)
                fprintf(fid, '<td>%d</td>\n', round(variables(row, 2))); % z₁ (列2)
                fprintf(fid, '<td>%d</td>\n', round(variables(row, 3))); % z₂ (列3)
                fprintf(fid, '<td>%.1f</td>\n', variables(row, 8)); % β₁ (列8: beta1)

                % 二级参数 (mn2,zs2,zp2,zr2,n2,k_h2...)
                fprintf(fid, '<td>%d</td>\n', round(variables(row, 18))); % z_{s2} (列18: zs2)
                fprintf(fid, '<td>%d</td>\n', round(variables(row, 19))); % z_{p2} (列19: zp2)
                fprintf(fid, '<td>%.2f</td>\n', variables(row, 22)); % k_{h2} (列22: k_h2)
                fprintf(fid, '<td>%.2f</td>\n', variables(row, 17)); % m₂ (列17: mn2)

                % 三级参数 (从列42开始: mn3,zs3,zp3,zr3,n3,k_h3...)
                fprintf(fid, '<td>%d</td>\n', round(variables(row, 43))); % z_{s3} (列43: zs3)
                fprintf(fid, '<td>%d</td>\n', round(variables(row, 44))); % z_{p3} (列44: zp3)
                fprintf(fid, '<td>%.2f</td>\n', variables(row, 47)); % k_{h3} (列47: k_h3)
                fprintf(fid, '<td>%.2f</td>\n', variables(row, 42)); % m₃ (列42: mn3)
            elseif size(variables, 2) >= 20
                % 这是原始的优化变量（约20列）
                % 根据MapToExpandedParams.m中的变量结构
                fprintf(fid, '<td>-</td>\n'); % m₁ (从一级参数表获取，这里无法显示)
                fprintf(fid, '<td>-</td>\n'); % z₁ (从一级参数表获取，这里无法显示)
                fprintf(fid, '<td>-</td>\n'); % z₂ (从一级参数表获取，这里无法显示)
                fprintf(fid, '<td>%.1f</td>\n', 10); % β₁ (默认值)

                % 二级参数 (原始变量索引)
                fprintf(fid, '<td>%d</td>\n', round(variables(row, 3))); % z_{s2} (原始变量列3)
                fprintf(fid, '<td>%d</td>\n', round(variables(row, 4))); % z_{p2} (原始变量列4)
                fprintf(fid, '<td>%.2f</td>\n', variables(row, 5)); % k_{h2} (原始变量列5)
                fprintf(fid, '<td>%.2f</td>\n', variables(row, 2)); % m₂ (原始变量列2)

                % 三级参数 (原始变量索引)
                fprintf(fid, '<td>%d</td>\n', round(variables(row, 7))); % z_{s3} (原始变量列7)
                fprintf(fid, '<td>%d</td>\n', round(variables(row, 8))); % z_{p3} (原始变量列8)
                fprintf(fid, '<td>%.2f</td>\n', variables(row, 9)); % k_{h3} (原始变量列9)
                fprintf(fid, '<td>%.2f</td>\n', variables(row, 6)); % m₃ (原始变量列6)
            else
                % 如果数据列不足，显示占位符
                for j = 1:12
                    fprintf(fid, '<td>-</td>\n');
                end
            end

            % 目标函数值
            if ~isempty(solutions) && size(solutions, 2) >= 3
                fprintf(fid, '<td>%.2f</td>\n', solutions(row, 1)); % 质量
                fprintf(fid, '<td>%.3f</td>\n', -solutions(row, 3)); % 接触安全系数
                fprintf(fid, '<td>%.3f</td>\n', -solutions(row, 2)); % 弯曲安全系数
            else
                fprintf(fid, '<td>-</td><td>-</td><td>-</td>\n');
            end

            fprintf(fid, '</tr>\n');
        end
        fprintf(fid, '</tbody>\n');
        fprintf(fid, '</table>\n');
        fprintf(fid, '</div>\n');

        if size(variables, 1) > 10
            fprintf(fid, '<p><em>注: 仅显示前10行数据</em></p>\n');
        end
    end

    fprintf(fid, '</div>\n');
end

% 添加页脚信息
fprintf(fid, '<div style="margin-top: 40px; padding: 20px; background: #f8f9fa; border-radius: 5px; text-align: center; color: #666;">\n');
fprintf(fid, '<p>三级齿轮传动系统参数综合显示 | 数据来源：优化算法结果表格</p>\n');
fprintf(fid, '<p>注：本页面仅用于参数传输显示，不进行任何计算处理</p>\n');
fprintf(fid, '</div>\n');

% 结束HTML
fprintf(fid, '</div>\n');
fprintf(fid, '</body>\n');
fprintf(fid, '</html>\n');

% 关闭文件
fclose(fid);

fprintf('已生成三级齿轮参数综合显示网页: %s\n', html_file);

end

function value = getFieldValue(struct_data, field_name, default_value)
% 安全获取结构体字段值
if isstruct(struct_data) && isfield(struct_data, field_name)
    value = struct_data.(field_name);
else
    value = default_value;
end
end

function safe_name = createSafeFileName(algorithm_name)
% 创建安全的文件名，替换特殊字符
safe_name = algorithm_name;
safe_name = strrep(safe_name, '/', '_');
safe_name = strrep(safe_name, '\', '_');
safe_name = strrep(safe_name, ':', '_');
safe_name = strrep(safe_name, '*', '_');
safe_name = strrep(safe_name, '?', '_');
safe_name = strrep(safe_name, '"', '_');
safe_name = strrep(safe_name, '<', '_');
safe_name = strrep(safe_name, '>', '_');
safe_name = strrep(safe_name, '|', '_');
end

function field_name = convertToValidFieldName(algorithm_name)
% 将算法名称转换为有效的MATLAB字段名
field_name = algorithm_name;
% 替换特殊字符为下划线
field_name = regexprep(field_name, '[^a-zA-Z0-9_]', '_');
% 确保以字母开头
if ~isempty(field_name) && ~isletter(field_name(1))
    field_name = ['alg_' field_name];
end
% 限制长度
if length(field_name) > 63
    field_name = field_name(1:63);
end
end
