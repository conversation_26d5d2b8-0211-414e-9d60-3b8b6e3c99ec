function [SF_min, SH_min] = ISO6336PlanetaryCalculator(sunParams, planetParams, ringParams, loadParams, materialParams, qualityGrade, planets_count)
% ISO6336PLANETARYCALCULATOR 根据ISO 6336标准计算行星齿轮系统的安全系数
%
% 输入参数:
%   sunParams    - 太阳轮参数结构体
%     .m         - 法向模数 (mm)
%     .z         - 齿数
%     .alpha     - 压力角 (度)
%     .beta      - 螺旋角 (度)
%     .b         - 齿宽 (mm)
%     .x         - 变位系数
%   planetParams - 行星轮参数结构体
%     .z         - 齿数
%     .x         - 变位系数
%   ringParams   - 内齿圈参数结构体
%     .z         - 齿数
%     .x         - 变位系数
%   loadParams   - 载荷参数结构体
%     .T         - 太阳轮输入转矩 (Nm)
%     .n         - 太阳轮转速 (rpm)
%     .KA        - 应用系数
%     .requiredLife - 要求寿命 (小时)
%   materialParams - 材料参数结构体
%     .sun       - 太阳轮材料参数
%     .planet    - 行星轮材料参数
%     .ring      - 内齿圈材料参数
%   qualityGrade - 齿轮精度等级 (ISO 1328), 5-12
%   planets_count - 行星轮数量
%
% 输出参数:
%   SF_min - 最小弯曲安全系数
%   SH_min - 最小接触安全系数

% 检查输入参数
if nargin < 7
    planets_count = 3;  % 默认3个行星轮
end

% 行星系统几何关系检查
if sunParams.z + 2*planetParams.z ~= ringParams.z
    error('行星系统几何关系不满足: zs + 2*zp = zr');
end

% 力的分配系数 (考虑行星轮数量)
% 根据ISO 6336，不是所有行星轮同时均匀承载
% 通常取0.7-0.9，这里根据精度等级估算
if qualityGrade <= 6
    load_sharing = 0.9;  % 高精度
elseif qualityGrade <= 8
    load_sharing = 0.8;  % 中等精度
else
    load_sharing = 0.7;  % 低精度
end

% 每个行星轮承受的转矩
T_per_planet = loadParams.T / (planets_count * load_sharing);

% 计算太阳轮-行星轮啮合的安全系数
% 创建太阳轮参数
sunGearParams = struct('m', sunParams.m, ...
                      'z', sunParams.z, ...
                      'alpha', sunParams.alpha, ...
                      'beta', sunParams.beta, ...
                      'b', sunParams.b, ...
                      'x', sunParams.x);

% 创建行星轮参数 (与太阳轮啮合)
planetSunGearParams = struct('m', sunParams.m, ...
                           'z', planetParams.z, ...
                           'alpha', sunParams.alpha, ...
                           'beta', sunParams.beta, ...
                           'b', sunParams.b, ...
                           'x', planetParams.x);

% 创建太阳轮-行星轮啮合的载荷参数
sunPlanetLoadParams = struct('T', T_per_planet, ...
                           'n', loadParams.n, ...
                           'KA', loadParams.KA, ...
                           'requiredLife', loadParams.requiredLife);

% 计算太阳轮的安全系数
[SF_sun, SH_sun] = ISO6336Calculator(sunGearParams, sunPlanetLoadParams, materialParams.sun, qualityGrade);

% 计算行星轮的安全系数 (与太阳轮啮合)
% 行星轮转速 = 太阳轮转速 * (太阳轮齿数 / 行星轮齿数)
planet_speed = loadParams.n * sunParams.z / planetParams.z;
planetSunLoadParams = struct('T', T_per_planet, ...
                           'n', planet_speed, ...
                           'KA', loadParams.KA, ...
                           'requiredLife', loadParams.requiredLife);
[SF_planet_sun, SH_planet_sun] = ISO6336Calculator(planetSunGearParams, planetSunLoadParams, materialParams.planet, qualityGrade);

% 计算行星轮-内齿圈啮合的安全系数
% 创建行星轮参数 (与内齿圈啮合)
planetRingGearParams = struct('m', sunParams.m, ...
                            'z', planetParams.z, ...
                            'alpha', sunParams.alpha, ...
                            'beta', sunParams.beta, ...
                            'b', sunParams.b, ...
                            'x', planetParams.x);

% 创建内齿圈参数
ringGearParams = struct('m', sunParams.m, ...
                       'z', ringParams.z, ...
                       'alpha', sunParams.alpha, ...
                       'beta', sunParams.beta, ...
                       'b', sunParams.b, ...
                       'x', ringParams.x);

% 行星轮-内齿圈啮合的载荷参数
planetRingLoadParams = struct('T', T_per_planet, ...
                            'n', planet_speed, ...
                            'KA', loadParams.KA, ...
                            'requiredLife', loadParams.requiredLife);

% 计算行星轮的安全系数 (与内齿圈啮合)
[SF_planet_ring, SH_planet_ring] = ISO6336Calculator(planetRingGearParams, planetRingLoadParams, materialParams.planet, qualityGrade);

% 计算内齿圈的安全系数
% 内齿圈转速 = 0 (假设固定)
ringLoadParams = struct('T', T_per_planet * planets_count, ...
                       'n', 0.1, ... % 非零小值，避免计算问题
                       'KA', loadParams.KA, ...
                       'requiredLife', loadParams.requiredLife);
[SF_ring, SH_ring] = ISO6336Calculator(ringGearParams, ringLoadParams, materialParams.ring, qualityGrade);

% 取最小安全系数
SF_min = min([SF_sun, SF_planet_sun, SF_planet_ring, SF_ring]);
SH_min = min([SH_sun, SH_planet_sun, SH_planet_ring, SH_ring]);

end 