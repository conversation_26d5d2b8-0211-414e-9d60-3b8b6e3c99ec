function [OUT] = MODE(CostFunction, nVar, VarMin, VarMax, MaxIt, nPop)
% MODE: 多目标差分进化算法 (Multi-objective Differential Evolution)
%
% 输入参数:
%   CostFunction: 目标函数句柄
%   nVar: 决策变量数量
%   VarMin, VarMax: 决策变量的上下界
%   MaxIt: 最大迭代次数
%   nPop: 种群大小
%
% 输出参数:
%   OUT: 包含最终结果的结构体
%       OUT.PSet: 帕累托最优解集
%       OUT.PFront: 帕累托前沿
%
% 参考文献:
%   Storn, R., Price, K., 1997. Differential evolution: A simple and 
%   efficient heuristic for global optimization over continuous spaces. 
%   Journal of Global Optimization 11, 341-359.

    % 默认参数值
    if nargin < 6
        nPop = 100;
    end
    if nargin < 5
        MaxIt = 200;
    end
    if nargin < 4
        VarMax = 1;
    end
    if nargin < 3
        VarMin = 0;
    end
    if nargin < 2
        nVar = 10;
    end
    
    % MODE 算法参数
    Bounds = [VarMin * ones(1, nVar); VarMax * ones(1, nVar)]'; % 决策变量边界
    Initial = Bounds;                                            % 初始化边界
    ScalingFactor = 0.5;                                         % DE 的缩放因子
    CrossOverP = 0.5;                                            % DE 的交叉概率
    
    % 创建 MODE 参数结构体
    MODEDat = struct();
    MODEDat.MAXGEN = MaxIt;               % 最大迭代次数
    MODEDat.XPOP = nPop;                  % 种群大小
    MODEDat.NVAR = nVar;                  % 决策变量数量
    MODEDat.FieldD = Bounds;              % 优化边界
    MODEDat.Initial = Initial;            % 初始化边界
    MODEDat.Esc = ScalingFactor;          % DE 缩放因子
    MODEDat.Pm = CrossOverP;              % DE 交叉概率
    MODEDat.mop = @(x, ~) CostFunction(x); % 目标函数封装
    MODEDat.MAXFUNEVALS = MaxIt * nPop;   % 最大函数评估次数
    MODEDat.SaveResults = 'no';           % 是否保存结果
    MODEDat.InitialPop = [];              % 初始种群
    MODEDat.CounterGEN = 0;               % 代数计数器
    MODEDat.CounterFES = 0;               % 函数评估计数器
    
    % 从目标函数获取目标数量
    sample = unifrnd(VarMin, VarMax, [1, nVar]);
    obj_values = CostFunction(sample);
    MODEDat.NOBJ = numel(obj_values);     % 目标函数数量
    
    % 初始化种群
    Parent = zeros(nPop, nVar);           % 父代种群
    Mutant = zeros(nPop, nVar);           % 变异种群
    Child = zeros(nPop, nVar);            % 子代种群
    FES = 0;                              % 函数评估计数

    % 随机初始化种群
    for i = 1:nPop
        for j = 1:nVar
            Parent(i, j) = Initial(j, 1) + (Initial(j, 2) - Initial(j, 1)) * rand();
        end
    end

    % 评估初始种群
    JxParent = zeros(nPop, MODEDat.NOBJ);
    for i = 1:nPop
        JxParent(i, :) = MODEDat.mop(Parent(i, :), MODEDat);
    end
    FES = FES + nPop;

    % 进化过程
    for gen = 1:MaxIt
        % 变异和交叉
        for i = 1:nPop
            % 随机选择三个不同的个体
            rev = randperm(nPop);
            
            % 变异向量计算
            Mutant(i, :) = Parent(rev(1), :) + ScalingFactor * (Parent(rev(2), :) - Parent(rev(3), :));
            
            % 边界检查
            for j = 1:nVar
                if Mutant(i, j) < Bounds(j, 1)
                    Mutant(i, j) = Bounds(j, 1);
                elseif Mutant(i, j) > Bounds(j, 2)
                    Mutant(i, j) = Bounds(j, 1);
                end
            end
            
            % 交叉操作
            for j = 1:nVar
                if rand() > CrossOverP
                    Child(i, j) = Parent(i, j);
                else
                    Child(i, j) = Mutant(i, j);
                end
            end
        end
        
        % 评估子代种群
        JxChild = zeros(nPop, MODEDat.NOBJ);
        for i = 1:nPop
            JxChild(i, :) = MODEDat.mop(Child(i, :), MODEDat);
        end
        FES = FES + nPop;
        
        % 贪婪选择（基于Pareto支配关系）
        for i = 1:nPop
            if MODEDat.NOBJ == 1
                % 单目标情况：直接比较目标值
                if JxChild(i, :) <= JxParent(i, :)
                    Parent(i, :) = Child(i, :);
                    JxParent(i, :) = JxChild(i, :);
                end
            else
                % 多目标情况：使用Pareto支配关系
                if dominates(JxChild(i, :), JxParent(i, :))
                    Parent(i, :) = Child(i, :);
                    JxParent(i, :) = JxChild(i, :);
                elseif ~dominates(JxParent(i, :), JxChild(i, :))
                    % 如果互不支配，随机选择
                    if rand() < 0.5
                        Parent(i, :) = Child(i, :);
                        JxParent(i, :) = JxChild(i, :);
                    end
                end
            end
        end
        
        % 更新计数器
        MODEDat.CounterGEN = gen;
        MODEDat.CounterFES = FES;
        
        % 提取当前的Pareto前沿
        PFront = JxParent;
        PSet = Parent;
        
        % 更新输出
        OUT.Xpop = Parent;        % 种群
        OUT.Jpop = JxParent;      % 种群的目标向量
        OUT.PSet = PSet;          % Pareto集
        OUT.PFront = PFront;      % Pareto前沿
        OUT.Param = MODEDat;      % MODE参数
    end
    
    % 应用支配过滤器得到最终的Pareto前沿
    [OUT.PFront, OUT.PSet] = DominanceFilter(PFront, PSet);
end

% 支配关系检查
function dom = dominates(x, y)
    % 检查向量x是否支配向量y
    % x支配y，如果x的所有目标都不劣于y，且至少有一个目标严格优于y
    dom = all(x <= y) && any(x < y);
end

% 基于支配关系的过滤器
function [PFront, PSet] = DominanceFilter(F, X)
    % 输入:
    %   F: 目标函数值矩阵
    %   X: 对应的决策变量矩阵
    % 输出:
    %   PFront: 非支配解的目标函数值
    %   PSet: 非支配解的决策变量
    
    nPop = size(F, 1);
    IsDominated = false(nPop, 1);
    
    % 确定每个解的支配状态
    for i = 1:nPop
        for j = i+1:nPop
            if dominates(F(i, :), F(j, :))
                IsDominated(j) = true;
            elseif dominates(F(j, :), F(i, :))
                IsDominated(i) = true;
                break;
            end
        end
    end
    
    % 提取非支配解
    PFront = F(~IsDominated, :);
    PSet = X(~IsDominated, :);
end 