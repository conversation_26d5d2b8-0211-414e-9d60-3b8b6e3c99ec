function [m1, m2, total_mass] = ParallelGearMassCalculator(gear_params)
% ParallelGearMassCalculator 平行轴齿轮质量计算器
% 严格按照GearOptObjectives.m中的原始计算方法，不做任何简化或改变
%
% 输入参数:
%   gear_params - 齿轮几何参数结构体，包含：
%     .m1: 模数 (mm)
%     .z1: 小齿轮齿数
%     .z2: 大齿轮齿数
%     .b1: 齿宽 (mm)
%     .helix_angle_1: 螺旋角 (度)
%     .pressure_angle: 压力角 (度)
%     .x1: 小齿轮变位系数
%     .x2: 大齿轮变位系数
%     .gear_materials: 材料参数结构体（可选）
%
% 输出:
%   m1 - 小齿轮质量 (kg)
%   m2 - 大齿轮质量 (kg)
%   total_mass - 总质量 (kg)

%% 提取参数（严格按照原始代码的变量名）
m1_module = gear_params.m1;
z1 = gear_params.z1;
z2 = gear_params.z2;
b1 = gear_params.b1;
helix_angle_1 = gear_params.helix_angle_1;
pressure_angle = gear_params.pressure_angle;
x1 = gear_params.x1;
x2 = gear_params.x2;

%% 获取材料密度
if isfield(gear_params, 'gear_materials') && ~isempty(gear_params.gear_materials)
    if isfield(gear_params.gear_materials, 'parallel') && isfield(gear_params.gear_materials.parallel, 'density')
        density = gear_params.gear_materials.parallel.density;
    else
        density = 7850;  % 默认17CrNiMo6密度
    end
else
    density = 7850;  % 默认17CrNiMo6密度
end

%% 质量计算（按照公式V = (π·b/4)·(mn/cosβ·z)² - V''计算，其中V'' = sa·h·l·z）
% 齿轮体积估算(mm^3)

%% 一级小齿轮体积计算
% 计算实体部分体积
V1_solid = (pi * b1 / 4) * (m1_module / cos(helix_angle_1 * pi / 180) * z1)^2;

% 计算齿轮间隙体积V''
% 计算压力角
alpha_t1 = pressure_angle * pi / 180; % 压力角（弧度）
inv_alpha_t1 = tan(alpha_t1) - alpha_t1; % 渐开线函数

% 计算变位后的实际啮合压力角
inv_alpha_w1 = inv_alpha_t1 + 2 * (x1 + x2) * tan(alpha_t1) / (z1 + z2);
alpha_w1 = fzero(@(a) tan(a) - a - inv_alpha_w1, alpha_t1);

% 计算齿高参数（标准齿高系数通常为2.25）
c_star = 2.25; % 标准齿高系数
h1 = c_star * m1_module - 2 * x1 * m1_module; % 齿高计算

% 计算有效长度
l1 = b1 / cos(helix_angle_1 * pi / 180);

% 计算齿宽系数sa
% 计算分度圆半径和齿顶圆半径
r1 = m1_module * z1 / 2; % 分度圆半径
ra1 = r1 + m1_module + x1 * m1_module; % 齿顶圆半径
rb1 = r1 * cos(alpha_t1); % 基圆半径

% 计算sa（这里是一个近似计算，完整计算需要更多参数）
s1 = pi * m1_module / 2; % 分度圆弧齿厚
% 计算齿顶压力角
alpha_a1 = acos(rb1 / ra1);
inv_alpha_a1 = tan(alpha_a1) - alpha_a1;
sa1 = s1 * ra1 / r1 - 2 * ra1 * (inv_alpha_a1 - inv_alpha_t1); % 使用齿顶压力角

% 计算齿轮间隙体积
V1_space = sa1 * h1 * l1 * z1;

% 最终一级小齿轮体积
V1 = V1_solid - V1_space;

%% 一级大齿轮体积计算 - 使用类似方法
% 计算实体部分体积
V2_solid = (pi * b1 / 4) * (m1_module / cos(helix_angle_1 * pi / 180) * z2)^2;

% 计算齿高参数
h2 = c_star * m1_module - 2 * x2 * m1_module; % 齿高计算

% 计算有效长度
l2 = b1 / cos(helix_angle_1 * pi / 180);

% 计算齿宽系数sa
r2 = m1_module * z2 / 2; % 分度圆半径
ra2 = r2 + m1_module + x2 * m1_module; % 齿顶圆半径
rb2 = r2 * cos(alpha_t1); % 基圆半径
s2 = pi * m1_module / 2; % 分度圆弧齿厚
% 计算齿顶压力角
alpha_a2 = acos(rb2 / ra2);
inv_alpha_a2 = tan(alpha_a2) - alpha_a2;
sa2 = s2 * ra2 / r2 - 2 * ra2 * (inv_alpha_a2 - inv_alpha_t1); % 使用齿顶压力角

% 计算齿轮间隙体积
V2_space = sa2 * h2 * l2 * z2;

% 最终一级大齿轮体积
V2 = V2_solid - V2_space;

%% 质量计算
% 使用MaterialManager提供的标准材料密度计算质量
m1 = density * V1 / 1e9;  % 一级小齿轮质量(kg) - 17CrNiMo6
m2 = density * V2 / 1e9;  % 一级大齿轮质量(kg) - 17CrNiMo6

% 总质量
total_mass = m1 + m2;

end
