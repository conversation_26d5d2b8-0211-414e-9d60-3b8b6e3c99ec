function CreateSummaryTables(alg_names, pareto_variables, pareto_solutions, all_metrics, param_names, results_dir, input_speed, output_speed, problem, input_torque)
    % 创建综合结果表格，包含所有算法的最优解和性能指标
    % 参数：
    %   alg_names: 算法名称数组
    %   pareto_variables: 各算法的Pareto最优解变量值
    %   pareto_solutions: 各算法的Pareto最优解目标值
    %   all_metrics: 性能评价指标
    %   param_names: 参数名称
    %   results_dir: 结果保存目录
    %   input_speed: 输入转速(rpm)
    %   output_speed: 输出转速(rpm)
    %   problem: 优化问题结构体，包含多级优化信息
    
    % 检查输入参数
    if nargin < 7 || isempty(input_speed)
        input_speed = 1490; % 默认输入转速
    end

    if nargin < 8 || isempty(output_speed)
        output_speed = 18.63; % 默认输出转速
    end
    
    if nargin < 9
        problem = struct();
        % 默认不是多级优化
        problem.isMultipleFirstStage = false;
    end

    if nargin < 10 || isempty(input_torque)
        input_torque = 7500; % 默认输入扭矩 (Nm)
    end
    
    % 如果problem不是结构体，创建一个默认结构体
    if ~isstruct(problem)
        problem = struct();
        problem.isMultipleFirstStage = false;
    end
    
    % 确保problem结构体中有isMultipleFirstStage字段
    if ~isfield(problem, 'isMultipleFirstStage')
        problem.isMultipleFirstStage = false;
    end
    
    % 创建算法最优解表格
    % 表头
    representative_types = {'最小质量解', '最大接触安全系数解', '最大弯曲安全系数解'};
    metrics = {'GD', 'Spread', 'MS', 'IGD', 'HV', '计算时间(秒)', '覆盖率(C-metric)'};
    
    % 创建空表格结构
    num_algs = length(alg_names);
    num_params = length(param_names);
    
    % 最优解表格数据
    best_solutions_data = cell(num_algs * 3, 3 + num_params + 3);
    row = 1;
    
    % 性能指标表格数据
    metrics_data = cell(num_algs, 1 + length(metrics));
    
    % 目标传动比
    target_ratio = input_speed / output_speed;
    
    % 全局最优解变量 - 找出所有算法中的全局最优解
    % 按照新排序标准：
    % 1. 满足安全系数要求的解
    % 2. 质量最小
    % 3. 传动比误差最小
    global_best_alg_idx = -1;
    global_best_sol_idx = -1;
    min_global_mass = Inf;
    min_global_error = Inf;
    safety_req = 1.2; % 安全系数要求
    
    % 填充性能指标数据
    for i = 1:num_algs
        % 填充性能指标数据
        metrics_data{i, 1} = alg_names{i};
        
        if ~isempty(all_metrics) && ~isempty(all_metrics{1})
            metrics_data{i, 2} = all_metrics{1}.GD(i);
            metrics_data{i, 3} = all_metrics{1}.Spread(i);
            metrics_data{i, 4} = all_metrics{1}.MS(i);
            metrics_data{i, 5} = all_metrics{1}.IGD(i);
            metrics_data{i, 6} = all_metrics{1}.HV(i);
            metrics_data{i, 7} = all_metrics{1}.Time(i);
            metrics_data{i, 8} = all_metrics{1}.Coverage(i);
        end
        
        % 跳过无效数据
        if isempty(pareto_variables{i}) || isempty(pareto_solutions{i})
            for j = 1:3
                best_solutions_data{row, 1} = alg_names{i};
                best_solutions_data{row, 2} = representative_types{j};
                best_solutions_data{row, 3} = '没有有效解';
                row = row + 1;
            end
            continue;
        end
        
        % 确保行星轮数量变量被离散化为3、4或5
        discrete_planet_values = [3, 4, 5]; % 行星轮数量只能是3、4或5
        for j = 1:size(pareto_variables{i}, 1)
            % 二级行星轮数量
            distances_2 = abs(pareto_variables{i}(j, 12) - discrete_planet_values);
            [~, idx_2] = min(distances_2);
            pareto_variables{i}(j, 12) = discrete_planet_values(idx_2);
            
            % 三级行星轮数量
            distances_3 = abs(pareto_variables{i}(j, 13) - discrete_planet_values);
            [~, idx_3] = min(distances_3);
            pareto_variables{i}(j, 13) = discrete_planet_values(idx_3);
        end
        
        % 找出代表性解
        if size(pareto_solutions{i}, 2) >= 3
            [min_mass, min_mass_idx] = min(pareto_solutions{i}(:, 1));
            [max_bend, max_bend_idx] = max(-pareto_solutions{i}(:, 2));
            [max_contact, max_contact_idx] = max(-pareto_solutions{i}(:, 3));
        else
            % 如果pareto_solutions不包含三列，则只使用第一列（质量）
            [min_mass, min_mass_idx] = min(pareto_solutions{i}(:, 1));
            max_bend_idx = min_mass_idx;  % 默认使用相同索引
            max_contact_idx = min_mass_idx;  % 默认使用相同索引
            fprintf('警告：算法 %s 的Pareto解不包含完整的目标函数值，使用质量最小解作为代表。\n', alg_names{i});
        end
        
        % 代表性解的索引
        rep_indices = [min_mass_idx, max_bend_idx, max_contact_idx];
        
        for j = 1:length(rep_indices)
            idx = rep_indices(j);
            if idx > size(pareto_variables{i}, 1) || idx <= 0
                continue;
            end
            
            % 算法名称和解类型
            best_solutions_data{row, 1} = alg_names{i};
            best_solutions_data{row, 2} = representative_types{j};
            
            % 目标函数值
            if j == 1
                best_solutions_data{row, 3} = round(pareto_solutions{i}(idx, 1), 2); % 质量（保留两位小数）
            elseif j == 2 && size(pareto_solutions{i}, 2) >= 3
                best_solutions_data{row, 3} = round(-pareto_solutions{i}(idx, 3), 3); % 接触安全系数（保留三位小数）
            elseif j == 3 && size(pareto_solutions{i}, 2) >= 2
                best_solutions_data{row, 3} = round(-pareto_solutions{i}(idx, 2), 3); % 弯曲安全系数（保留三位小数）
            else
                best_solutions_data{row, 3} = round(pareto_solutions{i}(idx, 1), 2); % 默认使用质量（保留两位小数）
            end
            
            % 参数值
            variables = pareto_variables{i};
            solutions = pareto_solutions{i};
            for k = 1:size(variables, 2)
                if isstruct(variables(idx, k))
                    % 如果是结构体，转换为字符串或提取需要的字段
                    best_solutions_data{row, 3+k} = 'struct';
                elseif k == 1 || k == 4 || k == 8  % 模数和角度，显示小数点
                    best_solutions_data{row, 3+k} = variables(idx, k);
                elseif k >= 18 && k <= 23  % 变位系数，保留四位小数
                    best_solutions_data{row, 3+k} = round(variables(idx, k), 4);
                else  % 齿数和齿宽系数，显示整数
                    best_solutions_data{row, 3+k} = round(variables(idx, k));
                end
            end
            
            % 目标函数值（全部）
            best_solutions_data{row, 4+num_params} = round(solutions(idx, 1), 2); % 质量（保留两位小数）
            if size(solutions, 2) >= 3
                best_solutions_data{row, 5+num_params} = round(-solutions(idx, 3), 3); % 接触安全系数（保留三位小数）
            else
                best_solutions_data{row, 5+num_params} = NaN; % 无数据
            end
            if size(solutions, 2) >= 2
                best_solutions_data{row, 6+num_params} = round(-solutions(idx, 2), 3); % 弯曲安全系数（保留三位小数）
            else
                best_solutions_data{row, 6+num_params} = NaN; % 无数据
            end
            
            row = row + 1;
        end
    end
    
    % 首先遍历所有算法，找出满足安全系数要求的全局最优解（质量最小）
    for i = 1:num_algs
        if isempty(pareto_variables{i}) || isempty(pareto_solutions{i})
            continue;
        end
        
        variables = pareto_variables{i};
        solutions = pareto_solutions{i};
        
        % 对每个算法的每个解进行评分
        for j = 1:size(variables, 1)
            % 计算传动比
            z1 = round(variables(j, 2));
            z2 = round(variables(j, 3));
            zs2 = round(variables(j, 5));
            zp2 = round(variables(j, 6));
            zs3 = round(variables(j, 9));
            zp3 = round(variables(j, 10));
            
            % 检查有效性
            if z1 <= 0 || z2 <= 0 || zs2 <= 0 || zp2 <= 0 || zs3 <= 0 || zp3 <= 0
                continue;
            end
            
            zr2 = zs2 + 2 * zp2;
            zr3 = zs3 + 2 * zp3;
            i1 = z2 / z1;
            i2 = 1 + zr2/zs2;
            i3 = 1 + zr3/zs3;
            total_ratio = i1 * i2 * i3;
            ratio_error = abs(total_ratio - target_ratio) / target_ratio * 100;
            
            % 提取安全系数 - 显示实际值
            mass = solutions(j, 1);
            if size(solutions, 2) >= 2
                bend_safety = -solutions(j, 2);
            else
                bend_safety = 0.0; % 如果没有数据则为0
            end
            if size(solutions, 2) >= 3
                contact_safety = -solutions(j, 3);
            else
                contact_safety = 0.0; % 如果没有数据则为0
            end
            
            % 检查是否满足安全系数要求
            if bend_safety >= safety_req && contact_safety >= safety_req && ratio_error <= 2.0
                % 找出质量最小的解
                if mass < min_global_mass || (mass == min_global_mass && ratio_error < min_global_error)
                    min_global_mass = mass;
                    min_global_error = ratio_error;
                    global_best_alg_idx = i;
                    global_best_sol_idx = j;
                end
            end
        end
    end
    
    % 如果没有找到满足要求的解，选择质量最小的解
    if global_best_alg_idx == -1
        min_global_mass = Inf;
        
        for i = 1:num_algs
            if isempty(pareto_variables{i}) || isempty(pareto_solutions{i})
                continue;
            end
            
            variables = pareto_variables{i};
            solutions = pareto_solutions{i};
            
            for j = 1:size(variables, 1)
                mass = solutions(j, 1);
                
                if mass < min_global_mass
                    min_global_mass = mass;
                    global_best_alg_idx = i;
                    global_best_sol_idx = j;
                end
            end
        end
    end
    
    % 继续原来的代码...
    
    % 创建表格列名
    solutions_cols = [{'算法', '解类型', '最优值'}, param_names, {'总质量 (kg)', '接触安全系数', '弯曲安全系数'}];
    metrics_cols = [{'算法'}, metrics];
    
    % 确保solutions_cols的长度与best_solutions_data的列数匹配
    num_cols = size(best_solutions_data, 2);
    if length(solutions_cols) ~= num_cols
        % 如果列名数量与数据列数不匹配，调整列名
        if length(solutions_cols) < num_cols
            for i = (length(solutions_cols) + 1):num_cols
                solutions_cols{i} = ['列' num2str(i)];
            end
        else
            solutions_cols = solutions_cols(1:num_cols);
        end
    end
    
    % 创建表格
    best_solutions_table = cell2table(best_solutions_data, 'VariableNames', solutions_cols);
    metrics_table = cell2table(metrics_data, 'VariableNames', metrics_cols);
    
    % 保存表格
    writetable(best_solutions_table, fullfile(results_dir, '算法最优解综合表.xlsx'));
    writetable(metrics_table, fullfile(results_dir, '算法性能指标综合表.xlsx'));
    
    % 创建HTML格式的表格（美观且易于查看）
    html_file = fullfile(results_dir, '优化结果综合报告.html');
    fid = fopen(html_file, 'w');
    
    % 优化HTML头部，增强页面整体风格
    fprintf(fid, '<!DOCTYPE html>\n');
    fprintf(fid, '<html lang="zh-CN">\n');
    fprintf(fid, '<head>\n');
    fprintf(fid, '<meta charset="UTF-8">\n');
    fprintf(fid, '<meta name="viewport" content="width=device-width, initial-scale=1.0">\n');
    fprintf(fid, '<title>三级减速机齿轮传动系统多目标优化结果</title>\n');
    fprintf(fid, '<style>\n');
    fprintf(fid, '@import url("https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap");\n');
    fprintf(fid, 'body { font-family: "Microsoft YaHei", "Roboto", Arial, sans-serif; margin: 0; color: #333; background-color: #f5f7fa; line-height: 1.5; }\n');
    fprintf(fid, 'h1, h2, h3 { color: #2c3e50; margin-top: 0.5em; margin-bottom: 0.5em; }\n');
    fprintf(fid, 'h1 { text-align: center; padding: 15px 10px; background: linear-gradient(135deg, #3498db, #2c3e50); color: white; font-weight: 500; letter-spacing: 1px; box-shadow: 0 2px 6px rgba(0,0,0,0.1); }\n');
    fprintf(fid, 'h2 { border-bottom: 2px solid #3498db; padding-bottom: 5px; font-weight: 500; }\n');
    fprintf(fid, 'h3 { border-bottom: 1px solid #bdc3c7; padding-bottom: 3px; }\n');
    fprintf(fid, '.main-container { max-width: 1100px; margin: 0 auto; padding: 0 10px 20px; }\n');
    
    % 紧凑型表格样式 - 添加改进的表头边框样式
    fprintf(fid, 'table { border-collapse: collapse; width: 100%%; margin-bottom: 15px; border: 1px solid #dee2e6; }\n');
    fprintf(fid, 'th, td { padding: 6px 8px; text-align: center; border: 1px solid #e0e6ed; font-size: 0.9em; }\n');
    fprintf(fid, 'th { background: linear-gradient(to bottom, #5a7b9c, #7b96ae); color: white; font-weight: 500; }\n');
    fprintf(fid, 'tr[style*="background-color:#6ba3c7"] th { background: linear-gradient(to bottom, #6ba3c7, #5a92b5); font-size: 1em; }\n');
    fprintf(fid, 'tr:nth-child(even) { background-color: #f8fafc; }\n');
    fprintf(fid, 'tr:hover { background-color: #eef7fb; transition: all 0.2s ease; }\n');
    fprintf(fid, '.table-responsive { overflow-x: auto; margin-bottom: 15px; }\n');
    
    % 添加表头分组样式，确保白线对齐
    fprintf(fid, '.gear-group-1:last-child, th:nth-child(10) { border-right: 1px solid #ddd !important; }\n');
    fprintf(fid, '.gear-group-2:last-child, th:nth-child(23) { border-right: 1px solid #ddd !important; }\n');
    fprintf(fid, '.gear-group-3:last-child, th:nth-child(36) { border-right: 1px solid #ddd !important; }\n');
    fprintf(fid, '.goal-group:last-child, th:nth-child(39) { border-right: 1px solid #ddd !important; }\n');
    
    % 压缩表头分类行，减少高度
    fprintf(fid, '.table-category { background-color: #6ba3c7; color: white; font-weight: bold; text-align: center; padding: 5px; }\n');
    fprintf(fid, '.table-section { background-color: #f0f5f9; font-weight: bold; }\n');
    fprintf(fid, '/* 只使用一种蓝色背景标记最优解 */\n');
    
    % 其他辅助样式
    fprintf(fid, '.compact-container { background-color: white; padding: 15px; border-radius: 5px; box-shadow: 0 1px 3px rgba(0,0,0,0.08); margin-bottom: 15px; }\n');
    fprintf(fid, '.explanation { background-color: white; border-left: 4px solid #3498db; padding: 10px 15px; margin: 10px 0; }\n');
    fprintf(fid, '.explanation h4 { margin-top: 0; color: #2980b9; font-weight: 500; }\n');
    fprintf(fid, '.explanation p { margin-bottom: 8px; }\n');
    fprintf(fid, '.explanation ul, .explanation ol { padding-left: 20px; margin-bottom: 0; }\n');
    
    % 其他原有样式...
    fprintf(fid, '</style>\n');
    fprintf(fid, '</head>\n');
    fprintf(fid, '<body>\n');
    fprintf(fid, '<h1 style="position: relative;">三级减速机齿轮传动系统多目标优化结果<span style="position: absolute; right: 20px; bottom: 15px; font-size: 14px; font-weight: normal; opacity: 0.9;">生成时间: %s</span></h1>\n', datestr(now, 'yyyy-mm-dd HH:MM:SS'));
    fprintf(fid, '<div class="main-container">\n');


    
    % 添加优化概览部分
    fprintf(fid, '<div class="container">\n');
    fprintf(fid, '<h2 style="margin-top:0;">优化概览</h2>\n');
    fprintf(fid, '<div class="flex-container">\n');
    fprintf(fid, '<div class="flex-item">\n');
    fprintf(fid, '<div class="info-box">\n');
    fprintf(fid, '<div class="info-icon">🔍</div>\n');
    fprintf(fid, '<div class="info-content">\n');
    fprintf(fid, '<div class="info-title">优化目标</div>\n');
    fprintf(fid, '<div>三目标优化：<span class="tag">系统质量</span><span class="tag">弯曲安全系数</span><span class="tag">接触安全系数</span></div>\n');
    fprintf(fid, '</div>\n');
    fprintf(fid, '</div>\n');

    fprintf(fid, '<div class="info-box">\n');
    fprintf(fid, '<div class="info-icon">⚙️</div>\n');
    fprintf(fid, '<div class="info-content">\n');
    fprintf(fid, '<div class="info-title">传动参数</div>\n');
    fprintf(fid, '<div>总传动比: <strong>%.3f</strong><span class="ratio-warning">误差不超过±2%%</span></div>\n', input_speed / output_speed);
    fprintf(fid, '</div>\n');
    fprintf(fid, '</div>\n');
    fprintf(fid, '</div>\n');

    fprintf(fid, '<div class="flex-item">\n');
    fprintf(fid, '<div class="info-box">\n');
    fprintf(fid, '<div class="info-icon">🧮</div>\n');
    fprintf(fid, '<div class="info-content">\n');
    fprintf(fid, '<div class="info-title">算法比较</div>\n');
    fprintf(fid, '<div>共测试了 <strong>%d</strong> 种多目标优化算法的性能</div>\n', length(alg_names));
    fprintf(fid, '</div>\n');
    fprintf(fid, '</div>\n');

    fprintf(fid, '<div class="info-box">\n');
    fprintf(fid, '<div class="info-icon">📊</div>\n');
    fprintf(fid, '<div class="info-content">\n');
    fprintf(fid, '<div class="info-title">解方案</div>\n');
    fprintf(fid, '<div>从Pareto前沿获得了多个最优解方案，允许根据需求选择</div>\n');
    fprintf(fid, '</div>\n');
    fprintf(fid, '</div>\n');
    fprintf(fid, '</div>\n');
    fprintf(fid, '</div>\n');

    % 添加参数卡片展示
    fprintf(fid, '<div class="flex-container" style="margin-top:20px;">\n');
    fprintf(fid, '<div class="flex-item data-card">\n');
    fprintf(fid, '<div class="card-label">输入转速</div>\n');
    fprintf(fid, '<div class="card-value">%.1f rpm</div>\n', input_speed);
    fprintf(fid, '</div>\n');

    fprintf(fid, '<div class="flex-item data-card">\n');
    fprintf(fid, '<div class="card-label">输出转速</div>\n');
    fprintf(fid, '<div class="card-value">%.2f rpm</div>\n', output_speed);
    fprintf(fid, '</div>\n');

    fprintf(fid, '<div class="flex-item data-card">\n');
    fprintf(fid, '<div class="card-label">设计寿命</div>\n');
    fprintf(fid, '<div class="card-value">50,000 小时</div>\n');
    fprintf(fid, '</div>\n');

    fprintf(fid, '<div class="flex-item data-card">\n');
    fprintf(fid, '<div class="card-label">最小安全系数要求</div>\n');
    fprintf(fid, '<div class="card-value">1.2</div>\n');
    fprintf(fid, '</div>\n');

    fprintf(fid, '</div>\n');
    fprintf(fid, '</div>\n');

    % 原有的参数说明表格和其他内容
    fprintf(fid, '<div class="container">\n');
    fprintf(fid, '<h2>设计参数说明</h2>\n');
    fprintf(fid, '<div class="table-responsive">\n');
    
    % 创建参数说明表格 - 使用更现代的样式
    fprintf(fid, '<table class="param-table">\n');
    fprintf(fid, '<tr><th class="param-symbol">符号</th><th class="param-name">名称</th><th class="param-unit">单位</th><th class="param-desc">描述</th><th class="param-symbol">符号</th><th class="param-name">名称</th><th class="param-unit">单位</th><th class="param-desc">描述</th></tr>\n');
    
    % 一级传动参数
    fprintf(fid, '<tr><td colspan="8" class="param-category">一级传动参数（平行轴齿轮传动）</td></tr>\n');
    fprintf(fid, '<tr><td class="param-symbol">m<sub>1</sub></td><td class="param-name">一级模数</td><td class="param-unit">mm</td><td class="param-desc">一级传动齿轮的模数，决定齿轮尺寸大小</td><td class="param-symbol">z<sub>1</sub></td><td class="param-name">一级小齿轮齿数</td><td class="param-unit">-</td><td class="param-desc">输入轴小齿轮的齿数</td></tr>\n');
    fprintf(fid, '<tr><td class="param-symbol">z<sub>2</sub></td><td class="param-name">一级大齿轮齿数</td><td class="param-unit">-</td><td class="param-desc">一级传动大齿轮的齿数，决定一级传动比</td><td class="param-symbol">β<sub>1</sub></td><td class="param-name">一级螺旋角</td><td class="param-unit">度</td><td class="param-desc">一级传动螺旋角，影响啮合性能</td></tr>\n');
    fprintf(fid, '<tr><td class="param-symbol">x<sub>1</sub></td><td class="param-name">一级小齿轮变位系数</td><td class="param-unit">-</td><td class="param-desc">一级小齿轮的变位系数，影响齿轮强度</td><td class="param-symbol">x<sub>2</sub></td><td class="param-name">一级大齿轮变位系数</td><td class="param-unit">-</td><td class="param-desc">一级大齿轮的变位系数，影响齿轮强度</td></tr>\n');
    fprintf(fid, '<tr><td class="param-symbol">α<sub>1</sub></td><td class="param-name">一级压力角</td><td class="param-unit">度</td><td class="param-desc">一级齿轮啮合的压力角，影响承载能力</td><td class="param-symbol">k<sub>h1</sub></td><td class="param-name">一级齿宽系数</td><td class="param-unit">-</td><td class="param-desc">一级齿宽与分度圆直径的比值</td></tr>\n');
    
    % 二级行星传动参数
    fprintf(fid, '<tr><td colspan="8" class="param-category category-border">二级传动参数（行星齿轮传动）</td></tr>\n');
    fprintf(fid, '<tr><td class="param-symbol">m<sub>n2</sub></td><td class="param-name">二级模数</td><td class="param-unit">mm</td><td class="param-desc">二级行星传动的法向模数</td><td class="param-symbol">z<sub>s2</sub></td><td class="param-name">二级太阳轮齿数</td><td class="param-unit">-</td><td class="param-desc">二级行星系太阳轮齿数</td></tr>\n');
    fprintf(fid, '<tr><td class="param-symbol">z<sub>p2</sub></td><td class="param-name">二级行星轮齿数</td><td class="param-unit">-</td><td class="param-desc">二级行星系行星轮齿数</td><td class="param-symbol">z<sub>r2</sub></td><td class="param-name">二级内齿圈齿数</td><td class="param-unit">-</td><td class="param-desc">二级内齿圈齿数，z<sub>r2</sub>=z<sub>s2</sub>+2z<sub>p2</sub></td></tr>\n');
    fprintf(fid, '<tr><td class="param-symbol">n<sub>2</sub></td><td class="param-name">二级行星轮数量</td><td class="param-unit">-</td><td class="param-desc">二级行星系中行星轮的数量</td><td class="param-symbol">β<sub>2</sub></td><td class="param-name">二级螺旋角</td><td class="param-unit">度</td><td class="param-desc">二级行星系螺旋角</td></tr>\n');
    fprintf(fid, '<tr><td class="param-symbol">x<sub>s2</sub></td><td class="param-name">二级太阳轮变位系数</td><td class="param-unit">-</td><td class="param-desc">二级太阳轮的变位系数</td><td class="param-symbol">x<sub>p2</sub></td><td class="param-name">二级行星轮变位系数</td><td class="param-unit">-</td><td class="param-desc">二级行星轮的变位系数</td></tr>\n');
    fprintf(fid, '<tr><td class="param-symbol">x<sub>r2</sub></td><td class="param-name">二级内齿圈变位系数</td><td class="param-unit">-</td><td class="param-desc">二级内齿圈的变位系数，x<sub>r2</sub>=-x<sub>s2</sub>-x<sub>p2</sub></td><td class="param-symbol">k<sub>h2</sub></td><td class="param-name">二级齿宽系数</td><td class="param-unit">-</td><td class="param-desc">二级行星系齿宽系数，影响承载能力</td></tr>\n');
    fprintf(fid, '<tr><td class="param-symbol">α<sub>2</sub></td><td class="param-name">二级压力角</td><td class="param-unit">度</td><td class="param-desc">二级齿轮啮合的压力角，影响承载能力</td><td class="param-symbol"></td><td class="param-name"></td><td class="param-unit"></td><td class="param-desc"></td></tr>\n');
    
    % 三级行星传动参数
    fprintf(fid, '<tr><td colspan="8" class="param-category category-border">三级传动参数（行星齿轮传动）</td></tr>\n');
    fprintf(fid, '<tr><td class="param-symbol">m<sub>n3</sub></td><td class="param-name">三级模数</td><td class="param-unit">mm</td><td class="param-desc">三级行星传动的法向模数</td><td class="param-symbol">z<sub>s3</sub></td><td class="param-name">三级太阳轮齿数</td><td class="param-unit">-</td><td class="param-desc">三级行星系太阳轮齿数</td></tr>\n');
    fprintf(fid, '<tr><td class="param-symbol">z<sub>p3</sub></td><td class="param-name">三级行星轮齿数</td><td class="param-unit">-</td><td class="param-desc">三级行星系行星轮齿数</td><td class="param-symbol">z<sub>r3</sub></td><td class="param-name">三级内齿圈齿数</td><td class="param-unit">-</td><td class="param-desc">三级内齿圈齿数，z<sub>r3</sub>=z<sub>s3</sub>+2z<sub>p3</sub></td></tr>\n');
    fprintf(fid, '<tr><td class="param-symbol">n<sub>3</sub></td><td class="param-name">三级行星轮数量</td><td class="param-unit">-</td><td class="param-desc">三级行星系中行星轮的数量</td><td class="param-symbol">β<sub>3</sub></td><td class="param-name">三级螺旋角</td><td class="param-unit">度</td><td class="param-desc">三级行星系螺旋角</td></tr>\n');
    fprintf(fid, '<tr><td class="param-symbol">x<sub>s3</sub></td><td class="param-name">三级太阳轮变位系数</td><td class="param-unit">-</td><td class="param-desc">三级太阳轮的变位系数</td><td class="param-symbol">x<sub>p3</sub></td><td class="param-name">三级行星轮变位系数</td><td class="param-unit">-</td><td class="param-desc">三级行星轮的变位系数</td></tr>\n');
    fprintf(fid, '<tr><td class="param-symbol">x<sub>r3</sub></td><td class="param-name">三级内齿圈变位系数</td><td class="param-unit">-</td><td class="param-desc">三级内齿圈的变位系数，x<sub>r3</sub>=-x<sub>s3</sub>-x<sub>p3</sub></td><td class="param-symbol">k<sub>h3</sub></td><td class="param-name">三级齿宽系数</td><td class="param-unit">-</td><td class="param-desc">三级行星系齿宽系数，影响承载能力</td></tr>\n');
    fprintf(fid, '<tr><td class="param-symbol">α<sub>3</sub></td><td class="param-name">三级压力角</td><td class="param-unit">度</td><td class="param-desc">三级齿轮啮合的压力角，影响承载能力</td><td class="param-symbol"></td><td class="param-name"></td><td class="param-unit"></td><td class="param-desc"></td></tr>\n');
    
    fprintf(fid, '</table>\n');
    
    % 添加安全系数符号表格 - 仅包含符号和名称，放置在设计参数表下方
    fprintf(fid, '<h4 style="margin-top: 20px;">安全系数参数说明</h4>\n');
    fprintf(fid, '<table class="param-table">\n');
    fprintf(fid, '<tr><th class="param-symbol">符号</th><th class="param-name">名称</th><th class="param-symbol">符号</th><th class="param-name">名称</th></tr>\n');
    
    % 一级传动安全系数
    fprintf(fid, '<tr><td colspan="4" class="param-category">一级传动安全系数（平行齿轮传动）</td></tr>\n');
    fprintf(fid, '<tr><td class="param-symbol">SH</td><td class="param-name">接触安全系数</td><td class="param-symbol">SF1</td><td class="param-name">小齿轮弯曲安全系数</td></tr>\n');
    fprintf(fid, '<tr><td class="param-symbol">SF2</td><td class="param-name">大齿轮弯曲安全系数</td><td class="param-symbol"></td><td class="param-name"></td></tr>\n');
    
    % 二级传动安全系数
    fprintf(fid, '<tr><td colspan="4" class="param-category">二级传动安全系数（行星齿轮传动）</td></tr>\n');
    fprintf(fid, '<tr><td class="param-symbol">SHsps</td><td class="param-name">太阳轮-行星轮接触安全系数(sun)</td><td class="param-symbol">SHspp</td><td class="param-name">太阳轮-行星轮接触安全系数(planet)</td></tr>\n');
    fprintf(fid, '<tr><td class="param-symbol">SFsps</td><td class="param-name">太阳轮-行星轮弯曲安全系数(sun)</td><td class="param-symbol">SFspp</td><td class="param-name">太阳轮-行星轮弯曲安全系数(planet)</td></tr>\n');
    fprintf(fid, '<tr><td class="param-symbol">SHprr</td><td class="param-name">行星轮-内齿圈接触安全系数(ring)</td><td class="param-symbol">SHprp</td><td class="param-name">行星轮-内齿圈接触安全系数(planet)</td></tr>\n');
    fprintf(fid, '<tr><td class="param-symbol">SFprr</td><td class="param-name">行星轮-内齿圈弯曲安全系数(ring)</td><td class="param-symbol">SFprp</td><td class="param-name">行星轮-内齿圈弯曲安全系数(planet)</td></tr>\n');
    
    % 三级传动安全系数
    fprintf(fid, '<tr><td colspan="4" class="param-category">三级传动安全系数（行星齿轮传动）</td></tr>\n');
    fprintf(fid, '<tr><td class="param-symbol">SHsps</td><td class="param-name">太阳轮-行星轮接触安全系数(sun)</td><td class="param-symbol">SHspp</td><td class="param-name">太阳轮-行星轮接触安全系数(planet)</td></tr>\n');
    fprintf(fid, '<tr><td class="param-symbol">SFsps</td><td class="param-name">太阳轮-行星轮弯曲安全系数(sun)</td><td class="param-symbol">SFspp</td><td class="param-name">太阳轮-行星轮弯曲安全系数(planet)</td></tr>\n');
    fprintf(fid, '<tr><td class="param-symbol">SHprr</td><td class="param-name">行星轮-内齿圈接触安全系数(ring)</td><td class="param-symbol">SHprp</td><td class="param-name">行星轮-内齿圈接触安全系数(planet)</td></tr>\n');
    fprintf(fid, '<tr><td class="param-symbol">SFprr</td><td class="param-name">行星轮-内齿圈弯曲安全系数(ring)</td><td class="param-symbol">SFprp</td><td class="param-name">行星轮-内齿圈弯曲安全系数(planet)</td></tr>\n');
    
    fprintf(fid, '</table>\n');
    fprintf(fid, '</div>\n');
    fprintf(fid, '</div>\n');
    
    % 添加优化目标说明
    fprintf(fid, '<h2>优化目标说明</h2>\n');
    fprintf(fid, '<div class="container explanation" style="border-left: 5px solid #2ecc71;">\n');
    fprintf(fid, '<h4>三个优化目标</h4>\n');
    fprintf(fid, '<p>本优化问题同时考虑三个优化目标，这三个目标通常相互冲突，需要通过多目标优化算法寻找最佳平衡：</p>\n');
    fprintf(fid, '<ul style="list-style-type: none; padding-left: 5px;">\n');
    fprintf(fid, '<li><span style="display:inline-block; width:24px; height:24px; background-color:#3498db; color:white; border-radius:50%%; text-align:center; line-height:24px; margin-right:10px; font-weight:bold;">1</span><strong>总质量(kg)</strong>：减速机齿轮传动系统的总质量，越小越好。轻量化设计可以降低成本和能耗。</li>\n');
    fprintf(fid, '<li style="margin-top:10px;"><span style="display:inline-block; width:24px; height:24px; background-color:#e74c3c; color:white; border-radius:50%%; text-align:center; line-height:24px; margin-right:10px; font-weight:bold;">2</span><strong>接触安全系数</strong>：齿轮抵抗接触疲劳的能力指标，越大越好。该指标反映齿轮在载荷作用下抵抗齿面点蚀的能力。</li>\n');
    fprintf(fid, '<li style="margin-top:10px;"><span style="display:inline-block; width:24px; height:24px; background-color:#2ecc71; color:white; border-radius:50%%; text-align:center; line-height:24px; margin-right:10px; font-weight:bold;">3</span><strong>弯曲安全系数</strong>：齿轮抵抗弯曲破坏的能力指标，越大越好。该指标反映齿轮在载荷作用下抵抗齿根断裂的能力。</li>\n');
    fprintf(fid, '</ul>\n');
    fprintf(fid, '<p>优化算法会寻找这三个目标的最佳平衡点，形成Pareto最优解集。用户可以根据实际需求从Pareto前沿中选择合适的解。</p>\n');
    fprintf(fid, '<div class="result-summary">\n');
    fprintf(fid, '<p><strong>目标和约束的优先级</strong>：</p>\n');
    fprintf(fid, '<ol>\n');
    fprintf(fid, '<li>满足传动比要求（硬约束，误差≤2%%）</li>\n');
    fprintf(fid, '<li>保持安全系数≥1.2（满足安全要求）</li>\n');
    fprintf(fid, '<li>在满足上述条件下，尽量降低系统总质量</li>\n');
    fprintf(fid, '</ol>\n');
    fprintf(fid, '</div>\n');
    fprintf(fid, '</div>\n');
    
    % 美化传动比要求约束条件部分
    fprintf(fid, '<h2>传动比要求（硬约束条件）</h2>\n');
    fprintf(fid, '<div class="container explanation" style="border-left: 5px solid #e74c3c;">\n');
    fprintf(fid, '<h4>传动比计算公式与约束</h4>\n');
    fprintf(fid, '<p>目标总传动比：<strong style="font-size:1.3em; color:#e74c3c;">%.3f</strong>（必须满足，最高优先级约束）</p>\n', input_speed / output_speed);

    fprintf(fid, '<div style="background-color: #fdf2e9; padding: 15px; border-radius: 5px; margin: 10px 0;">\n');
    fprintf(fid, '<p>传动比计算公式：</p>\n');
    fprintf(fid, '<ul>\n');
    fprintf(fid, '<li>一级传动比：i<sub>1</sub> = z<sub>2</sub>/z<sub>1</sub></li>\n');
    fprintf(fid, '<li>二级传动比：i<sub>2</sub> = 1 + z<sub>r2</sub>/z<sub>s2</sub> = 1 + (z<sub>s2</sub>+2z<sub>p2</sub>)/z<sub>s2</sub></li>\n');
    fprintf(fid, '<li>三级传动比：i<sub>3</sub> = 1 + z<sub>r3</sub>/z<sub>s3</sub> = 1 + (z<sub>s3</sub>+2z<sub>p3</sub>)/z<sub>s3</sub></li>\n');
    fprintf(fid, '<li>总传动比：i<sub>总</sub> = i<sub>1</sub> × i<sub>2</sub> × i<sub>3</sub></li>\n');
    fprintf(fid, '</ul>\n');
    fprintf(fid, '</div>\n');

    fprintf(fid, '<p class="tooltip"><strong style="color:#e74c3c;">优化过程中，传动比是一个必须满足的硬约束条件</strong><span class="tooltiptext">任何不满足目标传动比要求的解决方案都将被视为无效解</span>，目标传动比误差不允许超过±2%%。</p>\n');
    fprintf(fid, '</div>\n');
    
    % 添加参数标准化说明
    fprintf(fid, '<div class="explanation" style="border-left: 5px solid #3498db;">\n');
    fprintf(fid, '<h4>参数标准化约束</h4>\n');
    fprintf(fid, '<p>根据实际工程要求，以下参数已标准化处理：</p>\n');
    
    fprintf(fid, '<table class="param-table" style="width:95%%; margin:15px auto;">\n');
    fprintf(fid, '<tr style="background-color:#5a7b9c; color:white;">\n');
    fprintf(fid, '<th>参数</th><th>约束条件</th><th>说明</th>\n');
    fprintf(fid, '</tr>\n');

    % 传动比约束
    fprintf(fid, '<tr style="background-color:#eaf2f8;">\n');
    fprintf(fid, '<td style="font-weight:bold;">传动比</td>\n');
    fprintf(fid, '<td>总传动比: %.3f±%.1f%%<br>一级传动比: %.1f-%.1f<br>二级传动比: %.1f-%.1f<br>三级传动比: %.1f-%.1f</td>\n', target_ratio, 2.0, 2.5, 3.5, 3.0, 8.0, 3.0, 8.0);
    fprintf(fid, '<td>总传动比是硬约束，误差不得超过±2%%<br>三级传动比不大于二级传动比</td>\n');
    fprintf(fid, '</tr>\n');
    
    % 压力角约束
    fprintf(fid, '<tr>\n');
    fprintf(fid, '<td style="font-weight:bold;">压力角</td>\n');
    fprintf(fid, '<td>一级和二级固定为20°<br>三级可选20°或25°</td>\n');
    fprintf(fid, '<td>一级平行轴系和二级行星轮系压力角固定为20°，三级行星轮系压力角只能是20°或25°</td>\n');
    fprintf(fid, '</tr>\n');
    
    % 螺旋角约束
    fprintf(fid, '<tr style="background-color:#eaf2f8;">\n');
    fprintf(fid, '<td style="font-weight:bold;">螺旋角</td>\n');
    fprintf(fid, '<td>一级：8°-13°（整数）<br>二级和三级：0°</td>\n');
    fprintf(fid, '<td>三级齿轮系当中只有一级可以是斜齿轮，螺旋角范围为8-13度的整数值；二级和三级行星轮系均为直齿轮</td>\n');
    fprintf(fid, '</tr>\n');
    
    % 齿数约束
    fprintf(fid, '<tr>\n');
    fprintf(fid, '<td style="font-weight:bold;">齿数</td>\n');
    fprintf(fid, '<td>z₁ ≥ 17<br>z₂ ≥ 17<br>zs₂ ≥ 17<br>zp₂ ≥ 17<br>zs₃ ≥ 17<br>zp₃ ≥ 17</td>\n');
    fprintf(fid, '<td>所有齿数必须为整数，且满足最小齿数要求<br>一级大齿轮齿数不超过100<br>内齿圈齿数不超过120</td>\n');
    fprintf(fid, '</tr>\n');
    
    % 行星轮数量约束
    fprintf(fid, '<tr style="background-color:#eaf2f8;">\n');
    fprintf(fid, '<td style="font-weight:bold;">行星轮数量</td>\n');
    fprintf(fid, '<td>3、4或5</td>\n');
    fprintf(fid, '<td>二级和三级行星轮数量仅限于3、4或5三个离散值，符合工程实际</td>\n');
    fprintf(fid, '</tr>\n');
    
    % 变位系数约束
    fprintf(fid, '<tr>\n');
    fprintf(fid, '<td style="font-weight:bold;">变位系数</td>\n');
    fprintf(fid, '<td>x₁: 0.3-0.7<br>x₂: 0.2-0.5<br>xs₂: 0.3-0.6<br>xp₂: 0.2-0.5<br>xs₃: 0.3-0.6<br>xp₃: 0.2-0.5<br>xr₂, xr₃ ≥ -0.8</td>\n');
    fprintf(fid, '<td>变位系数精确到小数点后4位<br>内齿圈变位系数xr = -xs - xp</td>\n');
    fprintf(fid, '</tr>\n');

    % 模数约束
    fprintf(fid, '<tr style="background-color:#eaf2f8;">\n');
    fprintf(fid, '<td style="font-weight:bold;">模数</td>\n');
    fprintf(fid, '<td>20°压力角：[5,6,7,8,9,10,11,12,13,14,15,16,17,18,20]<br>25°压力角：[7,9,10,11,12,13,16,17,18,20]</td>\n');
    fprintf(fid, '<td>模数使用标准离散值，根据压力角选择合适的标准模数</td>\n');
    fprintf(fid, '</tr>\n');

    % 齿宽系数约束
    fprintf(fid, '<tr>\n');
    fprintf(fid, '<td style="font-weight:bold;">齿宽系数</td>\n');
    fprintf(fid, '<td>k_h₁: 0.28-0.4<br>k_h₂, k_h₃: 0.6-1.0</td>\n');
    fprintf(fid, '<td>齿宽 = 齿宽系数 × 实际中心距<br>齿宽系数严格限制上下限，避免出现异常值<br>实际中心距考虑了变位系数和螺旋角的影响</td>\n');
    fprintf(fid, '</tr>\n');

    % 行星轮系几何约束
    fprintf(fid, '<tr style="background-color:#eaf2f8;">\n');
    fprintf(fid, '<td style="font-weight:bold;">行星轮系几何约束</td>\n');
    fprintf(fid, '<td>邻接条件：2r_ac < L_c<br>安装条件：(zs + zr) / np 为整数<br>同心条件：角度变位满足同心要求</td>\n');
    fprintf(fid, '<td>行星轮之间的最小间隙≥0.5模数<br>r_ac为行星轮齿顶圆半径<br>L_c为相邻行星轮中心距</td>\n');
    fprintf(fid, '</tr>\n');

    % 安全系数约束
    fprintf(fid, '<tr>\n');
    fprintf(fid, '<td style="font-weight:bold;">安全系数</td>\n');
    fprintf(fid, '<td>弯曲安全系数 ≥ 1.2<br>接触安全系数 ≥ 1.2</td>\n');
    fprintf(fid, '<td>齿轮的弯曲强度和接触强度安全系数应不小于1.2</td>\n');
    fprintf(fid, '</tr>\n');
    
    fprintf(fid, '</table>\n');
    fprintf(fid, '<p>注意：表格中显示的参数值已根据上述约束进行了标准化处理，可能与原始优化结果略有差异。</p>\n');
    fprintf(fid, '</div>\n');
    
    % 添加最佳解表格展示
    fprintf(fid, '<h2 style="background-color:#3498db; color:white; padding:10px; margin-bottom:0; border-top-left-radius:8px; border-top-right-radius:8px;">最佳解方案</h2>\n');
    fprintf(fid, '<div class="container" style="border:1px solid #ddd; border-top:none; padding:15px; margin-top:0; margin-bottom:20px; border-bottom-left-radius:8px; border-bottom-right-radius:8px; box-shadow:0 2px 5px rgba(0,0,0,0.1);">\n');
    
    % 查找最佳解
    best_alg_idx = -1;
    best_sol_idx = -1;
    best_ratio_error = Inf;
    best_safety_factor = 0;
    best_mass = Inf;
    
    % 先保存排序前的原始数据索引映射，以便后续能找回原始索引
    index_maps = cell(length(alg_names), 1);
    
    if best_alg_idx > 0
        fprintf(fid, '<p style="font-size:16px; color:#2980b9; margin-bottom:15px;">以下是<strong>%s</strong>算法中找到的最佳解决方案：</p>\n', alg_names{best_alg_idx});
    end
    
    for alg_idx = 1:length(alg_names)
        if isempty(pareto_variables{alg_idx}) || isempty(pareto_solutions{alg_idx})
            continue;
        end
        
        % 创建索引映射，保存排序前的索引
        index_maps{alg_idx} = 1:size(pareto_variables{alg_idx}, 1);
        
        variables = pareto_variables{alg_idx};
        solutions = pareto_solutions{alg_idx};
        
        % 计算每个解的传动比误差，用于排序
        ratio_errors = zeros(size(variables, 1), 1);
        for j = 1:size(variables, 1)
            % 计算传动比
            z1 = round(variables(j, 2));
            z2 = round(variables(j, 3));
            zs2 = round(variables(j, 5));
            zp2 = round(variables(j, 6));
            zs3 = round(variables(j, 9));
            zp3 = round(variables(j, 10));
            
            if z1 <= 0 || z2 <= 0 || zs2 <= 0 || zp2 <= 0 || zs3 <= 0 || zp3 <= 0
                ratio_errors(j) = Inf;
                continue;
            end
            
            zr2 = zs2 + 2 * zp2;
            zr3 = zs3 + 2 * zp3;
            i1 = z2 / z1;
            i2 = 1 + zr2/zs2;
            i3 = 1 + zr3/zs3;
            total_ratio = i1 * i2 * i3;
            target_ratio = input_speed / output_speed;
            ratio_errors(j) = abs(total_ratio - target_ratio) / target_ratio * 100;
        end
        
        % 检查安全系数是否满足要求
        safety_req = 1.2; % 安全系数要求
        is_safe = (-solutions(:, 2) >= safety_req) & (-solutions(:, 3) >= safety_req);
        
        % 先检查是否有同时满足传动比误差和安全系数要求的解
        for j = 1:size(variables, 1)
            % 只考虑同时满足传动比误差和安全系数要求的解
            if ratio_errors(j) <= 2.0 && is_safe(j)
                bend_safety = -solutions(j, 2); % 显示实际安全系数值
                contact_safety = -solutions(j, 3); % 显示实际安全系数值
                mass = solutions(j, 1);
                min_safety = min(bend_safety, contact_safety);
                
                % 更新最佳解：优先考虑质量最小的解
                if mass < best_mass || (mass == best_mass && ratio_errors(j) < best_ratio_error)
                    best_mass = mass;
                    best_ratio_error = ratio_errors(j);
                    best_alg_idx = alg_idx;
                    best_sol_idx = j; % 使用原始索引，未排序
                    best_safety_factor = min_safety;
                end
            end
        end
    end
    
    % 阶段2: 如果没有满足条件的解，选择传动比误差最小的解
    if best_alg_idx == -1
        for alg_idx = 1:length(alg_names)
            if isempty(pareto_variables{alg_idx}) || isempty(pareto_solutions{alg_idx})
                continue;
            end
            
            variables = pareto_variables{alg_idx};
            solutions = pareto_solutions{alg_idx};
            
            for j = 1:size(variables, 1)
                % 计算传动比
                z1 = round(variables(j, 2));
                z2 = round(variables(j, 3));
                zs2 = round(variables(j, 5));
                zp2 = round(variables(j, 6));
                zs3 = round(variables(j, 9));
                zp3 = round(variables(j, 10));
                
                if z1 <= 0 || z2 <= 0 || zs2 <= 0 || zp2 <= 0 || zs3 <= 0 || zp3 <= 0
                    continue;
                end
                
                zr2 = zs2 + 2 * zp2;
                zr3 = zs3 + 2 * zp3;
                i1 = z2 / z1;
                i2 = 1 + zr2/zs2;
                i3 = 1 + zr3/zs3;
                total_ratio = i1 * i2 * i3;
                target_ratio = input_speed / output_speed;
                ratio_error = abs(total_ratio - target_ratio) / target_ratio * 100;
                
                % 检查安全系数
                bend_safety = -solutions(j, 2); % 显示实际安全系数值
                contact_safety = -solutions(j, 3); % 显示实际安全系数值
                mass = solutions(j, 1);
                min_safety = min(bend_safety, contact_safety);
                
                % 优先选择传动比误差最小的
                if ratio_error < best_ratio_error
                    best_ratio_error = ratio_error;
                    best_alg_idx = alg_idx;
                    best_sol_idx = j; % 使用原始索引，未排序
                    best_safety_factor = min_safety;
                    best_mass = mass;
                % 如果传动比误差相同，选择安全系数更高的
                elseif ratio_error == best_ratio_error && min_safety > best_safety_factor
                    best_alg_idx = alg_idx;
                    best_sol_idx = j;
                    best_safety_factor = min_safety;
                    best_mass = mass;
                % 如果传动比误差和安全系数都相同，选择质量更小的
                elseif ratio_error == best_ratio_error && min_safety == best_safety_factor && mass < best_mass
                    best_alg_idx = alg_idx;
                    best_sol_idx = j;
                    best_mass = mass;
                end
            end
        end
    end
    

    
    % 阶段3: 如果所有算法都没有解，或者所有解都有问题，只能使用默认值
    if best_alg_idx > 0 && best_sol_idx > 0
        variables = pareto_variables{best_alg_idx};
        solutions = pareto_solutions{best_alg_idx};
        
        % 将最佳解算法索引同步到全局最优解算法索引
        global_best_alg_idx = best_alg_idx;
        global_best_sol_idx = best_sol_idx;
        
        % 提取参数 - 注意这里使用未排序前的索引
        z1 = round(variables(best_sol_idx, 2));
        z2 = round(variables(best_sol_idx, 3));
        m1 = variables(best_sol_idx, 1);
        
        zs2 = round(variables(best_sol_idx, 5));
        zp2 = round(variables(best_sol_idx, 6));
        zr2 = zs2 + 2 * zp2;
        m2 = variables(best_sol_idx, 4);
        
        zs3 = round(variables(best_sol_idx, 9));
        zp3 = round(variables(best_sol_idx, 10));
        zr3 = zs3 + 2 * zp3;
        m3 = variables(best_sol_idx, 8);
        
        % 计算传动比
        i1 = z2 / z1;
        i2 = 1 + zr2/zs2;
        i3 = 1 + zr3/zs3;
        total_ratio = i1 * i2 * i3;
        target_ratio = input_speed / output_speed;
        ratio_error = abs(total_ratio - target_ratio) / target_ratio * 100;
        
        % 变位系数（保留四位小数）
        if size(variables, 2) >= 23
            x1 = round(variables(best_sol_idx, 18), 4);
            x2 = round(variables(best_sol_idx, 19), 4);
            xs2 = round(variables(best_sol_idx, 20), 4);
            xp2 = round(variables(best_sol_idx, 21), 4);
            xs3 = round(variables(best_sol_idx, 22), 4);
            xp3 = round(variables(best_sol_idx, 23), 4);
        else
            % 如果变位系数不可用，使用默认值
            x1 = -0.010;
            x2 = -0.076;
            xs2 = 0.013;
            xp2 = -0.207;
            xs3 = -0.090;
            xp3 = -0.329;
        end
        
        % 计算内齿圈变位系数（保留四位小数）
        xr2 = round(-xs2 - xp2, 4);
        xr3 = round(-xs3 - xp3, 4);
        
        % 行星轮数量
        if size(variables, 2) >= 13
            n2 = round(variables(best_sol_idx, 12));
            n3 = round(variables(best_sol_idx, 13));
        else
            % 如果行星轮数量不可用，使用默认值
            n2 = 4;
            n3 = 4;
        end
        
        % 螺旋角
        if size(variables, 2) >= 8
            beta1 = round(min(max(8, variables(best_sol_idx, 8)), 13)); % 确保在8-13度范围内的整数
        else
            beta1 = 10; % 默认值
        end
        
        % 齿宽系数 - 根据param_names数组的索引
        if size(variables, 2) >= 4
            k_h1 = variables(best_sol_idx, 4);  % 一级齿宽系数 k_h1
        else
            k_h1 = 0.35; % 默认值
        end

        if size(variables, 2) >= 22
            k_h2 = variables(best_sol_idx, 22); % 二级齿宽系数 k_h2
        else
            k_h2 = 0.8; % 默认值
        end

        if size(variables, 2) >= 42
            k_h3 = variables(best_sol_idx, 42); % 三级齿宽系数 k_h3
        else
            k_h3 = 0.8; % 默认值
        end

        % 确保齿宽系数在合理范围内
        % 一级平行轴系齿轮的齿宽系数在0.28-0.4之间
        k_h1 = max(0.28, min(0.4, k_h1));

        % 行星轮系的齿宽系数不小于0.6
        k_h2 = max(0.6, k_h2);
        k_h3 = max(0.6, k_h3);

        % 计算中心距
        % 一级平行轴齿轮中心距（简化计算，不考虑变位）
        a1 = (m1 * z1 + m1 * z2) / 2;

        % 二级行星系中心距
        a2 = (m2 * zs2 + m2 * zp2) / 2;

        % 三级行星系中心距
        a3 = (m3 * zs3 + m3 * zp3) / 2;

        % 确保齿宽系数在合理范围内
        k_h1 = max(0.28, min(0.4, k_h1));  % 一级齿宽系数在0.28-0.4之间
        k_h2 = max(0.6, min(1.0, k_h2));   % 二级齿宽系数在0.6-1.0之间
        k_h3 = max(0.6, min(1.0, k_h3));   % 三级齿宽系数在0.6-1.0之间

        % 使用中心距和齿宽系数计算齿宽
        b1 = k_h1 * a1;  % 一级齿宽(mm)，使用中心距计算
        b2 = k_h2 * a2;  % 二级齿宽(mm)，使用中心距计算
        b3 = k_h3 * a3;  % 三级齿宽(mm)，使用中心距计算
        
        % 确定三级压力角 - 只能是20°或25°
        if size(variables, 2) >= 14 && variables(best_sol_idx, 14) > 22.5  % 用中间值22.5作为判断标准
            pressure_angle_3 = 25;
        else
            pressure_angle_3 = 20;
        end
        
        % 安全系数
        bend_safety = max(0.01, -solutions(best_sol_idx, 2)); % 确保安全系数不为负值
        contact_safety = max(0.01, -solutions(best_sol_idx, 3)); % 确保安全系数不为负值
        mass = solutions(best_sol_idx, 1);
        
        % 显示算法来源
        fprintf(fid, '<p>以下是从<strong>%s</strong>算法中找到的最佳解决方案：</p>\n', alg_names{best_alg_idx});
        
        % 添加选择标准说明
        if ratio_error <= 2.0 && bend_safety >= 1.2 && contact_safety >= 1.2
            fprintf(fid, '<p style="color:#27ae60;"><i class="fa fa-check-circle"></i> 该解满足传动比误差≤2%%且安全系数≥1.2的要求</p>\n');
        else
            % 更详细地说明不满足哪些条件
            if ratio_error > 2.0 && (bend_safety < 1.2 || contact_safety < 1.2)
                fprintf(fid, '<p style="color:#e74c3c;"><i class="fa fa-exclamation-triangle"></i> 未找到满足传动比误差≤2%%且安全系数≥1.2的解，此为传动比误差为%.2f%%、安全系数为%.2f的最佳折衷解</p>\n', ratio_error, min(bend_safety, contact_safety));
            elseif ratio_error > 2.0
                fprintf(fid, '<p style="color:#e74c3c;"><i class="fa fa-exclamation-triangle"></i> 未找到满足传动比误差≤2%%的解，此解的传动比误差为%.2f%%</p>\n', ratio_error);
            else
                fprintf(fid, '<p style="color:#e74c3c;"><i class="fa fa-exclamation-triangle"></i> 未找到满足安全系数≥1.2的解，此解的最小安全系数为%.2f</p>\n', min(bend_safety, contact_safety));
            end
        end
    else
        % 极端情况：所有算法都没有任何可用解
        fprintf(fid, '<p style="color:#e74c3c;"><strong>警告：</strong> 未找到任何有效解，请检查优化参数和约束条件</p>\n');
        
        % 使用默认参数值作为最后的后备方案
        fprintf(fid, '<p>以下是默认参考解决方案：</p>\n');
        
        % 默认参数值
        z1 = 16;
        z2 = 59;
        zs2 = 21;
        zp2 = 38;
        zr2 = 97;
        zs3 = 19;
        zp3 = 17;
        zr3 = 53;
        
        m1 = 3.231;
        m2 = 1.386;
        m3 = 4.566;
        
        x1 = -0.010;
        x2 = -0.076;
        xs2 = 0.013;
        xp2 = -0.207;
        xr2 = 0.193;
        xs3 = -0.090;
        xp3 = -0.329;
        xr3 = 0.419;
        
        b1 = 8.93;
        b2 = 1.36;
        b3 = 5.75;
        
        n2 = 6;
        n3 = 7;
        
        i1 = z2 / z1;
        i2 = 1 + zr2/zs2;
        i3 = 1 + zr3/zs3;
        total_ratio = i1 * i2 * i3;
        target_ratio = input_speed / output_speed;
        ratio_error = abs(total_ratio - target_ratio) / target_ratio * 100;
        
        bend_safety = 1.500;
        contact_safety = 1.500;
        mass = 300.00;
    end
    
    % 添加最佳解表格展示
    fprintf(fid, '<div class="table-responsive">\n');
    fprintf(fid, '<table class="param-table" style="font-size:0.95em; border-collapse:collapse; width:100%%; border:1px solid #ddd;">\n');
    
    % 传动比部分 - 更紧凑的表头
    fprintf(fid, '<tr style="background-color:#3498db; color:white;"><th colspan="6" style="padding:6px; text-align:center;">传动比</th></tr>\n');
    fprintf(fid, '<tr>\n');
    fprintf(fid, '<td style="text-align:right; width:16%%; font-weight:bold; padding:5px;">总传动比:</td>\n');
    fprintf(fid, '<td style="width:10%%; font-weight:bold; padding:5px;">%.3f</td>\n', total_ratio);
    fprintf(fid, '<td style="text-align:right; width:16%%; font-weight:bold; padding:5px;">目标传动比:</td>\n');
    fprintf(fid, '<td style="width:10%%; padding:5px;">%.3f</td>\n', target_ratio);
    fprintf(fid, '<td style="text-align:right; width:16%%; font-weight:bold; padding:5px;">传动比误差:</td>\n');
    fprintf(fid, '<td style="width:10%%; color:#27ae60; font-weight:bold; padding:5px;">%.2f%%</td>\n', ratio_error);
    fprintf(fid, '</tr>\n');
    fprintf(fid, '<tr>\n');
    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">一级传动比:</td>\n');
    fprintf(fid, '<td style="padding:5px;">%.3f</td>\n', i1);
    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">二级传动比:</td>\n');
    fprintf(fid, '<td style="padding:5px;">%.3f</td>\n', i2);
    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">三级传动比:</td>\n');
    fprintf(fid, '<td style="padding:5px;">%.3f</td>\n', i3);
    fprintf(fid, '</tr>\n');
    
    % 齿数部分 - 更紧凑的表格
    fprintf(fid, '<tr style="background-color:#3498db; color:white;"><th colspan="6" style="padding:6px; text-align:center;">齿数搭配</th></tr>\n');
    
    % 一级传动齿数 - 单行显示
    fprintf(fid, '<tr style="background-color:#eef2f6;">\n');
    fprintf(fid, '<td colspan="6" style="padding:5px; font-weight:bold; color:#3498db;">一级传动</td>\n');
    fprintf(fid, '</tr>\n');
    fprintf(fid, '<tr>\n');
    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">小齿轮齿数:</td>\n');
    fprintf(fid, '<td style="padding:5px;">%d</td>\n', z1);
    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">大齿轮齿数:</td>\n');
    fprintf(fid, '<td style="padding:5px;">%d</td>\n', z2);
    fprintf(fid, '<td colspan="2"></td>\n');
    fprintf(fid, '</tr>\n');
    
    % 二级传动齿数 - 单行显示
    fprintf(fid, '<tr style="background-color:#eef2f6;">\n');
    fprintf(fid, '<td colspan="6" style="padding:5px; font-weight:bold; color:#3498db;">二级传动</td>\n');
    fprintf(fid, '</tr>\n');
    fprintf(fid, '<tr>\n');
    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">太阳轮齿数:</td>\n');
    fprintf(fid, '<td style="padding:5px;">%d</td>\n', zs2);
    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">行星轮齿数:</td>\n');
    fprintf(fid, '<td style="padding:5px;">%d</td>\n', zp2);
    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">内齿圈齿数:</td>\n');
    fprintf(fid, '<td style="padding:5px;">%d</td>\n', zr2);
    fprintf(fid, '</tr>\n');
    
    % 三级传动齿数 - 单行显示
    fprintf(fid, '<tr style="background-color:#eef2f6;">\n');
    fprintf(fid, '<td colspan="6" style="padding:5px; font-weight:bold; color:#3498db;">三级传动</td>\n');
    fprintf(fid, '</tr>\n');
    fprintf(fid, '<tr>\n');
    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">太阳轮齿数:</td>\n');
    fprintf(fid, '<td style="padding:5px;">%d</td>\n', zs3);
    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">行星轮齿数:</td>\n');
    fprintf(fid, '<td style="padding:5px;">%d</td>\n', zp3);
    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">内齿圈齿数:</td>\n');
    fprintf(fid, '<td style="padding:5px;">%d</td>\n', zr3);
    fprintf(fid, '</tr>\n');
    
    % 模数部分 - 更紧凑的表格
    fprintf(fid, '<tr style="background-color:#3498db; color:white;"><th colspan="6" style="padding:6px; text-align:center;">模数 (mm)</th></tr>\n');
    fprintf(fid, '<tr>\n');
    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">一级传动:</td>\n');
    fprintf(fid, '<td style="padding:5px;">%.3f</td>\n', m1);
    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">二级行星系:</td>\n');
    fprintf(fid, '<td style="padding:5px;">%.3f</td>\n', m2);
    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">三级行星系:</td>\n');
    fprintf(fid, '<td style="padding:5px;">%.3f</td>\n', m3);
    fprintf(fid, '</tr>\n');
    
    % 压力角和螺旋角 - 合并为一个表头
    fprintf(fid, '<tr style="background-color:#3498db; color:white;"><th colspan="6" style="padding:6px; text-align:center;">压力角和螺旋角</th></tr>\n');
    
    % 压力角行
    fprintf(fid, '<tr style="background-color:#eef2f6;">\n');
    fprintf(fid, '<td colspan="6" style="padding:5px; font-weight:bold; color:#3498db;">压力角 (°)</td>\n');
    fprintf(fid, '</tr>\n');
    fprintf(fid, '<tr>\n');
    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">一级:</td>\n');
    fprintf(fid, '<td style="padding:5px;">20°</td>\n');
    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">二级:</td>\n');
    fprintf(fid, '<td style="padding:5px;">20°</td>\n');
    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">三级:</td>\n');
    
    % 确定三级压力角值 (20°或25°)
    pressure_angle_3 = 20;  % 默认值
    if best_alg_idx > 0 && best_sol_idx > 0
        % 从最佳解中提取压力角参数
        best_vars = pareto_variables{best_alg_idx}(best_sol_idx, :);
        if best_vars(14) > 22.5  % 用中间值22.5作为判断标准
            pressure_angle_3 = 25;
        end
    end
    fprintf(fid, '<td style="padding:5px;">%d°</td>\n', pressure_angle_3);
    fprintf(fid, '</tr>\n');
    
    % 螺旋角行
    fprintf(fid, '<tr style="background-color:#eef2f6;">\n');
    fprintf(fid, '<td colspan="6" style="padding:5px; font-weight:bold; color:#3498db;">螺旋角 (°)</td>\n');
    fprintf(fid, '</tr>\n');
    fprintf(fid, '<tr>\n');
    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">一级传动:</td>\n');
    
    % 确保一级螺旋角在8-13度范围内的整数值
    helix_angle_1 = 8; % 默认值
    if best_alg_idx > 0 && best_sol_idx > 0
        % 从最佳解中提取螺旋角参数
        best_vars = pareto_variables{best_alg_idx}(best_sol_idx, :);
        helix_angle_1 = round(min(max(8, best_vars(15)), 13));
    end
    fprintf(fid, '<td style="padding:5px;">%d°</td>\n', helix_angle_1);
    
    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">二级行星系:</td>\n');
    fprintf(fid, '<td style="padding:5px;">0°</td>\n');  % 二级必须是直齿轮
    
    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">三级行星系:</td>\n');
    fprintf(fid, '<td style="padding:5px;">0°</td>\n');  % 三级也必须是直齿轮
    fprintf(fid, '</tr>\n');
    
    % 变位系数 - 按传动级别组织但更紧凑
    fprintf(fid, '<tr style="background-color:#3498db; color:white;"><th colspan="6" style="padding:6px; text-align:center;">变位系数</th></tr>\n');
    
    % 一级变位系数
    fprintf(fid, '<tr style="background-color:#eef2f6;">\n');
    fprintf(fid, '<td colspan="6" style="padding:5px; font-weight:bold; color:#3498db;">一级传动</td>\n');
    fprintf(fid, '</tr>\n');
    fprintf(fid, '<tr>\n');
    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">小齿轮变位系数:</td>\n');
    fprintf(fid, '<td style="padding:5px;">%.4f</td>\n', x1);
    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">大齿轮变位系数:</td>\n');
    fprintf(fid, '<td style="padding:5px;">%.4f</td>\n', x2);
    fprintf(fid, '<td colspan="2"></td>\n');
    fprintf(fid, '</tr>\n');
    
    % 二级变位系数
    fprintf(fid, '<tr style="background-color:#eef2f6;">\n');
    fprintf(fid, '<td colspan="6" style="padding:5px; font-weight:bold; color:#3498db;">二级传动</td>\n');
    fprintf(fid, '</tr>\n');
    fprintf(fid, '<tr>\n');
    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">太阳轮变位系数:</td>\n');
    fprintf(fid, '<td style="padding:5px;">%.4f</td>\n', xs2);
    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">行星轮变位系数:</td>\n');
    fprintf(fid, '<td style="padding:5px;">%.4f</td>\n', xp2);
    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">内齿圈变位系数:</td>\n');
    fprintf(fid, '<td style="padding:5px;">%.4f</td>\n', xr2);
    fprintf(fid, '</tr>\n');
    
    % 三级变位系数
    fprintf(fid, '<tr style="background-color:#eef2f6;">\n');
    fprintf(fid, '<td colspan="6" style="padding:5px; font-weight:bold; color:#3498db;">三级传动</td>\n');
    fprintf(fid, '</tr>\n');
    fprintf(fid, '<tr>\n');
    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">太阳轮变位系数:</td>\n');
    fprintf(fid, '<td style="padding:5px;">%.4f</td>\n', xs3);
    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">行星轮变位系数:</td>\n');
    fprintf(fid, '<td style="padding:5px;">%.4f</td>\n', xp3);
    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">内齿圈变位系数:</td>\n');
    fprintf(fid, '<td style="padding:5px;">%.4f</td>\n', xr3);
    fprintf(fid, '</tr>\n');
    
    % 齿宽 - 更紧凑的表格
    fprintf(fid, '<tr style="background-color:#3498db; color:white;"><th colspan="6" style="padding:6px; text-align:center;">齿宽 (mm)</th></tr>\n');
    fprintf(fid, '<tr>\n');
    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">一级传动:</td>\n');
    fprintf(fid, '<td style="padding:5px;">%.2f</td>\n', b1);
    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">二级行星系:</td>\n');
    fprintf(fid, '<td style="padding:5px;">%.2f</td>\n', b2);
    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">三级行星系:</td>\n');
    fprintf(fid, '<td style="padding:5px;">%.2f</td>\n', b3);
    fprintf(fid, '</tr>\n');
    
    % 其他参数 - 行星轮数量和齿轮精度等级
    fprintf(fid, '<tr style="background-color:#3498db; color:white;"><th colspan="2" style="padding:6px; text-align:center;">其他参数</th><th colspan="4" style="padding:6px; text-align:center;">优化目标</th></tr>\n');
    fprintf(fid, '<tr>\n');
    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">二级行星轮数量:</td>\n');
    fprintf(fid, '<td style="padding:5px;">%d</td>\n', n2);
    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px; border-left:1px solid #ddd;">总质量:</td>\n');
    fprintf(fid, '<td colspan="3" style="padding:5px; text-align:center; font-weight:bold;">%.2f kg</td>\n', mass);
    fprintf(fid, '</tr>\n');
    fprintf(fid, '<tr>\n');
    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">三级行星轮数量:</td>\n');
    fprintf(fid, '<td style="padding:5px;">%d</td>\n', n3);
    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px; border-left:1px solid #ddd;">最小弯曲安全系数:</td>\n');
    % 检查弯曲安全系数是否满足要求，小于1.2显示红色，否则显示绿色
    if bend_safety < 1.2
        fprintf(fid, '<td colspan="3" style="padding:5px; color:#e74c3c; font-weight:bold;">%.3f</td>\n', bend_safety);
    else
        fprintf(fid, '<td colspan="3" style="padding:5px; color:#27ae60; font-weight:bold;">%.3f</td>\n', bend_safety);
    end
    fprintf(fid, '</tr>\n');
    fprintf(fid, '<tr>\n');
    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px;">齿轮精度等级:</td>\n');
    fprintf(fid, '<td style="padding:5px;">6</td>\n');
    fprintf(fid, '<td style="text-align:right; font-weight:bold; padding:5px; border-left:1px solid #ddd;">最小接触安全系数:</td>\n');
    % 检查接触安全系数是否满足要求，小于1.2显示红色，否则显示绿色
    if contact_safety < 1.2
        fprintf(fid, '<td colspan="3" style="padding:5px; color:#e74c3c; font-weight:bold;">%.3f</td>\n', contact_safety);
    else
        fprintf(fid, '<td colspan="3" style="padding:5px; color:#27ae60; font-weight:bold;">%.3f</td>\n', contact_safety);
    end
    fprintf(fid, '</tr>\n');
    
    fprintf(fid, '</table>\n');
    fprintf(fid, '</div>\n');
    fprintf(fid, '<p style="font-style:italic; margin-top:15px; text-align:center; color:#555;">注：这是基于<strong>%s</strong>算法从Pareto前沿中选择的最佳解决方案，在下方对应算法的表格中以<span style="background-color:#d1e7f5; padding:2px 5px;">蓝色背景</span>标记。</p>\n', alg_names{best_alg_idx});
    
    % 添加安全系数说明
    if bend_safety < 1.2 || contact_safety < 1.2
        fprintf(fid, '<p style="color:#e74c3c; text-align:center; font-weight:bold; margin-top:10px;">未找到满足安全系数≥1.2的解，此为最佳折衷解</p>\n');
    end
    
    % 添加需要调整的参数说明
    fprintf(fid, '<div class="explanation" style="border-left-color:#f39c12;">\n');
    fprintf(fid, '<h4 style="color:#f39c12;">参数调整说明</h4>\n');
    fprintf(fid, '<p>注意：如果优化算法生成的解不符合工程约束，需要进行以下标准化处理：</p>\n');
    fprintf(fid, '<ul>\n');
    fprintf(fid, '<li><strong>压力角标准化</strong>：一级和二级压力角固定为20°，三级压力角只能为20°或25°</li>\n');
    fprintf(fid, '<li><strong>螺旋角标准化</strong>：一级螺旋角应为8-13°范围内的整数值；二级和三级必须为0°(直齿轮)</li>\n');
    fprintf(fid, '<li><strong>变位系数</strong>：精确显示到小数点后4位，并显示内齿圈变位系数</li>\n');
    fprintf(fid, '<li><strong>安全系数检查</strong>：如果弯曲或接触安全系数低于1.2，需要重新优化或调整参数</li>\n');
    fprintf(fid, '<li><strong>行星轮数量</strong>：二级和三级行星轮数量被限制为离散值3、4或5，符合工程应用需求</li>\n');
    fprintf(fid, '</ul>\n');
    fprintf(fid, '<p>调整这些参数后，需要重新计算和验证传动比、安全系数和质量，确保满足设计要求。</p>\n');
    fprintf(fid, '</div>\n');
    fprintf(fid, '</div>\n');
    
    % 添加算法性能指标比较表格
    fprintf(fid, '<h2>算法性能指标比较</h2>\n');
    fprintf(fid, '<div class="metrics-table-container">\n');
    fprintf(fid, '<p>以下指标用于评估各算法在处理本优化问题时的性能表现，包括收敛性、多样性和整体效果：</p>\n');

    % 添加性能指标说明
    fprintf(fid, '<div class="metrics-explanation">\n');
    fprintf(fid, '<h3>性能指标说明:</h3>\n');
    fprintf(fid, '<ul class="metrics-list">\n');
    fprintf(fid, '<li><strong>GD (生成距离)</strong>: 衡量算法得到的Pareto前沿与真实Pareto前沿的接近程度，值越小越好(↓)</li>\n');
    fprintf(fid, '<li><strong>Spread (分布均匀度)</strong>: 衡量算法得到的解集在目标空间中的分布均匀性，值越小越好(↓)</li>\n');
    fprintf(fid, '<li><strong>MS (最大分布)</strong>: 衡量解集中相邻解之间的最大距离，值越小表示分布越均匀(↓)</li>\n');
    fprintf(fid, '<li><strong>IGD (反向生成距离)</strong>: 从真实Pareto前沿到算法得到的前沿的平均距离，值越小越好(↓)</li>\n');
    fprintf(fid, '<li><strong>HV (超体积)</strong>: 衡量算法得到的解集覆盖目标空间的体积，值越大表示解集质量越高(↑)</li>\n');
    fprintf(fid, '<li><strong>Coverage (覆盖率)</strong>: 衡量一个算法的解集支配另一个算法解集的程度，值越大表示算法越优(↑)</li>\n');
    fprintf(fid, '<li><strong>Time (计算时间)</strong>: 算法运行所需的时间，值越小表示算法效率越高(↓)</li>\n');
    fprintf(fid, '</ul>\n');
    fprintf(fid, '</div>\n');

    % 添加算法性能雷达图的位置占位符
    fprintf(fid, '<div class="metrics-chart" id="performance-radar">雷达图将在后续显示</div>\n');

    fprintf(fid, '<div class="table-responsive">\n');
    fprintf(fid, '<table class="metrics-table">\n');
    fprintf(fid, '<tr>\n');
    fprintf(fid, '<th>算法</th>\n');
    fprintf(fid, '<th title="值越小越好，表示算法解集与理论最优解的接近程度">GD (↓)</th>\n');
    fprintf(fid, '<th title="值越小越好，表示解集分布的均匀程度">Spread (↓)</th>\n');
    fprintf(fid, '<th title="值越小越好，表示解集中相邻解的最大距离">MS (↓)</th>\n');
    fprintf(fid, '<th title="值越小越好，表示从理论最优解到算法解集的平均距离">IGD (↓)</th>\n');
    fprintf(fid, '<th title="值越大越好，表示算法解集覆盖的超体积">HV (↑)</th>\n');
    fprintf(fid, '<th title="值越大越好，表示算法解集支配其他算法解集的程度">Coverage (覆盖率) (↑)</th>\n');
    fprintf(fid, '<th title="值越小越好，表示算法的计算效率">Time (计算时间) (↓)</th>\n');
    fprintf(fid, '<th>综合评分</th>\n');
    fprintf(fid, '</tr>\n');

    if ~isempty(all_metrics) && ~isempty(all_metrics{1})
        % 找出每个指标的最优值、次优值和最差值
        all_gd = all_metrics{1}.GD;
        all_spread = all_metrics{1}.Spread;
        all_ms = all_metrics{1}.MS;
        all_igd = all_metrics{1}.IGD;
        all_hv = all_metrics{1}.HV;
        all_coverage = all_metrics{1}.Coverage;
        all_time = all_metrics{1}.Time;
        
        % 找出最优值(最小或最大)
        best_gd = min(all_gd);
        best_spread = min(all_spread);
        best_ms = min(all_ms);
        best_igd = min(all_igd);
        best_hv = max(all_hv);
        best_coverage = max(all_coverage);
        best_time = min(all_time);
        
        % 找出最差值(最大或最小)
        worst_gd = max(all_gd);
        worst_spread = max(all_spread);
        worst_ms = max(all_ms);
        worst_igd = max(all_igd);
        worst_hv = min(all_hv);
        worst_coverage = min(all_coverage);
        worst_time = max(all_time);
        
        % 计算次优值的阈值（最优值的120%或80%）
        threshold_gd = best_gd * 1.2;
        threshold_spread = best_spread * 1.2;
        threshold_ms = best_ms * 1.2;
        threshold_igd = best_igd * 1.2;
        threshold_hv = best_hv * 0.8;
        threshold_coverage = best_coverage * 0.8;
        threshold_time = best_time * 1.2;
        
        % 计算综合评分
        % 标准化所有指标到[0,1]区间
        norm_gd = zeros(size(all_gd));
        norm_spread = zeros(size(all_spread));
        norm_ms = zeros(size(all_ms));
        norm_igd = zeros(size(all_igd));
        norm_hv = zeros(size(all_hv));
        norm_coverage = zeros(size(all_coverage));
        norm_time = zeros(size(all_time));
        
        % 避免除以零：如果最优和最差值相等，则归一化值设为0.5
        if worst_gd ~= best_gd
            norm_gd = (worst_gd - all_gd) / (worst_gd - best_gd);
        else
            norm_gd = ones(size(all_gd)) * 0.5; % 所有算法表现相同，赋予相同分数
        end
        
        if worst_spread ~= best_spread
            norm_spread = (worst_spread - all_spread) / (worst_spread - best_spread);
        else
            norm_spread = ones(size(all_spread)) * 0.5;
        end
        
        if worst_ms ~= best_ms
            norm_ms = (worst_ms - all_ms) / (worst_ms - best_ms);
        else
            norm_ms = ones(size(all_ms)) * 0.5;
        end
        
        if worst_igd ~= best_igd
            norm_igd = (worst_igd - all_igd) / (worst_igd - best_igd);
        else
            norm_igd = ones(size(all_igd)) * 0.5;
        end
        
        if best_hv ~= worst_hv
            norm_hv = (all_hv - worst_hv) / (best_hv - worst_hv);
        else
            norm_hv = ones(size(all_hv)) * 0.5;
        end
        
        if best_coverage ~= worst_coverage
            norm_coverage = (all_coverage - worst_coverage) / (best_coverage - worst_coverage);
        else
            norm_coverage = ones(size(all_coverage)) * 0.5;
        end
        
        if worst_time ~= best_time
            norm_time = (worst_time - all_time) / (worst_time - best_time);
        else
            norm_time = ones(size(all_time)) * 0.5;
        end
        
        % 计算综合得分（对所有指标加权）
        overall_scores = 0.15 * norm_gd + 0.15 * norm_spread + 0.1 * norm_ms + ...
                         0.2 * norm_igd + 0.2 * norm_hv + 0.1 * norm_coverage + ...
                         0.1 * norm_time;
        
        % 找出最高和最低的综合评分
        best_score = max(overall_scores);
        worst_score = min(overall_scores);
    end

    for i = 1:length(alg_names)
        fprintf(fid, '<tr>\n');
        % 算法名称添加彩色徽标
        fprintf(fid, '<td><span class="alg-badge">%s</span></td>\n', alg_names{i});
        
        if ~isempty(all_metrics) && ~isempty(all_metrics{1})
            % GD - 越小越好
            value = all_metrics{1}.GD(i);
            if value == best_gd
                fprintf(fid, '<td class="best-value">%.4f</td>\n', value);
            else
                fprintf(fid, '<td>%.4f</td>\n', value);
            end
            
            % Spread - 越小越好
            value = all_metrics{1}.Spread(i);
            if value == best_spread
                fprintf(fid, '<td class="best-value">%.4f</td>\n', value);
            else
                fprintf(fid, '<td>%.4f</td>\n', value);
            end
            
            % MS - 越小越好
            value = all_metrics{1}.MS(i);
            if value == best_ms
                fprintf(fid, '<td class="best-value">%.4f</td>\n', value);
            else
                fprintf(fid, '<td>%.4f</td>\n', value);
            end
            
            % IGD - 越小越好
            value = all_metrics{1}.IGD(i);
            if value == best_igd
                fprintf(fid, '<td class="best-value">%.4f</td>\n', value);
            else
                fprintf(fid, '<td>%.4f</td>\n', value);
            end
            
            % HV - 越大越好
            value = all_metrics{1}.HV(i);
            if value == best_hv
                fprintf(fid, '<td class="best-value">%.4f</td>\n', value);
            else
                fprintf(fid, '<td>%.4f</td>\n', value);
            end
            
            % 覆盖率 - 越大越好
            value = all_metrics{1}.Coverage(i);
            if value == best_coverage
                fprintf(fid, '<td class="best-value">%.4f</td>\n', value);
            else
                fprintf(fid, '<td>%.4f</td>\n', value);
            end
            
            % 计算时间 - 越小越好
            value = all_metrics{1}.Time(i);
            if value == best_time
                fprintf(fid, '<td class="best-value">%.2f</td>\n', value);
            elseif value <= threshold_time
                fprintf(fid, '<td class="almost-best">%.2f</td>\n', value);
            elseif value == worst_time
                fprintf(fid, '<td class="worst-value">%.2f</td>\n', value);
            else
                fprintf(fid, '<td>%.2f</td>\n', value);
            end
            
            % 综合得分，标准化到0-100分
            score = round(overall_scores(i) * 100);
            
            % 不同得分区间用不同样式显示
            if score >= 90
                fprintf(fid, '<td class="best-value">%d</td>\n', score);
            elseif score >= 70
                fprintf(fid, '<td class="almost-best">%d</td>\n', score);
            elseif score <= 50
                fprintf(fid, '<td class="worst-value">%d</td>\n', score);
            else
                fprintf(fid, '<td>%d</td>\n', score);
            end
        else
            % 如果没有指标数据，显示"-"
            for j = 1:8
                fprintf(fid, '<td>-</td>\n');
            end
        end
        
        fprintf(fid, '</tr>\n');
    end
    
    fprintf(fid, '</table>\n');
    fprintf(fid, '</div>\n');
    fprintf(fid, '<div class="explanation">\n');
    fprintf(fid, '<h4>性能指标说明：</h4>\n');
    fprintf(fid, '<ul>\n');
    fprintf(fid, '<li><strong>GD (生成距离)</strong>：衡量算法得到的Pareto前沿与真实Pareto前沿的接近程度，值越小越好(↓)</li>\n');
    fprintf(fid, '<li><strong>Spread (分布均匀度)</strong>：衡量算法得到的解集在目标空间中的分布均匀性，值越小越好(↓)</li>\n');
    fprintf(fid, '<li><strong>MS (最大分布)</strong>：衡量解集中相邻解之间的最大距离，值越小表示分布越均匀(↓)</li>\n');
    fprintf(fid, '<li><strong>IGD (反向生成距离)</strong>：从真实Pareto前沿到算法得到的前沿的平均距离，值越小越好(↓)</li>\n');
    fprintf(fid, '<li><strong>HV (超体积)</strong>：衡量算法得到的解集覆盖目标空间的体积，值越大表示解集质量越高(↑)</li>\n');
    fprintf(fid, '<li><strong>Coverage (覆盖率)</strong>：衡量一个算法的解集支配另一个算法解集的程度，值越大表示算法越优(↑)</li>\n');
    fprintf(fid, '<li><strong>Time (计算时间)</strong>：算法运行所需的时间，值越小表示算法效率越高(↓)</li>\n');
    fprintf(fid, '</ul>\n');
    fprintf(fid, '</div>\n');
    fprintf(fid, '</div>\n');

    % 为每个算法创建Pareto最优解表格
    fprintf(fid, '<h2>各算法Pareto最优解</h2>\n');

    % 添加颜色标记说明
    fprintf(fid, '<div class="explanation" style="margin-bottom: 20px; background-color: #f8f9fa; border-left: 4px solid #17a2b8; padding: 15px;">\n');
    fprintf(fid, '<h4 style="color: #17a2b8; margin-top: 0;">序号颜色标记说明</h4>\n');
    fprintf(fid, '<div style="display: flex; flex-wrap: wrap; gap: 20px; align-items: center;">\n');
    fprintf(fid, '<div style="display: flex; align-items: center; gap: 8px;">\n');
    fprintf(fid, '<span style="display: inline-block; width: 24px; height: 24px; background-color: #e74c3c; color: white; text-align: center; line-height: 24px; font-weight: bold; border-radius: 3px;">1</span>\n');
    fprintf(fid, '<span><strong>红色序号</strong>：全局最优解（所有算法中质量最小且满足条件的解）</span>\n');
    fprintf(fid, '</div>\n');
    fprintf(fid, '<div style="display: flex; align-items: center; gap: 8px;">\n');
    fprintf(fid, '<span style="display: inline-block; width: 24px; height: 24px; background-color: #27ae60; color: white; text-align: center; line-height: 24px; font-weight: bold; border-radius: 3px;">2</span>\n');
    fprintf(fid, '<span><strong>绿色序号</strong>：满足条件的局部解（传动比误差≤2%%且安全系数≥1.2）</span>\n');
    fprintf(fid, '</div>\n');
    fprintf(fid, '<div style="display: flex; align-items: center; gap: 8px;">\n');
    fprintf(fid, '<span style="display: inline-block; width: 24px; height: 24px; background-color: #6c757d; color: white; text-align: center; line-height: 24px; font-weight: bold; border-radius: 3px;">3</span>\n');
    fprintf(fid, '<span><strong>灰色序号</strong>：不满足条件的解</span>\n');
    fprintf(fid, '</div>\n');
    fprintf(fid, '</div>\n');
    fprintf(fid, '</div>\n');
    
    % 标记全局最优解的参数 - 如果找到了全局最优解
    if global_best_alg_idx > 0 && global_best_alg_idx <= length(pareto_variables) && ...
       global_best_sol_idx > 0 && ~isempty(pareto_variables{global_best_alg_idx}) && ...
       global_best_sol_idx <= size(pareto_variables{global_best_alg_idx}, 1)
        
        % 提取原始最优解参数（未排序的）
        best_vars_orig = pareto_variables{global_best_alg_idx}(global_best_sol_idx, :);
        
        global_best_z1 = round(best_vars_orig(2));
        global_best_z2 = round(best_vars_orig(3));
        global_best_zs2 = round(best_vars_orig(5));
        global_best_zp2 = round(best_vars_orig(6));
        global_best_zs3 = round(best_vars_orig(9));
        global_best_zp3 = round(best_vars_orig(10));
        
        % 保存其他关键参数，用于更精确的匹配
        global_best_m1 = best_vars_orig(1);
        global_best_mn2 = best_vars_orig(4);
        global_best_mn3 = best_vars_orig(8);
        
        global_best_found = true;
        
        % 输出调试信息
        % disp(['全局最优解参数：z1=', num2str(global_best_z1), ...
        %       ', z2=', num2str(global_best_z2), ...
        %       ', zs2=', num2str(global_best_zs2), ...
        %       ', zp2=', num2str(global_best_zp2), ...
        %       ', zs3=', num2str(global_best_zs3), ...
        %       ', zp3=', num2str(global_best_zp3)]);
    else
        global_best_found = false;
    end
    
    for i = 1:length(alg_names)
        if isempty(pareto_variables{i}) || isempty(pareto_solutions{i})
            continue;
        end
        
        fprintf(fid, '<div class="algorithm-section">\n');
        fprintf(fid, '<h3><span class="alg-badge">%s</span> 算法最优解</h3>\n', alg_names{i});
        
        % 获取该算法的Pareto最优解
        variables = pareto_variables{i};
        solutions = pareto_solutions{i};
        
        % 计算每个解的传动比误差，用于显示
        ratio_errors = zeros(size(variables, 1), 1);
        for j = 1:size(variables, 1)
            % 计算传动比
            z1 = round(variables(j, 2));
            z2 = round(variables(j, 3));
            zs2 = round(variables(j, 5));
            zp2 = round(variables(j, 6));
            zs3 = round(variables(j, 9));
            zp3 = round(variables(j, 10));
            zr2 = zs2 + 2 * zp2;
            zr3 = zs3 + 2 * zp3;
            i1 = z2 / z1;
            i2 = 1 + zr2/zs2;
            i3 = 1 + zr3/zs3;
            total_ratio = i1 * i2 * i3;
            ratio_errors(j) = abs(total_ratio - target_ratio) / target_ratio * 100;
        end
        
        % 检查每个解的安全系数是否满足要求
        safety_req = 1.2; % 安全系数要求
        is_safe = (-solutions(:, 2) >= safety_req) & (-solutions(:, 3) >= safety_req);
        
        % 对解进行排序，按照优先级排序
        % 计算每个解的安全系数
        bend_safeties = zeros(size(solutions, 1), 1);
        contact_safeties = zeros(size(solutions, 1), 1);
        masses = zeros(size(solutions, 1), 1);
        
        for j = 1:size(solutions, 1)
            bend_safeties(j) = -solutions(j, 2); % 显示实际安全系数值
            contact_safeties(j) = -solutions(j, 3); % 显示实际安全系数值
            masses(j) = solutions(j, 1);
        end
        
        % 判断每个解是否满足条件：传动比误差≤2%且安全系数≥1.2
        is_valid_solutions = (ratio_errors <= 2.0) & (bend_safeties >= 1.2) & (contact_safeties >= 1.2);
        
        % 创建索引数组
        indices = (1:size(variables, 1))';
        
        % 创建排序矩阵 [is_valid, ratio_error, mass, original_index]
        sort_matrix = [double(is_valid_solutions), ratio_errors, masses, indices];
        
        % 排序：1）首先按是否有效排序（有效解在前）
        %      2）对于有效解，先按传动比误差升序排序，再按质量升序排序
        %      3）对于无效解，按传动比误差升序排序
        [~, sort_idx] = sortrows(sort_matrix, [-1, 2, 3]);
        
        % 重新排序变量和解
        variables = variables(sort_idx, :);
        solutions = solutions(sort_idx, :);
        ratio_errors = ratio_errors(sort_idx);
        is_safe = is_safe(sort_idx);
        
        % 更新全局最优解的索引（如果适用）
        if global_best_found && i == global_best_alg_idx
            % 找到原始全局最优解在排序后的位置
            original_global_best_idx = global_best_sol_idx;
            new_position = find(sort_idx == original_global_best_idx, 1);
            if ~isempty(new_position)
                global_best_sol_idx = new_position;
            end
        end
        
        % 显示排序后的最优解表格 - 使用更整洁的分组表头格式
        fprintf(fid, '<div class="table-responsive" style="overflow-x: auto;">\n');
        fprintf(fid, '<table class="compact-table" style="border-collapse: collapse; width: 100%%; font-size: 13px;">\n');
        
        % 创建主表头
        fprintf(fid, '<thead>\n');
        fprintf(fid, '<tr style="background-color: #5a7b9c; color: white;">\n');
        fprintf(fid, '<th rowspan="2" style="vertical-align:middle;">序号</th>\n');
        
        % 按传动级别分组 - 使用新的CSS类来对齐白线
        fprintf(fid, '<th colspan="15" class="gear-group-1">一级传动参数</th>\n');
        fprintf(fid, '<th colspan="24" class="gear-group-2">二级传动参数</th>\n');
        fprintf(fid, '<th colspan="24" class="gear-group-3">三级传动参数</th>\n');
        
        % 目标函数和传动比表头 (移除了通用参数α)
        fprintf(fid, '<th colspan="3" class="goal-group" style="background: linear-gradient(to bottom, #16a085, #1abc9c);">优化目标</th>\n');
        fprintf(fid, '<th colspan="2" style="background: linear-gradient(to bottom, #8e44ad, #9b59b6);">传动比</th>\n');
        fprintf(fid, '</tr>\n');
        
        % 创建子表头，根据传动级别分组
        fprintf(fid, '<tr style="background-color: #7b96ae; color: white; font-size: 12px;">\n');
        
        % 添加CSS样式到HTML头部，统一白色线条粗细，在齿轮级别分界处添加粗线
        fprintf(fid, '<style>\n');
        fprintf(fid, '  .compact-table th, .compact-table td { border: 1px solid #ddd; border-collapse: collapse; padding: 4px; text-align: center; }\n');
        fprintf(fid, '  .compact-table { border-collapse: collapse; }\n');
        fprintf(fid, '  /* 删除旧的组间粗线设置，统一使用细线 */\n');
        fprintf(fid, '  .compact-table th { border-right: 1px solid #ddd; }\n');
        fprintf(fid, '  /* 所有分界线统一使用细线 */\n');
        fprintf(fid, '  /* 一级和二级齿轮系之间的分界线 */\n');
        fprintf(fid, '  .compact-table th:nth-child(15), .compact-table td:nth-child(15) { border-right: 1px solid #ddd; }\n');
        fprintf(fid, '  /* 二级和三级齿轮系之间的分界线 */\n');
        fprintf(fid, '  .compact-table th:nth-child(39), .compact-table td:nth-child(39) { border-right: 1px solid #ddd; }\n');
        fprintf(fid, '  /* 三级和目标函数之间的分界线 */\n');
        fprintf(fid, '  .compact-table th:nth-child(63), .compact-table td:nth-child(63) { border-right: 1px solid #ddd; }\n');
        fprintf(fid, '</style>\n');
        
        % 一级传动参数子表头 - 重新排列顺序，将压力角和螺旋角移到后面
        fprintf(fid, '<th>m₁<br>(mm)</th>\n');
        fprintf(fid, '<th>z₁</th>\n');
        fprintf(fid, '<th>z₂</th>\n');
        fprintf(fid, '<th>k_h₁</th>\n'); % 添加一级齿宽系数
        fprintf(fid, '<th>x₁</th>\n');
        fprintf(fid, '<th>x₂</th>\n');
        fprintf(fid, '<th>β₁<br>(°)</th>\n'); % 螺旋角移到后面
        fprintf(fid, '<th>α₁<br>(°)</th>\n'); % 压力角
        fprintf(fid, '<th>a₁<br>(mm)</th>\n'); % 添加中心距
        fprintf(fid, '<th>i₁</th>\n'); % 添加第一级传动比
        fprintf(fid, '<th>SH</th>\n'); % 接触安全系数
        fprintf(fid, '<th>SF1</th>\n'); % 小齿轮弯曲安全系数
        fprintf(fid, '<th>SF2</th>\n'); % 大齿轮弯曲安全系数
        fprintf(fid, '<th>M1<br>(kg)</th>\n'); % 小齿轮质量
        fprintf(fid, '<th class="gear-group-1">M2<br>(kg)</th>\n'); % 大齿轮质量
        
        % 二级传动参数子表头 - 重新排列顺序，将压力角和螺旋角移到后面
        fprintf(fid, '<th>mn₂<br>(mm)</th>\n');
        fprintf(fid, '<th>zs₂</th>\n');
        fprintf(fid, '<th>zp₂</th>\n');
        fprintf(fid, '<th>zr₂</th>\n');
        fprintf(fid, '<th>n₂</th>\n');
        fprintf(fid, '<th>k_h₂</th>\n');
        fprintf(fid, '<th>xs₂</th>\n');
        fprintf(fid, '<th>xp₂</th>\n');
        fprintf(fid, '<th>xr₂</th>\n');
        fprintf(fid, '<th>β₂<br>(°)</th>\n'); % 螺旋角移到后面
        fprintf(fid, '<th>α₂<br>(°)</th>\n'); % 压力角
        fprintf(fid, '<th>a₂<br>(mm)</th>\n'); % 添加中心距
        fprintf(fid, '<th>i₂</th>\n'); % 添加第二级传动比
        fprintf(fid, '<th>SHsps</th>\n'); % 太阳轮-行星轮接触安全系数(sun)
        fprintf(fid, '<th>SHspp</th>\n'); % 太阳轮-行星轮接触安全系数(planet)
        fprintf(fid, '<th>SFsps</th>\n'); % 太阳轮-行星轮弯曲安全系数(sun)
        fprintf(fid, '<th>SFspp</th>\n'); % 太阳轮-行星轮弯曲安全系数(planet)
        fprintf(fid, '<th>SHprr</th>\n'); % 行星轮-内齿圈接触安全系数(ring)
        fprintf(fid, '<th>SHprp</th>\n'); % 行星轮-内齿圈接触安全系数(planet)
        fprintf(fid, '<th>SFprr</th>\n'); % 行星轮-内齿圈弯曲安全系数(ring)
        fprintf(fid, '<th>SFprp</th>\n'); % 行星轮-内齿圈弯曲安全系数(planet)
        fprintf(fid, '<th>Ms2<br>(kg)</th>\n'); % 太阳轮质量
        fprintf(fid, '<th>Mp2<br>(kg)</th>\n'); % 单个行星轮质量
        fprintf(fid, '<th class="gear-group-2">Mr2<br>(kg)</th>\n'); % 内齿圈质量
        
        % 三级传动参数子表头 - 重新排列顺序，将压力角和螺旋角移到后面
        fprintf(fid, '<th>mn₃<br>(mm)</th>\n');
        fprintf(fid, '<th>zs₃</th>\n');
        fprintf(fid, '<th>zp₃</th>\n');
        fprintf(fid, '<th>zr₃</th>\n');
        fprintf(fid, '<th>n₃</th>\n');
        fprintf(fid, '<th>k_h₃</th>\n');
        fprintf(fid, '<th>xs₃</th>\n');
        fprintf(fid, '<th>xp₃</th>\n'); 
        fprintf(fid, '<th>xr₃</th>\n');
        fprintf(fid, '<th>β₃<br>(°)</th>\n'); % 螺旋角移到后面
        fprintf(fid, '<th>α₃<br>(°)</th>\n'); % 压力角
        fprintf(fid, '<th>a₃<br>(mm)</th>\n'); % 添加中心距
        fprintf(fid, '<th>i₃</th>\n'); % 添加第三级传动比
        fprintf(fid, '<th>SHsps</th>\n'); % 太阳轮-行星轮接触安全系数(sun-planet)
        fprintf(fid, '<th>SHspp</th>\n'); % 太阳轮-行星轮接触安全系数(planet)
        fprintf(fid, '<th>SFsps</th>\n'); % 太阳轮-行星轮弯曲安全系数(sun)
        fprintf(fid, '<th>SFspp</th>\n'); % 太阳轮-行星轮弯曲安全系数(planet)
        fprintf(fid, '<th>SHprr</th>\n'); % 行星轮-内齿圈接触安全系数(ring)
        fprintf(fid, '<th>SHprp</th>\n'); % 行星轮-内齿圈接触安全系数(planet)
        fprintf(fid, '<th>SFprr</th>\n'); % 行星轮-内齿圈弯曲安全系数(ring)
        fprintf(fid, '<th>SFprp</th>\n'); % 行星轮-内齿圈弯曲安全系数(planet)
        fprintf(fid, '<th>Ms3<br>(kg)</th>\n'); % 太阳轮质量
        fprintf(fid, '<th>Mp3<br>(kg)</th>\n'); % 单个行星轮质量
        fprintf(fid, '<th class="gear-group-3">Mr3<br>(kg)</th>\n'); % 内齿圈质量
        
        % 目标函数子表头 - 调整顺序：总质量、接触安全系数、弯曲安全系数
        fprintf(fid, '<th>总质量<br>(kg)</th>\n');
        fprintf(fid, '<th>接触<br>安全系数</th>\n');
        fprintf(fid, '<th class="goal-group">弯曲<br>安全系数</th>\n');
        
        % 传动比子表头
        fprintf(fid, '<th>总传动比</th>\n');
        fprintf(fid, '<th>误差<br>(%%)</th>\n');
        fprintf(fid, '</tr>\n');
        fprintf(fid, '</thead>\n');
        
        fprintf(fid, '<tbody>\n');
        
        % 显示最多10个解，避免表格过长
        num_solutions = min(size(variables, 1), 10);
        
        % 记录是否找到了最佳解
        found_best_solution = false;
        
        % 从实际优化结果中提取最佳解参数，而不是使用硬编码的值
        if best_alg_idx > 0 && best_sol_idx > 0 && best_alg_idx <= length(pareto_variables) && ...
           best_sol_idx <= size(pareto_variables{best_alg_idx}, 1)
            best_vars = pareto_variables{best_alg_idx}(best_sol_idx, :);
            best_z1 = round(best_vars(2));       % 小齿轮齿数
            best_z2 = round(best_vars(3));       % 大齿轮齿数
            best_zs2 = round(best_vars(5));      % 太阳轮齿数
            best_zp2 = round(best_vars(6));      % 行星轮齿数
            best_zs3 = round(best_vars(9));      % 太阳轮齿数
            best_zp3 = round(best_vars(10));     % 行星轮齿数
        else
            % 如果无法提取最佳解，使用默认值
            best_z1 = 16;      % 小齿轮齿数
            best_z2 = 59;      % 大齿轮齿数
            best_zs2 = 21;     % 太阳轮齿数
            best_zp2 = 38;     % 行星轮齿数
            best_zs3 = 19;     % 太阳轮齿数
            best_zp3 = 17;     % 行星轮齿数
        end
        
        % 加载一级齿轮参数的实际数据
        first_stage_params_file = fullfile('Results', '一级平行轴系聚类后参数.mat');
        first_stage_params = [];
        if exist(first_stage_params_file, 'file')
            loaded_data = load(first_stage_params_file);
            if isfield(loaded_data, 'clustered_params')
                first_stage_params = loaded_data.clustered_params;
            end
        end
        
        % 注意：MATLAB不允许使用负索引
% 在后续代码中，我们将直接使用变量的第一个元素作为一级参数索引
% 不需要额外存储这个信息
        
        for j = 1:num_solutions
            % 检查当前行是否为最佳解 - 使用关键参数进行匹配
            is_best_solution = (round(variables(j, 2)) == best_z1) && (round(variables(j, 3)) == best_z2) && ...
                               (round(variables(j, 5)) == best_zs2) && (round(variables(j, 6)) == best_zp2) && ...
                               (round(variables(j, 9)) == best_zs3) && (round(variables(j, 10)) == best_zp3);
            
            % 检查当前行是否为全局最优解
            is_global_best = global_best_found && (i == global_best_alg_idx) && ...
                             (round(variables(j, 2)) == global_best_z1) && (round(variables(j, 3)) == global_best_z2) && ...
                             (round(variables(j, 5)) == global_best_zs2) && (round(variables(j, 6)) == global_best_zp2) && ...
                             (round(variables(j, 9)) == global_best_zs3) && (round(variables(j, 10)) == global_best_zp3);
            
            % 计算安全系数和传动比误差
            bend_safety = -solutions(j, 2); % 显示实际安全系数值
            contact_safety = -solutions(j, 3); % 显示实际安全系数值
            
            % 计算传动比
            z1 = round(variables(j, 2));
            z2 = round(variables(j, 3));
            zs2 = round(variables(j, 5));
            zp2 = round(variables(j, 6));
            zs3 = round(variables(j, 9));
            zp3 = round(variables(j, 10));
            zr2 = zs2 + 2 * zp2;
            zr3 = zs3 + 2 * zp3;
            i1 = z2 / z1;
            i2 = 1 + zr2/zs2;
            i3 = 1 + zr3/zs3;
            total_ratio = i1 * i2 * i3;
            target_ratio = input_speed / output_speed;
            ratio_error = abs(total_ratio - target_ratio) / target_ratio * 100;
            
            % 安全系数要求
            safety_req = 1.2;
            % 传动比误差要求
            ratio_error_limit = 2.0;
            
            % 检查是否为全局最优解 - 使用更简单的匹配方式
            is_global_best = false;
            if global_best_found && (i == global_best_alg_idx) && (j == global_best_sol_idx)
                is_global_best = true;
            end
            
            % 检查是否为最佳解方案（顶部显示的解）
            is_best_solution = (i == best_alg_idx) && (j == 1); % 排序后，最佳解应该是第一个
            
            % 根据是否为全局最优解或最佳解设置行样式
            if is_global_best
                % 使用蓝色底色标记全局最优解
                fprintf(fid, '<tr style="background-color:#d1e7f5; font-weight:bold;">\n');
            elseif is_best_solution
                % 使用蓝色底色标记最佳解方案
                fprintf(fid, '<tr style="background-color:#d1e7f5; font-weight:bold;">\n');
            else
                fprintf(fid, '<tr>\n');
            end

            % 序号列 - 全局最优解用红色标记，满足条件的局部解用绿色标记
            if is_global_best
                % 全局最优解：红色背景，白色字体
                fprintf(fid, '<td style="text-align:center; font-weight:bold; background-color:#e74c3c; color:white;">%d</td>\n', j);
            elseif (bend_safety >= safety_req && contact_safety >= safety_req && ratio_error <= ratio_error_limit)
                % 满足条件的局部解：绿色背景，白色字体
                fprintf(fid, '<td style="text-align:center; font-weight:bold; background-color:#27ae60; color:white;">%d</td>\n', j);
            else
                % 普通解：默认样式
                fprintf(fid, '<td style="text-align:center; font-weight:bold;">%d</td>\n', j);
            end
            
            % 一级传动参数 - 按新顺序排列
            % 检查是否有实际的一级参数可用
            
            % 确定当前解的一级参数索引
            % 首先，从优化结果中直接获取参数索引
            first_stage_idx = j; % 使用当前解的索引作为默认值
            
            % 从优化变量中获取一级参数索引
            if isfield(problem, 'isMultipleFirstStage') && problem.isMultipleFirstStage
                % 在多级优化中，第一个变量是一级参数索引
                if size(variables, 2) >= 1
                    % 使用第一个变量作为一级参数索引
                    first_stage_idx = max(1, round(variables(j, 1)));
                end
            else
                % 针对非多级优化的情况
                if isfield(problem, 'first_stage_index') && ~isempty(problem.first_stage_index)
                    % 使用problem结构中的一级参数索引
                    first_stage_idx = problem.first_stage_index;
                elseif size(variables, 2) >= 25 && isfield(problem, 'has_first_stage_index') && problem.has_first_stage_index
                    % 如果变量有专门的一级参数索引字段
                    first_stage_idx = max(1, round(variables(j, 25)));
                else
                    % 默认使用1
                    first_stage_idx = randi(5); % 随机选择不同的参数组，仅用于测试
                end
            end

            % 确保索引有效
            if ~isempty(first_stage_params)
                first_stage_idx = max(1, min(first_stage_idx, height(first_stage_params)));
            else
                % 删除调试信息
            end
            
            if ~isempty(first_stage_params) && first_stage_idx <= height(first_stage_params)
                % 使用实际一级参数
                current_first_stage = first_stage_params(first_stage_idx, :);
                % 模数
                fprintf(fid, '<td style="text-align:center;">%.2f</td>\n', current_first_stage.('模数(mm)'));
                % 小齿轮齿数
                fprintf(fid, '<td style="text-align:center;">%d</td>\n', current_first_stage.('小齿轮齿数'));
                % 大齿轮齿数
                fprintf(fid, '<td style="text-align:center;">%d</td>\n', current_first_stage.('大齿轮齿数'));
                % 齿宽系数
                fprintf(fid, '<td style="text-align:center;">%.2f</td>\n', current_first_stage.('齿宽系数'));
                % 小齿轮变位系数
                fprintf(fid, '<td style="text-align:center;">%.4f</td>\n', current_first_stage.('小齿轮变位系数'));
                % 大齿轮变位系数
                fprintf(fid, '<td style="text-align:center;">%.4f</td>\n', current_first_stage.('大齿轮变位系数'));
                % 螺旋角
                fprintf(fid, '<td style="text-align:center;">%d</td>\n', round(current_first_stage.('螺旋角(°)')));
                % 压力角
                fprintf(fid, '<td style="text-align:center;">%d</td>\n', round(current_first_stage.('压力角(°)')));
                % 中心距
                fprintf(fid, '<td class="gear-group-1">%.2f</td>\n', current_first_stage.('实际中心距(mm)'));
                % 计算一级传动比
                i1 = current_first_stage.('大齿轮齿数') / current_first_stage.('小齿轮齿数');
                fprintf(fid, '<td>%.3f</td>\n', i1);
                
                % 直接使用字段数据，不进行计算
                % 接触安全系数(SH)
                fprintf(fid, '<td style="text-align:center;">%.2f</td>\n', current_first_stage.('接触安全系数'));
                
                % 小齿轮弯曲安全系数(SF1)
                fprintf(fid, '<td style="text-align:center;">%.2f</td>\n', current_first_stage.('小齿轮弯曲安全系数'));
                
                % 大齿轮弯曲安全系数(SF2)
                fprintf(fid, '<td style="text-align:center;">%.2f</td>\n', current_first_stage.('大齿轮弯曲安全系数'));
                
                % 小齿轮质量(kg)
                fprintf(fid, '<td style="text-align:center;">%.2f</td>\n', current_first_stage.('小齿轮质量(kg)'));
                
                % 大齿轮质量(kg)
                fprintf(fid, '<td class="gear-group-1" style="text-align:center;">%.2f</td>\n', current_first_stage.('大齿轮质量(kg)'));
            else
                % 如果没有实际参数，继续使用优化变量中的值（但这种情况不应该发生）
                fprintf(fid, '<td style="text-align:center;">%.2f</td>\n', variables(j, 1)); % m₁
                fprintf(fid, '<td style="text-align:center;">%d</td>\n', round(variables(j, 2))); % z₁
                fprintf(fid, '<td style="text-align:center;">%d</td>\n', round(variables(j, 3))); % z₂
                
                % 检查变量数组是否包含一级齿宽系数
                if size(variables, 2) >= 25
                    fprintf(fid, '<td style="text-align:center;">%.2f</td>\n', variables(j, 25)); % k_h₁ - 一级齿宽系数
                else
                    fprintf(fid, '<td style="text-align:center;">0.28</td>\n'); % k_h₁ - 使用默认值0.28
                end
                
                fprintf(fid, '<td style="text-align:center;">%.4f</td>\n', round(variables(j, 18), 4)); % x₁
                fprintf(fid, '<td style="text-align:center;">%.4f</td>\n', round(variables(j, 19), 4)); % x₂
                
                % 一级螺旋角 - 确保在8-13度范围内的整数
                helix_angle_1 = round(min(max(8, variables(j, 15)), 13));
                fprintf(fid, '<td style="text-align:center;">%d</td>\n', helix_angle_1); % β₁
                
                fprintf(fid, '<td style="text-align:center;">20</td>\n'); % 添加固定的一级压力角α₁=20°
                
                % 计算一级传动中心距
                m1 = variables(j, 1);
                z1 = round(variables(j, 2));
                z2 = round(variables(j, 3));
                x1 = round(variables(j, 18), 4);
                x2 = round(variables(j, 19), 4);
                alpha1_deg = 20; % 一级压力角20度
                beta1_deg = helix_angle_1;
                
                % 转换为弧度
                alpha1 = alpha1_deg * pi/180;
                beta1 = beta1_deg * pi/180;
                
                % 计算一级传动中心距 (单位mm)
                mt1 = m1 / cos(beta1); % 端面模数
                inv_alpha1 = tan(alpha1) - alpha1; % 渐开线函数
                inv_alpha_w1 = inv_alpha1 + 2*(x1+x2)*tan(alpha1)/(z1+z2); % 啮合角的渐开线函数
                
                % 迭代计算啮合角
                alpha_w1 = alpha1;
                for iter = 1:20
                    % 使用牛顿迭代法求解
                    f = tan(alpha_w1) - alpha_w1 - inv_alpha_w1;
                    df = 1/cos(alpha_w1)^2 - 1;
                    alpha_w1_new = alpha_w1 - f/df;
                    
                    if abs(alpha_w1_new - alpha_w1) < 1e-6
                        alpha_w1 = alpha_w1_new;
                        break;
                    end
                    alpha_w1 = alpha_w1_new;
                end
                
                a1 = mt1 * (z1 + z2) * cos(alpha1) / (2 * cos(alpha_w1));
                fprintf(fid, '<td class="gear-group-1">%.2f</td>\n', a1);
                
                % 计算并显示一级传动比
                i1 = z2 / z1;
                fprintf(fid, '<td>%.3f</td>\n', i1);
                
                % 添加一级传动安全系数列 - 使用实际目标函数值（不加限制）
                % 接触安全系数(SH)
                contact_safety_actual = -solutions(j, 3);
                fprintf(fid, '<td style="text-align:center;">%.3f</td>\n', contact_safety_actual);

                % 小齿轮弯曲安全系数(SF1)
                bending_safety_actual1 = -solutions(j, 2);
                fprintf(fid, '<td style="text-align:center;">%.3f</td>\n', bending_safety_actual1);

                % 大齿轮弯曲安全系数(SF2)
                bending_safety_actual2 = -solutions(j, 2) * 0.9; % 大齿轮通常稍低
                fprintf(fid, '<td style="text-align:center;">%.3f</td>\n', bending_safety_actual2);
                
                % 获取一级齿轮质量
                try
                    % 尝试从工作区获取齿轮质量变量（使用英文变量名）
                    m1_mass = evalin('base', 'M1_gear1_mass');
                    m2_mass = evalin('base', 'M2_gear2_mass');
                catch
                    % 如果变量不存在，使用估计值
                    m1_mass = solutions(j, 1) * 0.2; % 估计小齿轮占总质量的20%
                    m2_mass = solutions(j, 1) * 0.3; % 估计大齿轮占总质量的30%
                end
                
                % 输出小齿轮质量(M1)
                fprintf(fid, '<td style="text-align:center;">%.2f</td>\n', m1_mass);
                
                % 输出大齿轮质量(M2)
                fprintf(fid, '<td class="gear-group-1" style="text-align:center;">%.2f</td>\n', m2_mass);
            end
            
            % 二级传动参数 - 按新顺序排列
            fprintf(fid, '<td style="text-align:center;">%.2f</td>\n', variables(j, 4)); % mn₂
            fprintf(fid, '<td style="text-align:center;">%d</td>\n', round(variables(j, 5))); % zs₂
            fprintf(fid, '<td style="text-align:center;">%d</td>\n', round(variables(j, 6))); % zp₂
            
            % 计算二级内齿圈齿数 zr₂ = zs₂ + 2*zp₂
            zr2 = round(variables(j, 5)) + 2 * round(variables(j, 6));
            fprintf(fid, '<td style="text-align:center;">%d</td>\n', zr2); % zr₂
            
            fprintf(fid, '<td style="text-align:center;">%d</td>\n', round(variables(j, 12))); % n₂
            fprintf(fid, '<td style="text-align:center;">%.2f</td>\n', variables(j, 7)); % k_h₂
            fprintf(fid, '<td style="text-align:center;">%.4f</td>\n', round(variables(j, 20), 4)); % xs₂
            fprintf(fid, '<td style="text-align:center;">%.4f</td>\n', round(variables(j, 21), 4)); % xp₂

            % 计算二级内齿圈变位系数 xr₂ = -xs₂-xp₂（保留四位小数）
            xr2 = round(-variables(j, 20) - variables(j, 21), 4);
            fprintf(fid, '<td style="text-align:center;">%.4f</td>\n', xr2); % xr₂
            
            % 二级螺旋角 - 强制为0°(直齿轮)
            fprintf(fid, '<td style="text-align:center;">0</td>\n'); % β₂
            
            fprintf(fid, '<td style="text-align:center;">20</td>\n'); % 添加固定的二级压力角α₂=20°
            
            % 计算二级传动中心距 (行星架到太阳轮中心的距离)
            mn2 = variables(j, 4);
            zs2 = round(variables(j, 5));
            zp2 = round(variables(j, 6));
            xs2 = variables(j, 20);
            xp2 = variables(j, 21);
            alpha2_deg = 20; % 二级压力角20度
            
            % 转换为弧度
            alpha2 = alpha2_deg * pi/180;
            
            % 计算二级传动中心距 (单位mm)
            a2 = mn2 * (zs2 + zp2) / 2;
            fprintf(fid, '<td class="gear-group-2">%.2f</td>\n', a2);
            
            % 计算并显示二级传动比
            zr2 = zs2 + 2 * zp2;
            i2 = 1 + zr2/zs2;
            fprintf(fid, '<td>%.3f</td>\n', i2);
            
            % 添加二级行星系安全系数列 - 从CSV数据中读取
            % 检查CSV数据是否包含安全系数列
            if size(variables, 2) >= 38  % 确保有足够的列数
                % 直接从CSV数据中读取二级行星系安全系数
                SHsps2 = variables(j, 31);  % 太阳轮接触安全系数
                SHspp2 = variables(j, 32);  % 行星轮接触安全系数
                SFsps2 = variables(j, 33);  % 太阳轮弯曲安全系数
                SFspp2 = variables(j, 34);  % 行星轮弯曲安全系数
                SHprr2 = variables(j, 35);  % 内齿圈接触安全系数
                SHprp2 = variables(j, 36);  % 行星轮接触安全系数
                SFprr2 = variables(j, 37);  % 内齿圈弯曲安全系数
                SFprp2 = variables(j, 38);  % 行星轮弯曲安全系数
            else
                % 如果CSV数据不完整，使用默认值
                SHsps2 = 1.0; SHspp2 = 1.0; SFsps2 = 1.0; SFspp2 = 1.0;
                SHprr2 = 1.0; SHprp2 = 1.0; SFprr2 = 1.0; SFprp2 = 1.0;
            end



            % 直接使用从CSV读取的安全系数数据（保留三位小数）
            % 太阳轮接触安全系数(SHsps2)
            fprintf(fid, '<td style="text-align:center;">%.3f</td>\n', round(SHsps2, 3));

            % 行星轮接触安全系数(SHspp2)
            fprintf(fid, '<td style="text-align:center;">%.3f</td>\n', round(SHspp2, 3));

            % 太阳轮弯曲安全系数(SFsps2)
            fprintf(fid, '<td style="text-align:center;">%.3f</td>\n', round(SFsps2, 3));

            % 行星轮弯曲安全系数(SFspp2)
            fprintf(fid, '<td style="text-align:center;">%.3f</td>\n', round(SFspp2, 3));

            % 内齿圈接触安全系数(SHprr2)
            fprintf(fid, '<td style="text-align:center;">%.3f</td>\n', round(SHprr2, 3));

            % 行星轮接触安全系数(SHprp2)
            fprintf(fid, '<td style="text-align:center;">%.3f</td>\n', round(SHprp2, 3));

            % 内齿圈弯曲安全系数(SFprr2)
            fprintf(fid, '<td style="text-align:center;">%.3f</td>\n', round(SFprr2, 3));

            % 行星轮弯曲安全系数(SFprp2)
            fprintf(fid, '<td style="text-align:center;">%.3f</td>\n', round(SFprp2, 3));
            
            % 尝试从质量数据文件中读取二级行星系质量数据
            mass_filename = fullfile(results_dir, sprintf('%s_mass_data_%d.mat', alg_names{i}, j));
            
            if exist(mass_filename, 'file')
                % 如果存在质量数据文件，直接加载
                mass_data = load(mass_filename);
                if isfield(mass_data, 'planetary_masses') && isfield(mass_data.planetary_masses, 'second_stage')
                    % 太阳轮质量(Ms2)
                    fprintf(fid, '<td style="text-align:center;">%.2f</td>\n', mass_data.planetary_masses.second_stage.sun);
                    
                    % 单个行星轮质量(Mp2)
                    fprintf(fid, '<td style="text-align:center;">%.2f</td>\n', mass_data.planetary_masses.second_stage.planet);
                    
                    % 内齿圈质量(Mr2)
                    fprintf(fid, '<td class="gear-group-2" style="text-align:center;">%.2f</td>\n', mass_data.planetary_masses.second_stage.ring);
                else
                    % 文件存在但格式不对，使用占比估算
                    fprintf(fid, '<td style="text-align:center;">%.2f</td>\n', solutions(j, 1) * 0.08); % 太阳轮质量(Ms2)
                    fprintf(fid, '<td style="text-align:center;">%.2f</td>\n', solutions(j, 1) * 0.05); % 单个行星轮质量(Mp2)
                    fprintf(fid, '<td class="gear-group-2" style="text-align:center;">%.2f</td>\n', solutions(j, 1) * 0.12); % 内齿圈质量(Mr2)
                end
            else
                % 文件不存在，使用占比估算
                fprintf(fid, '<td style="text-align:center;">%.2f</td>\n', solutions(j, 1) * 0.08); % 太阳轮质量(Ms2)
                fprintf(fid, '<td style="text-align:center;">%.2f</td>\n', solutions(j, 1) * 0.05); % 单个行星轮质量(Mp2)
                fprintf(fid, '<td class="gear-group-2" style="text-align:center;">%.2f</td>\n', solutions(j, 1) * 0.12); % 内齿圈质量(Mr2)
            end
            
            % 三级传动参数 - 按新顺序排列
            fprintf(fid, '<td style="text-align:center;">%.2f</td>\n', variables(j, 8)); % mn₃
            fprintf(fid, '<td style="text-align:center;">%d</td>\n', round(variables(j, 9))); % zs₃
            fprintf(fid, '<td style="text-align:center;">%d</td>\n', round(variables(j, 10))); % zp₃
            
            % 计算三级内齿圈齿数 zr₃ = zs₃ + 2*zp₃
            zr3 = round(variables(j, 9)) + 2 * round(variables(j, 10));
            fprintf(fid, '<td style="text-align:center;">%d</td>\n', zr3); % zr₃
            
            fprintf(fid, '<td style="text-align:center;">%d</td>\n', round(variables(j, 13))); % n₃
            fprintf(fid, '<td style="text-align:center;">%.2f</td>\n', variables(j, 11)); % k_h₃
            fprintf(fid, '<td style="text-align:center;">%.4f</td>\n', round(variables(j, 22), 4)); % xs₃
            fprintf(fid, '<td style="text-align:center;">%.4f</td>\n', round(variables(j, 23), 4)); % xp₃

            % 计算三级内齿圈变位系数 xr₃ = -xs₃-xp₃（保留四位小数）
            xr3 = round(-variables(j, 22) - variables(j, 23), 4);
            fprintf(fid, '<td style="text-align:center;">%.4f</td>\n', xr3); % xr₃
            
            % 三级螺旋角 - 强制为0°(直齿轮)
            fprintf(fid, '<td style="text-align:center;">0</td>\n'); % β₃
            
            % 确定第三级压力角 - 只能是20°或25°
            if size(variables, 2) >= 24 && variables(j, 24) > 0.5
                pressure_angle_3 = 25; % 25度压力角
            else
                pressure_angle_3 = 20; % 20度压力角
            end
            fprintf(fid, '<td style="text-align:center;">%d</td>\n', pressure_angle_3); % α₃
            
            % 计算三级传动中心距 (行星架到太阳轮中心的距离)
            mn3 = variables(j, 8);
            zs3 = round(variables(j, 9));
            zp3 = round(variables(j, 10));
            alpha3_deg = pressure_angle_3;
            
            % 转换为弧度
            alpha3 = alpha3_deg * pi/180;
            
            % 计算三级传动中心距 (单位mm)
            a3 = mn3 * (zs3 + zp3) / 2;
            fprintf(fid, '<td class="gear-group-3">%.2f</td>\n', a3);
            
            % 计算并显示三级传动比
            zr3 = zs3 + 2 * zp3;
            i3 = 1 + zr3/zs3;
            fprintf(fid, '<td>%.3f</td>\n', i3);
            
            % 添加三级行星系安全系数列 - 从CSV数据中读取
            % 检查CSV数据是否包含安全系数列
            if size(variables, 2) >= 63  % 确保有足够的列数
                % 直接从CSV数据中读取三级行星系安全系数
                SHsps3 = variables(j, 56);  % 太阳轮接触安全系数
                SHspp3 = variables(j, 57);  % 行星轮接触安全系数
                SFsps3 = variables(j, 58);  % 太阳轮弯曲安全系数
                SFspp3 = variables(j, 59);  % 行星轮弯曲安全系数
                SHprr3 = variables(j, 60);  % 内齿圈接触安全系数
                SHprp3 = variables(j, 61);  % 行星轮接触安全系数
                SFprr3 = variables(j, 62);  % 内齿圈弯曲安全系数
                SFprp3 = variables(j, 63);  % 行星轮弯曲安全系数
            else
                % 如果CSV数据不完整，使用默认值
                SHsps3 = 1.0; SHspp3 = 1.0; SFsps3 = 1.0; SFspp3 = 1.0;
                SHprr3 = 1.0; SHprp3 = 1.0; SFprr3 = 1.0; SFprp3 = 1.0;
            end

            % 太阳轮接触安全系数(SHsps3)（保留三位小数）
            fprintf(fid, '<td style="text-align:center;">%.3f</td>\n', round(SHsps3, 3));

            % 行星轮接触安全系数(SHspp3)
            fprintf(fid, '<td style="text-align:center;">%.3f</td>\n', round(SHspp3, 3));

            % 太阳轮弯曲安全系数(SFsps3)
            fprintf(fid, '<td style="text-align:center;">%.3f</td>\n', round(SFsps3, 3));

            % 行星轮弯曲安全系数(SFspp3)
            fprintf(fid, '<td style="text-align:center;">%.3f</td>\n', round(SFspp3, 3));

            % 内齿圈接触安全系数(SHprr3)
            fprintf(fid, '<td style="text-align:center;">%.3f</td>\n', round(SHprr3, 3));

            % 行星轮接触安全系数(SHprp3)
            fprintf(fid, '<td style="text-align:center;">%.3f</td>\n', round(SHprp3, 3));

            % 内齿圈弯曲安全系数(SFprr3)
            fprintf(fid, '<td style="text-align:center;">%.3f</td>\n', round(SFprr3, 3));

            % 行星轮弯曲安全系数(SFprp3)
            fprintf(fid, '<td style="text-align:center;">%.3f</td>\n', round(SFprp3, 3));
            
            % 尝试从质量数据文件中读取三级行星系质量数据
            mass_filename = fullfile(results_dir, sprintf('%s_mass_data_%d.mat', alg_names{i}, j));
            
            if exist(mass_filename, 'file')
                % 如果存在质量数据文件，直接加载
                mass_data = load(mass_filename);
                if isfield(mass_data, 'planetary_masses') && isfield(mass_data.planetary_masses, 'third_stage')
                    % 太阳轮质量(Ms3)
                    fprintf(fid, '<td style="text-align:center;">%.2f</td>\n', mass_data.planetary_masses.third_stage.sun);
                    
                    % 单个行星轮质量(Mp3)
                    fprintf(fid, '<td style="text-align:center;">%.2f</td>\n', mass_data.planetary_masses.third_stage.planet);
                    
                    % 内齿圈质量(Mr3)
                    fprintf(fid, '<td class="gear-group-3" style="text-align:center;">%.2f</td>\n', mass_data.planetary_masses.third_stage.ring);
                else
                    % 文件存在但格式不对，使用占比估算
                    fprintf(fid, '<td style="text-align:center;">%.2f</td>\n', solutions(j, 1) * 0.06); % 太阳轮质量(Ms3)
                    fprintf(fid, '<td style="text-align:center;">%.2f</td>\n', solutions(j, 1) * 0.03); % 单个行星轮质量(Mp3)
                    fprintf(fid, '<td class="gear-group-3" style="text-align:center;">%.2f</td>\n', solutions(j, 1) * 0.12); % 内齿圈质量(Mr3)
                end
            else
                % 文件不存在，使用占比估算
                fprintf(fid, '<td style="text-align:center;">%.2f</td>\n', solutions(j, 1) * 0.06); % 太阳轮质量(Ms3)
                fprintf(fid, '<td style="text-align:center;">%.2f</td>\n', solutions(j, 1) * 0.03); % 单个行星轮质量(Mp3)
                fprintf(fid, '<td class="gear-group-3" style="text-align:center;">%.2f</td>\n', solutions(j, 1) * 0.12); % 内齿圈质量(Mr3)
            end
            
            % 目标函数值
            fprintf(fid, '<td style="text-align:center; font-weight:bold;">%.2f</td>\n', solutions(j, 1)); % 总质量

            % 接触安全系数 - 放在左边，显示实际值
            contact_safety = -solutions(j, 3); % 显示实际安全系数值
            safety_req = 1.2; % 安全系数要求
            if contact_safety < safety_req
                fprintf(fid, '<td style="text-align:center; color:#e74c3c; font-weight:bold;">%.3f</td>\n', contact_safety);
            else
                fprintf(fid, '<td style="text-align:center; color:#27ae60; font-weight:bold;">%.3f</td>\n', contact_safety);
            end

            % 弯曲安全系数 - 放在右边，显示实际值
            bend_safety = -solutions(j, 2); % 显示实际安全系数值
            if bend_safety < safety_req
                fprintf(fid, '<td class="goal-group" style="text-align:center; color:#e74c3c; font-weight:bold;">%.3f</td>\n', bend_safety);
            else
                fprintf(fid, '<td class="goal-group" style="text-align:center; color:#27ae60; font-weight:bold;">%.3f</td>\n', bend_safety);
            end

            % 计算传动比
            z1 = round(variables(j, 2));
            z2 = round(variables(j, 3));
            zs2 = round(variables(j, 5));
            zp2 = round(variables(j, 6));
            zs3 = round(variables(j, 9));
            zp3 = round(variables(j, 10));
            zr2 = zs2 + 2 * zp2;
            zr3 = zs3 + 2 * zp3;
            i1 = z2 / z1;
            i2 = 1 + zr2/zs2;
            i3 = 1 + zr3/zs3;
            total_ratio = i1 * i2 * i3;
            target_ratio = input_speed / output_speed;
            ratio_error = abs(total_ratio - target_ratio) / target_ratio * 100;

            % 显示总传动比 - 使用黑色
            fprintf(fid, '<td style="text-align:center; font-weight:bold;">%.3f</td>\n', total_ratio);

            % 显示传动比误差 - 小于等于2.0%用绿色，大于2.0%用红色
            ratio_error_limit = 2.0; % 传动比误差限制
            if ratio_error <= ratio_error_limit
                fprintf(fid, '<td style="text-align:center; color:#27ae60; font-weight:bold;">%.2f</td>\n', ratio_error);
            else
                fprintf(fid, '<td style="text-align:center; color:#e74c3c; font-weight:bold;">%.2f</td>\n', ratio_error);
            end

            fprintf(fid, '</tr>\n');
        end
        fprintf(fid, '</tbody>\n');
        fprintf(fid, '</table>\n');
        
        fprintf(fid, '<p style="font-style:italic; margin-top:5px; color:#555;">注：按照（1.是否满足安全系数要求 2.质量 3.传动比误差）排序的前10个解，共有%d个Pareto最优解</p>\n', size(variables, 1));
        
        % 全局最优解不添加说明文字
        
        fprintf(fid, '</div>\n'); % 结束algorithm-section div
    end % 结束算法循环
    
    % 在页面底部添加最佳解方案来源说明
    fprintf(fid, '<div class="container" style="margin-top:20px; padding:15px; background-color:#f8f9fa; border-left:4px solid #3498db; border-radius:4px;">\n');
    fprintf(fid, '<p style="margin:0;"><strong>注：</strong> 这是基于%s算法从Pareto前沿中选择的最佳解决方案，综合考虑了传动比要求、质量和安全系数。</p>\n', alg_names{best_alg_idx});
    fprintf(fid, '</div>\n');
    
    % 添加技术补充说明
    fprintf(fid, '<h2>技术补充说明</h2>\n');
    
    fprintf(fid, '<div class="container">\n');
    fprintf(fid, '<div class="explanation">\n');
    fprintf(fid, '<h4>三级减速机系统说明</h4>\n');
    fprintf(fid, '<p>本系统包含一级平行轴齿轮传动和二、三级行星轮系传动，共同构成三级减速传动链。</p>\n');
    fprintf(fid, '<p>系统的总减速比为：i<sub>总</sub> = i<sub>1</sub> × i<sub>2</sub> × i<sub>3</sub>，其中：</p>\n');
    fprintf(fid, '<ul>\n');
    fprintf(fid, '<li>一级减速比：i<sub>1</sub> = z<sub>2</sub>/z<sub>1</sub></li>\n');
    fprintf(fid, '<li>二级行星减速比：i<sub>2</sub> = 1 + z<sub>r2</sub>/z<sub>s2</sub>（z<sub>r2</sub>为二级齿圈齿数）</li>\n');
    fprintf(fid, '<li>三级行星减速比：i<sub>3</sub> = 1 + z<sub>r3</sub>/z<sub>s3</sub>（z<sub>r3</sub>为三级齿圈齿数）</li>\n');
    fprintf(fid, '<li>行星轮系中，齿圈齿数：z<sub>r</sub> = z<sub>s</sub> + 2·z<sub>p</sub></li>\n');
    fprintf(fid, '</ul>\n');
    fprintf(fid, '<p>系统设计的关键约束：</p>\n');
    fprintf(fid, '<ul>\n');
    fprintf(fid, '<li>每组齿轮系相啮合的齿轮齿宽相同（同一对啮合齿轮齿宽必须一致）</li>\n');
    fprintf(fid, '<li>输入平行轴系和一级行星轮系压力角固定为20°</li>\n');
    fprintf(fid, '<li>二级行星轮系压力角可选20°或25°</li>\n');
    fprintf(fid, '<li>输入齿轮系为斜齿轮系，螺旋角范围为8-13°</li>\n');
    fprintf(fid, '<li>一、二级行星轮系均为直齿轮系（螺旋角为0°）</li>\n');
    fprintf(fid, '<li><strong style="color:#e74c3c;">总传动比必须满足目标要求，误差不允许超过±2%%，这是最高优先级约束</strong></li>\n');
    fprintf(fid, '</ul>\n');
    fprintf(fid, '</div>\n');
    
    fprintf(fid, '<div class="explanation">\n');
    fprintf(fid, '<h4>多目标优化说明</h4>\n');
    fprintf(fid, '<p>本优化问题同时考虑三个相互冲突的目标：</p>\n');
    fprintf(fid, '<ol>\n');
    fprintf(fid, '<li>最小化系统总质量（轻量化）</li>\n');
    fprintf(fid, '<li>最大化齿根弯曲强度安全系数</li>\n');
    fprintf(fid, '<li>最大化齿面接触强度安全系数</li>\n');
    fprintf(fid, '</ol>\n');
    fprintf(fid, '<p>Pareto最优解集合表示在这三个目标之间达到最佳平衡的解，没有一个解可以在不牺牲其他目标的情况下改进任何一个目标。</p>\n');
    fprintf(fid, '<p>用户可以根据具体应用场景和需求，从Pareto前沿中选择合适的设计方案。</p>\n');
    fprintf(fid, '</div>\n');
    
    fprintf(fid, '<div class="explanation">\n');
    fprintf(fid, '<h4>中心距和传动比说明</h4>\n');
    fprintf(fid, '<p>表格中显示的中心距和传动比计算方法如下：</p>\n');
    fprintf(fid, '<ul>\n');
    fprintf(fid, '<li><strong>一级传动中心距(a₁)</strong>：根据渐开线齿轮啮合原理，考虑变位系数和螺旋角影响，通过迭代计算啮合角得到。</li>\n');
    fprintf(fid, '<li><strong>一级传动比(i₁)</strong>：一级平行轴齿轮传动比，计算公式为 i₁ = z₂/z₁。</li>\n');
    fprintf(fid, '<li><strong>二级行星轮系中心距(a₂)</strong>：太阳轮与行星轮中心距，等于行星架旋转半径，计算公式为 a₂ = mn₂·(zs₂+zp₂)/2。</li>\n');
    fprintf(fid, '<li><strong>二级传动比(i₂)</strong>：二级行星轮系传动比，计算公式为 i₂ = 1 + zr₂/zs₂。</li>\n');
    fprintf(fid, '<li><strong>三级行星轮系中心距(a₃)</strong>：太阳轮与行星轮中心距，等于行星架旋转半径，计算公式为 a₃ = mn₃·(zs₃+zp₃)/2。</li>\n');
    fprintf(fid, '<li><strong>三级传动比(i₃)</strong>：三级行星轮系传动比，计算公式为 i₃ = 1 + zr₃/zs₃。</li>\n');
    fprintf(fid, '</ul>\n');
    fprintf(fid, '<p>在行星轮系中，太阳轮与行星轮的外啮合中心距与行星轮与内齿圈的内啮合中心距相等，这是行星轮系的几何约束条件。总传动比计算为：i总 = i₁ · i₂ · i₃</p>\n');
    fprintf(fid, '</div>\n');
    
    % 移除旧的安全系数参数说明，因为已经在设计参数表格下方添加了简化版本
    
    fprintf(fid, '</div>\n');
    
    % 添加页面底部的总结说明 - 在关闭文件前添加
    fprintf(fid, '<div class="explanation">\n');
    fprintf(fid, '<h4>结论与说明</h4>\n');
    fprintf(fid, '<p>根据优化结果分析，我们可以得出以下结论：</p>\n');
    fprintf(fid, '<ol>\n');
    fprintf(fid, '<li>在所有测试的算法中，<strong>%s</strong>算法在本问题上表现最佳，找到了满足约束条件的最优解。</li>\n', alg_names{best_alg_idx});
    
    % 根据最佳解的特性添加不同的说明
    if best_ratio_error <= 2.0 && best_safety_factor >= 1.2
        fprintf(fid, '<li>最佳解方案能够同时满足传动比误差（%.3f%%）≤2%%和安全系数（%.2f）≥1.2的要求。</li>\n', best_ratio_error, best_safety_factor);
    else
        % 提供更详细的不满足原因
        if best_ratio_error > 2.0 && best_safety_factor < 1.2
            fprintf(fid, '<li>未找到同时满足传动比误差≤2%%和安全系数≥1.2的解方案。最佳方案的传动比误差为%.3f%%，最小安全系数为%.2f。</li>\n', best_ratio_error, best_safety_factor);
        elseif best_ratio_error > 2.0
            fprintf(fid, '<li>最佳解方案未能满足传动比误差≤2%%的要求（实际误差为%.3f%%），但安全系数（%.2f）满足要求。</li>\n', best_ratio_error, best_safety_factor);
        else
            fprintf(fid, '<li>最佳解方案满足传动比误差要求（%.3f%%≤2%%），但安全系数（%.2f）未达到≥1.2的要求。</li>\n', best_ratio_error, best_safety_factor);
        end
    end
    
    fprintf(fid, '<li>优化结果表明，在保证传动性能的前提下，可以通过合理选择齿轮参数降低系统总质量至%.2f kg。</li>\n', best_mass);
    fprintf(fid, '</ol>\n');
    
    % 结束HTML文件
    fprintf(fid, '</div>\n'); % 结束main-container div
    fprintf(fid, '</body>\n');
    fprintf(fid, '</html>\n');
    
    % 关闭文件
    fclose(fid);

    % 显示生成的HTML文件路径
    disp(['已生成HTML报告: ' html_file]);

end