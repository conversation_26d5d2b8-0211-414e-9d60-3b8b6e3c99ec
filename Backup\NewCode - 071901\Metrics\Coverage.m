function c = Coverage(A, B)
% Coverage - C-metric覆盖率指标
% C(A,B) = |{b ∈ B | ∃a ∈ A: a ≼ b}| / |B|
% 计算集合A中有多少解支配了集合B中的解
%
% 输入:
%   A - 解集A
%   B - 解集B
%
% 输出:
%   c - 覆盖率值

if isempty(A) || isempty(B)
    c = 0;
    return;
end

n_B = size(B, 1);
n_dominated = 0;

for i = 1:n_B
    b = B(i, :);
    for j = 1:size(A, 1)
        a = A(j, :);
        % 检查a是否支配b
        % 对于最小化问题，a支配b意味着：a <= b（所有维度）且 a < b（至少一个维度）
        % 注意：在此代码中，第2、3列是安全系数，已经取负值转换为最小化问题
        if all(a <= b) && any(a < b)
            n_dominated = n_dominated + 1;
            break;  % 如果b被支配，不需要检查其他的a
        end
    end
end

c = n_dominated / n_B;
end
