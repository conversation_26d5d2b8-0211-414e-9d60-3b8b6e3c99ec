function param_def = GearParameterDefinition()
% GearParameterDefinition 统一的齿轮参数定义系统
% 定义优化变量、计算属性和表格输出的统一接口
%
% 输出:
%   param_def - 参数定义结构体，包含所有参数的定义和关系

%% 1. 优化变量定义（设计变量 - 可以被优化算法调整）
param_def.optimization_variables = {
    % 一级平行轴齿轮参数
    'm1',           % 一级模数 (mm)
    'z1',           % 一级小齿轮齿数
    'z2',           % 一级大齿轮齿数  
    'k_h1',         % 一级齿宽系数
    'x1',           % 一级小齿轮变位系数
    'x2',           % 一级大齿轮变位系数
    'beta1',        % 一级螺旋角 (度)
    
    % 二级行星轮系参数
    'mn2',          % 二级法向模数 (mm)
    'zs2',          % 二级太阳轮齿数
    'zp2',          % 二级行星轮齿数
    'k_h2',         % 二级齿宽系数
    'xs2',          % 二级太阳轮变位系数
    'xp2',          % 二级行星轮变位系数
    'beta2',        % 二级螺旋角 (度)
    'n2',           % 二级行星轮数量
    
    % 三级行星轮系参数
    'mn3',          % 三级法向模数 (mm)
    'zs3',          % 三级太阳轮齿数
    'zp3',          % 三级行星轮齿数
    'k_h3',         % 三级齿宽系数
    'xs3',          % 三级太阳轮变位系数
    'xp3',          % 三级行星轮变位系数
    'beta3',        % 三级螺旋角 (度)
    'n3'            % 三级行星轮数量
};

%% 2. 计算属性定义（由优化变量计算得出）
param_def.calculated_properties = {
    % 一级计算属性
    'x_sum1',       % 一级综合变位系数 = x1 + x2
    'alpha1',       % 一级压力角 (度) - 通常为20度
    'a1',           % 一级中心距 (mm) - 考虑变位系数
    'i1',           % 一级传动比 = z2/z1
    
    % 二级计算属性
    'zr2',          % 二级内齿圈齿数 = zs2 + 2*zp2
    'xr2',          % 二级内齿圈变位系数 - 根据几何关系计算
    'x_sum2',       % 二级综合变位系数 = xs2 + xp2 + xr2
    'alpha2',       % 二级压力角 (度)
    'a2',           % 二级中心距 (mm) - 太阳轮到行星轮
    'i2',           % 二级传动比 = 1 + zr2/zs2
    
    % 三级计算属性
    'zr3',          % 三级内齿圈齿数 = zs3 + 2*zp3
    'xr3',          % 三级内齿圈变位系数 - 根据几何关系计算
    'x_sum3',       % 三级综合变位系数 = xs3 + xp3 + xr3
    'alpha3',       % 三级压力角 (度)
    'a3',           % 三级中心距 (mm) - 太阳轮到行星轮
    'i3'            % 三级传动比 = 1 + zr3/zs3
};

%% 3. 性能属性定义（强度、质量等性能指标）
param_def.performance_properties = {
    % 一级性能属性
    'SH1',          % 一级接触安全系数
    'SF1',          % 一级小齿轮弯曲安全系数
    'SF2',          % 一级大齿轮弯曲安全系数
    'M1',           % 一级小齿轮质量 (kg)
    'M2',           % 一级大齿轮质量 (kg)
    
    % 二级性能属性
    'SHsps2',       % 二级太阳轮-行星轮接触安全系数
    'SHspp2',       % 二级行星轮-行星轮接触安全系数
    'SFsps2',       % 二级太阳轮-行星轮弯曲安全系数
    'SFspp2',       % 二级行星轮-行星轮弯曲安全系数
    'SHprr2',       % 二级行星轮-内齿圈接触安全系数
    'SHprp2',       % 二级行星轮-内齿圈接触安全系数
    'SFprr2',       % 二级行星轮-内齿圈弯曲安全系数
    'SFprp2',       % 二级行星轮-内齿圈弯曲安全系数
    'Ms2',          % 二级太阳轮质量 (kg)
    'Mp2',          % 二级行星轮质量 (kg) - 单个
    'Mr2',          % 二级内齿圈质量 (kg)
    
    % 三级性能属性
    'SHsps3',       % 三级太阳轮-行星轮接触安全系数
    'SHspp3',       % 三级行星轮-行星轮接触安全系数
    'SFsps3',       % 三级太阳轮-行星轮弯曲安全系数
    'SFspp3',       % 三级行星轮-行星轮弯曲安全系数
    'SHprr3',       % 三级行星轮-内齿圈接触安全系数
    'SHprp3',       % 三级行星轮-内齿圈接触安全系数
    'SFprr3',       % 三级行星轮-内齿圈弯曲安全系数
    'SFprp3',       % 三级行星轮-内齿圈弯曲安全系数
    'Ms3',          % 三级太阳轮质量 (kg)
    'Mp3',          % 三级行星轮质量 (kg) - 单个
    'Mr3'           % 三级内齿圈质量 (kg)
};

%% 4. 汇总指标定义（系统级性能指标）
param_def.summary_indicators = {
    'TotalMass',    % 总质量 (kg) = M1+M2+Ms2+Mp2*n2+Mr2+Ms3+Mp3*n3+Mr3
    'SH',           % 最小接触安全系数 = min(所有接触安全系数)
    'SF',           % 最小弯曲安全系数 = min(所有弯曲安全系数)
    'TotalRatio',   % 总传动比 = i1*i2*i3
    'Error'         % 传动比误差百分比 = |实际比-目标比|/目标比*100
};

%% 5. 完整表格参数列表（按显示顺序）
param_def.table_columns = [
    param_def.optimization_variables;
    param_def.calculated_properties;
    param_def.performance_properties;
    param_def.summary_indicators
];

%% 6. 参数约束定义
param_def.constraints = struct();

% 优化变量约束
param_def.constraints.m1 = [1.5, 8.0];           % 一级模数范围
param_def.constraints.z1 = [17, 100];            % 一级小齿轮齿数范围
param_def.constraints.z2 = [17, 120];            % 一级大齿轮齿数范围
param_def.constraints.k_h1 = [0.28, 0.4];        % 一级齿宽系数范围
param_def.constraints.x1 = [-0.8, 0.8];          % 一级小齿轮变位系数范围
param_def.constraints.x2 = [-0.8, 0.8];          % 一级大齿轮变位系数范围
param_def.constraints.beta1 = [8, 13];           % 一级螺旋角范围

param_def.constraints.mn2 = [1.5, 8.0];          % 二级法向模数范围
param_def.constraints.zs2 = [17, 100];           % 二级太阳轮齿数范围
param_def.constraints.zp2 = [17, 100];           % 二级行星轮齿数范围
param_def.constraints.k_h2 = [0.6, 1.0];         % 二级齿宽系数范围
param_def.constraints.xs2 = [-0.8, 0.8];         % 二级太阳轮变位系数范围
param_def.constraints.xp2 = [-0.8, 0.8];         % 二级行星轮变位系数范围
param_def.constraints.beta2 = [0, 0];            % 二级螺旋角（直齿）
param_def.constraints.n2 = [3, 6];               % 二级行星轮数量范围

param_def.constraints.mn3 = [1.5, 8.0];          % 三级法向模数范围
param_def.constraints.zs3 = [17, 100];           % 三级太阳轮齿数范围
param_def.constraints.zp3 = [17, 100];           % 三级行星轮齿数范围
param_def.constraints.k_h3 = [0.6, 1.0];         % 三级齿宽系数范围
param_def.constraints.xs3 = [-0.8, 0.8];         % 三级太阳轮变位系数范围
param_def.constraints.xp3 = [-0.8, 0.8];         % 三级行星轮变位系数范围
param_def.constraints.beta3 = [0, 0];            % 三级螺旋角（直齿）
param_def.constraints.n3 = [3, 6];               % 三级行星轮数量范围

%% 7. 参数类型标识
param_def.parameter_types = struct();
for i = 1:length(param_def.optimization_variables)
    param_def.parameter_types.(param_def.optimization_variables{i}) = 'optimization';
end
for i = 1:length(param_def.calculated_properties)
    param_def.parameter_types.(param_def.calculated_properties{i}) = 'calculated';
end
for i = 1:length(param_def.performance_properties)
    param_def.parameter_types.(param_def.performance_properties{i}) = 'performance';
end
for i = 1:length(param_def.summary_indicators)
    param_def.parameter_types.(param_def.summary_indicators{i}) = 'summary';
end

%% 8. 参数计算依赖关系
param_def.dependencies = struct();
param_def.dependencies.x_sum1 = {'x1', 'x2'};
param_def.dependencies.a1 = {'m1', 'z1', 'z2', 'x1', 'x2', 'beta1', 'alpha1'};
param_def.dependencies.i1 = {'z1', 'z2'};
param_def.dependencies.zr2 = {'zs2', 'zp2'};
param_def.dependencies.i2 = {'zs2', 'zr2'};
param_def.dependencies.zr3 = {'zs3', 'zp3'};
param_def.dependencies.i3 = {'zs3', 'zr3'};
param_def.dependencies.TotalRatio = {'i1', 'i2', 'i3'};
param_def.dependencies.TotalMass = {'M1', 'M2', 'Ms2', 'Mp2', 'Mr2', 'Ms3', 'Mp3', 'Mr3', 'n2', 'n3'};

end
