function [SF, SH] = ISO6336Calculator(gearParams, loadParams, materialParams, qualityGrade)
% ISO6336Calculator 根据ISO 6336标准计算齿轮安全系数
%
% 输入:
%   gearParams - 齿轮几何参数结构体
%       .m - 模数(mm)
%       .z - 齿数
%       .alpha - 压力角(度)
%       .beta - 螺旋角(度)
%       .b - 齿宽(mm)
%       .x - 变位系数
%   loadParams - 载荷参数结构体
%       .T - 扭矩(N·m)
%       .n - 转速(rpm)
%       .KA - 应用系数
%       .requiredLife - 要求寿命(h)
%   materialParams - 材料参数结构体
%       .E - 弹性模量(MPa)
%       .v - 泊松比
%       .HB - 布氏硬度
%       .sigmaFlim - 齿根弯曲疲劳极限(MPa)
%       .sigmaHlim - 接触疲劳极限(MPa)
%   qualityGrade - 齿轮精度等级(ISO 1328)
%
% 输出:
%   SF - 弯曲安全系数
%   SH - 接触安全系数

% 提取参数
m = gearParams.m;
z = gearParams.z;
alpha = gearParams.alpha * pi/180; % 转换为弧度
beta = gearParams.beta * pi/180;   % 转换为弧度
b = gearParams.b;
x = gearParams.x;

T = loadParams.T;
n = loadParams.n;
KA = loadParams.KA;
requiredLife = loadParams.requiredLife;

% 如果是螺旋齿轮，计算法向模数
if beta ~= 0
    mn = m;  % 法向模数
    mt = mn / cos(beta);  % 端面模数
else
    mn = m;
    mt = m;
end

% 计算基本几何参数
d = z * mt;  % 分度圆直径
da = d + 2 * (1 + x) * m;  % 齿顶圆直径
db = d * cos(alpha);  % 基圆直径
ha = (1 + x) * m;  % 齿顶高
hf = (1.25 - x) * m;  % 齿根高
h = ha + hf;  % 全齿高

% 计算载荷
Ft = 2000 * T / d;  % 切向力(N)

% 计算动载系数 Kv (ISO 6336-1)
v = pi * d * n / 60000;  % 齿轮节圆线速度(m/s)
if qualityGrade <= 5
    K1 = 0.075;
elseif qualityGrade <= 7
    K1 = 0.100;
elseif qualityGrade <= 10
    K1 = 0.125;
else
    K1 = 0.150;
end

% 简化的Kv计算
if v <= 10
    Kv = 1 + K1 * v;
else
    Kv = 1 + K1 * 10 + 0.5 * K1 * (v - 10);
end

% 计算载荷分布系数 KHbeta (ISO 6336-1)
% 简化计算，基于齿轮精度等级
if qualityGrade <= 5
    KHbeta = 1.05 + 0.005 * (b / d);
elseif qualityGrade <= 7
    KHbeta = 1.10 + 0.010 * (b / d);
elseif qualityGrade <= 10
    KHbeta = 1.15 + 0.015 * (b / d);
else
    KHbeta = 1.20 + 0.020 * (b / d);
end

% 计算载荷分布系数 KFbeta (ISO 6336-1)
KFbeta = KHbeta^0.9;

% 计算齿面系数 ZH (ISO 6336-2)
ZH = sqrt(2 * cos(beta) / cos(alpha)^2);

% 计算区域系数 ZE (ISO 6336-2)
ZE = sqrt(materialParams.E / (2 * (1 - materialParams.v^2)));

% 计算螺旋角系数 Zbeta (ISO 6336-2)
Zbeta = sqrt(cos(beta));

% 计算接触比系数 Zepsilon (ISO 6336-2)
% 简化计算
alpha_t = atan(tan(alpha) / cos(beta));  % 端面啮合角
pb = pi * m * cos(alpha);  % 基节圆
pe = pi * m / cos(alpha_t);  % 端面节圆
epsilon_alpha = (sqrt(da^2 - db^2) - d * sin(alpha_t)) / (pe);  % 端面重合度
if beta == 0
    epsilon_beta = 0;  % 直齿轮无轴向重合度
else
    epsilon_beta = b * sin(beta) / (pi * m);  % 轴向重合度
end
epsilon_gamma = epsilon_alpha + epsilon_beta;  % 总重合度

if epsilon_gamma < 1.2
    Zepsilon = sqrt(1.2 / epsilon_gamma);
else
    Zepsilon = sqrt(1 / epsilon_gamma);
end

% 计算寿命系数 ZN (ISO 6336-2)
NL = 60 * n * requiredLife;  % 循环次数
if NL <= 10^6
    ZN = 1.6 - 0.1 * log10(NL);
elseif NL <= 10^10
    ZN = 2 - 0.2 * log10(NL);
else
    ZN = 1.0;
end

% 计算硬度比系数 ZW (ISO 6336-2)
% 假设对偶齿轮硬度相同
ZW = 1.0;

% 计算尺寸系数 ZX (ISO 6336-3)
if m <= 10
    ZX = 1.0;
elseif m <= 30
    ZX = 1.05 - 0.005 * m;
else
    ZX = 0.9;
end

% 计算接触应力 sigmaH
sigmaH = ZH * ZE * Zepsilon * Zbeta * sqrt(Ft * KA * Kv * KHbeta / (b * d)) * ZW;

% 计算接触安全系数 SH
SH = materialParams.sigmaHlim * ZN * ZX / sigmaH;

% 计算齿形系数 YF (ISO 6336-3)
% 简化计算
h_haP = ha / m;  % 标准齿顶高系数
s_PR = m * (pi/2 + 2*x*tan(alpha));  % 分度圆齿厚
YF = (6 * h_haP * cos(alpha)) / (s_PR / m)^2;

% 计算应力修正系数 YS (ISO 6336-3)
% 简化计算
YS = 1.2 + 0.13 * h_haP;

% 计算螺旋角系数 Ybeta (ISO 6336-3)
if beta == 0
    Ybeta = 1.0;
else
    Ybeta = 1 - epsilon_beta * beta / 120;
end

% 计算载荷分布系数 YN (ISO 6336-3)
if NL <= 10^3
    YN = 2.5;
elseif NL <= 10^6
    YN = 4.5 - 0.4 * log10(NL);
elseif NL <= 10^10
    YN = 1.6 - 0.1 * log10(NL);
else
    YN = 1.0;
end

% 计算敏感系数 YdrelT (ISO 6336-3)
% 简化计算
YdrelT = 1.0;

% 计算尺寸系数 YX (ISO 6336-3)
if m <= 5
    YX = 1.0;
elseif m <= 30
    YX = 1.05 - 0.01 * m;
else
    YX = 0.75;
end

% 计算齿根应力 sigmaF
sigmaF = (Ft * KA * Kv * KFbeta / (b * m)) * YF * YS * Ybeta;

% 计算弯曲安全系数 SF
SF = materialParams.sigmaFlim * YN * YdrelT * YX / sigmaF;

end 