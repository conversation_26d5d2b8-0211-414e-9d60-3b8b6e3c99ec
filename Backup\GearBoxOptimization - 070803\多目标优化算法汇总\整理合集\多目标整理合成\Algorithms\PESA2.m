function [PF, PSet] = PESA2(CostFunction, nVar, VarMin, VarMax, MaxIt, nPop)
% PESA2: 基于范围选择的Pareto进化算法II (Pareto Envelope-based Selection Algorithm II)
%
% 输入参数:
%   CostFunction: 目标函数句柄
%   nVar: 决策变量数量
%   VarMin, VarMax: 决策变量的上下界
%   MaxIt: 最大迭代次数
%   nPop: 种群大小
%
% 输出参数:
%   PF: 非支配解的目标函数值（Pareto前沿）
%   PSet: 非支配解的决策变量（Pareto集）
%
% 参考文献:
%   Corne, D<PERSON>., <PERSON><PERSON><PERSON>, <PERSON>. <PERSON>, <PERSON>, J. <PERSON>, & <PERSON>, M. J. (2001).
%   "PESA-II: Region-based selection in evolutionary multiobjective optimization."
%   Proceedings of the Genetic and Evolutionary Computation Conference (GECCO).

    % 默认参数值
    if nargin < 6
        nPop = 50;
    end
    if nargin < 5
        MaxIt = 100;
    end
    if nargin < 4
        VarMax = 1;
    end
    if nargin < 3
        VarMin = 0;
    end
    if nargin < 2
        nVar = 3;
    end
    
    % PESA-II 参数
    nArchive = 50;            % 存档大小
    nGrid = 7;                % 每个维度的网格数量
    InflationFactor = 0.1;    % 网格膨胀因子
    beta_deletion = 1;        % 删除操作的压力参数
    beta_selection = 2;       % 选择操作的压力参数
    
    % 交叉参数
    pCrossover = 0.5;         % 交叉概率
    nCrossover = round(pCrossover * nPop / 2) * 2;
    crossover_params.gamma = 0.15;
    crossover_params.VarMin = VarMin;
    crossover_params.VarMax = VarMax;
    
    % 变异参数
    pMutation = 1 - pCrossover;   % 变异概率
    nMutation = nPop - nCrossover;
    mutation_params.h = 0.3;      % 变异步长
    mutation_params.VarMin = VarMin;
    mutation_params.VarMax = VarMax;
    
    % 获取目标函数数量
    sample = unifrnd(VarMin, VarMax, [1, nVar]);
    obj_values = CostFunction(sample);
    nObj = numel(obj_values);
    
    % 初始化个体结构体
    empty_individual.Position = [];
    empty_individual.Cost = [];
    empty_individual.IsDominated = [];
    empty_individual.GridIndex = [];
    
    % 初始化种群
    pop = repmat(empty_individual, nPop, 1);
    for i = 1:nPop
        pop(i).Position = unifrnd(VarMin, VarMax, [1, nVar]);
        pop(i).Cost = CostFunction(pop(i).Position);
    end
    
    % 初始化存档
    archive = [];
    
    % 主循环
    for it = 1:MaxIt
        % 确定支配关系
        pop = DetermineDomination(pop);
        
        % 提取非支配解
        ndpop = pop(~[pop.IsDominated]);
        
        % 更新存档
        archive = [archive; ndpop];
        
        % 确定存档中的支配关系
        archive = DetermineDomination(archive);
        
        % 保留非支配解
        archive = archive(~[archive.IsDominated]);
        
        % 创建网格
        [archive, grid] = CreateGrid(archive, nGrid, InflationFactor);
        
        % 如果存档太大，截断存档
        if numel(archive) > nArchive
            E = numel(archive) - nArchive;
            archive = TruncatePopulation(archive, grid, E, beta_deletion);
            [archive, grid] = CreateGrid(archive, nGrid, InflationFactor);
        end
        
        % 获取当前Pareto前沿
        PF_temp = archive;
        
        % 达到最大迭代次数时退出
        if it >= MaxIt
            break;
        end
        
        % 交叉操作
        popc = repmat(empty_individual, nCrossover/2, 2);
        for c = 1:nCrossover/2
            % 从存档中选择父代
            p1 = SelectFromPopulation(archive, grid, beta_selection);
            p2 = SelectFromPopulation(archive, grid, beta_selection);
            
            % 交叉操作
            [popc(c, 1).Position, popc(c, 2).Position] = Crossover(p1.Position, p2.Position, crossover_params);
            
            % 评估子代
            popc(c, 1).Cost = CostFunction(popc(c, 1).Position);
            popc(c, 2).Cost = CostFunction(popc(c, 2).Position);
        end
        popc = popc(:);
        
        % 变异操作
        popm = repmat(empty_individual, nMutation, 1);
        for m = 1:nMutation
            % 从存档中选择父代
            p = SelectFromPopulation(archive, grid, beta_selection);
            
            % 变异操作
            popm(m).Position = Mutate(p.Position, mutation_params);
            
            % 评估变异后的个体
            popm(m).Cost = CostFunction(popm(m).Position);
        end
        
        % 创建新种群
        pop = [popc; popm];
    end
    
    % 提取最终结果
    PF_members = archive;
    
    % 提取决策变量和目标函数值
    nPF = numel(PF_members);
    PSet = zeros(nPF, nVar);
    PF = zeros(nPF, nObj);
    
    for i = 1:nPF
        PSet(i, :) = PF_members(i).Position;
        PF(i, :) = PF_members(i).Cost;
    end
end

% 确定支配关系
function pop = DetermineDomination(pop)
    nPop = numel(pop);
    
    for i = 1:nPop
        pop(i).IsDominated = false;
    end
    
    for i = 1:nPop-1
        for j = i+1:nPop
            if Dominates(pop(i).Cost, pop(j).Cost)
                pop(j).IsDominated = true;
            elseif Dominates(pop(j).Cost, pop(i).Cost)
                pop(i).IsDominated = true;
            end
        end
    end
end

% 检查支配关系
function result = Dominates(x, y)
    % x支配y，如果x的所有目标都不劣于y，且至少有一个目标严格优于y
    result = all(x <= y) && any(x < y);
end

% 创建网格
function [pop, grid] = CreateGrid(pop, nGrid, alpha)
    if isempty(pop)
        grid = [];
        return;
    end
    
    % 提取目标函数值
    costs = reshape([pop.Cost], [], numel(pop))';
    
    % 计算网格边界
    grid.min = min(costs, [], 1);
    grid.max = max(costs, [], 1);
    
    % 防止min和max相同导致网格宽度为0
    delta = grid.max - grid.min;
    delta(delta == 0) = 1e-6;
    
    % 膨胀网格
    grid.min = grid.min - alpha * delta;
    grid.max = grid.max + alpha * delta;
    
    % 计算网格大小
    grid.size = (grid.max - grid.min) / nGrid;
    grid.nGrid = nGrid;
    
    % 计算每个个体的网格索引
    nObj = size(costs, 2);
    for i = 1:numel(pop)
        GridSubIndices = zeros(1, nObj);
        for j = 1:nObj
            GridSubIndices(j) = ceil((pop(i).Cost(j) - grid.min(j)) / grid.size(j));
            if GridSubIndices(j) < 1
                GridSubIndices(j) = 1;
            end
            if GridSubIndices(j) > nGrid
                GridSubIndices(j) = nGrid;
            end
        end
        
        % 计算总网格索引
        GridIndex = 0;
        for j = 1:nObj
            GridIndex = GridIndex + GridSubIndices(j) * nGrid^(j-1);
        end
        
        pop(i).GridIndex = GridIndex;
    end
end

% 截断存档
function pop = TruncatePopulation(pop, grid, E, beta)
    % 计算每个网格的个体数量
    GridIndices = [pop.GridIndex];
    OccupiedCells = unique(GridIndices);
    nOccupiedCells = numel(OccupiedCells);
    
    % 计算每个网格的拥挤度
    CellCount = zeros(1, nOccupiedCells);
    for k = 1:nOccupiedCells
        CellCount(k) = sum(GridIndices == OccupiedCells(k));
    end
    
    % 计算选择概率（拥挤度越高，概率越高）
    P = CellCount.^beta;
    P = P / sum(P);
    
    % 删除E个个体
    for e = 1:E
        % 选择一个网格
        SelectedCellIndex = RouletteWheelSelection(P);
        SelectedCell = OccupiedCells(SelectedCellIndex);
        
        % 找出选中网格中的个体
        SelectedCellMembers = find(GridIndices == SelectedCell);
        
        % 随机选择一个个体删除
        MemberToRemove = SelectedCellMembers(randi(numel(SelectedCellMembers)));
        
        % 删除选中的个体
        pop(MemberToRemove) = [];
        
        % 更新GridIndices
        GridIndices = [pop.GridIndex];
        
        % 更新网格拥挤度
        CellCount(SelectedCellIndex) = sum(GridIndices == SelectedCell);
        
        % 检查是否删空了某个网格
        if CellCount(SelectedCellIndex) == 0
            OccupiedCells(SelectedCellIndex) = [];
            CellCount(SelectedCellIndex) = [];
            P(SelectedCellIndex) = [];
            nOccupiedCells = nOccupiedCells - 1;
        else
            % 更新选择概率
            P = CellCount.^beta;
            P = P / sum(P);
        end
    end
end

% 从存档中选择个体
function p = SelectFromPopulation(pop, grid, beta)
    % 计算每个网格的个体数量
    GridIndices = [pop.GridIndex];
    OccupiedCells = unique(GridIndices);
    nOccupiedCells = numel(OccupiedCells);
    
    % 计算每个网格的拥挤度
    CellCount = zeros(1, nOccupiedCells);
    for k = 1:nOccupiedCells
        CellCount(k) = sum(GridIndices == OccupiedCells(k));
    end
    
    % 计算选择概率（拥挤度越低，概率越高）
    P = (1./CellCount).^beta;
    P = P / sum(P);
    
    % 轮盘赌选择一个网格
    SelectedCellIndex = RouletteWheelSelection(P);
    SelectedCell = OccupiedCells(SelectedCellIndex);
    
    % 找出选中网格中的个体
    SelectedCellMembers = find(GridIndices == SelectedCell);
    
    % 随机选择一个个体
    MemberToSelect = SelectedCellMembers(randi(numel(SelectedCellMembers)));
    
    % 返回选中的个体
    p = pop(MemberToSelect);
end

% 轮盘赌选择
function index = RouletteWheelSelection(P)
    r = rand;
    c = cumsum(P);
    index = find(r <= c, 1, 'first');
end

% 模拟二进制交叉
function [y1, y2] = Crossover(x1, x2, params)
    gamma = params.gamma;
    VarMin = params.VarMin;
    VarMax = params.VarMax;
    
    % 判断是否为向量
    if isscalar(VarMin)
        VarMin = VarMin * ones(size(x1));
    end
    if isscalar(VarMax)
        VarMax = VarMax * ones(size(x1));
    end
    
    % 交叉操作
    alpha = rand(size(x1)) * (1 + 2 * gamma) - gamma;
    
    y1 = alpha .* x1 + (1 - alpha) .* x2;
    y2 = alpha .* x2 + (1 - alpha) .* x1;
    
    % 边界处理
    y1 = max(y1, VarMin);
    y1 = min(y1, VarMax);
    
    y2 = max(y2, VarMin);
    y2 = min(y2, VarMax);
end

% 多项式变异
function y = Mutate(x, params)
    h = params.h;
    VarMin = params.VarMin;
    VarMax = params.VarMax;
    
    % 判断是否为向量
    if isscalar(VarMin)
        VarMin = VarMin * ones(size(x));
    end
    if isscalar(VarMax)
        VarMax = VarMax * ones(size(x));
    end
    
    % 变异操作
    y = x;
    
    % 对每个决策变量随机变异
    nVar = numel(x);
    j = randi(nVar);
    
    sigma = h * (VarMax(j) - VarMin(j));
    
    y(j) = x(j) + sigma * randn;
    
    % 边界处理
    y = max(y, VarMin);
    y = min(y, VarMax);
end 