classdef SPEA2 < ALGORITHM
% <multi> <real/integer/label/binary/permutation>
% Strength Pareto evolutionary algorithm 2

%------------------------------- Reference --------------------------------
% <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, SPEA2: Improving the strength
% Pareto evolutionary algorithm, Proceedings of the Conference on
% Evolutionary Methods for Design, Optimization and Control with
% Applications to Industrial Problems, 2001, 95-100.
%------------------------------- Copyright --------------------------------
% Copyright (c) 2024 BIMK Group. You are free to use the PlatEMO for
% research purposes. All publications which use this platform or any code
% in the platform should acknowledge the use of "PlatEMO" and reference "Ye
<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>, PlatEMO: A MATLAB platform
% for evolutionary multi-objective optimization [educational forum], IEEE
% Computational Intelligence Magazine, 2017, 12(4): 73-87".
%--------------------------------------------------------------------------

    methods
        function main(Algorithm,Problem)
            %% Generate random population
            Population = Problem.Initialization();
            Fitness    = CalFitness(Population.objs);

            %% Optimization
            while Algorithm.NotTerminated(Population)
                MatingPool = TournamentSelection(2,Problem.N,Fitness);
                Offspring  = OperatorGA(Problem,Population(MatingPool));
                [Population,Fitness] = EnvironmentalSelection([Population,Offspring],Problem.N);
            end
        end
    end
end