function [Pop_X, Pop_F] = NSWOA(CostFunction, nVar, VarMin, VarMax, MaxIt, nPop)
% NSWOA: 多目标非支配排序鲸鱼优化算法 (Non-dominated Sorting Whale Optimization Algorithm)
%
% 输入参数:
%   CostFunction: 目标函数句柄
%   nVar: 决策变量数量
%   VarMin, VarMax: 决策变量的上下界
%   MaxIt: 最大迭代次数
%   nPop: 种群大小
%
% 输出参数:
%   Pop_X: 非支配解的决策变量
%   Pop_F: 非支配解的目标函数值
%
% 参考文献:
%   Mir<PERSON><PERSON><PERSON>, S<PERSON>, & <PERSON>, A. (2016). 
%   "The Whale Optimization Algorithm." 
%   Advances in Engineering Software, 95, 51-67.

    % 默认参数值
    if nargin < 6
        nPop = 100;
    end
    if nargin < 5
        MaxIt = 100;
    end
    if nargin < 4
        VarMax = 1;
    end
    if nargin < 3
        VarMin = 0;
    end
    if nargin < 2
        nVar = 5;
    end
    
    % 获取目标函数数量
    sample = unifrnd(VarMin, VarMax, [1, nVar]);
    obj_values = CostFunction(sample);
    nObj = numel(obj_values);
    
    % 初始化种群
    K = nVar + nObj;  % 位置 + 目标函数值
    
    % 随机初始化位置
    Pop = zeros(nPop, K + 1);  % 最后一列用于标记非支配排序的等级
    
    for i = 1:nPop
        Pop(i, 1:nVar) = unifrnd(VarMin, VarMax, [1, nVar]);
        Pop(i, nVar+1:K) = CostFunction(Pop(i, 1:nVar));
    end
    
    % 初始化鲸鱼位置和辅助位置
    Whale_pos = Pop(:, 1:K+1);
    Whale_pos_ad = zeros(nPop, K);
    
    % 主循环
    for Iteration = 1:MaxIt
        for i = 1:nPop
            % 随机选择一个个体进行交互
            j = floor(rand() * nPop) + 1;
            while j == i
                j = floor(rand() * nPop) + 1;
            end
            
            % 缩放因子，用于MFO中的最佳覆盖
            SF = round(1 + rand);
            
            % 从第一前沿中随机选择最佳个体
            Ffront = Whale_pos((Whale_pos(:, K+1) == 1), :);  % 第一前沿
            ri = floor(size(Ffront, 1) * rand()) + 1;         % 随机索引
            sorted_population = Ffront(ri, 1:nVar);          % 随机选择的最佳个体
            
            % 计算新解 - 第一个试验向量
            Whale_posNew1 = Whale_pos(i, 1:nVar) + rand(1, nVar) .* (sorted_population - SF .* Whale_pos(i, 1:nVar));
            
            % 边界处理
            Whale_posNew1 = bound(Whale_posNew1, VarMax, VarMin);
            
            % 评估目标函数
            Whale_posNew1(:, nVar+1:K) = CostFunction(Whale_posNew1(:, 1:nVar));
            
            % 支配关系比较
            dom_less = 0;
            dom_equal = 0;
            dom_more = 0;
            
            for k = 1:nObj
                if Whale_posNew1(:, nVar+k) < Whale_pos(i, nVar+k)
                    dom_less = dom_less + 1;
                elseif Whale_posNew1(:, nVar+k) == Whale_pos(i, nVar+k)
                    dom_equal = dom_equal + 1;
                else
                    dom_more = dom_more + 1;
                end
            end
            
            % 如果新解支配当前解
            if dom_more == 0 && dom_equal ~= nObj
                % 保存当前解到辅助位置，用新解替换当前解
                Whale_pos_ad(i, 1:K) = Whale_pos(i, 1:K);
                Whale_pos(i, 1:K) = Whale_posNew1(:, 1:K);
            else
                % 保存新解到辅助位置
                Whale_pos_ad(i, 1:K) = Whale_posNew1;
            end
            
            % 重置支配计数
            dom_equal = 0;
            dom_more = 0;
            
            for k = 1:nObj
                dom_more = dom_more + 1;
            end
            
            if dom_more == 0 && dom_equal ~= nObj
                Whale_pos_ad(j, 1:K) = Whale_pos(j, 1:K);
            end
            
            % 再随机选择一个个体
            j = floor(rand() * nPop) + 1;
            while j == i
                j = floor(rand() * nPop) + 1;
            end
            
            % 鲸鱼优化算法参数
            a = 2 - Iteration * ((2) / MaxIt);       % a线性减小从2到0
            a2 = -1 + Iteration * ((-1) / MaxIt);    % a2线性减小从-1到-2
            r1 = rand();                             % r1是[0,1]中的随机数
            r2 = rand();                             % r2是[0,1]中的随机数
            A = 2 * a * r1 - a;                      % 公式(2.3)
            C = 2 * r2;                              % 公式(2.4)
            b = 1;                                   % 公式(2.5)中的参数
            t = (a2 - 1) * rand + 1;                 % 公式(2.5)中的参数
            p = rand();                              % 公式(2.6)中的参数p
            
            % 基于p值的位置更新策略
            if p < 0.5
                % 包围猎物：使用公式(2.3)和(2.4)
                X_rand = sorted_population;
                Whale_posNew1 = Whale_pos(i, 1:nVar) + X_rand - A .* abs(C * X_rand - Whale_pos(j, 1:nVar));
            elseif p >= 0.5
                % 螺旋更新位置：使用公式(2.5)
                Whale_posNew1 = Whale_pos(i, 1:nVar) + abs(sorted_population - Whale_pos(j, 1:nVar)) * exp(b .* t) .* cos(t .* 2 * pi) + sorted_population;
            end
            
            % 边界处理
            Whale_posNew1 = bound(Whale_posNew1(:, 1:nVar), VarMax, VarMin);
            
            % 评估目标函数
            Whale_posNew1(:, nVar+1:K) = CostFunction(Whale_posNew1(:, 1:nVar));
            
            % 支配关系比较
            dom_less = 0;
            dom_equal = 0;
            dom_more = 0;
            
            for k = 1:nObj
                if Whale_posNew1(:, nVar+k) < Whale_pos(i, nVar+k)
                    dom_less = dom_less + 1;
                elseif Whale_posNew1(:, nVar+k) == Whale_pos(i, nVar+k)
                    dom_equal = dom_equal + 1;
                else
                    dom_more = dom_more + 1;
                end
            end
            
            % 如果新解支配当前解
            if dom_more == 0 && dom_equal ~= nObj
                % 保存当前解到辅助位置，用新解替换当前解
                Whale_pos_ad(i, 1:K) = Whale_pos(i, 1:K);
                Whale_pos(i, 1:K) = Whale_posNew1(:, 1:K);
            else
                % 保存新解到辅助位置
                Whale_pos_ad(i, 1:K) = Whale_posNew1;
            end
            
            % 再随机选择一个个体
            j = floor(rand() * nPop) + 1;
            while j == i
                j = floor(rand() * nPop) + 1;
            end
            
            % 寄生向量机制
            parasiteVector = Whale_pos(i, 1:nVar);
            seed = randperm(nVar);
            pick = seed(1:ceil(rand * nVar));  % 随机选择维度
            
            % 在选定的维度上进行随机变异
            parasiteVector(:, pick) = rand(1, length(pick)) .* (VarMax - VarMin) + VarMin;
            
            % 评估寄生向量
            parasiteVector(:, nVar+1:K) = CostFunction(parasiteVector(:, 1:nVar));
            
            % 支配关系比较
            dom_less = 0;
            dom_equal = 0;
            dom_more = 0;
            
            for k = 1:nObj
                if parasiteVector(:, nVar+k) < Whale_pos(j, nVar+k)
                    dom_less = dom_less + 1;
                elseif parasiteVector(:, nVar+k) == Whale_pos(j, nVar+k)
                    dom_equal = dom_equal + 1;
                else
                    dom_more = dom_more + 1;
                end
            end
            
            % 如果寄生向量支配个体j
            if dom_more == 0 && dom_equal ~= nObj
                Whale_pos_ad(j, 1:K) = Whale_pos(j, 1:K);
                Whale_pos(j, 1:K) = parasiteVector(:, 1:K);
            else
                Whale_pos_ad(j, 1:K) = parasiteVector;
            end
        end
        
        % 合并当前位置和辅助位置
        Whale_pos_com = [Whale_pos(:, 1:K); Whale_pos_ad];
        
        % 非支配排序
        intermediate_Whale_pos = non_domination_sort_mod(Whale_pos_com, nObj, nVar);
        
        % 选择下一代种群
        Pop = replace_chromosome(intermediate_Whale_pos, nObj, nVar, nPop);
        Whale_pos = Pop(:, 1:K+1);
    end
    
    % 提取最终的非支配解
    % 仅返回第一前沿
    f = Whale_pos;
    first_front = f(f(:, K+1) == 1, :);
    
    % 提取决策变量和目标函数值
    Pop_X = first_front(:, 1:nVar);
    Pop_F = first_front(:, nVar+1:nVar+nObj);
end

% 边界处理函数
function a = bound(a, ub, lb)
    % 如果标量边界，转换为向量
    if isscalar(ub)
        ub = ones(size(a)) * ub;
    end
    if isscalar(lb)
        lb = ones(size(a)) * lb;
    end
    
    % 边界检查
    a(a > ub) = ub(a > ub);
    a(a < lb) = lb(a < lb);
end

% 非支配排序函数
function sorted_chromosome = non_domination_sort_mod(chromosome, M, V)
    [N, ~] = size(chromosome);
    
    % 初始化排序结果
    sorted_chromosome = chromosome;
    
    % 初始化等级
    for i = 1:N
        sorted_chromosome(i, V+M+1) = 0;
    end
    
    % 计算每个个体的支配数和被支配个体集合
    dominate_count = zeros(N, 1);
    dominated_by = cell(N, 1);
    
    for i = 1:N
        for j = i+1:N
            if dominates_check(sorted_chromosome(i, V+1:V+M), sorted_chromosome(j, V+1:V+M))
                dominated_by{i} = [dominated_by{i}, j];
                dominate_count(j) = dominate_count(j) + 1;
            elseif dominates_check(sorted_chromosome(j, V+1:V+M), sorted_chromosome(i, V+1:V+M))
                dominated_by{j} = [dominated_by{j}, i];
                dominate_count(i) = dominate_count(i) + 1;
            end
        end
    end
    
    % 等级分配
    current_front = find(dominate_count == 0)';
    front_num = 1;
    
    while ~isempty(current_front)
        for i = 1:length(current_front)
            sorted_chromosome(current_front(i), V+M+1) = front_num;
            for j = 1:length(dominated_by{current_front(i)})
                dominate_count(dominated_by{current_front(i)}(j)) = dominate_count(dominated_by{current_front(i)}(j)) - 1;
            end
        end
        
        front_num = front_num + 1;
        current_front = find(dominate_count == 0 & sorted_chromosome(:, V+M+1) == 0)';
    end
end

% 支配检查辅助函数
function result = dominates_check(x, y)
    result = all(x <= y) && any(x < y);
end

% 替换染色体函数
function new_chromosome = replace_chromosome(sorted_chromosome, M, V, N)
    [~, idx] = sortrows(sorted_chromosome(:, V+M+1));
    sorted_chromosome = sorted_chromosome(idx, :);
    
    max_rank = max(sorted_chromosome(:, V+M+1));
    new_chromosome = [];
    
    rank = 1;
    while size(new_chromosome, 1) < N && rank <= max_rank
        current_front = sorted_chromosome(sorted_chromosome(:, V+M+1) == rank, :);
        
        if size(new_chromosome, 1) + size(current_front, 1) <= N
            new_chromosome = [new_chromosome; current_front];
        else
            % 剩余可以添加的个体数量
            remaining = N - size(new_chromosome, 1);
            
            % 计算拥挤度
            crowding_distance = compute_crowding_distance(current_front, M, V);
            
            % 根据拥挤度排序
            [~, idx] = sort(crowding_distance, 'descend');
            current_front = current_front(idx, :);
            
            % 添加拥挤度最大的个体
            new_chromosome = [new_chromosome; current_front(1:remaining, :)];
        end
        
        rank = rank + 1;
    end
end

% 计算拥挤度
function crowding_distance = compute_crowding_distance(front, M, V)
    [N, ~] = size(front);
    crowding_distance = zeros(N, 1);
    
    for m = 1:M
        [~, idx] = sort(front(:, V+m));
        
        % 边界点拥挤度设为无穷大
        crowding_distance(idx(1)) = inf;
        crowding_distance(idx(N)) = inf;
        
        f_max = max(front(:, V+m));
        f_min = min(front(:, V+m));
        
        if f_max > f_min
            for i = 2:N-1
                crowding_distance(idx(i)) = crowding_distance(idx(i)) + ...
                    (front(idx(i+1), V+m) - front(idx(i-1), V+m)) / (f_max - f_min);
            end
        end
    end
end 