function GenerateFirstStageGearReport(center_distance, input_power, input_speed, service_life, contact_safety_factor, bending_safety_factor, gear_material)
% GenerateFirstStageGearReport 生成一级平行轴系齿轮参数组合报告并保存为HTML
%
%   输入参数:
%   - center_distance: 目标中心距 (mm)
%   - input_power: 输入功率 (kW)
%   - input_speed: 输入转速 (rpm)
%   - service_life: 设计寿命 (h)
%   - contact_safety_factor: 接触安全系数
%   - bending_safety_factor: 弯曲安全系数
%   - gear_material: 齿轮材料参数
%
%   输出:
%   - 无返回值，直接生成HTML报告文件

% 创建结果文件夹
results_dir = 'Results';
if ~exist(results_dir, 'dir')
    mkdir(results_dir);
    fprintf('创建结果文件夹: %s\n', results_dir);
end

% 螺旋角范围（度）- 按照原代码要求为8°-13°
helix_angle_range = [8, 13];

% 生成有效的齿轮组合
fprintf('开始生成一级平行轴系有效参数组合...\n');
fprintf('目标中心距: %.2f mm (误差范围：±0.05%%)\n', center_distance);
fprintf('输入功率: %.2f kW\n', input_power);
fprintf('输入转速: %.2f rpm\n', input_speed);
fprintf('螺旋角范围: %.1f°-%.1f°\n', helix_angle_range(1), helix_angle_range(2));
fprintf('变位系数范围: 总和(0.0-1.0)\n');

% 调用生成函数
gear_combinations = GenerateValidFirstStageGears(center_distance, input_power, input_speed, service_life, contact_safety_factor, bending_safety_factor, gear_material, helix_angle_range);

% 检查是否找到有效组合
if isempty(gear_combinations) || height(gear_combinations) == 0
    fprintf('警告：未找到满足约束的有效齿轮组合\n');
else
    % 这里不再重复输出组合数量，因为GenerateValidFirstStageGears已经输出了
    
    % 不显示前5个组合表格，只显示找到的组合总数
    fprintf('\n已找到 %d 个有效参数组合，详细信息请查看HTML报告\n', height(gear_combinations));
end

% 筛选满足安全系数要求的组合
fprintf('\n=== 安全系数筛选 ===\n');
fprintf('要求：接触安全系数 ≥ %.2f，弯曲安全系数 ≥ %.2f\n', contact_safety_factor, bending_safety_factor);

sf_combinations = [];
if ~isempty(gear_combinations)
    % 检查列名
    col_names = gear_combinations.Properties.VariableNames;
    contact_sf_col = find(strcmp(col_names, '接触安全系数'), 1);
    small_bending_sf_col = find(strcmp(col_names, '小齿轮弯曲安全系数'), 1);
    large_bending_sf_col = find(strcmp(col_names, '大齿轮弯曲安全系数'), 1);
    
    if isempty(contact_sf_col) || isempty(small_bending_sf_col) || isempty(large_bending_sf_col)
        % 如果找不到列名，使用位置索引
        contact_sf_col = 18;  % 接触安全系数
        small_bending_sf_col = 16;  % 小齿轮弯曲安全系数
        large_bending_sf_col = 17;  % 大齿轮弯曲安全系数
    end
    
    % 筛选满足安全系数要求的行
    sf_rows = gear_combinations{:, contact_sf_col} >= contact_safety_factor & ...
              gear_combinations{:, small_bending_sf_col} >= bending_safety_factor & ...
              gear_combinations{:, large_bending_sf_col} >= bending_safety_factor;
    
    sf_combinations = gear_combinations(sf_rows, :);
    
    % 检查筛选后是否还有组合
    if isempty(sf_combinations) || height(sf_combinations) == 0
        fprintf('警告：未找到同时满足安全系数要求的组合\n');
    else
        fprintf('找到 %d 个满足安全系数要求的组合\n', height(sf_combinations));
    end
end

% 保存满足安全系数要求的组合
if ~isempty(sf_combinations) && height(sf_combinations) > 0
    % 按质量从小到大排序
    sf_combinations = sortrows(sf_combinations, 13); % 按总质量排序
    
    % 保存CSV和MAT文件
    csv_filename = fullfile(results_dir, '一级平行轴系满足安全系数的参数.csv');
    mat_filename = fullfile(results_dir, '一级平行轴系满足安全系数的参数.mat');
    
    try
        writetable(sf_combinations, csv_filename);
        first_stage_valid_params = sf_combinations;
        save(mat_filename, 'first_stage_valid_params');
        fprintf('参数已保存至CSV和MAT文件\n');
        
        % 执行聚类
        fprintf('=== 一级参数聚类 ===\n');
        addpath('Model');  % 确保可以找到聚类函数
        
        % 执行聚类
        clustered_params = ClusterFirstStageParams(first_stage_valid_params);
        
        % 保存聚类结果
        clustered_csv = fullfile(results_dir, '一级平行轴系聚类后参数.csv');
        clustered_mat = fullfile(results_dir, '一级平行轴系聚类后参数.mat');
        writetable(clustered_params, clustered_csv);
        save(clustered_mat, 'clustered_params');
        
        % 显示聚类结果
        fprintf('聚类结果：%d 组原始参数 → %d 组代表性参数\n', height(first_stage_valid_params), height(clustered_params));
        fprintf('聚类结果已保存到CSV和MAT文件\n');
    catch ME
        fprintf('保存文件或执行聚类时出错: %s\n', ME.message);
    end
else
    fprintf('警告：没有满足安全系数要求的组合，无法保存文件\n');
end

% 生成HTML报告
fprintf('=== 生成HTML报告 ===\n');
fprintf('详细参数信息将包含在HTML报告中，按质量从小到大排序\n');
html_content = GenerateFirstStageHTML(gear_combinations, sf_combinations, contact_safety_factor, bending_safety_factor);

% 保存HTML文件
html_filename = fullfile(results_dir, '一级平行轴系参数组合报告.html');
fid = fopen(html_filename, 'w', 'n', 'utf-8');
if fid ~= -1
    fwrite(fid, html_content, 'char');
    fclose(fid);
    
    % 尝试在浏览器中打开报告
    try
        web(html_filename, '-browser');
        fprintf('HTML报告已生成并在浏览器中打开\n');
    catch
        fprintf('HTML报告已生成，请手动打开文件查看详细信息\n');
    end
else
    fprintf('错误：无法创建HTML报告文件\n');
end

end 