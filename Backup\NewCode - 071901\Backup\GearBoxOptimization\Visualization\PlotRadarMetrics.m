function [varargout] = PlotRadarMetrics(metrics, alg_names, save_path)
% PLOTRADARMETRICS 绘制算法多指标雷达图
%
% 输入:
%   metrics - 包含各算法评价指标的结构体
%   alg_names - 算法名称数组
%   save_path - 可选，保存图像的路径
%
% 输出:
%   varargout{1} - 可选，返回图窗句柄

% 设置默认字体为宋体
set(0, 'DefaultAxesFontName', 'SimSun');
set(0, 'DefaultTextFontName', 'SimSun');
set(0, 'DefaultUicontrolFontName', 'SimSun');

% 确保算法名称匹配
if isfield(metrics, 'AlgorithmNames')
    % 使用metrics中的算法名称
    alg_names = metrics.AlgorithmNames;
end

% 获取有效的算法数量
valid_algs = ~cellfun(@isempty, alg_names);
n_algs = sum(valid_algs);
alg_names = alg_names(valid_algs);

% 提取我们需要的六个指标（增加Time）
indicator_names = {'GD', 'IGD', 'Spread', 'MS', 'HV', 'Coverage', 'Time'};
% 使用简洁的原始标签，不含中文
n_indicators = length(indicator_names);

% 创建数据矩阵
data = zeros(n_algs, n_indicators);

% 填充数据矩阵
for i = 1:n_algs
    for j = 1:n_indicators
        field_name = indicator_names{j};
        if isfield(metrics, field_name) && i <= length(metrics.(field_name))
            data(i, j) = metrics.(field_name)(i);
        end
    end
end

% 标准化数据（0-1范围），考虑指标的方向性
norm_data = zeros(size(data));
for j = 1:n_indicators
    if strcmp(indicator_names{j}, 'GD') || strcmp(indicator_names{j}, 'IGD') || strcmp(indicator_names{j}, 'Spread') || strcmp(indicator_names{j}, 'Time')
        % 对于这些指标，值越小越好，需要反转
        min_val = min(data(:, j));
        max_val = max(data(:, j));
        if max_val > min_val
            norm_data(:, j) = 1 - (data(:, j) - min_val) / (max_val - min_val);
        else
            norm_data(:, j) = ones(n_algs, 1);
        end
    else
        % 对于HV和Coverage，值越大越好
        min_val = min(data(:, j));
        max_val = max(data(:, j));
        if max_val > min_val
            norm_data(:, j) = (data(:, j) - min_val) / (max_val - min_val);
        else
            norm_data(:, j) = ones(n_algs, 1);
        end
    end
end

% 获取屏幕尺寸以创建自适应窗口
screenSize = get(0, 'ScreenSize');
screenWidth = screenSize(3);
screenHeight = screenSize(4);

% 计算合适的窗口大小
figWidth = min(1000, screenWidth * 0.8);
figHeight = min(800, screenHeight * 0.8);
figLeft = (screenWidth - figWidth) / 2;
figBottom = (screenHeight - figHeight) / 2;

% 创建图形 - 使用自适应窗口大小
fig = figure('Name', '算法性能雷达图', 'NumberTitle', 'off', 'Color', 'white', 'Position', [figLeft, figBottom, figWidth, figHeight]);

% 设置鲜艳的颜色方案 - 与前沿图保持一致
colors = [
    1.0000, 0.0000, 0.0000;  % 红色 - NSGA-II
    0.0000, 0.0000, 1.0000;  % 蓝色 - NSGA-III
    0.0000, 0.7000, 0.0000;  % 鲜绿色 - SPEA2
    0.8000, 0.0000, 0.8000;  % 亮紫色 - MOEA-D
    0.0000, 0.8000, 0.8000;  % 青绿色 - MOEA-D-DE
    1.0000, 0.6000, 0.0000;  % 橙色 - MOEA-D-M2M
    0.5000, 0.0000, 1.0000;  % 紫色 - MOPSO
    1.0000, 1.0000, 0.0000;  % 鲜黄色 - MOGWO
    1.0000, 0.0000, 0.5000;  % 品红色 - MOWOA
];

% 确保颜色数量足够
if n_algs > size(colors, 1)
    colors = [colors; jet(n_algs - size(colors, 1))];
end

% 计算雷达图的角度
theta = linspace(0, 2*pi, n_indicators+1);
theta = theta(1:end-1); % 移除最后一个重复的点

% 设置3×3子图布局
rows = 3;
cols = 3;

% 添加总标题，减小字体大小
sgtitle('多目标优化算法性能雷达图对比', 'FontSize', 14, 'FontWeight', 'bold', 'FontName', 'SimSun');

% 绘制每个算法的雷达图到独立子图
for i = 1:min(n_algs, 9) % 最多显示9个算法
    % 创建子图
    subplot(rows, cols, i);
    
    % 初始化子图设置
    hold on;
    axis equal;
    axis off;
    
    % 绘制参考圆
    for r = 0.2:0.2:1
        circle_x = r * cos(linspace(0, 2*pi, 100));
        circle_y = r * sin(linspace(0, 2*pi, 100));
        plot(circle_x, circle_y, 'Color', [0.7 0.7 0.7], 'LineStyle', ':');
        
        % 添加刻度标签
        if r == 0.5
            text(0, r+0.05, sprintf('%.1f', r), 'HorizontalAlignment', 'center', 'FontSize', 7, 'Color', [0.5 0.5 0.5], 'FontName', 'SimSun');
        end
    end
    
    % 绘制径向线
    for j = 1:n_indicators
        line([0, 1.1*cos(theta(j))], [0, 1.1*sin(theta(j))], 'Color', [0.7 0.7 0.7], 'LineStyle', '-');
    end
    
    % 添加指标标签 - 简化版，只显示指标名
    for j = 1:n_indicators
        label_x = 1.15 * cos(theta(j));
        label_y = 1.15 * sin(theta(j));
        
        % 根据位置调整对齐方式
        if abs(label_x) < 0.1
            ha = 'center';
        elseif label_x > 0
            ha = 'left';
        else
            ha = 'right';
        end
        
        if abs(label_y) < 0.1
            va = 'middle';
        elseif label_y > 0
            va = 'bottom';
        else
            va = 'top';
        end
        
        % 简洁风格的文本标签，没有边框和背景
        text(label_x, label_y, indicator_names{j}, 'HorizontalAlignment', ha, 'VerticalAlignment', va, ...
            'FontWeight', 'bold', 'FontSize', 8, 'FontName', 'SimSun');
    end
    
    % 闭合多边形的数据
    values = [norm_data(i, :), norm_data(i, 1)];
    angles = [theta, theta(1)];
    
    % 转换为笛卡尔坐标
    x = values .* cos(angles);
    y = values .* sin(angles);
    
    % 绘制多边形
    patch(x, y, colors(i,:), 'FaceAlpha', 0.3, 'EdgeColor', colors(i,:), 'LineWidth', 2);
    
    % 添加算法名称作为子图标题
    title(alg_names{i}, 'FontSize', 11, 'Color', [0 0 0], 'FontWeight', 'bold', 'FontName', 'SimSun');
    
    % 设置轴范围
    axis([-1.2 1.2 -1.2 1.2]);
end

% 自适应调整子图布局，根据窗口大小计算位置
set(fig, 'Units', 'normalized');
for i = 1:min(n_algs, 9)
    % 计算行列位置
    row = ceil(i/cols);
    col = mod(i-1, cols) + 1;
    
    % 设置子图间距
    h_margin = 0.07; % 水平边距
    v_margin = 0.07; % 垂直边距
    h_spacing = (1 - 2*h_margin) / cols; % 水平间距
    v_spacing = (1 - 2*v_margin) / rows; % 垂直间距
    
    % 计算子图位置
    x = h_margin + (col-1) * h_spacing;
    y = 1 - v_margin - row * v_spacing;
    width = h_spacing * 0.85; % 宽度略小于间距
    height = v_spacing * 0.85; % 高度略小于间距
    
    % 设置子图位置
    pos = [x, y, width, height];
    set(subplot(rows, cols, i), 'Position', pos);
end

% 如果提供了保存路径，则保存图像
if nargin >= 3 && ~isempty(save_path)
    saveas(fig, save_path);
    fprintf('雷达图已保存至: %s\n', save_path);
end

% 返回图窗句柄，方便调用者进一步处理
if nargout > 0
    varargout{1} = fig;
end
end 