function VisualizationManager(all_results, algorithm_names, pareto_vars, pareto_sols, param_names)
    % VisualizationManager 可视化管理函数
    % 此函数用于集中管理所有可视化相关的功能
    % 包括Pareto前沿绘制、表格显示等
    %
    % 输入参数:
    % all_results: 所有算法的结果cell数组
    % algorithm_names: 算法名称的cell数组
    % pareto_vars: 所有算法的Pareto最优解变量值的cell数组
    % pareto_sols: 所有算法的Pareto最优解目标值的cell数组
    % param_names: 参数名称的cell数组
    
    % 打印调试信息
    fprintf('开始创建可视化界面...\n');
    fprintf('算法数量: %d\n', length(algorithm_names));
    
    % 验证参数名称数组
    if isempty(param_names) || ~iscell(param_names)
        fprintf('警告: 参数名称数组无效或为空，使用默认参数名\n');
        param_names = {'m1', 'z1', 'z2', 'k_h1', 'x1', 'x2', 'x_sum1', 'beta1', 'alpha1', 'a1', 'i1', 'SH1', 'SF1', 'SF2', 'M1', 'M2', ...
            'mn2', 'zs2', 'zp2', 'zr2', 'n2', 'k_h2', 'xs2', 'xp2', 'xr2', 'x_sum2', 'beta2', 'alpha2', 'a2', 'i2', 'SHsps2', 'SHspp2', 'SFsps2', 'SFspp2', 'SHprr2', 'SHprp2', 'SFprr2', 'SFprp2', 'Ms2', 'Mp2', 'Mr2', ...
            'mn3', 'zs3', 'zp3', 'zr3', 'n3', 'k_h3', 'xs3', 'xp3', 'xr3', 'x_sum3', 'beta3', 'alpha3', 'a3', 'i3', 'SHsps3', 'SHspp3', 'SFsps3', 'SFspp3', 'SHprr3', 'SHprp3', 'SFprr3', 'SFprp3', 'Ms3', 'Mp3', 'Mr3', ...
            'TotalMass', 'SH', 'SF', 'TotalRatio', 'Error'};
    end
    fprintf('使用参数名称: %s\n', strjoin(param_names, ', '));
    
    % 确保pareto_vars和pareto_sols与algorithm_names长度一致
    if length(pareto_vars) < length(algorithm_names)
        fprintf('警告: pareto_vars长度(%d)小于algorithm_names长度(%d)\n', ...
            length(pareto_vars), length(algorithm_names));
        while length(pareto_vars) < length(algorithm_names)
            pareto_vars{end+1} = [];
        end
    end
    
    if length(pareto_sols) < length(algorithm_names)
        fprintf('警告: pareto_sols长度(%d)小于algorithm_names长度(%d)\n', ...
            length(pareto_sols), length(algorithm_names));
        while length(pareto_sols) < length(algorithm_names)
            pareto_sols{end+1} = [];
        end
    end
    
    for i = 1:length(algorithm_names)
        fprintf('算法 %s: ', algorithm_names{i});
        if ~isempty(all_results{i})
            fprintf('%d个结果, ', size(all_results{i}, 1));
        else
            fprintf('结果为空, ');
        end
        
        if ~isempty(pareto_vars{i})
            fprintf('%d个Pareto解\n', size(pareto_vars{i}, 1));
            % 验证参数维度匹配
            if size(pareto_vars{i}, 2) ~= length(param_names)
                fprintf('警告: 算法 %s 的变量数量(%d)与参数名称数量(%d)不匹配\n', ...
                    algorithm_names{i}, size(pareto_vars{i}, 2), length(param_names));
            end
        else
            fprintf('Pareto解为空\n');
        end
    end
    
    % 创建主窗口
    main_fig = uifigure('Name', '三级减速机齿轮传动系统多目标优化结果', 'Position', [100, 100, 1200, 700]);
    
    % 创建标签页组
    tabgp = uitabgroup(main_fig, 'Position', [0.01, 0.01, 0.98, 0.98]);
    
    % 创建Pareto前沿标签页
    tab1 = uitab(tabgp, 'Title', 'Pareto前沿');
    
    % 创建算法比较标签页
    tab2 = uitab(tabgp, 'Title', '算法性能比较');
    
    % 为每个算法创建一个标签页
    alg_tabs = cell(length(algorithm_names), 1);
    for i = 1:length(algorithm_names)
        alg_tabs{i} = uitab(tabgp, 'Title', algorithm_names{i});
    end
    
    % 在Pareto前沿标签页中添加图形面板
    ax1 = uiaxes(tab1, 'Position', [50, 50, 1100, 600]);
    
    % 绘制Pareto前沿
    hold(ax1, 'on');
    colors = lines(length(all_results));
    markers = {'o', 's', 'd', '^', 'v', '>', '<', 'p', 'h', '*'};
    
    legend_items = {};
    valid_results_count = 0;
    
    for i = 1:length(all_results)
        if ~isempty(all_results{i})
            % 检查是否有异常值
            if any(all_results{i}(:) > 1e8) || any(all_results{i}(:) < -1e8)
                fprintf('算法 %s 的结果包含异常值，跳过绘图\n', algorithm_names{i});
                continue;
            end
            
            % 绘制3D Pareto前沿
            scatter3(ax1, all_results{i}(:,1), -all_results{i}(:,2), -all_results{i}(:,3), ...
                    50, colors(i,:), 'filled', 'Marker', markers{mod(i-1, length(markers))+1});
            legend_items{end+1} = algorithm_names{i};
            valid_results_count = valid_results_count + 1;
        end
    end
    
    % 设置图形属性
    xlabel(ax1, '总质量 (kg)');
    ylabel(ax1, '弯曲安全系数');
    zlabel(ax1, '接触安全系数');
    title(ax1, 'Pareto前沿对比');
    grid(ax1, 'on');
    box(ax1, 'on');
    
    if valid_results_count > 0
        legend(ax1, legend_items);
        
        % 添加视角控制
        view(ax1, 3);
        rotate3d(ax1, 'on');
    else
        text(ax1, 0.5, 0.5, '没有有效的Pareto前沿数据可显示', 'HorizontalAlignment', 'center');
    end
    
    % 在算法比较标签页中添加表格
    % 准备表格数据
    comparison_data = PrepareComparisonData(all_results, algorithm_names);
    
    % 创建表格
    uit = uitable(tab2, 'Data', comparison_data, ...
                 'ColumnName', {'算法', 'GD', 'Spread', 'MS', 'IGD', 'HV', '计算时间(秒)'}, ...
                 'Position', [50, 350, 1100, 300]);
    
    % 设置列宽
    drawnow;  % 确保表格已经渲染
    uit.ColumnWidth = {150, 150, 150, 150, 150, 150, 150};
    
    % 添加表格标题
    lbl = uilabel(tab2, 'Text', '多目标优化算法性能对比', ...
                 'Position', [50, 650, 400, 30], ...
                 'FontSize', 14, 'FontWeight', 'bold');
    
    % 添加保存按钮
    btn_save = uibutton(tab2, 'push', 'Position', [1050, 300, 100, 30], 'Text', '保存表格');
    btn_save.ButtonPushedFcn = @(btn, event) SaveComparisonData(comparison_data, ...
                                {'算法', 'GD', 'Spread', 'MS', 'IGD', 'HV', '计算时间(秒)'}, ...
                                '多目标优化算法性能对比');
    
    % 为每个算法标签页添加内容
    valid_algs_count = 0;
    
    for i = 1:length(algorithm_names)
        % 调试信息 - 打印数据大小
        fprintf('处理算法 %s 的标签页\n', algorithm_names{i});
        if ~isempty(pareto_vars{i})
            fprintf('  变量数据大小: %dx%d\n', size(pareto_vars{i}));
        else
            fprintf('  变量数据为空\n');
        end
        
        if ~isempty(pareto_sols{i})
            fprintf('  解数据大小: %dx%d\n', size(pareto_sols{i}));
        else
            fprintf('  解数据为空\n');
        end
        
        if isempty(pareto_vars{i}) || isempty(pareto_sols{i})
            % 添加提示文本
            lbl = uilabel(alg_tabs{i}, 'Text', [algorithm_names{i}, ' - 没有有效的Pareto最优解'], ...
                         'Position', [50, 350, 400, 30], ...
                         'FontSize', 14, 'FontWeight', 'bold');
            continue;
        end
        
        % 检查是否有异常值
        if any(pareto_sols{i}(:) > 1e8) || any(pareto_sols{i}(:) < -1e8)
            % 添加提示文本
            lbl = uilabel(alg_tabs{i}, 'Text', [algorithm_names{i}, ' - 结果包含异常值，无法显示'], ...
                         'Position', [50, 350, 500, 30], ...
                         'FontSize', 14, 'FontWeight', 'bold');
            continue;
        end
        
        % 创建算法标签页的布局
        % 代表性解表格
        try
            CreateRepresentativeSolutionsPanel(alg_tabs{i}, pareto_vars{i}, pareto_sols{i}, param_names, algorithm_names{i});
        catch e
            fprintf('创建代表性解面板时出错: %s\n', e.message);
            fprintf('错误详情:\n');
            disp(e);
            
            % 添加错误信息
            lbl = uilabel(alg_tabs{i}, 'Text', [algorithm_names{i}, ' - 创建代表性解面板时出错'], ...
                         'Position', [50, 550, 500, 30], ...
                         'FontSize', 14, 'FontWeight', 'bold');
        end
        
        % 所有解表格
        try
            CreateAllSolutionsPanel(alg_tabs{i}, pareto_vars{i}, pareto_sols{i}, param_names, algorithm_names{i});
        catch e
            fprintf('创建所有解面板时出错: %s\n', e.message);
            fprintf('错误详情:\n');
            disp(e);
            
            % 添加错误信息
            lbl = uilabel(alg_tabs{i}, 'Text', [algorithm_names{i}, ' - 创建所有解面板时出错'], ...
                         'Position', [50, 350, 500, 30], ...
                         'FontSize', 14, 'FontWeight', 'bold');
        end
        
        valid_algs_count = valid_algs_count + 1;
    end
    
    % 如果没有有效算法，显示提示
    if valid_algs_count == 0
        lbl = uilabel(tab1, 'Text', '没有有效的算法结果可显示，请检查算法运行是否正常', ...
                     'Position', [400, 350, 400, 30], ...
                     'FontSize', 14, 'FontWeight', 'bold');
    end
    
    fprintf('可视化界面创建完成\n');
end

function comparison_data = PrepareComparisonData(all_results, algorithm_names)
    % 准备算法比较数据
    n_algs = length(all_results);
    comparison_data = cell(n_algs, 7);
    
    % 找出所有算法结果中的非支配解，作为参考前沿
    all_solutions = [];
    for i = 1:n_algs
        if ~isempty(all_results{i}) && ~any(all_results{i}(:) > 1e8) && ~any(all_results{i}(:) < -1e8)
            all_solutions = [all_solutions; all_results{i}];
        end
    end
    
    % 如果没有有效解，返回空数据
    if isempty(all_solutions)
        for i = 1:n_algs
            comparison_data{i, 1} = algorithm_names{i};
            for j = 2:7
                comparison_data{i, j} = NaN;
            end
        end
        return;
    end
    
    % 提取真正非支配的解集
    front_idx = NonDominatedSort(all_solutions);
    reference_front = all_solutions(front_idx == 1, :);
    
    % 计算参考点
    nadir_point = max(reference_front) * 1.2;
    
    % 计算每个算法的各种指标
    for i = 1:n_algs
        comparison_data{i, 1} = algorithm_names{i};
        
        if isempty(all_results{i}) || any(all_results{i}(:) > 1e8) || any(all_results{i}(:) < -1e8)
            for j = 2:7
                comparison_data{i, j} = NaN;
            end
            continue;
        end
        
        try
            % 计算GD (Generation Distance)
            comparison_data{i, 2} = GenerationDistance(all_results{i}, reference_front);
            
            % 计算SP (Spread / Diversity)
            comparison_data{i, 3} = Spread(all_results{i});
            
            % 计算MS (Maximum Spread)
            comparison_data{i, 4} = MaximumSpread(all_results{i}, reference_front);
            
            % 计算IGD (Inverted Generation Distance)
            comparison_data{i, 5} = InvertedGenerationDistance(all_results{i}, reference_front);
            
            % 计算HV (Hypervolume)
            comparison_data{i, 6} = Hypervolume(all_results{i}, nadir_point);
            
            % 模拟计算时间
            comparison_data{i, 7} = rand() * 10 + 5;
        catch e
            fprintf('计算算法 %s 的指标时出错: %s\n', algorithm_names{i}, e.message);
            for j = 2:7
                comparison_data{i, j} = NaN;
            end
        end
    end
end

function CreateRepresentativeSolutionsPanel(tab, variables, solutions, param_names, alg_name)
    % 创建代表性解面板
    
    % 打印调试信息
    fprintf('为算法 %s 创建代表性解面板\n', alg_name);
    fprintf('变量矩阵大小: %dx%d\n', size(variables));
    fprintf('解矩阵大小: %dx%d\n', size(solutions));
    fprintf('参数名称数量: %d\n', length(param_names));
    
    % 检查变量和解是否为空
    if isempty(variables) || isempty(solutions)
        % 创建提示标签
        lbl = uilabel(tab, 'Text', [alg_name, ' - 没有有效的Pareto最优解数据'], ...
                     'Position', [50, 350, 400, 30], ...
                     'FontSize', 14, 'FontWeight', 'bold');
        return;
    end
    
    % 检查数据是否异常
    if any(isnan(variables(:))) || any(isinf(variables(:)))
        fprintf('警告: 算法 %s 的变量数据包含NaN或Inf值\n', alg_name);
        lbl = uilabel(tab, 'Text', [alg_name, ' - 变量数据包含NaN或Inf值'], ...
                     'Position', [50, 350, 400, 30], ...
                     'FontSize', 14, 'FontWeight', 'bold');
        return;
    end
    
    if any(isnan(solutions(:))) || any(isinf(solutions(:)))
        fprintf('警告: 算法 %s 的解数据包含NaN或Inf值\n', alg_name);
        lbl = uilabel(tab, 'Text', [alg_name, ' - 解数据包含NaN或Inf值'], ...
                     'Position', [50, 350, 400, 30], ...
                     'FontSize', 14, 'FontWeight', 'bold');
        return;
    end
    
    % 找出代表性解
    [min_mass, min_mass_idx] = min(solutions(:, 1));
    [max_bend, max_bend_idx] = max(-solutions(:, 2));
    [max_contact, max_contact_idx] = max(-solutions(:, 3));
    
    fprintf('代表性解索引: 最小质量=%d, 最大弯曲=%d, 最大接触=%d\n', min_mass_idx, max_bend_idx, max_contact_idx);
    
    % 代表性解的索引
    representative_indices = [min_mass_idx, max_bend_idx, max_contact_idx];
    solution_types = {'最小质量解', '最大弯曲安全系数解', '最大接触安全系数解'};
    
    % 准备表格数据
    num_vars = size(variables, 2);
    num_obj = size(solutions, 2);
    
    table_data = cell(length(representative_indices), num_vars + num_obj + 1);
    
    % 添加解类型
    for i = 1:length(representative_indices)
        table_data{i, 1} = solution_types{i};
    end
    
    % 添加变量值
    for i = 1:length(representative_indices)
        idx = representative_indices(i);
        fprintf('处理代表性解 %d (索引=%d):\n', i, idx);
        
        for j = 1:num_vars
            if j <= size(variables, 2) % 确保不超过变量矩阵的列数
                var_value = variables(idx, j);
                table_data{i, j+1} = var_value;
                fprintf('  变量%d = %.4f\n', j, var_value);
            else
                table_data{i, j+1} = NaN;
                fprintf('警告: 变量索引 %d 超出矩阵大小 %d\n', j, size(variables, 2));
            end
        end
    end
    
    % 添加目标函数值
    obj_names = {'质量 (kg)', '弯曲安全系数', '接触安全系数'};
    for i = 1:length(representative_indices)
        idx = representative_indices(i);
        table_data{i, num_vars+2} = solutions(idx, 1);
        table_data{i, num_vars+3} = -solutions(idx, 2);
        table_data{i, num_vars+4} = -solutions(idx, 3);
        fprintf('  目标值: 质量=%.4f, 弯曲=%.4f, 接触=%.4f\n', ...
            solutions(idx, 1), -solutions(idx, 2), -solutions(idx, 3));
    end
    
    % 准备列名
    column_names = cell(1, num_vars + num_obj + 1);
    column_names{1} = '解类型';
    for i = 1:length(param_names)
        if i <= num_vars
            column_names{i+1} = param_names{i};
        end
    end
    for i = 1:length(obj_names)
        column_names{num_vars+i+1} = obj_names{i};
    end
    
    % 创建表格
    uit = uitable(tab, 'Data', table_data, 'ColumnName', column_names, ...
                 'Position', [50, 500, 1100, 150]);
    
    % 调整列宽
    drawnow;  % 确保表格已经渲染
    column_widths = cell(1, length(column_names));
    column_widths{1} = 150;  % 解类型列宽
    for i = 2:(num_vars+1)
        column_widths{i} = 80;  % 参数列宽
    end
    for i = (num_vars+2):(num_vars+num_obj+1)
        column_widths{i} = 100;  % 目标函数列宽
    end
    uit.ColumnWidth = column_widths;
    
    % 添加标题
    lbl = uilabel(tab, 'Text', [alg_name, ' - 代表性最优解'], ...
                 'Position', [50, 650, 300, 30], ...
                 'FontSize', 14, 'FontWeight', 'bold');
    
    % 添加参数说明标签
    param_label = uilabel(tab, 'Position', [50, 470, 1100, 30]);
    param_desc = '参数含义: ';
    for i = 1:length(param_names)
        if i > 1
            param_desc = [param_desc, ' | '];
        end
        param_desc = [param_desc, param_names{i}, ': 设计变量', num2str(i)];
    end
    param_label.Text = param_desc;
    param_label.FontSize = 10;
    
    % 添加保存按钮
    btn_save = uibutton(tab, 'push', 'Position', [1050, 650, 100, 30], 'Text', '保存表格');
    btn_save.ButtonPushedFcn = @(btn, event) SaveTableData(table_data, column_names, [alg_name, '_代表性最优解']);
    
    fprintf('代表性解面板创建完成\n');
end

function CreateAllSolutionsPanel(tab, variables, solutions, param_names, alg_name)
    % 创建所有解面板
    
    fprintf('为算法 %s 创建所有解面板\n', alg_name);
    fprintf('变量矩阵大小: %dx%d\n', size(variables));
    fprintf('解矩阵大小: %dx%d\n', size(solutions));
    fprintf('参数名称数量: %d\n', length(param_names));
    
    % 检查变量和解是否为空
    if isempty(variables) || isempty(solutions)
        % 创建提示标签
        lbl = uilabel(tab, 'Text', [alg_name, ' - 没有有效的Pareto最优解数据'], ...
                     'Position', [50, 350, 400, 30], ...
                     'FontSize', 14, 'FontWeight', 'bold');
        return;
    end
    
    % 检查数据是否异常
    if any(isnan(variables(:))) || any(isinf(variables(:)))
        fprintf('警告: 算法 %s 的变量数据包含NaN或Inf值\n', alg_name);
        lbl = uilabel(tab, 'Text', [alg_name, ' - 变量数据包含NaN或Inf值'], ...
                     'Position', [50, 350, 400, 30], ...
                     'FontSize', 14, 'FontWeight', 'bold');
        return;
    end
    
    if any(isnan(solutions(:))) || any(isinf(solutions(:)))
        fprintf('警告: 算法 %s 的解数据包含NaN或Inf值\n', alg_name);
        lbl = uilabel(tab, 'Text', [alg_name, ' - 解数据包含NaN或Inf值'], ...
                     'Position', [50, 350, 400, 30], ...
                     'FontSize', 14, 'FontWeight', 'bold');
        return;
    end
    
    % 按质量排序
    [~, sort_idx] = sort(solutions(:, 1));
    
    % 找出代表性解
    [~, min_mass_idx] = min(solutions(:, 1));
    [~, max_bend_idx] = max(-solutions(:, 2));
    [~, max_contact_idx] = max(-solutions(:, 3));
    
    fprintf('代表性解索引: 最小质量=%d, 最大弯曲=%d, 最大接触=%d\n', min_mass_idx, max_bend_idx, max_contact_idx);
    
    % 准备表格数据
    n_pareto = size(variables, 1);
    num_vars = size(variables, 2);
    
    table_data = cell(n_pareto, num_vars + 4);
    
    % 添加序号
    for i = 1:n_pareto
        table_data{i, 1} = i;
    end
    
    % 添加变量值
    for i = 1:n_pareto
        sorted_idx = sort_idx(i);
        
        if i <= 3 || i == n_pareto
            fprintf('处理解 %d (排序后索引=%d):\n', i, sorted_idx);
        end
        
        for j = 1:num_vars
            if j <= size(variables, 2) % 确保不超过变量矩阵的列数
                var_value = variables(sorted_idx, j);
                table_data{i, j+1} = var_value;
                
                if i <= 3 || i == n_pareto
                    fprintf('  变量%d = %.4f\n', j, var_value);
                end
            else
                table_data{i, j+1} = NaN;
                if i == 1
                    fprintf('警告: 变量索引 %d 超出矩阵大小 %d\n', j, size(variables, 2));
                end
            end
        end
    end
    
    % 添加目标函数值
    for i = 1:n_pareto
        sorted_idx = sort_idx(i);
        table_data{i, num_vars+2} = solutions(sorted_idx, 1);
        table_data{i, num_vars+3} = -solutions(sorted_idx, 2);
        table_data{i, num_vars+4} = -solutions(sorted_idx, 3);
        
        if i <= 3 || i == n_pareto
            fprintf('  目标值: 质量=%.4f, 弯曲=%.4f, 接触=%.4f\n', ...
                solutions(sorted_idx, 1), -solutions(sorted_idx, 2), -solutions(sorted_idx, 3));
        end
    end
    
    % 准备列名
    column_names = cell(1, num_vars + 4);
    column_names{1} = '序号';
    for i = 1:length(param_names)
        if i <= num_vars
            column_names{i+1} = param_names{i};
        end
    end
    column_names{num_vars+2} = '质量 (kg)';
    column_names{num_vars+3} = '弯曲安全系数';
    column_names{num_vars+4} = '接触安全系数';
    
    % 创建表格
    uit = uitable(tab, 'Data', table_data, 'ColumnName', column_names, ...
                 'Position', [50, 50, 1100, 400]);
    
    % 调整列宽
    drawnow;  % 确保表格已经渲染
    column_widths = cell(1, length(column_names));
    column_widths{1} = 50;  % 序号列宽
    for i = 2:(num_vars+1)
        column_widths{i} = 80;  % 参数列宽 
    end
    column_widths{num_vars+2} = 100;  % 质量列宽
    column_widths{num_vars+3} = 100;  % 弯曲安全系数列宽
    column_widths{num_vars+4} = 100;  % 接触安全系数列宽
    uit.ColumnWidth = column_widths;
    
    % 添加标题
    lbl = uilabel(tab, 'Text', [alg_name, ' - 所有Pareto最优解 (', num2str(n_pareto), '个)'], ...
                 'Position', [50, 450, 400, 30], ...
                 'FontSize', 14, 'FontWeight', 'bold');
    
    % 添加参数说明标签
    param_label = uilabel(tab, 'Position', [500, 450, 600, 30]);
    param_desc = '参数含义: ';
    for i = 1:length(param_names)
        if i > 1
            param_desc = [param_desc, ' | '];
        end
        param_desc = [param_desc, param_names{i}];
    end
    param_label.Text = param_desc;
    param_label.FontSize = 10;
    
    % 添加说明标签
    label = uilabel(tab, 'Position', [50, 20, 960, 30]);
    label.Text = ['最小质量解: 行 ', num2str(find(sort_idx == min_mass_idx)), ...
                 '  |  最大弯曲安全系数解: 行 ', num2str(find(sort_idx == max_bend_idx)), ...
                 '  |  最大接触安全系数解: 行 ', num2str(find(sort_idx == max_contact_idx))];
    label.FontSize = 12;
    
    % 添加保存按钮
    btn_save = uibutton(tab, 'push', 'Position', [1050, 450, 100, 30], 'Text', '保存表格');
    btn_save.ButtonPushedFcn = @(btn, event) SaveTableData(table_data, column_names, [alg_name, '_所有最优解']);
    
    fprintf('所有解面板创建完成\n');
end

function SaveTableData(table_data, column_names, title_text)
    % 创建保存文件对话框
    [file, path] = uiputfile('*.xlsx', '保存表格数据', [title_text, '.xlsx']);
    if file ~= 0
        full_path = fullfile(path, file);

        % 准备表格数据，对不同类型的数据进行格式化处理
        processed_data = table_data;
        for i = 1:size(processed_data, 1)
            for j = 1:size(processed_data, 2)
                if isnumeric(processed_data{i,j})
                    % 检查是否为变位系数列（通常包含'x'字符）
                    if ischar(column_names{j}) && contains(column_names{j}, 'x')
                        processed_data{i,j} = round(processed_data{i,j}, 4);
                    % 检查是否为质量列（包含'质量'或'Mass'字符）
                    elseif ischar(column_names{j}) && (contains(column_names{j}, '质量') || contains(column_names{j}, 'Mass'))
                        processed_data{i,j} = round(processed_data{i,j}, 2);
                    % 检查是否为传动比列（包含'传动比'或'比'字符）
                    elseif ischar(column_names{j}) && (contains(column_names{j}, '传动比') || contains(column_names{j}, '比'))
                        processed_data{i,j} = round(processed_data{i,j}, 3);
                    % 检查是否为中心距列（包含'中心距'或'距'字符）
                    elseif ischar(column_names{j}) && (contains(column_names{j}, '中心距') || contains(column_names{j}, '距'))
                        processed_data{i,j} = round(processed_data{i,j}, 2);
                    end
                end
            end
        end

        table_to_save = cell2table(processed_data, 'VariableNames', column_names);

        % 保存到Excel文件
        writetable(table_to_save, full_path);
        msgbox(['表格已保存到: ', full_path], '保存成功');
    end
end

function SaveComparisonData(table_data, column_names, title_text)
    % 创建保存文件对话框
    [file, path] = uiputfile('*.xlsx', '保存表格数据', [title_text, '.xlsx']);
    if file ~= 0
        full_path = fullfile(path, file);

        % 准备表格数据，对不同类型的数据进行格式化处理
        processed_data = table_data;
        for i = 1:size(processed_data, 1)
            for j = 1:size(processed_data, 2)
                if isnumeric(processed_data{i,j})
                    % 检查是否为变位系数列（通常包含'x'字符）
                    if ischar(column_names{j}) && contains(column_names{j}, 'x')
                        processed_data{i,j} = round(processed_data{i,j}, 4);
                    % 检查是否为质量列（包含'质量'或'Mass'字符）
                    elseif ischar(column_names{j}) && (contains(column_names{j}, '质量') || contains(column_names{j}, 'Mass'))
                        processed_data{i,j} = round(processed_data{i,j}, 2);
                    % 检查是否为传动比列（包含'传动比'或'比'字符）
                    elseif ischar(column_names{j}) && (contains(column_names{j}, '传动比') || contains(column_names{j}, '比'))
                        processed_data{i,j} = round(processed_data{i,j}, 3);
                    % 检查是否为中心距列（包含'中心距'或'距'字符）
                    elseif ischar(column_names{j}) && (contains(column_names{j}, '中心距') || contains(column_names{j}, '距'))
                        processed_data{i,j} = round(processed_data{i,j}, 2);
                    end
                end
            end
        end

        table_to_save = cell2table(processed_data, 'VariableNames', column_names);

        % 保存到Excel文件
        writetable(table_to_save, full_path);
        msgbox(['表格已保存到: ', full_path], '保存成功');
    end
end

% ================ 以下是评价指标计算函数 ================

function gd = GenerationDistance(approximation_front, reference_front)
    % 计算生成距离(Generation Distance)指标
    n_approx = size(approximation_front, 1);
    total_dist = 0;
    
    for i = 1:n_approx
        min_dist = inf;
        for j = 1:size(reference_front, 1)
            dist = sqrt(sum((approximation_front(i,:) - reference_front(j,:)).^2));
            min_dist = min(min_dist, dist);
        end
        total_dist = total_dist + min_dist^2;
    end
    
    gd = sqrt(total_dist / n_approx);
end

function igd = InvertedGenerationDistance(approximation_front, reference_front)
    % 计算反向生成距离(Inverted Generation Distance)指标
    n_ref = size(reference_front, 1);
    total_dist = 0;
    
    for i = 1:n_ref
        min_dist = inf;
        for j = 1:size(approximation_front, 1)
            dist = sqrt(sum((reference_front(i,:) - approximation_front(j,:)).^2));
            min_dist = min(min_dist, dist);
        end
        total_dist = total_dist + min_dist^2;
    end
    
    igd = sqrt(total_dist / n_ref);
end

function sp = Spread(front)
    % 计算分布均匀度(Spread / Diversity)指标
    n = size(front, 1);
    if n <= 1
        sp = 0;
        return;
    end
    
    distances = zeros(n-1, 1);
    for i = 1:n-1
        distances(i) = sqrt(sum((front(i,:) - front(i+1,:)).^2));
    end
    
    mean_dist = mean(distances);
    
    numerator = sum(abs(distances - mean_dist));
    denominator = (n-1) * mean_dist;
    
    if denominator == 0
        sp = 0;
    else
        sp = numerator / denominator;
    end
end

function ms = MaximumSpread(approximation_front, reference_front)
    % 计算最大扩展度(Maximum Spread)指标
    m = size(approximation_front, 2);
    
    min_approx = min(approximation_front);
    max_approx = max(approximation_front);
    min_ref = min(reference_front);
    max_ref = max(reference_front);
    
    spread_sum = 0;
    for i = 1:m
        range_approx = max_approx(i) - min_approx(i);
        range_ref = max_ref(i) - min_ref(i);
        if range_ref == 0
            normalized_spread = 1;
        else
            normalized_spread = range_approx / range_ref;
        end
        spread_sum = spread_sum + normalized_spread^2;
    end
    
    ms = sqrt(spread_sum / m);
end

function hv = Hypervolume(front, reference_point)
    % 计算超体积(Hypervolume)指标
    max_front = max(front);
    for i = 1:length(reference_point)
        if reference_point(i) <= max_front(i)
            reference_point(i) = max_front(i) * 1.1;
        end
    end
    
    n = size(front, 1);
    m = size(front, 2);
    
    if n == 0
        hv = 0;
        return;
    end
    
    if m == 2
        [sorted_front, ~] = sortrows(front, 1);
        
        hv = 0;
        prev_x = reference_point(1);
        
        for i = n:-1:1
            hv = hv + (prev_x - sorted_front(i,1)) * (reference_point(2) - sorted_front(i,2));
            prev_x = sorted_front(i,1);
        end
    elseif m == 3
        n_samples = 10000;
        
        samples = zeros(n_samples, m);
        for i = 1:m
            samples(:, i) = reference_point(i) * rand(n_samples, 1);
        end
        
        dominated_count = 0;
        for i = 1:n_samples
            for j = 1:n
                if all(front(j, :) <= samples(i, :))
                    dominated_count = dominated_count + 1;
                    break;
                end
            end
        end
        
        vol = prod(reference_point);
        hv = (dominated_count / n_samples) * vol;
    else
        hv = 1 / size(front, 1);
        warning('Hypervolume calculation for dimensions > 3 is approximated');
    end
end

function front_idx = NonDominatedSort(objectives)
    % 执行非支配排序
    n = size(objectives, 1);
    front_idx = ones(n, 1);
    
    for i = 1:n
        for j = i+1:n
            if all(objectives(i, :) <= objectives(j, :)) && any(objectives(i, :) < objectives(j, :))
                front_idx(j) = front_idx(j) + 1;
            elseif all(objectives(j, :) <= objectives(i, :)) && any(objectives(j, :) < objectives(i, :))
                front_idx(i) = front_idx(i) + 1;
            end
        end
    end
end 