function Score = GD(approximation_front, reference_front)
% GD - Generation Distance 生成距离指标
% 计算近似前沿到参考前沿的平均距离
%
% 输入:
%   approximation_front - 近似前沿解集 (算法求得的解)
%   reference_front - 参考前沿解集 (真实前沿或参考解)
%
% 输出:
%   Score - GD值，越小越好
%
% 公式: GD = sqrt(1/|S| * Σ(di^2))
% 其中 di 是第i个解到参考前沿的最小欧几里得距离
%
% 参考文献:
% <PERSON>, D. A. (1999). Multiobjective evolutionary algorithms:
% Classifications, analyses, and new innovations.

if isempty(approximation_front) || isempty(reference_front)
    Score = Inf;
    return;
end

n_approx = size(approximation_front, 1);
total_dist = 0;

for i = 1:n_approx
    % 计算当前点到参考前沿的最小欧几里得距离
    min_dist = inf;
    for j = 1:size(reference_front, 1)
        dist = sqrt(sum((approximation_front(i,:) - reference_front(j,:)).^2));
        min_dist = min(min_dist, dist);
    end
    total_dist = total_dist + min_dist^2;
end

Score = sqrt(total_dist / n_approx);
end