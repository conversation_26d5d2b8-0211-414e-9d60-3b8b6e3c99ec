function [population, objectives] = RunMOWOA(problem, params)
% RunMOWOA - 运行多目标鲸鱼优化算法
%
% 输入:
%   problem - 包含问题信息的结构体
%   params  - 算法参数
%
% 输出:
%   population - 最终种群
%   objectives - 最终的非支配解集目标函数值
%
% 参考文献:
% <PERSON><PERSON> and <PERSON><PERSON>, The whale optimization algorithm,
% Advances in Engineering Software, 2016, 95: 51-67.

%% 初始化参数
nVar = problem.nVar;
varSize = [1, nVar];
varMin = problem.varMin;
varMax = problem.varMax;
nPop = params.nPop;
maxIt = params.maxIt;
nObj = problem.nObj;

% MOWOA特有参数
nArchive = 100;  % 档案大小
nGrid = 10;      % 网格数量
alpha = 0.1;     % 网格扩展因子
beta = 2;        % 领导者选择参数
gamma = 2;       % 档案截断参数

% 确保有评价次数计数器
if ~isfield(problem, 'FE')
    problem.FE = 0;
end

%% 鲸鱼结构定义
empty_whale.Position = [];
empty_whale.Cost = [];
empty_whale.IsDominated = [];
empty_whale.GridIndex = [];
empty_whale.GridSubIndex = [];

%% 初始化鲸鱼群
whales = repmat(empty_whale, nPop, 1);

for i = 1:nPop
    whales(i).Position = unifrnd(varMin, varMax, varSize);
    
    % 对离散变量特殊处理
    if isfield(problem, 'discreteVars')
        for j = 1:length(problem.discreteVars)
            idx = problem.discreteVars(j).idx;
            if problem.discreteVars(j).isInteger
                whales(i).Position(idx) = round(whales(i).Position(idx));
            else
                % 找到最接近的离散值
                values = problem.discreteVars(j).values;
                [~, closest_idx] = min(abs(whales(i).Position(idx) - values));
                whales(i).Position(idx) = values(closest_idx);
            end
        end
    end
    
    whales(i).Cost = problem.costFunction(whales(i).Position);
    problem.FE = problem.FE + 1;
end

% 确定支配关系
whales = DetermineDomination(whales);

% 初始化档案
archive = whales(~[whales.IsDominated]);

% 创建网格
Grid = CreateGrid(archive, nGrid, alpha);

% 更新网格索引
for i = 1:numel(archive)
    archive(i) = FindGridIndex(archive(i), Grid);
end

%% 优化主循环
for it = 1:maxIt
    % 计算a参数（从2线性递减到0）
    a = 2 - it * (2 / maxIt);
    
    for i = 1:nPop
        % 选择领导者
        leader = SelectLeader(archive, beta);
        
        % 更新位置
        r1 = rand();
        r2 = rand();
        
        A = 2 * a * r1 - a;
        C = 2 * r2;
        
        b = 1;  % 螺旋形状参数
        l = (2 - 1) * rand() - 1;  % [-1, 1]之间的随机数
        
        p = rand();  % 选择更新机制的概率
        
        for j = 1:nVar
            if p < 0.5
                if abs(A) >= 1
                    % 搜索猎物（探索）
                    rand_whale_index = randi([1 numel(archive)]);
                    X_rand = archive(rand_whale_index).Position;
                    D = abs(C * X_rand(j) - whales(i).Position(j));
                    whales(i).Position(j) = X_rand(j) - A * D;
                    
                else
                    % 包围猎物（开发）
                    D = abs(C * leader.Position(j) - whales(i).Position(j));
                    whales(i).Position(j) = leader.Position(j) - A * D;
                end
                
            else
                % 螺旋更新位置
                distance2Leader = abs(leader.Position(j) - whales(i).Position(j));
                whales(i).Position(j) = distance2Leader * exp(b * l) * cos(l * 2 * pi) + leader.Position(j);
            end
        end
        
        % 边界处理
        whales(i).Position = max(whales(i).Position, varMin);
        whales(i).Position = min(whales(i).Position, varMax);
        
        % 对离散变量特殊处理
        if isfield(problem, 'discreteVars')
            for j = 1:length(problem.discreteVars)
                idx = problem.discreteVars(j).idx;
                if problem.discreteVars(j).isInteger
                    whales(i).Position(idx) = round(whales(i).Position(idx));
                else
                    values = problem.discreteVars(j).values;
                    [~, closest_idx] = min(abs(whales(i).Position(idx) - values));
                    whales(i).Position(idx) = values(closest_idx);
                end
            end
        end
        
        % 评估
        whales(i).Cost = problem.costFunction(whales(i).Position);
        problem.FE = problem.FE + 1;
    end
    
    % 确定支配关系
    whales = DetermineDomination(whales);
    
    % 添加非支配解到档案
    non_dominated_whales = whales(~[whales.IsDominated]);
    archive = [archive; non_dominated_whales];
    
    % 确定档案中的支配关系
    archive = DetermineDomination(archive);
    archive = archive(~[archive.IsDominated]);
    
    % 更新网格
    Grid = CreateGrid(archive, nGrid, alpha);
    
    % 更新网格索引
    for i = 1:numel(archive)
        archive(i) = FindGridIndex(archive(i), Grid);
    end
    
    % 如果档案过大，删除一些解
    if numel(archive) > nArchive
        Extra = numel(archive) - nArchive;
        for e = 1:Extra
            archive = DeleteOneArchiveMember(archive, gamma);
        end
    end
    
    % 显示迭代信息
    if mod(it, 10) == 0 || it == maxIt
        disp(['MOWOA: 迭代 ' num2str(it) '/' num2str(maxIt) ', 档案大小 = ' num2str(numel(archive)) ', 评价次数 = ' num2str(problem.FE)]);
    end
end

%% 提取最终结果
% 将结构体数组转换为数值矩阵
if ~isempty(archive)
    population = vertcat(archive.Position);  % 提取位置矩阵
    objectives = vertcat(archive.Cost);      % 提取目标函数值矩阵
else
    population = [];
    objectives = [];
end

end

%% ========== 辅助函数 ==========

function pop = DetermineDomination(pop)
% 确定支配关系
nPop = numel(pop);

for i = 1:nPop
    pop(i).IsDominated = false;
end

for i = 1:nPop-1
    for j = i+1:nPop
        if Dominates(pop(i), pop(j))
            pop(j).IsDominated = true;
        elseif Dominates(pop(j), pop(i))
            pop(i).IsDominated = true;
        end
    end
end
end

function Grid = CreateGrid(pop, nGrid, alpha)
% 创建网格
if isempty(pop)
    Grid = [];
    return;
end

costs = vertcat(pop.Cost);
nObj = size(costs, 2);

cmin = min(costs, [], 1);
cmax = max(costs, [], 1);

dc = cmax - cmin;
cmin = cmin - alpha * dc;
cmax = cmax + alpha * dc;

nGrid = ones(1, nObj) * nGrid;

Grid.LowerBounds = cmin;
Grid.UpperBounds = cmax;
Grid.nGrid = nGrid;
Grid.Delta = (cmax - cmin) ./ nGrid;
end

function whale = FindGridIndex(whale, Grid)
% 找到网格索引
if isempty(Grid)
    whale.GridIndex = [];
    whale.GridSubIndex = [];
    return;
end

nObj = numel(whale.Cost);

nGrid = Grid.nGrid;
LowerBounds = Grid.LowerBounds;
Delta = Grid.Delta;

whale.GridSubIndex = 1 + floor((whale.Cost - LowerBounds) ./ Delta);
whale.GridSubIndex = max(whale.GridSubIndex, 1);
whale.GridSubIndex = min(whale.GridSubIndex, nGrid);

whale.GridIndex = 1;
for j = 1:nObj
    whale.GridIndex = whale.GridIndex + (whale.GridSubIndex(j) - 1) * prod(nGrid(1:j-1));
end
end

function leader = SelectLeader(archive, beta)
% 选择领导者
if isempty(archive)
    leader = [];
    return;
end

% 计算每个网格的占用数量
GridIndices = [archive.GridIndex];
OccupiedCells = unique(GridIndices);
N = zeros(size(OccupiedCells));

for k = 1:numel(OccupiedCells)
    N(k) = sum(GridIndices == OccupiedCells(k));
end

% 计算选择概率（偏向于较少占用的网格）
P = exp(-beta * N);
P = P / sum(P);

% 轮盘赌选择网格
sc = cumsum(P);
r = rand;
SelectedCellIndex = find(sc >= r, 1, 'first');
SelectedCell = OccupiedCells(SelectedCellIndex);

% 从选中的网格中随机选择一个解
InSelectedCell = find(GridIndices == SelectedCell);
n = numel(InSelectedCell);

SelectedIndex = InSelectedCell(randi([1 n]));

leader = archive(SelectedIndex);
end

function archive = DeleteOneArchiveMember(archive, gamma)
% 删除一个档案成员
if isempty(archive)
    return;
end

% 计算每个网格的占用数量
GridIndices = [archive.GridIndex];
OccupiedCells = unique(GridIndices);
N = zeros(size(OccupiedCells));

for k = 1:numel(OccupiedCells)
    N(k) = sum(GridIndices == OccupiedCells(k));
end

% 计算删除概率（偏向于删除较多占用网格中的解）
P = N.^gamma;
P = P / sum(P);

% 轮盘赌选择网格
sc = cumsum(P);
r = rand;
SelectedCellIndex = find(sc >= r, 1, 'first');
SelectedCell = OccupiedCells(SelectedCellIndex);

% 从该网格中随机删除一个解
InSelectedCell = find(GridIndices == SelectedCell);
n = numel(InSelectedCell);

SelectedIndex = InSelectedCell(randi([1 n]));

archive(SelectedIndex) = [];
end

function b = Dominates(p, q)
% 判断p是否支配q
if isfield(p, 'Cost')
    p_cost = p.Cost;
else
    p_cost = p;
end

if isfield(q, 'Cost')
    q_cost = q.Cost;
else
    q_cost = q;
end

b = all(p_cost <= q_cost) && any(p_cost < q_cost);
end
