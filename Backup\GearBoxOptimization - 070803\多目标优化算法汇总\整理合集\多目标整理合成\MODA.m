function [Archive_X, Archive_F] = MODA(CostFunction, nVar, VarMin, VarMax, MaxIt, nPop)
% MODA: Multi-Objective Dragonfly Algorithm
%
% Inputs:
%   CostFunction: Function handle to the objective function
%   nVar: Number of decision variables
%   VarMin, VarMax: Lower and upper bounds of variables
%   MaxIt: Maximum number of iterations
%   nPop: Population size
%
% Outputs:
%   Archive_X: Non-dominated solutions
%   Archive_F: Objective values of non-dominated solutions
%
% Reference:
%   <PERSON><PERSON>, Dragonfly algorithm: a new meta-heuristic optimization technique 
%   for solving single-objective, discrete, and multi-objective problems, 
%   Neural Computing and Applications

    % Default parameter values if not provided
    if nargin < 6
        nPop = 100;
    end
    if nargin < 5
        MaxIt = 100;
    end
    if nargin < 4
        VarMax = 1;
    end
    if nargin < 3
        VarMin = 0;
    end
    if nargin < 2
        nVar = 5;
    end
    
    % Initial parameters of the MODA algorithm
    ArchiveMaxSize = 100;
    
    % Get objective count
    sample = unifrnd(VarMin, VarMax, [1, nVar]);
    obj_values = CostFunction(sample);
    obj_no = numel(obj_values);
    
    % Initialize archive
    Archive_X = zeros(ArchiveMaxSize, nVar);
    Archive_F = ones(ArchiveMaxSize, obj_no) * inf;
    Archive_member_no = 0;
    
    % Parameters
    r = (VarMax - VarMin) / 2;
    
    % Initialize food and enemy positions
    Food_fitness = inf * ones(1, obj_no);
    Food_pos = zeros(nVar, 1);
    
    Enemy_fitness = -inf * ones(1, obj_no);
    Enemy_pos = zeros(nVar, 1);
    
    % Initialize positions and velocities
    X = initialization(nPop, nVar, VarMax, VarMin);
    DeltaX = initialization(nPop, nVar, VarMax, VarMin);
    
    % Main loop
    for iter = 1:MaxIt
        
        % Update radius
        r = (VarMax - VarMin) / 4 + ((VarMax - VarMin) * (iter / MaxIt) * 2);
        
        % Update weights
        w = 0.9 - iter * ((0.9 - 0.2) / MaxIt);
        
        my_c = 0.1 - iter * ((0.1 - 0) / (MaxIt / 2));
        if my_c < 0
            my_c = 0;
        end
        
        % Set weights for separation, alignment, cohesion, food, and enemy
        if iter < (3 * MaxIt / 4)
            s = my_c;             % Separation weight
            a = my_c;             % Alignment weight
            c = my_c;             % Cohesion weight
            f = 2 * rand;         % Food attraction weight
            e = my_c;             % Enemy distraction weight
        else
            s = my_c / iter;      % Separation weight
            a = my_c / iter;      % Alignment weight
            c = my_c / iter;      % Cohesion weight
            f = 2 * rand;         % Food attraction weight
            e = my_c / iter;      % Enemy distraction weight
        end
        
        % Calculate objective values and update food and enemy
        Particles_F = zeros(nPop, obj_no);
        for i = 1:nPop
            Particles_F(i,:) = CostFunction(X(:,i)');
            
            % Update food (best position)
            if dominates(Particles_F(i,:), Food_fitness)
                Food_fitness = Particles_F(i,:);
                Food_pos = X(:,i);
            end
            
            % Update enemy (worst position)
            if dominates(Enemy_fitness, Particles_F(i,:))
                if all(X(:,i) < VarMax') && all(X(:,i) > VarMin')
                    Enemy_fitness = Particles_F(i,:);
                    Enemy_pos = X(:,i);
                end
            end
        end
        
        % Update archive
        [Archive_X, Archive_F, Archive_member_no] = UpdateArchive(Archive_X, Archive_F, X, Particles_F, Archive_member_no);
        
        % Handle full archive
        if Archive_member_no > ArchiveMaxSize
            Archive_mem_ranks = RankingProcess(Archive_F, ArchiveMaxSize, obj_no);
            [Archive_X, Archive_F, Archive_mem_ranks, Archive_member_no] = HandleFullArchive(Archive_X, Archive_F, Archive_member_no, Archive_mem_ranks, ArchiveMaxSize);
        else
            Archive_mem_ranks = RankingProcess(Archive_F, ArchiveMaxSize, obj_no);
        end
        
        % Select food and enemy from archive
        index = RouletteWheelSelection(1./Archive_mem_ranks);
        if index == -1
            index = 1;
        end
        Food_fitness = Archive_F(index,:);
        Food_pos = Archive_X(index,:)';
        
        index = RouletteWheelSelection(Archive_mem_ranks);
        if index == -1
            index = 1;
        end
        Enemy_fitness = Archive_F(index,:);
        Enemy_pos = Archive_X(index,:)';
        
        % Update dragonflies' positions
        for i = 1:nPop
            % Find neighbors
            index = 0;
            neighbors_no = 0;
            
            Neighbours_V = [];
            Neighbours_X = [];
            
            for j = 1:nPop
                Dist = distance(X(:,i), X(:,j));
                if all(Dist <= r) && all(Dist ~= 0)
                    index = index + 1;
                    neighbors_no = neighbors_no + 1;
                    Neighbours_V(:,index) = DeltaX(:,j);
                    Neighbours_X(:,index) = X(:,j);
                end
            end
            
            % Separation
            S = zeros(nVar, 1);
            if neighbors_no > 1
                for k = 1:neighbors_no
                    S = S + (Neighbours_X(:,k) - X(:,i));
                end
                S = -S;
            end
            
            % Alignment
            if neighbors_no > 1
                A = sum(Neighbours_V, 2) / neighbors_no;
            else
                A = DeltaX(:,i);
            end
            
            % Cohesion
            if neighbors_no > 1
                C_temp = sum(Neighbours_X, 2) / neighbors_no;
            else
                C_temp = X(:,i);
            end
            C = C_temp - X(:,i);
            
            % Attraction to food
            Dist2Food = distance(X(:,i), Food_pos);
            if all(Dist2Food <= r)
                F = Food_pos - X(:,i);
            else
                F = 0;
            end
            
            % Distraction from enemy
            Dist2Enemy = distance(X(:,i), Enemy_pos);
            if all(Dist2Enemy <= r)
                E = X(:,i) + Enemy_pos;
            else
                E = zeros(nVar, 1);
            end
            
            % Update velocity and position
            DeltaX(:,i) = s*S + a*A + c*C + f*F + e*E + w*DeltaX(:,i);
            
            % Limit velocity
            if any(isnan(DeltaX(:,i)))
                DeltaX(:,i) = w * DeltaX(:,i);
            end
            
            % Update position
            X(:,i) = X(:,i) + DeltaX(:,i);
            
            % Boundary checking
            Flag4ub = X(:,i) > VarMax';
            Flag4lb = X(:,i) < VarMin';
            X(:,i) = X(:,i).*(~(Flag4ub + Flag4lb)) + VarMax'.*Flag4ub + VarMin'.*Flag4lb;
            
            % Update individual's objective value
            Particles_F(i,:) = CostFunction(X(:,i)');
            
            % Update archive
            [Archive_X, Archive_F, Archive_member_no] = UpdateArchive(Archive_X, Archive_F, X(:,i), Particles_F(i,:), Archive_member_no);
            
            % Handle full archive
            if Archive_member_no > ArchiveMaxSize
                Archive_mem_ranks = RankingProcess(Archive_F, ArchiveMaxSize, obj_no);
                [Archive_X, Archive_F, Archive_mem_ranks, Archive_member_no] = HandleFullArchive(Archive_X, Archive_F, Archive_member_no, Archive_mem_ranks, ArchiveMaxSize);
            end
        end
    end
    
    % Return only the non-dominated solutions
    Archive_X = Archive_X(1:Archive_member_no, :);
    Archive_F = Archive_F(1:Archive_member_no, :);
end

% Initialize population
function Positions = initialization(pop_size, dim, ub, lb)
    Positions = zeros(dim, pop_size);
    for i = 1:dim
        Positions(i,:) = rand(1, pop_size) .* (ub - lb) + lb;
    end
end

% Calculate distance between two solutions
function d = distance(x1, x2)
    d = abs(x1 - x2);
end

% Check if x dominates y
function dom = dominates(x, y)
    dom = all(x <= y) && any(x < y);
end

% Update archive with new solutions
function [Archive_X, Archive_F, Archive_member_no] = UpdateArchive(Archive_X, Archive_F, Particles_X, Particles_F, Archive_member_no)
    if Archive_member_no == 0
        % If archive is empty, add the first solution
        if size(Particles_X, 2) > size(Particles_X, 1)
            % Single solution case
            Archive_X(Archive_member_no+1, :) = Particles_X';
            Archive_F(Archive_member_no+1, :) = Particles_F;
        else
            % Multiple solutions case
            Archive_X(Archive_member_no+1, :) = Particles_X;
            Archive_F(Archive_member_no+1, :) = Particles_F;
        end
        Archive_member_no = Archive_member_no + 1;
    else
        % If archive is not empty
        if size(Particles_X, 2) > size(Particles_X, 1)
            % Single solution case
            particle_position = Particles_X';
            particle_fitness = Particles_F;
            
            dominated = false;
            should_add = true;
            
            i = 1;
            while i <= Archive_member_no && ~dominated
                if dominates(Archive_F(i, :), particle_fitness)
                    dominated = true;
                    should_add = false;
                elseif dominates(particle_fitness, Archive_F(i, :))
                    % Remove dominated solution from archive
                    Archive_X(i:Archive_member_no-1, :) = Archive_X(i+1:Archive_member_no, :);
                    Archive_F(i:Archive_member_no-1, :) = Archive_F(i+1:Archive_member_no, :);
                    Archive_member_no = Archive_member_no - 1;
                    i = i - 1;
                end
                i = i + 1;
            end
            
            if should_add
                Archive_X(Archive_member_no+1, :) = particle_position;
                Archive_F(Archive_member_no+1, :) = particle_fitness;
                Archive_member_no = Archive_member_no + 1;
            end
        else
            % Multiple solutions case
            for j = 1:size(Particles_X, 2)
                particle_position = Particles_X(:, j)';
                particle_fitness = Particles_F(j, :);
                
                dominated = false;
                should_add = true;
                
                i = 1;
                while i <= Archive_member_no && ~dominated
                    if dominates(Archive_F(i, :), particle_fitness)
                        dominated = true;
                        should_add = false;
                    elseif dominates(particle_fitness, Archive_F(i, :))
                        % Remove dominated solution from archive
                        Archive_X(i:Archive_member_no-1, :) = Archive_X(i+1:Archive_member_no, :);
                        Archive_F(i:Archive_member_no-1, :) = Archive_F(i+1:Archive_member_no, :);
                        Archive_member_no = Archive_member_no - 1;
                        i = i - 1;
                    end
                    i = i + 1;
                end
                
                if should_add
                    Archive_X(Archive_member_no+1, :) = particle_position;
                    Archive_F(Archive_member_no+1, :) = particle_fitness;
                    Archive_member_no = Archive_member_no + 1;
                end
            end
        end
    end
end

% Calculate ranks for crowding
function ranks = RankingProcess(Archive_F, ArchiveMaxSize, obj_no)
    [Archive_member_no, ~] = size(Archive_F);
    
    % Calculate distance matrix
    distance_matrix = zeros(Archive_member_no, Archive_member_no);
    for i = 1:Archive_member_no
        for j = i:Archive_member_no
            distance_matrix(i, j) = sqrt(sum((Archive_F(i, :) - Archive_F(j, :)).^2));
            distance_matrix(j, i) = distance_matrix(i, j);
        end
    end
    
    % Calculate crowding distance
    crowding = zeros(Archive_member_no, 1);
    for i = 1:Archive_member_no
        [sorted_dist, ~] = sort(distance_matrix(i, :));
        % Sum the distance to k nearest neighbors
        k = min(3, Archive_member_no-1);
        crowding(i) = sum(sorted_dist(2:k+1));
    end
    
    % Normalize crowding
    if max(crowding) ~= min(crowding)
        crowding = (crowding - min(crowding)) / (max(crowding) - min(crowding));
    else
        crowding = ones(Archive_member_no, 1);
    end
    
    ranks = crowding;
end

% Handle archive when it exceeds maximum size
function [Archive_X, Archive_F, Archive_mem_ranks, Archive_member_no] = HandleFullArchive(Archive_X, Archive_F, Archive_member_no, Archive_mem_ranks, ArchiveMaxSize)
    [~, indices] = sort(Archive_mem_ranks);
    
    % Keep only the solutions with lowest ranks (highest crowding)
    Archive_X = Archive_X(indices(1:ArchiveMaxSize), :);
    Archive_F = Archive_F(indices(1:ArchiveMaxSize), :);
    Archive_mem_ranks = Archive_mem_ranks(indices(1:ArchiveMaxSize));
    Archive_member_no = ArchiveMaxSize;
end

% Roulette wheel selection
function index = RouletteWheelSelection(weights)
    if sum(weights) == 0
        index = -1;
        return;
    end
    
    accumulation = cumsum(weights);
    p = rand() * accumulation(end);
    chosen_index = -1;
    for i = 1:length(accumulation)
        if accumulation(i) > p
            chosen_index = i;
            break;
        end
    end
    index = chosen_index;
end 