function [objectives, constraints] = CostFunctionWrapper(x, input_power, input_speed, output_speed, ...
                                             service_life, contact_safety_factor, bending_safety_factor, gear_material, gear_materials, quality_grade, center_distance)
% 包装函数，调用GearOptObjectives并处理有效性检查

% 对模数进行离散化处理
% 标准模数 - 20度压力角
m1_values_20deg = [5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 20];
% 标准模数 - 25度压力角
m1_values_25deg = [7, 9, 10, 11, 12, 13, 16, 17, 18, 20];

% 处理三级压力角的离散变量（第24个变量）
% 0表示选择20°，1表示选择25°
if length(x) >= 24
    % 将第24个变量四舍五入到最接近的整数（0或1）
    pressure_angle_choice = round(x(24));
    
    % 限制在0-1范围内
    pressure_angle_choice = max(0, min(1, pressure_angle_choice));
    
    % 转换为实际压力角值
    discrete_values = [20, 25];
    pressure_angle_3 = discrete_values(pressure_angle_choice + 1);
else
    % 向后兼容：如果没有第24个变量，使用老方法
    % 三级行星轮系的压力角只能是20°或25°（离散选择）
    discrete_values = [20, 25];
    distances = abs(pressure_angle - discrete_values);
    [~, idx] = min(distances);
    pressure_angle_3 = discrete_values(idx);
end

% 选择最接近的标准模数值
% 一级模数(m1) - 始终使用20度压力角的标准模数
distances_m1 = abs(x(1) - m1_values_20deg);
[~, idx_m1] = min(distances_m1);
x(1) = m1_values_20deg(idx_m1);

% 二级模数(mn2) - 始终使用20度压力角的标准模数
distances_mn2 = abs(x(4) - m1_values_20deg);
[~, idx_mn2] = min(distances_mn2);
x(4) = m1_values_20deg(idx_mn2);

% 三级模数(mn3) - 根据压力角选择不同的标准模数
if pressure_angle_3 == 20
    distances_mn3 = abs(x(8) - m1_values_20deg);
    [~, idx_mn3] = min(distances_mn3);
    x(8) = m1_values_20deg(idx_mn3);
else % 压力角为25度
    distances_mn3 = abs(x(8) - m1_values_25deg);
    [~, idx_mn3] = min(distances_mn3);
    x(8) = m1_values_25deg(idx_mn3);
end

% 从设计变量中提取行星轮数量并处理为离散值(3、4或5)
% 计算与离散值的距离，选择最接近的值
discrete_planet_values = [3, 4, 5]; % 行星轮数量只能是3、4或5
% 二级行星轮数量
distances_2 = abs(x(12) - discrete_planet_values);
[~, idx_2] = min(distances_2);
planets_count_2 = discrete_planet_values(idx_2);

% 三级行星轮数量
distances_3 = abs(x(13) - discrete_planet_values);
[~, idx_3] = min(distances_3);
planets_count_3 = discrete_planet_values(idx_3);

% 提取角度参数
pressure_angle = x(14);
helix_angle_1 = round(x(15)); % 一级螺旋角取整为整数值
helix_angle_2 = round(x(16)); % 二级螺旋角取整为整数值
helix_angle_3 = round(x(17)); % 三级螺旋角取整为整数值

% 提取齿数并确保为整数
z1 = max(17, round(x(2)));  % 一级小齿轮齿数不小于17
z2 = max(20, min(100, round(x(3))));  % 一级大齿轮齿数不小于20且不超过100
zs2 = max(17, round(x(5))); % 二级太阳轮齿数
zp2 = max(17, round(x(6))); % 二级行星轮齿数
zs3 = max(17, round(x(9))); % 三级太阳轮齿数
zp3 = max(17, round(x(10))); % 三级行星轮齿数

% 计算内齿圈齿数
zr2 = zs2 + 2 * zp2;
zr3 = zs3 + 2 * zp3;

% 提取变位系数
profile_shifts = x(18:23); % [x1, x2, xs2, xp2, xs3, xp3]

% ===== 使用科学的变位系数计算方法 =====
% 1. 一级平行轴齿轮系变位系数计算
gear_params_1 = struct(...
    'z1', z1, ...
    'z2', z2, ...
    'alpha', pressure_angle, ...
    'beta', helix_angle_1, ...
    'module', x(1), ...
    'center_distance', center_distance, ...
    'is_planetary', false, ...
    'is_internal', false);

[shift_ranges_1, optimal_shifts_1] = CalculateOptimalShiftCoefficients(gear_params_1);

% 2. 二级行星轮系变位系数计算 (太阳轮-行星轮啮合)
gear_params_2_sun_planet = struct(...
    'z1', zs2, ...
    'z2', zp2, ...
    'zr', zr2, ...
    'alpha', pressure_angle, ...
    'beta', helix_angle_2, ...
    'module', x(4), ...
    'is_planetary', true, ...
    'is_internal', false);

[shift_ranges_2_sp, optimal_shifts_2_sp] = CalculateOptimalShiftCoefficients(gear_params_2_sun_planet);

% 3. 二级行星轮系变位系数计算 (行星轮-内齿圈啮合)
gear_params_2_planet_ring = struct(...
    'z1', zp2, ...
    'z2', zr2, ...
    'alpha', pressure_angle, ...
    'beta', helix_angle_2, ...
    'module', x(4), ...
    'is_planetary', true, ...
    'is_internal', true);

[shift_ranges_2_pr, optimal_shifts_2_pr] = CalculateOptimalShiftCoefficients(gear_params_2_planet_ring);

% 4. 三级行星轮系变位系数计算 (太阳轮-行星轮啮合)
gear_params_3_sun_planet = struct(...
    'z1', zs3, ...
    'z2', zp3, ...
    'zr', zr3, ...
    'alpha', pressure_angle_3, ...
    'beta', helix_angle_3, ...
    'module', x(8), ...
    'is_planetary', true, ...
    'is_internal', false);

[shift_ranges_3_sp, optimal_shifts_3_sp] = CalculateOptimalShiftCoefficients(gear_params_3_sun_planet);

% 5. 三级行星轮系变位系数计算 (行星轮-内齿圈啮合)
gear_params_3_planet_ring = struct(...
    'z1', zp3, ...
    'z2', zr3, ...
    'alpha', pressure_angle_3, ...
    'beta', helix_angle_3, ...
    'module', x(8), ...
    'is_planetary', true, ...
    'is_internal', true);

[shift_ranges_3_pr, optimal_shifts_3_pr] = CalculateOptimalShiftCoefficients(gear_params_3_planet_ring);

% 6. 调整变位系数到科学计算的范围内
% 一级平行轴齿轮系
x(18) = min(max(profile_shifts(1), shift_ranges_1.x1_min), shift_ranges_1.x1_max); % x1
x(19) = min(max(profile_shifts(2), shift_ranges_1.x2_min), shift_ranges_1.x2_max); % x2

% 二级行星轮系
% 对太阳轮-行星轮啮合和行星轮-内齿圈啮合同时考虑
x(20) = min(max(profile_shifts(3), shift_ranges_2_sp.x1_min), shift_ranges_2_sp.x1_max); % xs2
x(21) = min(max(profile_shifts(4), max(shift_ranges_2_sp.x2_min, shift_ranges_2_pr.x1_min)), ...
          min(shift_ranges_2_sp.x2_max, shift_ranges_2_pr.x1_max)); % xp2

% 三级行星轮系
% 对太阳轮-行星轮啮合和行星轮-内齿圈啮合同时考虑
x(22) = min(max(profile_shifts(5), shift_ranges_3_sp.x1_min), shift_ranges_3_sp.x1_max); % xs3
x(23) = min(max(profile_shifts(6), max(shift_ranges_3_sp.x2_min, shift_ranges_3_pr.x1_min)), ...
          min(shift_ranges_3_sp.x2_max, shift_ranges_3_pr.x1_max)); % xp3

% 7. 计算内齿圈变位系数
% 二级内齿圈变位系数
xr2 = -(x(20) * zs2 + x(21) * zp2) / zr2;

% 三级内齿圈变位系数
xr3 = -(x(22) * zs3 + x(23) * zp3) / zr3;

% 8. 验证内齿圈变位系数是否在合理范围内
% 如果超出范围，可能需要重新调整太阳轮和行星轮的变位系数
if isfield(shift_ranges_2_pr, 'xr_min') && isfield(shift_ranges_2_pr, 'xr_max')
    if xr2 < shift_ranges_2_pr.xr_min || xr2 > shift_ranges_2_pr.xr_max
        % 使用最优解重新计算
        x(20) = optimal_shifts_2_sp.x1_opt; % xs2
        x(21) = optimal_shifts_2_sp.x2_opt; % xp2
        xr2 = -(x(20) * zs2 + x(21) * zp2) / zr2;
    end
end

if isfield(shift_ranges_3_pr, 'xr_min') && isfield(shift_ranges_3_pr, 'xr_max')
    if xr3 < shift_ranges_3_pr.xr_min || xr3 > shift_ranges_3_pr.xr_max
        % 使用最优解重新计算
        x(22) = optimal_shifts_3_sp.x1_opt; % xs3
        x(23) = optimal_shifts_3_sp.x2_opt; % xp3
        xr3 = -(x(22) * zs3 + x(23) * zp3) / zr3;
    end
end

% 更新变位系数
profile_shifts = x(18:23);

% 提取一级齿宽系数
if length(x) >= 25
    k_h1 = x(25); % 使用优化变量中的一级齿宽系数
    % 确保一级齿宽系数在0.28-0.4之间
    k_h1 = max(0.28, min(0.4, k_h1));
    x(25) = k_h1;
else
    k_h1 = 0.3; % 默认值为0.3
end

% ===== 中心距精确控制机制 =====
% 提取一级平行轴系参数
m1 = x(1);       % 一级模数
z1 = max(17, round(x(2)));  % 一级小齿轮齿数不小于17
z2 = max(20, min(100, round(x(3))));  % 一级大齿轮齿数不小于20且不超过100
x1 = profile_shifts(1); % 小齿轮变位系数
x2 = profile_shifts(2); % 大齿轮变位系数

% 计算实际中心距
if helix_angle_1 == 0
    % 直齿轮情况
    inv_alpha = tan(pressure_angle * pi / 180) - (pressure_angle * pi / 180); % 渐开线函数
    inv_alpha_w = inv_alpha + 2 * (x1 + x2) * tan(pressure_angle * pi / 180) / (z1 + z2);
    alpha_w = fzero(@(a) tan(a) - a - inv_alpha_w, pressure_angle * pi / 180);
    actual_center_distance = m1 * (z1 + z2) * cos(pressure_angle * pi / 180) / (2 * cos(alpha_w));
else
    % 斜齿轮情况
    mt1 = m1 / cos(helix_angle_1 * pi / 180);  % 端面模数
    inv_alpha_t = tan(pressure_angle * pi / 180) - (pressure_angle * pi / 180);
    inv_alpha_tw = inv_alpha_t + 2 * (x1 + x2) * tan(pressure_angle * pi / 180) / (z1 + z2);
    alpha_tw = fzero(@(a) tan(a) - a - inv_alpha_tw, pressure_angle * pi / 180);
    actual_center_distance = mt1 * (z1 + z2) * cos(pressure_angle * pi / 180) / (2 * cos(alpha_tw));
end

% 计算中心距误差
center_distance_tolerance = 0.0001;  % 0.01%的误差容忍度
center_distance_error = abs(actual_center_distance - center_distance) / center_distance;

% 检查是否存在传动比约束文件
try
    if exist('ratio_constraints.mat', 'file')
        % 文件存在，已在GearOptObjectives中处理
    end
catch
    % 忽略错误
end

% 检查是否提供了精度等级
if nargin < 9 || isempty(quality_grade)
    quality_grade = 7;  % 默认7级精度
end

% 检查是否提供了一级齿轮中心距参数
if nargin < 10 || isempty(center_distance)
    center_distance = 400;  % 默认中心距为400mm
end

% 检查是否提供了详细材料参数
if nargin < 8 || isempty(gear_materials)
    % 使用旧版本调用（向后兼容）
    [objectives, constraints, is_valid] = GearOptObjectives(x(1:11), input_power, input_speed, output_speed, ...
                                                      service_life, contact_safety_factor, bending_safety_factor, gear_material, ...
                                                      planets_count_2, planets_count_3);
else
    % 使用新版本调用，传递详细材料参数和精度等级
    % 更新：使用第24个变量决定的三级压力角（只能是20°或25°）
    [objectives, constraints, is_valid] = GearOptObjectives(x(1:11), input_power, input_speed, output_speed, ...
                                                      service_life, contact_safety_factor, bending_safety_factor, gear_material, ...
                                                      planets_count_2, planets_count_3, gear_materials, quality_grade, ...
                                                      pressure_angle, helix_angle_1, helix_angle_2, helix_angle_3, ...
                                                      profile_shifts, pressure_angle_3, center_distance, k_h1);
end

% 注意：即使结果无效，我们也返回GearOptObjectives提供的合理目标函数值
% 这样可以帮助算法更好地收敛，而不是使用极端值导致算法失效

% 对于无效解，我们通过约束值来惩罚，而不是修改目标函数值
if ~is_valid
    constraints = constraints + 10; % 增加约束惩罚，但不至于过大
end

% 检查变位系数约束（作为二次保险，虽然已经进行了调整）
shift_constraint_violated = false;
shift_violations = 0;

% 计算各对啮合齿轮的变位系数和
% 一级外啮合：小齿轮x1 + 大齿轮x2
sum_shift_1 = x(18) + x(19);
% 二级行星-太阳轮啮合：太阳轮xs2 + 行星轮xp2
sum_shift_2 = x(20) + x(21);
% 三级行星-太阳轮啮合：太阳轮xs3 + 行星轮xp3
sum_shift_3 = x(22) + x(23);

% 变位系数约束范围 - 使用科学计算结果
min_sum_shift = 0.6;  % 调整为0.6
max_sum_shift = 1.2;  % 调整为1.2

% 检查一级变位系数和是否在范围内
if sum_shift_1 < min_sum_shift || sum_shift_1 > max_sum_shift
    shift_constraint_violated = true;
    shift_violations = shift_violations + min(abs(sum_shift_1 - min_sum_shift), abs(sum_shift_1 - max_sum_shift));
end

% 检查二级行星-太阳轮变位系数和是否在范围内
if sum_shift_2 < min_sum_shift || sum_shift_2 > max_sum_shift
    shift_constraint_violated = true;
    shift_violations = shift_violations + min(abs(sum_shift_2 - min_sum_shift), abs(sum_shift_2 - max_sum_shift));
end

% 检查三级行星-太阳轮变位系数和是否在范围内
if sum_shift_3 < min_sum_shift || sum_shift_3 > max_sum_shift
    shift_constraint_violated = true;
    shift_violations = shift_violations + min(abs(sum_shift_3 - min_sum_shift), abs(sum_shift_3 - max_sum_shift));
end

% 如果违反变位系数约束，将解标记为无效并施加严重惩罚
if shift_constraint_violated
    is_valid = false;
    constraints = constraints + 100 * shift_violations; % 大幅增加惩罚力度
end

% 确保二级和三级齿宽系数在0.6-1.0之间
x(7) = max(0.6, min(1.0, x(7)));   % 二级齿宽系数
x(11) = max(0.6, min(1.0, x(11))); % 三级齿宽系数

% 确保所有齿数大于等于最小限制，并为整数
z1 = max(17, round(x(2))); % 一级小齿轮齿数不小于17
z2 = max(17, min(100, round(x(3)))); % 一级大齿轮齿数不小于17且不超过100
zs2 = max(17, round(x(5))); % 二级太阳轮齿数
zp2 = max(17, round(x(6))); % 二级行星轮齿数
zs3 = max(17, round(x(9))); % 三级太阳轮齿数
zp3 = max(17, round(x(10))); % 三级行星轮齿数

% 计算内齿圈齿数并限制其最大值
zr2 = zs2 + 2*zp2;  % 二级齿圈齿数
zr3 = zs3 + 2*zp3;  % 三级齿圈齿数

% 如果内齿圈齿数超过最大值，调整行星轮或太阳轮齿数
if zr2 > 120
    % 计算需要减少的齿数
    excess = zr2 - 120;
    % 优先减少行星轮齿数，因为行星轮通常比太阳轮小
    if zp2 - ceil(excess/2) >= 10
        zp2 = zp2 - ceil(excess/2);
    else
        % 如果减少行星轮齿数会导致低于最小值，则调整太阳轮
        zp2 = 10;
        zs2 = 120 - 2*zp2;
    end
    zr2 = zs2 + 2*zp2;  % 重新计算内齿圈齿数
end

if zr3 > 120
    excess = zr3 - 120;
    if zp3 - ceil(excess/2) >= 10
        zp3 = zp3 - ceil(excess/2);
    else
        zp3 = 10;
        zs3 = 120 - 2*zp3;
    end
    zr3 = zs3 + 2*zp3;  % 重新计算内齿圈齿数
end

% 更新到x中，确保优化算法使用正确的齿数值
x(2) = z1;
x(3) = z2;
x(5) = zs2;
x(6) = zp2;
x(9) = zs3;
x(10) = zp3;

% 最后检查传动比约束，确保满足要求
try
    if exist('ratio_constraints.mat', 'file')
        load('ratio_constraints.mat', 'ratio_constraints');
        if ratio_constraints.enabled
            % 计算当前齿数对应的传动比
            i1 = z2 / z1;  % 一级传动比
            i2 = (1 + (zs2 + 2*zp2)/zs2);  % 二级传动比
            i3 = (1 + (zs3 + 2*zp3)/zs3);  % 三级传动比
            total_ratio = i1 * i2 * i3;
            
            % 检查总传动比是否满足要求
            target_ratio = ratio_constraints.total_ratio;
            allowed_error = ratio_constraints.tolerance;
            ratio_error = abs(total_ratio - target_ratio) / target_ratio;
            
            % 检查各级传动比是否满足约束
            i1_violated = (i1 < ratio_constraints.i1_min) || (i1 > ratio_constraints.i1_max);
            i2_violated = (i2 < ratio_constraints.i2_min) || (i2 > ratio_constraints.i2_max);
            i3_violated = (i3 < ratio_constraints.i3_min) || (i3 > ratio_constraints.i3_max);
            
            % 检查i3 <= i2的约束
            i3_vs_i2_violated = ratio_constraints.i3_leq_i2 && (i3 > i2);
            
            % 各级传动比违反约束标志
            ratio_constraints_violated = i1_violated || i2_violated || i3_violated || i3_vs_i2_violated;
            
            % 计算违反程度
            i1_violation = 0;
            i2_violation = 0;
            i3_violation = 0;
            i3_vs_i2_violation = 0;
            
            if i1_violated
                i1_violation = min(abs(i1 - ratio_constraints.i1_min), abs(i1 - ratio_constraints.i1_max));
            end
            
            if i2_violated
                i2_violation = min(abs(i2 - ratio_constraints.i2_min), abs(i2 - ratio_constraints.i2_max));
            end
            
            if i3_violated
                i3_violation = min(abs(i3 - ratio_constraints.i3_min), abs(i3 - ratio_constraints.i3_max));
            end
            
            if i3_vs_i2_violated
                i3_vs_i2_violation = i3 - i2;
            end
            
            % 总违反程度
            total_violation = i1_violation + i2_violation + i3_violation + i3_vs_i2_violation;
            
            % 如果传动比误差超过允许范围或违反各级传动比约束，加强惩罚
            if ratio_error > allowed_error || ratio_constraints_violated
                is_valid = false;
                % 显著提高传动比约束的惩罚权重 - 极高惩罚使算法更倾向于满足约束
                constraints = constraints + 1000 * (ratio_error + 10 * total_violation); 
                
                % 尝试直接调整齿数以满足一级传动比约束
                if i1_violated
                    % 如果一级传动比过小，尝试增加z2或减少z1
                    if i1 < ratio_constraints.i1_min
                        target_i1 = ratio_constraints.i1_min;
                        % 尝试方法1：增加z2，保持z1不变
                        new_z2 = ceil(target_i1 * z1);
                        if new_z2 <= 100  % 确保不超过z2上限
                            z2 = new_z2;
                            x(3) = z2;
                        else
                            % 尝试方法2：减少z1，尽量保持z2不变
                            new_z1 = floor(z2 / target_i1);
                            if new_z1 >= 17  % 确保不低于最小齿数
                                z1 = new_z1;
                                x(2) = z1;
                            end
                        end
                    end
                    
                    % 如果一级传动比过大，尝试减少z2或增加z1
                    if i1 > ratio_constraints.i1_max
                        target_i1 = ratio_constraints.i1_max;
                        % 尝试方法1：减少z2，保持z1不变
                        new_z2 = floor(target_i1 * z1);
                        if new_z2 >= 17  % 确保不低于最小齿数
                            z2 = new_z2;
                            x(3) = z2;
                        else
                            % 尝试方法2：增加z1，尽量保持z2不变
                            new_z1 = ceil(z2 / target_i1);
                            if new_z1 <= 30  % 确保不超过z1上限
                                z1 = new_z1;
                                x(2) = z1;
                            end
                        end
                    end
                end
                
                % 类似地，尝试调整二级行星系统齿数以满足i2约束
                if i2_violated
                    if i2 < ratio_constraints.i2_min
                        target_i2 = ratio_constraints.i2_min;
                        % 尝试调整太阳轮和行星轮齿数
                        for test_zs2 = 17:50
                            for test_zp2 = 17:35
                                test_zr2 = test_zs2 + 2 * test_zp2;
                                test_i2 = (1 + test_zr2/test_zs2);
                                if abs(test_i2 - target_i2) < 0.1 && test_zr2 <= 120
                                    zs2 = test_zs2;
                                    zp2 = test_zp2;
                                    x(5) = zs2;
                                    x(6) = zp2;
                                    break;
                                end
                            end
                            if abs((1 + (zs2 + 2*zp2)/zs2) - target_i2) < 0.1
                                break;
                            end
                        end
                    end
                    
                    if i2 > ratio_constraints.i2_max
                        target_i2 = ratio_constraints.i2_max;
                        % 尝试调整太阳轮和行星轮齿数
                        for test_zs2 = 50:-1:17
                            for test_zp2 = 17:35
                                test_zr2 = test_zs2 + 2 * test_zp2;
                                test_i2 = (1 + test_zr2/test_zs2);
                                if abs(test_i2 - target_i2) < 0.1 && test_zr2 <= 120
                                    zs2 = test_zs2;
                                    zp2 = test_zp2;
                                    x(5) = zs2;
                                    x(6) = zp2;
                                    break;
                                end
                            end
                            if abs((1 + (zs2 + 2*zp2)/zs2) - target_i2) < 0.1
                                break;
                            end
                        end
                    end
                end
                
                % 尝试调整三级行星系统齿数以满足i3约束和i3 <= i2约束
                if i3_violated || i3_vs_i2_violated
                    % 如果i3 > i2但需要满足i3 <= i2
                    if i3_vs_i2_violated
                        target_i3 = i2;  % 目标i3等于当前i2
                    elseif i3 < ratio_constraints.i3_min
                        target_i3 = ratio_constraints.i3_min;
                    else
                        target_i3 = ratio_constraints.i3_max;
                    end
                    
                    % 尝试调整太阳轮和行星轮齿数
                    for test_zs3 = 17:50
                        for test_zp3 = 17:35
                            test_zr3 = test_zs3 + 2 * test_zp3;
                            test_i3 = (1 + test_zr3/test_zs3);
                            if abs(test_i3 - target_i3) < 0.1 && test_zr3 <= 120 && ...
                                (~ratio_constraints.i3_leq_i2 || test_i3 <= i2)
                                zs3 = test_zs3;
                                zp3 = test_zp3;
                                x(9) = zs3;
                                x(10) = zp3;
                                break;
                            end
                        end
                        if abs((1 + (zs3 + 2*zp3)/zs3) - target_i3) < 0.1 && ...
                           (~ratio_constraints.i3_leq_i2 || (1 + (zs3 + 2*zp3)/zs3) <= i2)
                            break;
                        end
                    end
                end
                
                % 更新传动比
                i1 = z2 / z1;
                i2 = (1 + (zs2 + 2*zp2)/zs2);
                i3 = (1 + (zs3 + 2*zp3)/zs3);
                total_ratio = i1 * i2 * i3;
                ratio_error = abs(total_ratio - target_ratio) / target_ratio;
                
                % 如果齿数调整后传动比仍然违反约束，保持高惩罚
                if ratio_error > allowed_error
                    constraints = constraints + 2000 * ratio_error;
                end
            end
        end
    end
catch
    % 忽略错误
end

end 