function FirstStageGenerator(system_params)
% FirstStageGenerator 一级齿轮参数穷举生成器
% 保持与原有GenerateFirstStageGearReport完全一致的功能
%
% 输入:
%   system_params - 系统参数结构体
%
% 输出:
%   无返回值，生成MAT文件和HTML报告

%% 1. 参数验证和初始化
if nargin < 1
    system_params = struct();
end

% 设置默认参数
if ~isfield(system_params, 'center_distance')
    system_params.center_distance = 400;  % mm
end
if ~isfield(system_params, 'input_power')
    system_params.input_power = 1170.24;  % kW
end
if ~isfield(system_params, 'input_speed')
    system_params.input_speed = 1490;  % rpm
end
if ~isfield(system_params, 'service_life')
    system_params.service_life = 50000;  % h
end
if ~isfield(system_params, 'contact_safety_factor')
    system_params.contact_safety_factor = 1.2;
end
if ~isfield(system_params, 'bending_safety_factor')
    system_params.bending_safety_factor = 1.2;
end

%% 2. 获取材料参数
try
    % 使用MaterialManager获取一级平行轴齿轮系材料参数
    parallel_gear_material = MaterialManager('17CrNiMo6');
catch
    % 如果MaterialManager不可用，使用配置文件
    material_config = MaterialProperties();
    parallel_gear_material = material_config.getMaterialByName('17CrNiMo6');
end

%% 3. 调用原有的一级参数生成函数
fprintf('\n=== 一级平行轴系参数生成阶段 ===\n');

try
    % 直接调用原有的GenerateFirstStageGearReport函数
    GenerateFirstStageGearReport(system_params.center_distance, ...
                                system_params.input_power, ...
                                system_params.input_speed, ...
                                system_params.service_life, ...
                                system_params.contact_safety_factor, ...
                                system_params.bending_safety_factor, ...
                                parallel_gear_material);
    
    fprintf('一级平行轴系参数组合报告已生成\n');
    
catch e
    fprintf('调用原有生成函数失败，使用重构版本: %s\n', e.message);
    
    % 如果原有函数不可用，使用重构版本
    generateFirstStageParametersRefactored(system_params, parallel_gear_material);
end

%% 4. 验证生成结果
first_stage_params_file = fullfile('Results', '一级平行轴系满足安全系数的参数.mat');
if exist(first_stage_params_file, 'file')
    fprintf('✓ 一级参数MAT文件生成成功: %s\n', first_stage_params_file);
    
    % 加载并显示统计信息
    try
        loaded_data = load(first_stage_params_file);
        if isfield(loaded_data, 'selected_params')
            fprintf('  - 满足安全系数的参数组合数量: %d\n', height(loaded_data.selected_params));
        end
    catch
        fprintf('  - MAT文件加载成功，但无法读取统计信息\n');
    end
else
    fprintf('✗ 一级参数MAT文件生成失败！\n');
end

% 检查HTML报告
html_report_file = fullfile('Results', '一级平行轴系参数组合报告.html');
if exist(html_report_file, 'file')
    fprintf('✓ 一级参数HTML报告生成成功: %s\n', html_report_file);
else
    fprintf('✗ 一级参数HTML报告生成失败！\n');
end

end

%% ========== 重构版本的一级参数生成函数 ==========

function generateFirstStageParametersRefactored(system_params, gear_material)
% 重构版本的一级参数生成函数
% 当原有函数不可用时使用

fprintf('使用重构版本生成一级参数...\n');

%% 1. 定义穷举参数范围（严格按照原有代码）
% 模数离散值
modules = [7, 8, 9, 10, 11, 12, 13];

% 齿数范围
min_teeth = 17;
max_teeth = 100;

% 螺旋角范围（度）
helix_angle_range = [8, 13];
beta_values = linspace(helix_angle_range(1), helix_angle_range(2), 6);

% 齿宽系数范围
k_h1_values = 0.28:0.01:0.4;

% 压力角（固定20度）
pressure_angle_deg = 20;

% 传动比范围
ratio_min = 2.5;
ratio_max = 3.5;
ratio_step = 0.1;

%% 2. 穷举计算
fprintf('开始穷举计算一级参数组合...\n');
fprintf('模数范围: %s\n', mat2str(modules));
fprintf('齿数范围: %d-%d\n', min_teeth, max_teeth);
fprintf('螺旋角范围: %.1f°-%.1f°\n', helix_angle_range(1), helix_angle_range(2));
fprintf('齿宽系数范围: %.2f-%.2f\n', min(k_h1_values), max(k_h1_values));

combinations = [];
valid_combinations = 0;

% 计算输入扭矩
input_torque = 9550 * system_params.input_power / system_params.input_speed;

for m_idx = 1:length(modules)
    m1 = modules(m_idx);
    
    for z1 = min_teeth:min(60, max_teeth)
        for ratio = ratio_min:ratio_step:ratio_max
            z2_exact = z1 * ratio;
            z2 = round(z2_exact);
            
            % 检查z2范围
            if z2 < min_teeth || z2 > max_teeth
                continue;
            end
            
            % 检查传动比误差
            actual_ratio = z2 / z1;
            if actual_ratio < 2.5 || actual_ratio > 3.5
                continue;
            end
            
            ratio_error = abs(actual_ratio - ratio) / ratio;
            if ratio_error > 0.05
                continue;
            end
            
            % 遍历螺旋角
            for beta = beta_values
                % 遍历齿宽系数
                for k_h1 = k_h1_values
                    % 遍历变位系数组合
                    for x1 = 0.0:0.1:1.0
                        for x2 = 0.0:0.1:1.0
                            x_sum = x1 + x2;
                            if x_sum < 0.0 || x_sum > 1.0
                                continue;
                            end
                            
                            % 计算实际中心距
                            [actual_center_distance, is_valid] = calculateCenterDistance(m1, z1, z2, x1, x2, beta, pressure_angle_deg);
                            
                            if ~is_valid
                                continue;
                            end
                            
                            % 检查中心距误差
                            center_distance_error = abs(actual_center_distance - system_params.center_distance) / system_params.center_distance;
                            if center_distance_error > 0.0005  % 0.05%误差
                                continue;
                            end
                            
                            % 计算齿宽
                            gear_width = k_h1 * actual_center_distance;
                            
                            % 计算质量（使用与原始代码完全相同的方法）
                            [mass1, mass2, total_mass] = calculateGearMass(m1, z1, z2, gear_width, beta, x1, x2, gear_material);
                            
                            % 计算安全系数（简化计算）
                            [contact_sf, bending_sf1, bending_sf2] = calculateSafetyFactors(m1, z1, z2, beta, x1, x2, input_torque, gear_material);
                            
                            % 检查安全系数
                            if contact_sf >= system_params.contact_safety_factor && ...
                               bending_sf1 >= system_params.bending_safety_factor && ...
                               bending_sf2 >= system_params.bending_safety_factor
                                
                                % 保存有效组合
                                valid_combinations = valid_combinations + 1;
                                combinations(end+1, :) = [m1, z1, z2, actual_ratio, beta, pressure_angle_deg, k_h1, ...
                                                         x1, x2, x_sum, actual_center_distance, gear_width, ...
                                                         total_mass, mass1, mass2, bending_sf1, bending_sf2, contact_sf];
                            end
                        end
                    end
                end
            end
        end
    end
    
    fprintf('模数 %d mm 完成，当前有效组合: %d\n', m1, valid_combinations);
end

%% 3. 保存结果
if ~isempty(combinations)
    % 创建表格
    var_names = {'模数(mm)', '小齿轮齿数', '大齿轮齿数', '传动比', '螺旋角(°)', '压力角(°)', '齿宽系数', ...
                '小齿轮变位系数', '大齿轮变位系数', '综合变位系数', '实际中心距(mm)', ...
                '齿宽(mm)', '总质量(kg)', '小齿轮质量(kg)', '大齿轮质量(kg)', '小齿轮弯曲安全系数', '大齿轮弯曲安全系数', '接触安全系数'};
    
    gear_combinations = array2table(combinations, 'VariableNames', var_names);
    
    % 按质量排序
    gear_combinations = sortrows(gear_combinations, 13);
    
    % 筛选满足安全系数的组合
    sf_mask = gear_combinations{:, 18} >= system_params.contact_safety_factor & ...
              gear_combinations{:, 16} >= system_params.bending_safety_factor & ...
              gear_combinations{:, 17} >= system_params.bending_safety_factor;
    
    selected_params = gear_combinations(sf_mask, :);
    
    % 保存MAT文件
    results_dir = 'Results';
    if ~exist(results_dir, 'dir')
        mkdir(results_dir);
    end
    
    mat_filename = fullfile(results_dir, '一级平行轴系满足安全系数的参数.mat');
    save(mat_filename, 'selected_params', 'gear_combinations');
    
    fprintf('✓ 保存MAT文件: %s\n', mat_filename);
    fprintf('  - 总组合数: %d\n', height(gear_combinations));
    fprintf('  - 满足安全系数组合数: %d\n', height(selected_params));

    % 执行聚类分析
    fprintf('\n=== 一级参数聚类分析 ===\n');
    clustered_params = ClusterFirstStageParams(selected_params);
    fprintf('聚类完成：%d 组原始参数 → %d 组代表性参数\n', height(selected_params), height(clustered_params));

    % 生成HTML报告（包含聚类结果）
    generateHTMLReportWithClustering(gear_combinations, selected_params, clustered_params, system_params);
    
else
    fprintf('✗ 未找到满足条件的一级参数组合！\n');
end

end

%% ========== 辅助计算函数 ==========

function [center_distance, is_valid] = calculateCenterDistance(m1, z1, z2, x1, x2, beta, pressure_angle_deg)
% 计算实际中心距
try
    pressure_angle = pressure_angle_deg * pi / 180;
    beta_rad = beta * pi / 180;
    
    if beta == 0
        % 直齿轮
        inv_alpha = tan(pressure_angle) - pressure_angle;
        inv_alpha_w = inv_alpha + 2 * (x1 + x2) * tan(pressure_angle) / (z1 + z2);
        alpha_w = fzero(@(a) tan(a) - a - inv_alpha_w, pressure_angle);
        center_distance = m1 * (z1 + z2) * cos(pressure_angle) / (2 * cos(alpha_w));
    else
        % 斜齿轮
        mt1 = m1 / cos(beta_rad);
        inv_alpha_t = tan(pressure_angle) - pressure_angle;
        inv_alpha_tw = inv_alpha_t + 2 * (x1 + x2) * tan(pressure_angle) / (z1 + z2);
        alpha_tw = fzero(@(a) tan(a) - a - inv_alpha_tw, pressure_angle);
        center_distance = mt1 * (z1 + z2) * cos(pressure_angle) / (2 * cos(alpha_tw));
    end
    
    is_valid = true;
catch e
    error('中心距计算失败: %s', e.message);
end
end

function [mass1, mass2, total_mass] = calculateGearMass(m1, z1, z2, gear_width, beta, x1, x2, gear_material)
% 齿轮质量计算 - 严格按照原始代码的计算方法
% 构建齿轮参数结构体，包含所有必要参数
gear_params = struct();
gear_params.m1 = m1;
gear_params.z1 = z1;
gear_params.z2 = z2;
gear_params.b1 = gear_width;
gear_params.helix_angle_1 = beta;  % 使用实际螺旋角
gear_params.pressure_angle = 20;   % 标准压力角
gear_params.x1 = x1;               % 使用实际变位系数
gear_params.x2 = x2;
gear_params.gear_materials = struct('parallel', gear_material);

% 调用新的平行轴齿轮质量计算函数
[mass1, mass2, total_mass] = ParallelGearMassCalculator(gear_params);
end

function [contact_sf, bending_sf1, bending_sf2] = calculateSafetyFactors(m1, z1, z2, beta, x1, x2, input_torque, gear_material)
% 安全系数计算 - 使用原有的计算方法，不允许简化估算

    % 构建齿轮参数结构体 - 完全匹配ParallelGearSafetyCalculator的期望格式
    gear_params = struct();
    gear_params.m = m1;
    gear_params.z = z1;          % 主动齿轮（小齿轮）齿数
    gear_params.mating_z = z2;   % 啮合齿轮（大齿轮）齿数
    gear_params.alpha = 20;      % 压力角 (度)
    gear_params.beta = beta;     % 螺旋角 (度)
    gear_params.b = m1 * 10;     % 齿宽，简化为10倍模数
    gear_params.x = x1;          % 主动齿轮变位系数
    gear_params.mating_x = x2;   % 啮合齿轮变位系数

    % 构建载荷参数结构体
    load_params = struct();
    load_params.T = input_torque;     % 扭矩 (N·m)
    load_params.n = 1500;             % 转速 (rpm)，假设值
    load_params.KA = 1.25;            % 应用系数
    load_params.service_life = 20000; % 设计寿命 (h)

    % 构建材料参数结构体（17CrNiMo6材料）- 完全匹配ParallelGearSafetyCalculator的期望格式
    material_params = struct();
    material_params.sigmaHlim = 1500;  % 接触疲劳极限 (MPa)
    material_params.sigmaFlim = 400;   % 弯曲疲劳极限 (MPa)
    material_params.E = 206000;        % 弹性模量 (MPa)
    material_params.poisson = 0.3;     % 泊松比

    % 调用安全系数计算函数 - 不使用try-catch，强制使用正确的计算方法
    [bending_sf1, bending_sf2, contact_sf] = ParallelGearSafetyCalculator(gear_params, load_params, material_params, 'ISO6336');
end

function generateSimpleHTMLReport(gear_combinations, selected_params, system_params)
% 生成简化的HTML报告
html_content = sprintf(['<!DOCTYPE html>\n<html>\n<head>\n' ...
                       '<meta charset="UTF-8">\n' ...
                       '<title>一级平行轴系参数组合报告</title>\n' ...
                       '</head>\n<body>\n' ...
                       '<h1>一级平行轴系参数组合报告</h1>\n' ...
                       '<p>总组合数: %d</p>\n' ...
                       '<p>满足安全系数组合数: %d</p>\n' ...
                       '</body>\n</html>'], ...
                       height(gear_combinations), height(selected_params));

html_filename = fullfile('Results', '一级平行轴系参数组合报告.html');
fid = fopen(html_filename, 'w', 'n', 'UTF-8');
if fid ~= -1
    fprintf(fid, '%s', html_content);
    fclose(fid);
    fprintf('✓ 生成HTML报告: %s\n', html_filename);
else
    fprintf('✗ HTML报告生成失败\n');
end
end

function generateHTMLReportWithClustering(gear_combinations, selected_params, clustered_params, system_params)
% 生成包含聚类结果的HTML报告
try
    % 调用原有的HTML生成函数
    html_file = GenerateFirstStageHTML(gear_combinations, selected_params, system_params.contact_safety_factor, system_params.bending_safety_factor);
    fprintf('✓ HTML报告已生成: %s\n', html_file);
catch ME
    fprintf('✗ HTML报告生成失败: %s\n', ME.message);
    % 生成简化版本
    generateSimpleHTMLReport(gear_combinations, selected_params, system_params);
end
end
