# 齿轮传动系统优化约束更新

## 新增优化约束

根据需求，我们对三级齿轮传动系统优化逻辑进行了以下更新：

1. **螺旋角约束**：
   - 只有一级传动可以使用斜齿轮，螺旋角范围限制为8°-13°
   - 二级和三级行星轮系必须使用直齿轮（螺旋角 β₂ = β₃ = 0°）

2. **压力角约束**：
   - 一级传动和二级行星轮系的压力角固定为20°
   - 三级行星轮系的压力角是一个优化变量，但只能在20°或25°之间离散选择

3. **行星轮数量约束**：
   - 二级和三级行星轮数量被设置为离散变量，只能取值为3、4或5
   - 这保证了行星轮数量始终为整数且符合工程实际

4. **模数离散化约束**：
   - 所有齿轮模数现在必须使用标准离散值
   - 模数值根据压力角进行差异化设置：
     * 对于20°压力角（适用于一级、二级以及压力角为20°的三级齿轮）：
       模数只能取值为：5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 20
     * 对于25°压力角（只适用于压力角为25°的三级齿轮）：
       模数只能取值为：7, 9, 10, 11, 12, 13, 16, 17, 18, 20
   - 确保模数值符合实际工程标准和压力角要求

5. **齿数约束**：
   - 所有齿轮齿数必须为整数，且满足最小齿数要求
   - 所有齿轮的齿数下限都设置为17，确保足够的强度和啮合性能
   - 一级平行轴大齿轮齿数不超过100
   - 内齿圈齿数（zr2, zr3）不超过120
   - 内齿圈齿数通过限制太阳轮和行星轮齿数来间接控制：
     * 二级内齿圈：zr2 = zs2 + 2 * zp2 ≤ 120
     * 三级内齿圈：zr3 = zs3 + 2 * zp3 ≤ 120
   
6. **齿宽系数约束**：
   - 一级传动系统的齿宽系数（k_h1）严格限制在0.28-0.4之间
   - 二级和三级行星轮系的齿宽系数（k_h2和k_h3）严格限制在0.6-1.0之间
   - 增加了齿宽系数的上限控制，避免出现异常值（如k_h2=1.16或更高的值）
   - 这些约束基于工程经验，确保齿轮具有足够的强度和耐久性，同时避免过大的齿宽带来的制造困难

7. **变位系数约束**：
   - 外啮合齿轮（平行轴大小齿轮、行星轮系中的太阳轮和行星轮）的变位系数必须为正值
   - 内齿圈（行星轮系中的内齿圈）可以使用负值变位系数
   - 每对啮合齿轮的变位系数总和必须在0.0到1.0之间，以确保良好的啮合性能
   - 变位系数约束适用于以下啮合对：
     * 一级平行轴大小齿轮（x1 + x2）
     * 二级行星轮系太阳轮-行星轮（xs2 + xp2）
     * 三级行星轮系太阳轮-行星轮（xs3 + xp3）

## 修改的文件

1. **GearOptimizationMain.m**
   - 更新了变量范围设置，将螺旋角和压力角限制在指定范围内
   - 添加了离散变量信息，指定第三级压力角只能是20°或25°
   - 添加了行星轮数量的离散变量设置，限制只能为3、4或5
   - 更新了齿数约束，将所有齿轮齿数下限设为17
   - 限制内齿圈齿数不超过120，通过调整行星轮齿数上限实现
   - 更新了模数离散变量设置，根据压力角区分不同的标准模数值
   - 更新了问题描述，说明约束条件
   - 在结果处理中确保模数都是标准离散值，并根据压力角选择合适的模数集合
   - 修改了变位系数的下限，确保外啮合齿轮的变位系数为正值
   - 增加了变位系数约束的参数设置
   - 设置了齿宽系数的严格范围限制：一级齿宽系数在0.28-0.4之间，二级和三级齿宽系数在0.6-1.0之间

2. **Model/CostFunctionWrapper.m**
   - 修改了函数参数，添加了对三级行星轮系专用压力角参数的支持
   - 实现了离散压力角选择逻辑，根据优化变量值选择最近的离散值（20°或25°）
   - 实现了行星轮数量的离散处理，根据连续优化变量确定最近的离散值（3、4或5）
   - 更新了模数离散化处理，根据压力角选择不同的标准模数值集合
   - 确保20°压力角的齿轮使用特定模数集合，25°压力角的齿轮使用另一特定模数集合
   - 增加了变位系数的约束处理，确保外啮合齿轮变位系数为正值
   - 添加了对啮合齿轮变位系数总和的约束检查，确保总和在0.0到1.0之间
   - 对违反变位系数约束的解施加惩罚
   - 增加了齿宽系数的严格上限约束，确保二级和三级齿宽系数不超过1.0
   - 添加了更严格的传动比约束检查和惩罚机制

3. **Model/GearOptObjectives.m**
   - 更新了函数签名，添加了三级行星轮系压力角参数
   - 修改了内部逻辑，在三级行星轮系计算中使用专用压力角值
   - 添加了内齿圈变位系数的计算，基于外啮合齿轮的变位系数
   - 增加了齿宽系数的上限约束，确保二级和三级齿宽系数不超过1.0

## 优化逻辑变化

修改后的优化逻辑现在确保：

1. 只有一级传动系统会使用斜齿轮设计，其螺旋角在8°-13°之间
2. 二级和三级行星轮系均使用直齿轮设计（螺旋角为0°）
3. 一级和二级的压力角固定为标准的20°
4. 三级行星轮系的压力角通过优化算法在20°和25°中选择最佳值
5. 所有齿轮的齿数下限均设为17，保证足够的强度和啮合性能
6. 一级平行轴大齿轮齿数不超过100，内齿圈齿数不超过120，以符合制造工艺要求
7. 行星轮数量保证是实际可用的整数值（3、4或5），符合工程实际需求
8. 所有模数都使用标准离散值，根据压力角区分不同的标准模数集合：
   - 20°压力角使用更广泛的模数集合（5-20之间的特定值）
   - 25°压力角使用更专用的模数集合（7-20之间的部分特定值）
9. 外啮合齿轮的变位系数保证为正值，内齿圈的变位系数可以为负值
10. 所有啮合齿轮对的变位系数总和都在0.0到1.0之间，以确保良好的啮合性能
11. 齿宽系数有严格的界限限制：一级齿宽系数在0.28-0.4之间，二级和三级齿宽系数在0.6-1.0之间

这些变化将有助于找到更符合实际工程约束的优化解决方案，同时满足系统的性能要求和制造标准。

## 变位系数优化科学化改进

针对之前变位系数优化中存在的缺陷，我们进行了全面的科学改进：

1. **变位系数动态优化**：
   - 取代原有的固定范围约束(例如x1: 0.3-0.7, x2: 0.2-0.5等)，引入了基于齿轮几何学和啮合理论的科学变位计算
   - 新增了`CalculatePlanetaryShiftCoefficients.m`函数，实现行星轮系变位系数的动态计算
   - 新增了`CalculateParallelShiftCoefficients.m`函数，实现平行轴系统变位系数的动态计算
   - 变位系数范围现在基于防止根切、保证齿顶厚度及最佳啮合性能等因素动态确定

2. **防根切变位计算**：
   - 引入了基于压力角和齿数的最小变位系数计算：
     * 对于外啮合齿轮：`x_min_undercut = (min_teeth_no_undercut - z) / 2`
     * 其中 `min_teeth_no_undercut = 2 / sin(α)²`
   - 小齿数齿轮自动获得更大的正变位，避免根切

3. **齿顶厚度保证**：
   - 限制最大变位系数以保证足够的齿顶厚度：
     * `x_max_tip = z * (0.5 + sa_min - ha_star/z) - 0.5`
     * 其中`sa_min`为最小齿顶厚度系数(0.25-0.35)

4. **滑动磨损均衡优化**：
   - 采用Maag变位系数分配公式:
     * `x1_opt = sum_x * (u - 1) / (u + 1)`
     * `x2_opt = sum_x - x1_opt`
   - 确保齿轮对的滑动率平衡，提高啮合效率和使用寿命

5. **中心距精确控制**：
   - 变位系数总和根据中心距要求自动计算：
     * `sum_x = center_distance_factor * (z1 + z2) / (2 * tan(α))`
   - 确保满足预定的中心距要求

6. **行星轮系专用处理**：
   - 考虑行星轮系的特殊性，同时处理两对啮合齿轮:
     * 太阳轮-行星轮啮合
     * 行星轮-内齿圈啮合
   - 内齿圈变位系数通过力平衡关系自动计算：
     * `xr = -(xs*zs + xp*zp) / zr`

7. **内啮合专用处理**：
   - 专门为内啮合齿轮(如行星轮-内齿圈)设计的变位计算
   - 内啮合滑动率平衡采用特殊公式：
     * `xp_opt = sum_x * (u + 1) / (u - 1)` (内啮合传动比u为负值)
     * `xr_opt = -xp_opt * (zp / zr)`

8. **优化迭代集成**：
   - 在CostFunctionWrapper中无缝集成了科学变位计算
   - 对优化算法生成的候选解自动应用科学变位约束
   - 为满足同时啮合的行星轮系提供特殊处理

这些改进确保了变位系数的优化不再是简单的固定范围约束，而是基于齿轮啮合理论的科学计算，能够动态适应不同齿轮参数组合的需求，提高齿轮传动系统的性能和可靠性。

## 变位系数计算优化更新 (2023-07-28)

### 改进内容
我们对齿轮变位系数的计算方法进行了全面优化，主要包括以下几个方面：

1. **重合度计算与约束**
   - 加入了精确的重合度(ε)计算方法
   - 确保所有齿轮副重合度不小于1.2，提高传动平稳性
   - 区分了外啮合和内啮合的重合度计算方法

2. **更精确的齿顶厚度计算**
   - 改进了齿顶厚度的计算方法，确保齿顶厚度不小于0.4模数
   - 使用反函数求解方法精确计算满足齿顶厚度要求的最大变位系数

3. **更详细的啮合干涉检查**
   - 使用公式(17)(18)(19)实现了更精确的啮合干涉检查
   - 区分了外啮合和内啮合的干涉判断条件
   - 根据齿轮啮合几何理论，确保无根切和无顶切

4. **工作压力角计算**
   - 准确计算工作压力角(α_w)和啮合线长度
   - 基于渐开线函数反解，确保计算的精确性

5. **变位系数和的科学约束**
   - 将变位系数和的约束范围设定为0.6-1.2
   - 根据工程经验，保证合理的传动性能

6. **行星轮系特殊处理**
   - 针对行星轮系同时啮合太阳轮和内齿圈的特点，实现了综合优化
   - 确保行星轮变位系数同时满足与太阳轮啮合和与内齿圈啮合的要求
   - 引入迭代调整机制，在不满足重合度要求时进行修正

### 计算方法说明
新的`CalculatePlanetaryShiftCoefficients`函数实现了基于齿轮几何学和啮合理论的科学变位系数计算，主要包括：

1. **防根切原则**：确保齿根有足够强度，避免加工时出现根切
2. **齿顶厚度保证**：确保齿顶有足够厚度，避免尖齿问题
3. **滑动磨损均衡**：使用Maag公式计算最优变位系数，实现滑动率平衡
4. **重合度约束**：确保足够的重合度，提高传动平稳性
5. **啮合干涉检查**：防止齿顶干涉，确保正常啮合
6. **行星轮系特殊处理**：考虑行星轮同时与太阳轮和内齿圈啮合的特点

### 改进效果
1. **更科学的变位系数**：变位系数计算基于齿轮理论，而非简单的固定范围约束
2. **更可靠的传动性能**：通过重合度约束和齿顶厚度约束，提高传动可靠性
3. **更均衡的齿轮磨损**：基于Maag公式的变位系数分配，使齿轮磨损更均衡
4. **更精确的中心距控制**：考虑变位系数和压力角的影响，精确控制中心距
5. **更完善的内齿圈处理**：专门针对内齿圈啮合的特点，设计合理的变位系数

### 使用说明
优化后的变位系数计算方法已在`CostFunctionWrapper.m`中集成。在优化过程中，变位系数会根据齿轮几何参数自动计算科学的变位系数范围，并基于此调整优化变量。如有特殊需求，可以在`CalculatePlanetaryShiftCoefficients.m`和`CalculateParallelShiftCoefficients.m`中调整相关参数。

## 最新更新（变位系数和约束）

* **变位系数和控制**：修改了变位系数约束，确保外啮合齿轮对的变位系数总和严格控制在0.0-1.0之间，以获得更佳的啮合性能。
* **实现方式**：在`CalculatePlanetaryShiftCoefficients.m`和`CalculateParallelShiftCoefficients.m`中加强了变位系数和的约束检查，对于普通平行轴齿轮和行星轮系都实施了这一约束。
* **计算原理**：当变位系数和小于0.0时，按比例增加各齿轮变位系数；当大于1.0时，按比例减少，确保变位系数总和精确落在要求范围内。 

## 文件命名更新 (2023-08-15)

为了提高代码可读性和易用性，变位系数计算功能已经按照齿轮类型进行了分离：

1. `CalculatePlanetaryShiftCoefficients.m` - 专门用于行星轮系变位系数计算
   - 包括太阳轮-行星轮啮合（外啮合）
   - 行星轮-内齿圈啮合（内啮合）
   - 考虑行星轮同时啮合两对齿轮的特殊约束

2. `CalculateParallelShiftCoefficients.m` - 专门用于平行轴齿轮系统变位系数计算
   - 基于给定中心距计算最优变位系数
   - 适用于斜齿轮和直齿轮
   - 考虑滑动率平衡和齿顶厚度约束

这种分离使得代码更专注于各自的功能，并且命名更加直观，有利于系统维护和扩展。 