function PlotParetoFront(PF, algorithm_fronts, algorithm_names, problem_name, fig_num)
% PlotParetoFront - 绘制Pareto前沿图
%
% 输入:
%   PF - 真实的Pareto前沿，如果为空则不绘制
%   algorithm_fronts - 各算法获得的前沿，元胞数组，每个元素是一个nx2矩阵
%   algorithm_names - 算法名称，元胞数组
%   problem_name - 问题名称，字符串
%   fig_num - 图形编号，可选，默认为1
%
% 示例:
%   PlotParetoFront(PF, {F1, F2}, {'NSGA2', 'MOPSO'}, 'ZDT1', 1)

    % 默认参数
    if nargin < 5
        fig_num = 1;
    end

    % 创建图形
    figure(fig_num);
    clf;
    hold on;
    
    % 设置图形属性
    set(gcf, 'Position', [100, 100, 800, 600]);
    
    % 颜色和标记
    colors = {'b', 'r', 'g', 'm', 'c', [0.8 0.2 0.2], [0.2 0.8 0.2], [0.2 0.2 0.8], [0.8 0.8 0.2], [0.8 0.2 0.8]};
    markers = {'o', 's', 'd', '^', 'v', '>', '<', 'p', 'h', '*'};
    
    % 绘制真实Pareto前沿
    if ~isempty(PF)
        plot(PF(:, 1), PF(:, 2), 'k-', 'LineWidth', 2);
    end
    
    % 绘制各算法获得的前沿
    legend_entries = {};
    if ~isempty(PF)
        legend_entries{1} = '真实Pareto前沿';
    end
    
    for i = 1:length(algorithm_fronts)
        F = algorithm_fronts{i};
        color_idx = mod(i-1, length(colors)) + 1;
        marker_idx = mod(i-1, length(markers)) + 1;
        
        % 绘制散点图
        plot(F(:, 1), F(:, 2), ...
             [colors{color_idx}, markers{marker_idx}], ...
             'MarkerSize', 6, ...
             'LineWidth', 1.5, ...
             'MarkerFaceColor', colors{color_idx}, ...
             'MarkerEdgeColor', 'k');
        
        % 添加图例条目
        legend_entries{end+1} = algorithm_names{i};
    end
    
    % 添加图例
    legend(legend_entries, 'Location', 'best', 'FontSize', 12, 'Interpreter', 'none');
    
    % 添加标题和坐标轴标签
    title(['Pareto前沿 - ', problem_name], 'FontSize', 14, 'FontWeight', 'bold');
    xlabel('f_1', 'FontSize', 12);
    ylabel('f_2', 'FontSize', 12);
    
    % 添加网格
    grid on;
    box on;
    
    % 调整坐标轴
    if ~isempty(algorithm_fronts)
        all_f1 = [];
        all_f2 = [];
        for i = 1:length(algorithm_fronts)
            all_f1 = [all_f1; algorithm_fronts{i}(:, 1)];
            all_f2 = [all_f2; algorithm_fronts{i}(:, 2)];
        end
        
        if ~isempty(PF)
            all_f1 = [all_f1; PF(:, 1)];
            all_f2 = [all_f2; PF(:, 2)];
        end
        
        xlim([min(all_f1)*0.9, max(all_f1)*1.1]);
        ylim([min(all_f2)*0.9, max(all_f2)*1.1]);
    end
    
    hold off;
end 