function [Archive_X, Archive_F] = MOGWO(CostFunction, nVar, VarMin, VarMax, MaxIt, nPop)
% MOGWO: 多目标灰狼优化算法 (Multi-objective Grey Wolf Optimizer)
%
% 输入参数:
%   CostFunction: 目标函数句柄
%   nVar: 决策变量数量
%   VarMin, VarMax: 决策变量的上下界
%   MaxIt: 最大迭代次数
%   nPop: 种群大小
%
% 输出参数:
%   Archive_X: 非支配解的决策变量
%   Archive_F: 非支配解的目标函数值
%
% 参考文献:
%   <PERSON><PERSON> et al., "Multi-objective grey wolf optimizer: A novel algorithm for 
%   multi-criterion optimization," Expert Systems with Applications, vol. 47, pp. 106-119, 2016.

    % 默认参数值
    if nargin < 6
        nPop = 100;
    end
    if nargin < 5
        MaxIt = 50;
    end
    if nargin < 4
        VarMax = 1;
    end
    if nargin < 3
        VarMin = 0;
    end
    if nargin < 2
        nVar = 5;
    end
    
    % 变量区间向量化
    if isscalar(VarMin)
        VarMin = VarMin * ones(1, nVar);
    end
    if isscalar(VarMax)
        VarMax = VarMax * ones(1, nVar);
    end
    
    % 算法参数
    Archive_size = 100;  % 存档大小
    alpha = 0.1;         % 网格膨胀参数
    nGrid = 10;          % 每个维度上的网格数量
    beta = 4;            % 领导者选择压力参数
    gamma = 2;           % 额外存档成员选择压力
    
    % 初始化灰狼种群
    GreyWolves = CreateEmptyParticle(nPop);
    for i = 1:nPop
        GreyWolves(i).Position = unifrnd(VarMin, VarMax);
        GreyWolves(i).Cost = CostFunction(GreyWolves(i).Position')';
        GreyWolves(i).Best.Position = GreyWolves(i).Position;
        GreyWolves(i).Best.Cost = GreyWolves(i).Cost;
    end
    
    % 确定支配关系
    GreyWolves = DetermineDomination(GreyWolves);
    
    % 初始化存档
    Archive = GetNonDominatedParticles(GreyWolves);
    
    % 获取存档中解的目标函数值
    Archive_costs = GetCosts(Archive);
    
    % 创建超立方体（用于网格划分）
    G = CreateHypercubes(Archive_costs, nGrid, alpha);
    
    % 计算存档中每个解的网格索引
    for i = 1:numel(Archive)
        [Archive(i).GridIndex, Archive(i).GridSubIndex] = GetGridIndex(Archive(i), G);
    end
    
    % 主循环
    for it = 1:MaxIt
        % 更新控制参数 a
        a = 2 - it * (2 / MaxIt);
        
        % 更新每个灰狼的位置
        for i = 1:nPop
            % 选择alpha、beta和delta灰狼作为领导者
            Delta = SelectLeader(Archive, beta);
            Beta = SelectLeader(Archive, beta);
            Alpha = SelectLeader(Archive);
            
            % 确保选择三个不同的领导者
            if numel(Archive) > 1
                counter = 0;
                rep2 = [];
                for j = 1:numel(Archive)
                    if sum(Delta.Position ~= Archive(j).Position) ~= 0
                        counter = counter + 1;
                        rep2(counter, 1) = Archive(j);
                    end
                end
                if ~isempty(rep2)
                    Beta = SelectLeader(rep2, beta);
                end
            end
            
            if numel(Archive) > 2
                counter = 0;
                rep3 = [];
                for j = 1:numel(rep2)
                    if sum(Beta.Position ~= rep2(j).Position) ~= 0
                        counter = counter + 1;
                        rep3(counter, 1) = rep2(j);
                    end
                end
                if ~isempty(rep3)
                    Alpha = SelectLeader(rep3, beta);
                end
            end
            
            % 对领导者Alpha的包围机制
            c = 2 .* rand(1, nVar);
            D = abs(c .* Alpha.Position - GreyWolves(i).Position);
            A = 2 * a * rand(1, nVar) - a;
            X1 = Alpha.Position - A .* abs(D);
            
            % 对领导者Beta的包围机制
            c = 2 .* rand(1, nVar);
            D = abs(c .* Beta.Position - GreyWolves(i).Position);
            A = 2 * a * rand - a;
            X2 = Beta.Position - A .* abs(D);
            
            % 对领导者Delta的包围机制
            c = 2 .* rand(1, nVar);
            D = abs(c .* Delta.Position - GreyWolves(i).Position);
            A = 2 * a * rand - a;
            X3 = Delta.Position - A .* abs(D);
            
            % 更新灰狼位置（基于三个领导者的位置）
            GreyWolves(i).Position = (X1 + X2 + X3) ./ 3;
            
            % 边界检查
            GreyWolves(i).Position = min(max(GreyWolves(i).Position, VarMin), VarMax);
            
            % 评估新位置
            GreyWolves(i).Cost = CostFunction(GreyWolves(i).Position')';
        end
        
        % 确定种群支配关系
        GreyWolves = DetermineDomination(GreyWolves);
        non_dominated_wolves = GetNonDominatedParticles(GreyWolves);
        
        % 更新存档
        Archive = [Archive; non_dominated_wolves];
        Archive = DetermineDomination(Archive);
        Archive = GetNonDominatedParticles(Archive);
        
        % 更新网格索引
        for i = 1:numel(Archive)
            [Archive(i).GridIndex, Archive(i).GridSubIndex] = GetGridIndex(Archive(i), G);
        end
        
        % 如果存档太大，删除一些解
        if numel(Archive) > Archive_size
            EXTRA = numel(Archive) - Archive_size;
            Archive = DeleteFromRep(Archive, EXTRA, gamma);
            
            % 更新网格
            Archive_costs = GetCosts(Archive);
            G = CreateHypercubes(Archive_costs, nGrid, alpha);
        end
    end
    
    % 提取结果
    Archive_X = zeros(numel(Archive), nVar);
    Archive_F = zeros(numel(Archive), size(Archive(1).Cost, 2));
    
    for i = 1:numel(Archive)
        Archive_X(i, :) = Archive(i).Position;
        Archive_F(i, :) = Archive(i).Cost;
    end
end

% 创建空粒子
function particles = CreateEmptyParticle(n)
    if nargin < 1
        n = 1;
    end
    
    empty_particle.Position = [];
    empty_particle.Velocity = [];
    empty_particle.Cost = [];
    empty_particle.Best.Position = [];
    empty_particle.Best.Cost = [];
    empty_particle.GridIndex = [];
    empty_particle.GridSubIndex = [];
    empty_particle.IsDominated = false;
    
    particles = repmat(empty_particle, n, 1);
end

% 确定支配关系
function pop = DetermineDomination(pop)
    nPop = numel(pop);
    
    for i = 1:nPop
        pop(i).IsDominated = false;
    end
    
    for i = 1:nPop-1
        for j = i+1:nPop
            if Dominates(pop(i).Cost, pop(j).Cost)
                pop(j).IsDominated = true;
            end
            
            if Dominates(pop(j).Cost, pop(i).Cost)
                pop(i).IsDominated = true;
            end
        end
    end
end

% 获取非支配粒子
function nd_pop = GetNonDominatedParticles(pop)
    nd_pop = pop(~[pop.IsDominated]);
end

% 提取目标函数值
function costs = GetCosts(pop)
    nObj = size(pop(1).Cost, 2);
    nPop = numel(pop);
    
    costs = zeros(nPop, nObj);
    for i = 1:nPop
        costs(i, :) = pop(i).Cost;
    end
end

% 创建超立方体（用于网格划分）
function G = CreateHypercubes(costs, nGrid, alpha)
    min_costs = min(costs, [], 1);
    max_costs = max(costs, [], 1);
    
    % 防止min和max相同导致网格宽度为0
    diff_costs = max_costs - min_costs;
    diff_costs(diff_costs == 0) = 0.1 * max_costs(diff_costs == 0);
    
    G.min = min_costs - alpha * diff_costs;
    G.max = max_costs + alpha * diff_costs;
    G.nGrid = nGrid;
    G.size = (G.max - G.min) / nGrid;
end

% 获取粒子的网格索引
function [GridIndex, GridSubIndex] = GetGridIndex(particle, G)
    nObj = size(particle.Cost, 2);
    
    % 计算在每个维度上的网格索引
    SubIndices = zeros(1, nObj);
    for j = 1:nObj
        SubIndices(j) = floor((particle.Cost(j) - G.min(j)) / G.size(j)) + 1;
        if SubIndices(j) < 1
            SubIndices(j) = 1;
        end
        if SubIndices(j) > G.nGrid
            SubIndices(j) = G.nGrid;
        end
    end
    
    % 计算总的网格索引
    GridIndex = 0;
    for j = 1:nObj
        GridIndex = GridIndex + SubIndices(j) * G.nGrid^(j-1);
    end
    
    GridSubIndex = SubIndices;
end

% 从存档中选择领导者
function leader = SelectLeader(pop, beta)
    if nargin < 2
        beta = 1;
    end
    
    % 获取所有网格索引
    GridIndices = [pop.GridIndex];
    
    % 获取每个网格中的粒子数
    OccupiedCells = unique(GridIndices);
    N = numel(OccupiedCells);
    
    % 计算每个网格的拥挤度（即每个网格中粒子的数量）
    CellCount = zeros(1, N);
    for k = 1:N
        CellCount(k) = sum(GridIndices == OccupiedCells(k));
    end
    
    % 计算选择概率
    P = (1./CellCount).^beta;
    P = P / sum(P);
    
    % 轮盘赌选择一个网格
    selected_cell_index = RouletteWheelSelection(P);
    selected_cell = OccupiedCells(selected_cell_index);
    
    % 从选中的网格中随机选择一个粒子
    selected_particle_indices = find(GridIndices == selected_cell);
    h = randi(numel(selected_particle_indices));
    leader = pop(selected_particle_indices(h));
end

% 从存档中删除多余的解
function rep = DeleteFromRep(rep, EXTRA, gamma)
    if nargin < 3
        gamma = 1;
    end
    
    GridIndices = [rep.GridIndex];
    OccupiedCells = unique(GridIndices);
    N = numel(OccupiedCells);
    
    % 计算每个网格的拥挤度
    CellCount = zeros(1, N);
    for k = 1:N
        CellCount(k) = sum(GridIndices == OccupiedCells(k));
    end
    
    % 计算选择概率
    P = CellCount.^gamma;
    P = P / sum(P);
    
    % 选择要删除的网格
    for e = 1:EXTRA
        % 更新选择概率
        if sum(P) > 0
            selected_cell_index = RouletteWheelSelection(P);
        else
            selected_cell_index = randi(N);
        end
        
        if selected_cell_index == 0
            selected_cell_index = randi(N);
        end
        
        selected_cell = OccupiedCells(selected_cell_index);
        
        % 从选中的网格中随机选择一个粒子删除
        selected_indices = find(GridIndices == selected_cell);
        h = randi(numel(selected_indices));
        
        % 删除选中的粒子
        rep(selected_indices(h)) = [];
        
        % 更新GridIndices
        GridIndices = [rep.GridIndex];
        
        % 更新拥挤度
        CellCount(selected_cell_index) = sum(GridIndices == selected_cell);
        
        if CellCount(selected_cell_index) == 0
            % 如果网格空了，删除这个网格
            OccupiedCells(selected_cell_index) = [];
            CellCount(selected_cell_index) = [];
            P(selected_cell_index) = [];
            N = N - 1;
        else
            % 否则更新选择概率
            P = CellCount.^gamma;
            P = P / sum(P);
        end
    end
end

% 轮盘赌选择
function index = RouletteWheelSelection(P)
    if sum(P) == 0
        index = 0;
        return;
    end
    
    r = rand;
    c = cumsum(P);
    index = find(r <= c, 1, 'first');
end

% 支配关系检查
function dom = Dominates(x, y)
    % x支配y，如果x的所有目标都不劣于y，且至少有一个目标严格优于y
    dom = all(x <= y) && any(x < y);
end 