%
% Copyright (c) 2015, Yarpiz (www.yarpiz.com)
% All rights reserved. Please read the "license.txt" for license terms.
%
% Project Code: YOEA122
% Project Title: Strength Pareto Evolutionary Algorithm 2 (SPEA2)
% Publisher: Ya<PERSON><PERSON> (www.yarpiz.com)
% 
% Developer: <PERSON><PERSON><PERSON> (Member of Yarpiz Team)
% 
% Contact Info: <EMAIL>, <EMAIL>
%

function z=MOP2(x)

    n=numel(x);
    
    z=[1-exp(-sum((x-1/sqrt(n)).^2))
       1-exp(-sum((x+1/sqrt(n)).^2))];
    
end