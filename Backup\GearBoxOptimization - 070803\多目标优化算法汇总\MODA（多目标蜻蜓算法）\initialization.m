%___________________________________________________________________%
%  Multi-Objective Dragonfly Algorithm (MODA) source codes demo     %
%                           version 1.0                             %
%                                                                   %
%  Developed in MATLAB R2011b(7.13)                                 %
%                                                                   %
%  Author and programmer: <PERSON><PERSON><PERSON>                        %
%                                                                   %
%         e-Mail: ali.mir<PERSON><PERSON><PERSON>@gmail.com                           %
%                 <EMAIL>             %
%                                                                   %
%       Homepage: http://www.alimirjalili.com                       %
%                                                                   %
%   Main paper:                                                     %
%                                                                   %
%   <PERSON><PERSON>, Dragonfly algorithm: a new meta-heuristic         %
%   optimization technique for solving single-objective, discrete,  %
%   and multi-objective problems, Neural Computing and Applications %
%   DOI: http://dx.doi.org/10.1007/s00521-015-1920-1                %
%___________________________________________________________________%

% This function initialize the first population of search agents
function X=initialization(SearchAgents_no,dim,ub,lb)

Boundary_no= size(ub,2); % numnber of boundaries

% If the boundaries of all variables are equal and user enter a signle
% number for both ub and lb
if Boundary_no==1
    ub_new=ones(1,dim)*ub;
    lb_new=ones(1,dim)*lb;
else
    ub_new=ub;
    lb_new=lb;
end

% If each variable has a different lb and ub
for i=1:dim
    ub_i=ub_new(i);
    lb_i=lb_new(i);
    X(:,i)=rand(SearchAgents_no,1).*(ub_i-lb_i)+lb_i;
end

X=X';
