function [objectives, constraints] = OptimizeWithFixedFirstStage(x, first_stage_params, input_power, input_speed, output_speed, ...
                                             service_life, contact_safety_factor, bending_safety_factor, gear_material, gear_materials, quality_grade)
% OptimizeWithFixedFirstStage 在固定一级平行轴系参数的情况下优化二三级行星轮系
%
%   输入参数:
%   - x: 优化变量 [mn2, zs2, zp2, k_h2, mn3, zs3, zp3, k_h3, planets_count_2, planets_count_3, 
%                  pressure_angle, helix_angle_2, helix_angle_3, xs2, xp2, xs3, xp3, pressure_angle_3_choice]
%   - first_stage_params: 一级平行轴系固定参数，包含以下字段:
%       - m: 模数 (mm)
%       - z1: 小齿轮齿数
%       - z2: 大齿轮齿数
%       - beta: 螺旋角 (度)
%       - alpha: 压力角 (度)
%       - x1: 小齿轮变位系数
%       - x2: 大齿轮变位系数
%       - k_h: 齿宽系数
%       - b: 齿宽 (mm)
%       - a: 中心距 (mm)
%   - input_power: 输入功率 (kW)
%   - input_speed: 输入转速 (rpm)
%   - output_speed: 输出转速 (rpm)
%   - service_life: 设计寿命 (h)
%   - contact_safety_factor: 接触安全系数要求
%   - bending_safety_factor: 弯曲安全系数要求
%   - gear_material: 齿轮材料参数
%   - gear_materials: 详细的齿轮材料参数
%   - quality_grade: 齿轮精度等级
%
%   输出:
%   - objectives: 优化目标值 [总质量, 最小弯曲安全系数, 最小接触安全系数]
%   - constraints: 约束违反程度

% 对二三级行星轮系的优化变量进行处理
% 标准模数 - 20度压力角
m_values_20deg = [5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 20];
% 标准模数 - 25度压力角
m_values_25deg = [7, 9, 10, 11, 12, 13, 16, 17, 18, 20];

% 检查输入变量x的有效性和维度
if isempty(x) || ~isvector(x)
    error('输入的优化变量x必须是非空向量');
end

% 确保x是行向量
if size(x, 1) > 1
    x = x';
end

% 检查x的长度是否足够
if length(x) < 17
    error('优化变量x至少需要17个元素，当前只有%d个', length(x));
end

% 处理三级压力角的离散变量
if length(x) >= 18
    % 将第18个变量四舍五入到最接近的整数（0或1）
    pressure_angle_choice = round(x(18));
    
    % 限制在0-1范围内
    pressure_angle_choice = max(0, min(1, pressure_angle_choice));
    
    % 转换为实际压力角值
    discrete_values = [20, 25];
    pressure_angle_3 = discrete_values(pressure_angle_choice + 1);
else
    % 如果没有第18个变量，默认使用20度
    pressure_angle_3 = 20;
    pressure_angle_choice = 0;
end

% 选择最接近的标准模数值
% 二级模数(mn2) - 始终使用20度压力角的标准模数
distances_mn2 = abs(x(1) - m_values_20deg);
[~, idx_mn2] = min(distances_mn2);
x(1) = m_values_20deg(idx_mn2);

% 三级模数(mn3) - 根据压力角选择不同的标准模数
if pressure_angle_3 == 20
    distances_mn3 = abs(x(5) - m_values_20deg);
    [~, idx_mn3] = min(distances_mn3);
    x(5) = m_values_20deg(idx_mn3);
else % 压力角为25度
    distances_mn3 = abs(x(5) - m_values_25deg);
    [~, idx_mn3] = min(distances_mn3);
    x(5) = m_values_25deg(idx_mn3);
end

% 从设计变量中提取行星轮数量并处理为离散值(3、4或5)
discrete_planet_values = [3, 4, 5]; % 行星轮数量只能是3、4或5
% 二级行星轮数量
distances_2 = abs(x(9) - discrete_planet_values);
[~, idx_2] = min(distances_2);
planets_count_2 = discrete_planet_values(idx_2);

% 三级行星轮数量
distances_3 = abs(x(10) - discrete_planet_values);
[~, idx_3] = min(distances_3);
planets_count_3 = discrete_planet_values(idx_3);

% 提取角度参数
pressure_angle = x(11);
helix_angle_2 = 0; % 二级螺旋角强制设置为0°（直齿轮）
helix_angle_3 = 0; % 三级螺旋角强制设置为0°（直齿轮）

% 提取齿数并确保为整数，约束在17-100范围内
zs2 = max(17, min(100, round(x(2)))); % 二级太阳轮齿数17-100
zp2 = max(17, min(100, round(x(3)))); % 二级行星轮齿数17-100
zs3 = max(17, min(100, round(x(6)))); % 三级太阳轮齿数17-100
zp3 = max(17, min(100, round(x(7)))); % 三级行星轮齿数17-100

% 计算内齿圈齿数
zr2 = zs2 + 2 * zp2;
zr3 = zs3 + 2 * zp3;

% 检查内齿圈齿数约束（17-120）
if zr2 < 17 || zr2 > 120
    if zr2 < 17
        % 内齿圈齿数过小，增加行星轮齿数
        new_zp2 = ceil((17 - zs2) / 2);
        zp2 = max(17, min(100, new_zp2));
    else
        % 内齿圈齿数过大，减少行星轮齿数
        new_zp2 = floor((120 - zs2) / 2);
        zp2 = max(17, min(100, new_zp2));
    end
    zr2 = zs2 + 2 * zp2;  % 重新计算
end

if zr3 < 17 || zr3 > 120
    if zr3 < 17
        % 内齿圈齿数过小，增加行星轮齿数
        new_zp3 = ceil((17 - zs3) / 2);
        zp3 = max(17, min(100, new_zp3));
    else
        % 内齿圈齿数过大，减少行星轮齿数
        new_zp3 = floor((120 - zs3) / 2);
        zp3 = max(17, min(100, new_zp3));
    end
    zr3 = zs3 + 2 * zp3;  % 重新计算
end

% 提取变位系数（确保索引存在）
profile_shifts = zeros(1, 6); % 初始化为零
if length(x) >= 14
    profile_shifts(3) = x(14); % xs2
end
if length(x) >= 15
    profile_shifts(4) = x(15); % xp2
end
if length(x) >= 16
    profile_shifts(5) = x(16); % xs3
end
if length(x) >= 17
    profile_shifts(6) = x(17); % xp3
end

% 提取齿宽系数
k_h2 = max(0.6, min(1.0, x(4)));   % 二级齿宽系数
k_h3 = max(0.6, min(1.0, x(8)));   % 三级齿宽系数

% 检查first_stage_params的有效性
if ~isstruct(first_stage_params) || ~isfield(first_stage_params, 'm') || ...
   ~isfield(first_stage_params, 'z1') || ~isfield(first_stage_params, 'z2')
    error('first_stage_params必须是包含必要字段(m, z1, z2等)的结构体');
end

% 从first_stage_params提取一级参数（带安全检查）
% 确保每个字段都是标量
m1 = extractScalarField(first_stage_params, 'm', 10);
z1 = extractScalarField(first_stage_params, 'z1', 20);
z2 = extractScalarField(first_stage_params, 'z2', 60);
helix_angle_1 = extractScalarField(first_stage_params, 'beta', 10);
x1 = extractScalarField(first_stage_params, 'x1', 0.4);
x2 = extractScalarField(first_stage_params, 'x2', 0.2);
k_h1 = extractScalarField(first_stage_params, 'k_h', 0.35);

% 构建完整的优化变量向量 - 确保所有元素都是标量
% 使用命名数组来清晰地组织数据
full_x = zeros(1, 25); % 预分配固定大小的数组

% 逐个填充数组元素，以确保所有元素都是标量
full_x(1) = m1;
full_x(2) = z1;
full_x(3) = z2;
full_x(4) = x(1);
full_x(5) = zs2;
full_x(6) = zp2;
full_x(7) = k_h2;
full_x(8) = x(5);
full_x(9) = zs3;
full_x(10) = zp3;
full_x(11) = k_h3;
full_x(12) = planets_count_2;
full_x(13) = planets_count_3;
full_x(14) = pressure_angle;
full_x(15) = helix_angle_1;
full_x(16) = helix_angle_2;
full_x(17) = helix_angle_3;
full_x(18) = x1;
full_x(19) = x2;
full_x(20) = profile_shifts(3); % xs2
full_x(21) = profile_shifts(4); % xp2
full_x(22) = profile_shifts(5); % xs3
full_x(23) = profile_shifts(6); % xp3
full_x(24) = pressure_angle_choice;
full_x(25) = k_h1;

% 检查是否应该显示详细调试信息
debug_output = false;
try
    % 尝试从主工作区获取debug_output标志
    debug_output = evalin('base', 'debug_output');
catch
    % 如果不存在，保持为false
    debug_output = false;
end

% 添加调试输出，显示使用的一级参数
if debug_output
    fprintf('DEBUG: OptimizeWithFixedFirstStage 使用的一级参数: 模数=%.1f, z1=%d, z2=%d, k_h1=%.3f\n', m1, z1, z2, k_h1);
end

% 调用原始的CostFunctionWrapper进行评估
center_distance = extractScalarField(first_stage_params, 'a', 400);
try
    [objectives, constraints] = CostFunctionWrapper(full_x, input_power, input_speed, output_speed, ...
                                           service_life, contact_safety_factor, bending_safety_factor, ...
                                           gear_material, gear_materials, quality_grade, center_distance);
catch e
    fprintf('计算目标函数时出错: %s\n', e.message);
    % 返回无效的结果而不是抛出错误
    objectives = [Inf, 0, 0];
    constraints = Inf;
end

% 辅助函数：从结构体中安全提取标量字段
function value = extractScalarField(struct_data, field_name, default_value)
    if isfield(struct_data, field_name)
        value = struct_data.(field_name);
        
        % 确保值是标量
        if ~isscalar(value)
            fprintf('警告：字段 "%s" 不是标量，使用第一个元素\n', field_name);
            value = value(1);
        end
        
        % 检查是否为有效数值
        if isnan(value) || isinf(value)
            fprintf('警告：字段 "%s" 的值无效 (%s)，使用默认值 %g\n', field_name, num2str(value), default_value);
            value = default_value;
        end
    else
        fprintf('警告：一级参数结构体中缺少字段 "%s"，使用默认值 %g\n', field_name, default_value);
        value = default_value;
    end
end

end 

function [best_objectives, best_constraints, best_solution, best_first_stage_idx] = OptimizeWithMultipleFirstStages(x, first_stage_params_array, input_power, input_speed, output_speed, ...
                                             service_life, contact_safety_factor, bending_safety_factor, gear_material, gear_materials, quality_grade)
% OptimizeWithMultipleFirstStages 使用多组一级平行轴系参数进行优化，自动选择最佳结果
%
%   输入参数:
%   - x: 优化变量 [mn2, zs2, zp2, k_h2, mn3, zs3, zp3, k_h3, planets_count_2, planets_count_3, 
%                  pressure_angle, helix_angle_2, helix_angle_3, xs2, xp2, xs3, xp3, pressure_angle_3_choice]
%   - first_stage_params_array: 一级平行轴系固定参数数组，每个元素包含以下字段:
%       - m: 模数 (mm)
%       - z1: 小齿轮齿数
%       - z2: 大齿轮齿数
%       - beta: 螺旋角 (度)
%       - alpha: 压力角 (度)
%       - x1: 小齿轮变位系数
%       - x2: 大齿轮变位系数
%       - k_h: 齿宽系数
%       - b: 齿宽 (mm)
%       - a: 中心距 (mm)
%   - input_power: 输入功率 (kW)
%   - input_speed: 输入转速 (rpm)
%   - output_speed: 输出转速 (rpm)
%   - service_life: 设计寿命 (h)
%   - contact_safety_factor: 接触安全系数要求
%   - bending_safety_factor: 弯曲安全系数要求
%   - gear_material: 齿轮材料参数
%   - gear_materials: 详细的齿轮材料参数
%   - quality_grade: 齿轮精度等级
%
%   输出:
%   - best_objectives: 最佳优化目标值 [总质量, 最小弯曲安全系数, 最小接触安全系数]
%   - best_constraints: 最佳约束违反程度
%   - best_solution: 最佳解的完整参数
%   - best_first_stage_idx: 最佳一级参数的索引

% 初始化最佳结果
best_objectives = [Inf, 0, 0]; % 初始化为最差情况
best_constraints = Inf;
best_solution = [];
best_first_stage_idx = 0;

% 记录所有可行解
feasible_solutions = struct('objectives', {}, 'constraints', {}, 'solution', {}, 'first_stage_idx', {});
feasible_count = 0;

% 遍历每组一级参数
num_params = length(first_stage_params_array);
fprintf('评估 %d 组一级参数...\n', num_params);

for i = 1:num_params
    % 获取当前一级参数
    current_params = first_stage_params_array(i);
    
    % 使用当前一级参数进行优化
    [objectives, constraints] = OptimizeWithFixedFirstStage(x, current_params, input_power, input_speed, output_speed, ...
                                                 service_life, contact_safety_factor, bending_safety_factor, ...
                                                 gear_material, gear_materials, quality_grade);
    
    % 构建完整解
    full_solution = struct('first_stage', current_params, 'second_stage_third_stage', x, 'objectives', objectives);
    
    % 检查是否为可行解（约束违反程度为0）
    if constraints <= 0
        % 记录可行解
        feasible_count = feasible_count + 1;
        feasible_solutions(feasible_count).objectives = objectives;
        feasible_solutions(feasible_count).constraints = constraints;
        feasible_solutions(feasible_count).solution = full_solution;
        feasible_solutions(feasible_count).first_stage_idx = i;
        
        % 检查是否为最佳解（按总质量比较）
        if objectives(1) < best_objectives(1)
            best_objectives = objectives;
            best_constraints = constraints;
            best_solution = full_solution;
            best_first_stage_idx = i;
        end
    elseif best_constraints > 0 && constraints < best_constraints
        % 如果还没有找到可行解，则记录约束违反程度最小的解
        best_objectives = objectives;
        best_constraints = constraints;
        best_solution = full_solution;
        best_first_stage_idx = i;
    end
    
    % 显示进度
    if mod(i, 5) == 0 || i == num_params
        fprintf('已评估 %d/%d 组参数，找到 %d 个可行解\n', i, num_params, feasible_count);
    end
end

% 如果找到了可行解，输出最佳结果
if feasible_count > 0
    fprintf('\n找到 %d 个可行解，最佳解使用第 %d 组一级参数\n', feasible_count, best_first_stage_idx);
    fprintf('最佳解的总质量: %.2f kg\n', best_objectives(1));
    fprintf('最佳解的最小弯曲安全系数: %.3f\n', best_objectives(2));
    fprintf('最佳解的最小接触安全系数: %.3f\n', best_objectives(3));
else
    fprintf('\n未找到可行解，返回约束违反程度最小的解（第 %d 组一级参数）\n', best_first_stage_idx);
    fprintf('约束违反程度: %.6f\n', best_constraints);
end

end 