% 调试NSGA-II算法
clear; clc;

% 添加路径
addpath('Config');
addpath('Model');
addpath('Algorithms/NSGA-II');

% 设置简单的测试问题
problem = struct();
problem.nVar = 19;
problem.nObj = 3;
problem.varMin = ones(1, 19);
problem.varMax = 10 * ones(1, 19);
problem.varSize = [1, 19];
problem.costFunction = @(x) [sum(x), sum(x.^2), sum(abs(x))];
problem.FE = 0;

% 设置算法参数
params = struct();
params.nPop = 10;  % 使用小种群便于调试
params.maxIt = 5;  % 使用少量迭代便于调试
params.pCrossover = 0.8;
params.pMutation = 0.1;

fprintf('=== 调试NSGA-II算法 ===\n');
fprintf('问题参数:\n');
fprintf('  nVar: %d\n', problem.nVar);
fprintf('  nObj: %d\n', problem.nObj);
fprintf('  varMin: %s\n', mat2str(problem.varMin));
fprintf('  varMax: %s\n', mat2str(problem.varMax));
fprintf('  varSize: %s\n', mat2str(problem.varSize));

fprintf('\n算法参数:\n');
fprintf('  种群大小: %d\n', params.nPop);
fprintf('  最大迭代: %d\n', params.maxIt);

try
    fprintf('\n开始运行NSGA-II...\n');
    [population, objectives] = RunNSGAII(problem, params);
    
    fprintf('✓ NSGA-II运行成功！\n');
    fprintf('返回结果:\n');
    fprintf('  population 维度: [%d, %d]\n', size(population, 1), size(population, 2));
    fprintf('  objectives 维度: [%d, %d]\n', size(objectives, 1), size(objectives, 2));
    
catch e
    fprintf('✗ NSGA-II运行失败: %s\n', e.message);
    fprintf('错误位置: %s (第 %d 行)\n', e.stack(1).name, e.stack(1).line);
    
    % 显示完整的错误堆栈
    fprintf('\n完整错误堆栈:\n');
    for i = 1:length(e.stack)
        fprintf('  %d. %s (第 %d 行)\n', i, e.stack(i).name, e.stack(i).line);
    end
end

fprintf('\n调试完成\n');
