% Multi-Objective Optimization Algorithms Comparison
% This script compares various MOO algorithms on benchmark problems

%% Parameters
clear;
clc;
close all;

% Problem parameters
nVar = 10;                % Number of decision variables
VarMin = 0;               % Lower bound of variables
VarMax = 1;               % Upper bound of variables
MaxIt = 100;              % Maximum number of iterations
nPop = 100;               % Population size
nRuns = 5;                % Number of independent runs

% Test problems
test_problems = {'ZDT1', 'ZDT2', 'ZDT3'};
nProblems = length(test_problems);

% Algorithms to compare
algorithms = {'NSGA2', 'NSGA3', 'MODA', 'MODE', 'MOEAD', 'MOGOA', 'MOGWO', 'MOMVO', 'MOPSO'};
nAlgs = length(algorithms);

% Create result storage
results = struct();

% Define metrics
metrics = {'GD', 'IGD', 'Spacing', 'Spread', 'HV'};
nMetrics = length(metrics);

% Initialize results structure
for p = 1:nProblems
    results.(test_problems{p}) = struct();
    for a = 1:nAlgs
        results.(test_problems{p}).(algorithms{a}) = struct();
        for m = 1:nMetrics
            results.(test_problems{p}).(algorithms{a}).(metrics{m}) = zeros(nRuns, 1);
        end
        results.(test_problems{p}).(algorithms{a}).Time = zeros(nRuns, 1);
        results.(test_problems{p}).(algorithms{a}).BestF = cell(nRuns, 1);
        results.(test_problems{p}).(algorithms{a}).BestX = cell(nRuns, 1);
    end
end

%% Run experiments
for p = 1:nProblems
    problem = test_problems{p};
    disp(['Problem: ' problem]);
    
    % Create cost function handle
    CostFunction = @(x) TestProblems(problem, x);
    
    % Get true Pareto front for reference
    [~, PF] = TestProblems(problem, []);
    
    for a = 1:nAlgs
        alg = algorithms{a};
        disp(['  Algorithm: ' alg]);
        
        for run = 1:nRuns
            disp(['    Run: ' num2str(run) '/' num2str(nRuns)]);
            
            % Run algorithm
            tic;
            switch alg
                case 'NSGA2'
                    [pop, F1] = NSGA2(CostFunction, nVar, VarMin, VarMax, MaxIt, nPop);
                    % Extract solution and objective values
                    X = zeros(length(F1), nVar);
                    F = zeros(length(F1), size(F1(1).Cost, 2));
                    for i = 1:length(F1)
                        X(i, :) = F1(i).Position;
                        F(i, :) = F1(i).Cost;
                    end
                
                case 'MODA'
                    [X, F] = MODA(CostFunction, nVar, VarMin, VarMax, MaxIt, nPop);
                
                case 'NSGA3'
                    [pop, F1] = NSGA3(CostFunction, nVar, VarMin, VarMax, MaxIt, nPop);
                    % Extract solution and objective values
                    X = zeros(length(F1), nVar);
                    F = zeros(length(F1), size(F1(1).Cost, 2));
                    for i = 1:length(F1)
                        X(i, :) = F1(i).Position;
                        F(i, :) = F1(i).Cost;
                    end
                
                case 'MODE'
                    OUT = MODE(CostFunction, nVar, VarMin, VarMax, MaxIt, nPop);
                    X = OUT.PSet;
                    F = OUT.PFront;
                
                case 'MOEAD'
                    EP = MOEAD(CostFunction, nVar, VarMin, VarMax, MaxIt, nPop);
                    X = zeros(length(EP), nVar);
                    F = zeros(length(EP), size(EP(1).Cost, 2));
                    for i = 1:length(EP)
                        X(i, :) = EP(i).Position;
                        F(i, :) = EP(i).Cost;
                    end
                
                case 'MOGOA'
                    [X, F] = MOGOA(CostFunction, nVar, VarMin, VarMax, MaxIt, nPop);
                
                case 'MOGWO'
                    [X, F] = MOGWO(CostFunction, nVar, VarMin, VarMax, MaxIt, nPop);
                
                case 'MOMVO'
                    [X, F] = MOMVO(CostFunction, nVar, VarMin, VarMax, MaxIt, nPop);
                
                case 'MOPSO'
                    [X, F] = MOPSO(CostFunction, nVar, VarMin, VarMax, MaxIt, nPop);
                
                otherwise
                    error(['Unknown algorithm: ' alg]);
            end
            time = toc;
            
            % Store results
            results.(problem).(alg).Time(run) = time;
            results.(problem).(alg).BestF{run} = F;
            results.(problem).(alg).BestX{run} = X;
            
            % Calculate metrics
            results.(problem).(alg).GD(run) = GD(F, PF);
            results.(problem).(alg).IGD(run) = IGD(F, PF);
            results.(problem).(alg).Spacing(run) = Spacing(F, PF);
            results.(problem).(alg).Spread(run) = Spread(F, PF);
            results.(problem).(alg).HV(run) = HV(F, PF);
        end
    end
end

%% Display results
disp('Results Summary:');
for p = 1:nProblems
    problem = test_problems{p};
    disp(['Problem: ' problem]);
    
    % Prepare table data
    tableData = cell(nAlgs, nMetrics+1);
    rowNames = cell(nAlgs, 1);
    
    for a = 1:nAlgs
        alg = algorithms{a};
        rowNames{a} = alg;
        
        % Average execution time
        tableData{a, 1} = mean(results.(problem).(alg).Time);
        
        % Average metrics
        for m = 1:nMetrics
            metric = metrics{m};
            tableData{a, m+1} = mean(results.(problem).(alg).(metric));
        end
    end
    
    % Create and display table
    colNames = ['Time', metrics];
    T = cell2table(tableData, 'RowNames', rowNames, 'VariableNames', colNames);
    disp(T);
    
    % Plot Pareto fronts
    figure;
    hold on;
    
    % Plot true Pareto front
    if ~isempty(PF)
        plot(PF(:, 1), PF(:, 2), 'k-', 'LineWidth', 2);
    end
    
    % Plot obtained fronts from last run
    colors = {'b', 'r', 'g', 'm', 'c', 'y'};
    for a = 1:nAlgs
        alg = algorithms{a};
        F = results.(problem).(alg).BestF{end};
        plot(F(:, 1), F(:, 2), [colors{mod(a-1, length(colors))+1} 'o'], 'MarkerSize', 4);
    end
    
    legend(['True PF', algorithms]);
    title(['Pareto Front - ' problem]);
    xlabel('f_1');
    ylabel('f_2');
    grid on;
    hold off;
end

% Save results
save('MOO_Results.mat', 'results');
disp('Results saved to MOO_Results.mat'); 