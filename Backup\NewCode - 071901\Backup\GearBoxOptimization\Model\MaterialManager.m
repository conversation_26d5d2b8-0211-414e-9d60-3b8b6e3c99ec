function [materials, gear_materials] = MaterialManager(material_name)
% MaterialManager 统一管理齿轮材料属性
%   该函数根据材料名称返回标准化的材料属性结构体
%
%   输入参数:
%   - material_name: 材料名称，如'17CrNiMo6'、'20CrNi2MoA'、'42CrMoA'等
%                   如果为空，则返回所有材料组合的结构体
%
%   输出:
%   - materials: 包含材料属性的结构体
%   - gear_materials: (可选) 综合齿轮系统材料结构体，适用于优化计算
%
%   材料属性包括:
%   - name: 材料名称
%   - density: 密度 (kg/m^3)
%   - youngs_modulus: 杨氏模量 (Pa)
%   - poissons_ratio: 泊松比
%   - yield_strength: 屈服强度 (Pa)
%   - tensile_strength: 拉伸强度 (Pa)
%   - bending_strength: 弯曲疲劳极限 (Pa)
%   - contact_strength: 接触疲劳极限 (Pa)
%   - HB: 布氏硬度 (可选)

% 创建所有材料的属性
material_17CrNiMo6 = struct('name', '17CrNiMo6', ...
                            'density', 7850, ...
                            'youngs_modulus', 206e9, ...
                            'poissons_ratio', 0.3, ...
                            'yield_strength', 785e6, ...
                            'tensile_strength', 1080e6, ...
                            'bending_strength', 430e6, ...
                            'contact_strength', 1500e6, ...
                            'HB', 300);

material_20CrNi2MoA = struct('name', '20CrNi2MoA', ...
                             'density', 7870, ...
                             'youngs_modulus', 210e9, ...
                             'poissons_ratio', 0.275, ...
                             'yield_strength', 785e6, ...
                             'tensile_strength', 980e6, ...
                             'bending_strength', 410e6, ...  % 从表中提取，410MPa
                             'contact_strength', 1400e6, ... % 从表中提取，1400MPa
                             'HB', 300);

material_42CrMoA = struct('name', '42CrMoA', ...
                          'density', 7800, ...
                          'youngs_modulus', 200e9, ...
                          'poissons_ratio', 0.3, ...
                          'yield_strength', 930e6, ...
                          'tensile_strength', 1080e6, ...
                          'bending_strength', 182e6, ...
                          'contact_strength', 490e6, ...
                          'HB', 320);

% 如果指定了材料名称，返回该材料的属性
if nargin > 0 && ~isempty(material_name)
    % 默认值
    materials = struct('name', material_name, ...
                      'density', 7850, ...
                      'youngs_modulus', 206e9, ...
                      'poissons_ratio', 0.3, ...
                      'yield_strength', 785e6, ...
                      'tensile_strength', 1080e6, ...
                      'bending_strength', 430e6, ...
                      'contact_strength', 1500e6, ...
                      'HB', 300);
    
    % 根据材料名称设置特定属性
    switch upper(material_name)
        case '17CRNIMO6'
            materials = material_17CrNiMo6;
        case '20CRNI2MOA'
            materials = material_20CrNi2MoA;
        case '42CRMOA'
            materials = material_42CrMoA;
        otherwise
            warning('未知材料名称: %s，使用默认值', material_name);
    end
    
    % 创建ISO 6336计算所需的材料参数格式
    materials.iso_params = struct('E', materials.youngs_modulus, ...
                                'poisson', materials.poissons_ratio, ...
                                'HB', materials.HB, ...
                                'sigmaFlim', materials.bending_strength, ...
                                'sigmaHlim', materials.contact_strength, ...
                                'yield_strength', materials.yield_strength, ...
                                'tensile_strength', materials.tensile_strength);
    
    % 如果需要第二个输出参数，创建综合齿轮材料结构体
    if nargout > 1
        % 创建三级减速机齿轮传动系统的标准材料配置
        gear_materials = getStandardGearMaterials();
    end
else
    % 如果没有指定材料名称，返回所有材料和标准齿轮系统材料结构体
    materials = struct('material_17CrNiMo6', material_17CrNiMo6, ...
                      'material_20CrNi2MoA', material_20CrNi2MoA, ...
                      'material_42CrMoA', material_42CrMoA);
    
    % 创建三级减速机齿轮传动系统的标准材料配置
    gear_materials = getStandardGearMaterials();
end

% 添加ISO参数
if ~isfield(materials, 'iso_params') && isstruct(materials) && isfield(materials, 'name')
    materials.iso_params = struct('E', materials.youngs_modulus, ...
                                'poisson', materials.poissons_ratio, ...
                                'HB', materials.HB, ...
                                'sigmaFlim', materials.bending_strength, ...
                                'sigmaHlim', materials.contact_strength, ...
                                'yield_strength', materials.yield_strength, ...
                                'tensile_strength', materials.tensile_strength);
end
end

function gear_materials = getStandardGearMaterials()
% 获取标准齿轮系统材料结构体
% 平行轴齿轮材料 - 17CrNiMo6
parallel_gear_material = struct('name', '17CrNiMo6', ...
                               'density', 7850,  ... % kg/m^3
                               'youngs_modulus', 206e9, ... % Pa
                               'poissons_ratio', 0.3, ...
                               'yield_strength', 785e6, ... % Pa，屈服强度
                               'tensile_strength', 1080e6, ... % Pa，拉伸强度
                               'bending_strength', 430e6, ... % Pa，弯曲疲劳极限
                               'contact_strength', 1500e6); % Pa，接触疲劳极限

% 一级行星系材料
planet1_sun_material = struct('name', '17CrNiMo6', ...
                             'density', 7850,  ... % kg/m^3
                             'youngs_modulus', 206e9, ... % Pa
                             'poissons_ratio', 0.3, ...
                             'yield_strength', 785e6, ... % Pa
                             'tensile_strength', 1080e6, ... % Pa
                             'bending_strength', 430e6, ... % Pa，弯曲疲劳极限
                             'contact_strength', 1500e6); % Pa，接触疲劳极限

planet1_planet_material = struct('name', '20CrNi2MoA', ...
                                'density', 7870,  ... % kg/m^3
                                'youngs_modulus', 210e9, ... % Pa
                                'poissons_ratio', 0.275, ...
                                'yield_strength', 785e6, ... % Pa
                                'tensile_strength', 980e6, ... % Pa
                                'bending_strength', 410e6, ... % Pa，弯曲疲劳极限
                                'contact_strength', 1400e6); % Pa，接触疲劳极限

planet1_ring_material = struct('name', '42CrMoA', ...
                              'density', 7800,  ... % kg/m^3
                              'youngs_modulus', 200e9, ... % Pa
                              'poissons_ratio', 0.3, ...
                              'yield_strength', 930e6, ... % Pa
                              'tensile_strength', 1080e6, ... % Pa
                              'bending_strength', 182e6, ... % Pa
                              'contact_strength', 490e6); % Pa

% 二级行星系材料
planet2_sun_material = struct('name', '17CrNiMo6', ...
                             'density', 7850,  ... % kg/m^3
                             'youngs_modulus', 206e9, ... % Pa
                             'poissons_ratio', 0.3, ...
                             'yield_strength', 785e6, ... % Pa
                             'tensile_strength', 1080e6, ... % Pa
                             'bending_strength', 430e6, ... % Pa，弯曲疲劳极限
                             'contact_strength', 1500e6); % Pa，接触疲劳极限

planet2_planet_material = struct('name', '20CrNi2MoA', ...
                                'density', 7870,  ... % kg/m^3
                                'youngs_modulus', 210e9, ... % Pa
                                'poissons_ratio', 0.275, ...
                                'yield_strength', 785e6, ... % Pa
                                'tensile_strength', 980e6, ... % Pa
                                'bending_strength', 410e6, ... % Pa，弯曲疲劳极限
                                'contact_strength', 1400e6); % Pa，接触疲劳极限

planet2_ring_material = struct('name', '42CrMoA', ...
                              'density', 7800,  ... % kg/m^3
                              'youngs_modulus', 200e9, ... % Pa
                              'poissons_ratio', 0.3, ...
                              'yield_strength', 930e6, ... % Pa
                              'tensile_strength', 1080e6, ... % Pa
                              'bending_strength', 182e6, ... % Pa
                              'contact_strength', 490e6); % Pa

% 创建综合材料结构体，包含所有齿轮的材料参数
gear_materials = struct('parallel', parallel_gear_material, ...
                       'planet1_sun', planet1_sun_material, ...
                       'planet1_planet', planet1_planet_material, ...
                       'planet1_ring', planet1_ring_material, ...
                       'planet2_sun', planet2_sun_material, ...
                       'planet2_planet', planet2_planet_material, ...
                       'planet2_ring', planet2_ring_material);
end 