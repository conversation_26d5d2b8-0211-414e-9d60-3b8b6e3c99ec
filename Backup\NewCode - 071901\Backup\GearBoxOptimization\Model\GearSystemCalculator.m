function [gear_system, objectives, constraints] = GearSystemCalculator(optimization_vars, system_params)
% GearSystemCalculator 统一的齿轮系统计算器
% 基于优化变量计算完整的齿轮系统参数，保持原有计算公式不变
%
% 输入:
%   optimization_vars - 优化变量向量或结构体
%   system_params - 系统参数（载荷、材料、约束等）
%
% 输出:
%   gear_system - 完整的齿轮系统参数结构体
%   objectives - 目标函数值 [总质量, -最小弯曲安全系数, -最小接触安全系数]
%   constraints - 约束违反值

%% 1. 参数解析和初始化
param_def = GearParameterDefinition();

% 解析优化变量
if isstruct(optimization_vars)
    gear_vars = optimization_vars;
else
    % 将向量转换为结构体
    gear_vars = struct();
    for i = 1:length(param_def.optimization_variables)
        if i <= length(optimization_vars)
            gear_vars.(param_def.optimization_variables{i}) = optimization_vars(i);
        end
    end
end

% 初始化齿轮系统结构体
gear_system = struct();

% 复制优化变量到齿轮系统
for i = 1:length(param_def.optimization_variables)
    var_name = param_def.optimization_variables{i};
    if isfield(gear_vars, var_name)
        gear_system.(var_name) = gear_vars.(var_name);
    end
end

%% 2. 设置系统参数默认值
if nargin < 2 || isempty(system_params)
    system_params = struct();
end

% 载荷参数
system_params = setDefaultField(system_params, 'input_power', 1170.24);      % 输入功率 (kW)
system_params = setDefaultField(system_params, 'input_speed', 1500);         % 输入转速 (rpm)
system_params = setDefaultField(system_params, 'output_speed', 18.75);       % 输出转速 (rpm)
system_params = setDefaultField(system_params, 'service_life', 20000);       % 设计寿命 (h)

% 安全系数要求
system_params = setDefaultField(system_params, 'contact_safety_factor', 1.2);
system_params = setDefaultField(system_params, 'bending_safety_factor', 1.5);

% 材料参数
system_params = setDefaultField(system_params, 'gear_materials', struct());
system_params = setDefaultField(system_params, 'quality_grade', 6);

% 约束参数
system_params = setDefaultField(system_params, 'center_distance', 400);      % 一级中心距约束 (mm)

%% 3. 计算几何属性（保持原有计算公式）
gear_system = calculateGeometricProperties(gear_system, system_params);

%% 4. 调用原有的GearOptObjectives进行完整计算
try
    % 构建与原有接口兼容的参数向量
    x_vector = buildCompatibleVector(gear_system, param_def);
    
    % 调用原有的计算函数（保持所有原有计算公式不变）
    [obj_values, constraint_values, is_valid] = GearOptObjectives(x_vector, ...
        system_params.input_power, system_params.input_speed, system_params.output_speed, ...
        system_params.service_life, system_params.contact_safety_factor, system_params.bending_safety_factor, ...
        system_params.gear_materials, gear_system.n2, gear_system.n3, system_params.gear_materials, ...
        system_params.quality_grade, 20, gear_system.beta1, gear_system.beta2, gear_system.beta3, ...
        [gear_system.x1, gear_system.x2, gear_system.xs2, gear_system.xp2, gear_system.xs3, gear_system.xp3], ...
        20, system_params.center_distance, gear_system.k_h1);
    
    % 提取计算结果
    gear_system = extractCalculationResults(gear_system);
    
    % 设置目标函数值
    objectives = obj_values;
    constraints = constraint_values;
    
catch e
    fprintf('计算过程中出错: %s\n', e.message);
    % 返回无效结果
    objectives = [Inf, 0, 0];
    constraints = Inf;
    gear_system.is_valid = false;
end

%% 5. 计算汇总指标
gear_system = calculateSummaryIndicators(gear_system, system_params);

end

%% 辅助函数

function s = setDefaultField(s, field, default_value)
% 设置结构体字段的默认值
if ~isfield(s, field) || isempty(s.(field))
    s.(field) = default_value;
end
end

function gear_system = calculateGeometricProperties(gear_system, system_params)
% 计算几何属性（使用原有的计算公式）

% 一级几何属性
gear_system.alpha1 = 20;  % 一级压力角（度）
gear_system.x_sum1 = gear_system.x1 + gear_system.x2;  % 综合变位系数

% 一级中心距计算（保持原有公式 - 考虑变位系数）
% 这里调用原有的中心距计算方法
if gear_system.beta1 == 0
    % 直齿轮情况
    inv_alpha = tan(gear_system.alpha1 * pi / 180) - (gear_system.alpha1 * pi / 180);
    inv_alpha_w = inv_alpha + 2 * gear_system.x_sum1 * tan(gear_system.alpha1 * pi / 180) / (gear_system.z1 + gear_system.z2);
    alpha_w = fzero(@(a) tan(a) - a - inv_alpha_w, gear_system.alpha1 * pi / 180);
    gear_system.a1 = gear_system.m1 * (gear_system.z1 + gear_system.z2) * cos(gear_system.alpha1 * pi / 180) / (2 * cos(alpha_w));
else
    % 斜齿轮情况
    mt1 = gear_system.m1 / cos(gear_system.beta1 * pi / 180);
    inv_alpha_t = tan(gear_system.alpha1 * pi / 180) - (gear_system.alpha1 * pi / 180);
    inv_alpha_tw = inv_alpha_t + 2 * gear_system.x_sum1 * tan(gear_system.alpha1 * pi / 180) / (gear_system.z1 + gear_system.z2);
    alpha_tw = fzero(@(a) tan(a) - a - inv_alpha_tw, gear_system.alpha1 * pi / 180);
    gear_system.a1 = mt1 * (gear_system.z1 + gear_system.z2) * cos(gear_system.alpha1 * pi / 180) / (2 * cos(alpha_tw));
end

% 一级传动比
gear_system.i1 = gear_system.z2 / gear_system.z1;

% 二级几何属性
gear_system.zr2 = gear_system.zs2 + 2 * gear_system.zp2;  % 内齿圈齿数
gear_system.alpha2 = 20;  % 二级压力角（度）

% 二级内齿圈变位系数计算（保持原有计算方法）
% 这里需要调用原有的变位系数计算函数
try
    gear_params_2 = struct('z1', gear_system.zs2, 'z2', gear_system.zp2, 'zr', gear_system.zr2, ...
                          'alpha', gear_system.alpha2, 'beta', gear_system.beta2, 'module', gear_system.mn2, ...
                          'is_planetary', true, 'is_internal', true);
    [~, optimal_shifts_2] = CalculatePlanetaryShiftCoefficients(gear_params_2);
    gear_system.xr2 = optimal_shifts_2.xr;
catch
    gear_system.xr2 = -(gear_system.xs2 + gear_system.xp2);  % 简化计算
end

gear_system.x_sum2 = gear_system.xs2 + gear_system.xp2 + gear_system.xr2;
gear_system.a2 = gear_system.mn2 * (gear_system.zs2 + gear_system.zp2) / 2;  % 二级中心距
gear_system.i2 = 1 + gear_system.zr2 / gear_system.zs2;  % 二级传动比

% 三级几何属性
gear_system.zr3 = gear_system.zs3 + 2 * gear_system.zp3;  % 内齿圈齿数
gear_system.alpha3 = 20;  % 三级压力角（度）

% 三级内齿圈变位系数计算（保持原有计算方法）
try
    gear_params_3 = struct('z1', gear_system.zs3, 'z2', gear_system.zp3, 'zr', gear_system.zr3, ...
                          'alpha', gear_system.alpha3, 'beta', gear_system.beta3, 'module', gear_system.mn3, ...
                          'is_planetary', true, 'is_internal', true);
    [~, optimal_shifts_3] = CalculatePlanetaryShiftCoefficients(gear_params_3);
    gear_system.xr3 = optimal_shifts_3.xr;
catch
    gear_system.xr3 = -(gear_system.xs3 + gear_system.xp3);  % 简化计算
end

gear_system.x_sum3 = gear_system.xs3 + gear_system.xp3 + gear_system.xr3;
gear_system.a3 = gear_system.mn3 * (gear_system.zs3 + gear_system.zp3) / 2;  % 三级中心距
gear_system.i3 = 1 + gear_system.zr3 / gear_system.zs3;  % 三级传动比

end

function x_vector = buildCompatibleVector(gear_system, param_def)
% 构建与原有GearOptObjectives兼容的参数向量
% 这个函数确保新的结构化参数能够与原有的向量接口兼容

x_vector = zeros(1, length(param_def.optimization_variables));
for i = 1:length(param_def.optimization_variables)
    var_name = param_def.optimization_variables{i};
    if isfield(gear_system, var_name)
        x_vector(i) = gear_system.(var_name);
    end
end

end

function gear_system = extractCalculationResults(gear_system)
% 从全局变量中提取计算结果（保持与原有代码的兼容性）

try
    % 提取质量计算结果
    gear_system.M1 = evalin('base', 'M1_gear1_mass');
    gear_system.M2 = evalin('base', 'M2_gear2_mass');
    gear_system.Ms2 = evalin('base', 'Ms2_sun2_mass');
    gear_system.Mp2 = evalin('base', 'Mp2_planet2_mass');
    gear_system.Mr2 = evalin('base', 'Mr2_ring2_mass');
    gear_system.Ms3 = evalin('base', 'Ms3_sun3_mass');
    gear_system.Mp3 = evalin('base', 'Mp3_planet3_mass');
    gear_system.Mr3 = evalin('base', 'Mr3_ring3_mass');
    
    % 提取安全系数计算结果（如果有的话）
    % 这里可以添加更多的结果提取逻辑
    
catch e
    fprintf('提取计算结果时出错: %s\n', e.message);
    % 设置默认值
    gear_system.M1 = 0; gear_system.M2 = 0;
    gear_system.Ms2 = 0; gear_system.Mp2 = 0; gear_system.Mr2 = 0;
    gear_system.Ms3 = 0; gear_system.Mp3 = 0; gear_system.Mr3 = 0;
end

end

function gear_system = calculateSummaryIndicators(gear_system, system_params)
% 计算汇总指标

% 总质量（考虑行星轮数量）
gear_system.TotalMass = gear_system.M1 + gear_system.M2 + gear_system.Ms2 + ...
                       (gear_system.Mp2 * gear_system.n2) + gear_system.Mr2 + ...
                       gear_system.Ms3 + (gear_system.Mp3 * gear_system.n3) + gear_system.Mr3;

% 总传动比
gear_system.TotalRatio = gear_system.i1 * gear_system.i2 * gear_system.i3;

% 传动比误差
target_ratio = system_params.input_speed / system_params.output_speed;
gear_system.Error = abs(gear_system.TotalRatio - target_ratio) / target_ratio * 100;

% 最小安全系数（需要从安全系数计算结果中提取）
% 这里需要根据实际的安全系数计算结果来设置
gear_system.SH = 1.0;  % 临时值，需要从实际计算中获取
gear_system.SF = 1.0;  % 临时值，需要从实际计算中获取

end
