
%% 三级减速机齿轮传动系统多目标优化主程序
% 一级为平行轴，二、三级为行星轮系
% 目标：1. 系统轻量化（总质量最小）
%       2. 齿轮弯曲应力安全系数最大 
%       3. 齿轮接触应力安全系数最大
% 作者：AI辅助工程师
% 日期：2025-06-18


clc;
clear;
close all;

% 设置调试输出控制
debug_output = false; % 设置为false以禁用详细的调试输出

% 检查是否使用GUI模式
use_gui = false;
try
    % 检查是否有图形界面支持
    if usejava('desktop') && ~isempty(strfind(mfilename('fullpath'), '_gui'))
        use_gui = true;
        % 创建简单的GUI界面
        fig = figure('Name', '三级减速机齿轮传动系统多目标优化', ...
                    'NumberTitle', 'off', ...
                    'Position', [100, 100, 800, 600]);
        % 这里可以添加更多GUI元素
        % 注意：完整GUI开发需要更多代码，这里只是一个示例
    end
catch
    use_gui = false;
end

%% 清理之前的结果文件
results_dir = 'Results';

% 使用更简单直接的方法处理Results文件夹
fprintf('\n=== 自动清空结果文件夹 ===\n');
try
    % 检查目录是否存在
    if exist(results_dir, 'dir')
        % 使用系统命令删除目录内容
        if ispc % Windows系统
            % 先删除所有文件
            system(['del /Q "' results_dir '\*.*"']);
            % 再删除所有子目录
            system(['for /D %i in ("' results_dir '\*") do rmdir /S /Q "%i"']);
        else % Unix/Linux/Mac系统
            system(['rm -rf "' results_dir '/"*']);
        end
        fprintf('已清空结果文件夹内容: %s\n', results_dir);
    else
        % 创建结果文件夹
        [status, msg] = mkdir(results_dir);
        if status
            fprintf('创建结果文件夹: %s\n', results_dir);
        else
            fprintf('警告: 无法创建结果文件夹 %s: %s\n', results_dir, msg);
        end
    end
catch ME
    fprintf('警告: 处理结果文件夹时出错: %s\n', ME.message);
    % 确保文件夹存在
    if ~exist(results_dir, 'dir')
        mkdir(results_dir);
        fprintf('创建结果文件夹: %s\n', results_dir);
    end
end

% 确保文件夹确实存在
if ~exist(results_dir, 'dir')
    mkdir(results_dir);
    fprintf('重新创建结果文件夹: %s\n', results_dir);
end

%% 添加路径
addpath('Algorithms/NSGA-II');
addpath('Algorithms/NSGA-III');
addpath('Algorithms/SPEA2');
addpath('Algorithms/MOEA-D');
addpath('Algorithms/MOPSO');
addpath('Algorithms/MOEA-D-DE');    % 新增：MOEA/D-DE算法
addpath('Algorithms/MOEA-D-M2M');   % 新增：MOEA/D-M2M算法
addpath('Algorithms/MOGWO');        % 新增：多目标灰狼优化算法
addpath('Algorithms/MOWOA');        % 新增：多目标鲸鱼优化算法
addpath('Metrics');
addpath('Model');
addpath('Visualization');

% 确保所有模型函数都可以被访问
addpath(genpath('Model'));

%% Core Parameter Settings
% Default parameter values
default_input_torque = 7500;    % Input torque (Nm)
default_input_speed = 1490;     % Input speed (rpm)
default_output_speed = 18.63;   % Output speed (rpm)
default_service_life = 50000;   % Design life (h) - Updated to 50000h
default_contact_safety_factor = 1.2;    % Contact safety factor
default_bending_safety_factor = 1.2;    % Bending safety factor
default_center_distance = 400;  % 一级平行轴齿轮系中心距默认值 (mm)

% 水泥辊压机推荐参数值（已考虑保守载荷）
cement_service_life = 50000;    % 水泥辊压机推荐设计寿命 (h)
cement_safety_factor = 1.2;     % 调整后的安全系数（考虑到载荷已保守估计）

% Interactive parameter settings
disp(' ');
disp('=== 减速器核心参数设置 ===');

% Input torque setting
input_torque = input(['输入扭矩 (Nm) (默认: ', num2str(default_input_torque), '): ']);
if isempty(input_torque)
    input_torque = default_input_torque;
end

% Input speed setting
input_speed = input(['输入转速 (rpm) (默认: ', num2str(default_input_speed), '): ']);
if isempty(input_speed)
    input_speed = default_input_speed;
end

% Output speed setting
output_speed = input(['输出转速 (rpm) (默认: ', num2str(default_output_speed), '): ']);
if isempty(output_speed)
    output_speed = default_output_speed;
end

% 一级平行轴齿轮系中心距设置
center_distance = input(['一级平行轴齿轮系中心距 (mm) (默认: ', num2str(default_center_distance), '): ']);
if isempty(center_distance)
    center_distance = default_center_distance;
end

% Service life setting
service_life = input(['设计寿命 (h) (默认: ', num2str(default_service_life), '): ']);
if isempty(service_life)
    service_life = default_service_life;
end

% Contact safety factor setting
contact_safety_factor = input(['接触安全系数 (默认: ', num2str(default_contact_safety_factor), '): ']);
if isempty(contact_safety_factor)
    contact_safety_factor = default_contact_safety_factor;
end

% Bending safety factor setting
bending_safety_factor = input(['弯曲安全系数 (默认: ', num2str(default_bending_safety_factor), '): ']);
if isempty(bending_safety_factor)
    bending_safety_factor = default_bending_safety_factor;
end

% Calculate total ratio
total_ratio = input_speed / output_speed;
fprintf('总传动比: %.3f\n', total_ratio);

% Calculate input power (kW)
input_power = (input_torque * input_speed * 2 * pi / 60) / 1000;
fprintf('输入功率: %.2f kW\n', input_power);

% Calculate output torque (Nm)
output_torque = input_torque * total_ratio; % 不考虑效率损失
fprintf('输出扭矩: %.2f Nm\n', output_torque);

% 启用传动比约束
disp(' ');
disp('=== 传动比约束设置 ===');
fprintf('总传动比: %.3f (必须满足)\n', total_ratio);

% 计算建议的各级传动比
% 计算建议的各级传动比 - 使用立方根分配法
suggested_stage_ratio = total_ratio^(1/3);
fprintf('建议的各级传动比 (立方根分配): %.2f\n', suggested_stage_ratio);

% 启用传动比约束
ratio_constraints = struct();
ratio_constraints.enabled = true;
ratio_constraints.total_ratio = total_ratio;  % 总传动比必须满足
ratio_constraints.tolerance = 0.02;  % 允许2%的误差

% 设置各级传动比范围约束
ratio_constraints.i1_min = 2.5;  % 一级传动比下限
ratio_constraints.i1_max = 3.5;  % 一级传动比上限
ratio_constraints.i2_min = 3.0;  % 二级传动比下限
ratio_constraints.i2_max = 8.0;  % 二级传动比上限
ratio_constraints.i3_min = 3.0;  % 三级传动比下限
ratio_constraints.i3_max = 8.0;  % 三级传动比上限

% 设置三级传动比不大于二级传动比的约束
ratio_constraints.i3_leq_i2 = true;  % i3 <= i2

% 保存传动比约束
save('ratio_constraints.mat', 'ratio_constraints');

fprintf('传动比约束已启用:\n');
fprintf('  - 总传动比: %.3f ± %.1f%%\n', total_ratio, ratio_constraints.tolerance * 100);
fprintf('  - 一级传动比范围: %.1f - %.1f\n', ratio_constraints.i1_min, ratio_constraints.i1_max);
fprintf('  - 二级传动比范围: %.1f - %.1f\n', ratio_constraints.i2_min, ratio_constraints.i2_max);
fprintf('  - 三级传动比范围: %.1f - %.1f\n', ratio_constraints.i3_min, ratio_constraints.i3_max);
fprintf('  - 三级传动比不大于二级传动比\n');

% 显示其他重要参数
disp(' ');
disp('=== 其他重要参数 ===');
fprintf('设计寿命: %d 小时\n', service_life);
fprintf('接触安全系数: %.1f\n', contact_safety_factor);
fprintf('弯曲安全系数: %.1f\n', bending_safety_factor);
disp(' ');

% 材料参数设置
disp('=== 齿轮材料参数 ===');
% 使用更宽的字段宽度和固定宽度格式
fprintf('%-12s %-16s %12s %16s %10s %16s %16s %16s %16s\n', '材料', '用途', '密度(kg/m³)', '弹性模量(MPa)', '泊松比', '屈服强度(MPa)', '拉伸强度(MPa)', '弯曲强度(MPa)', '接触强度(MPa)');
fprintf('%-12s %-16s %12s %16s %10s %16s %16s %16s %16s\n', '------------', '----------------', '------------', '----------------', '----------', '----------------', '----------------', '----------------', '----------------');
fprintf('%-12s %-16s %12d %16d %10.3f %16d %16d %16d %16d\n', '17CrNiMo6', '平行轴/太阳轮', 7850, 206000, 0.300, 785, 1080, 430, 1500);
fprintf('%-12s %-16s %12d %16d %10.3f %16d %16d %16d %16d\n', '20CrNi2MoA', '行星轮', 7870, 210000, 0.275, 785, 980, 410, 1400);
fprintf('%-12s %-16s %12d %16d %10.3f %16d %16d %16d %16d\n', '42CrMoA', '内齿圈', 7800, 200000, 0.300, 930, 1080, 182, 490);
disp(' ');
fprintf('材料使用情况:\n');
fprintf('- 17CrNiMo6: 用于一级平行轴齿轮系、一级和二级太阳轮\n');
fprintf('- 20CrNi2MoA: 用于一级和二级行星轮\n');
fprintf('- 42CrMoA: 用于一级和二级内齿圈\n');
disp(' ');

% 设计参数优化范围设置
% 移除这行显示信息
% disp('=== 优化参数范围设置 ===');
% 移除这行显示信息
% disp('使用默认优化参数范围');

% 齿轮精度等级设置
disp('=== 齿轮精度等级设置 ===');
fprintf('齿轮精度等级 (ISO 1328): ');
quality_grade = input('请输入精度等级 (5-12, 默认6): ');
if isempty(quality_grade) || quality_grade < 5 || quality_grade > 12
    quality_grade = 6;  % 默认6级精度
    fprintf('使用默认精度等级: %d\n', quality_grade);
else
    fprintf('设置精度等级为: %d\n', quality_grade);
end
disp(' ');

%% 添加一级平行轴系参数组合生成选项
disp('=== 一级平行轴系参数生成 ===');
disp('是否需要生成满足中心距和变位系数约束的一级平行轴系参数组合?');
disp('1. 是，生成一级平行轴系参数组合报告');
disp('2. 否，继续进行完整的优化过程');
disp(' ');

gen_first_stage = input('请选择 (默认: 1): ');
if isempty(gen_first_stage)
    gen_first_stage = 1;
end

if gen_first_stage == 1
    % 使用MaterialManager获取一级平行轴齿轮系材料参数
    parallel_gear_material = MaterialManager('17CrNiMo6');
    
    % 调用函数生成一级平行轴系参数组合报告
    fprintf('=== 一级平行轴系参数生成阶段 ===\n');
    GenerateFirstStageGearReport(center_distance, input_power, input_speed, service_life, contact_safety_factor, bending_safety_factor, parallel_gear_material);
    fprintf('一级平行轴系参数组合报告已生成\n');
    
    % 检查是否存在预生成的一级参数文件
    first_stage_params_file = fullfile('Results', '一级平行轴系满足安全系数的参数.mat');
    if ~exist(first_stage_params_file, 'file')
        fprintf('错误：未找到预生成的一级平行轴系参数文件！\n');
        fprintf('请确保已成功生成一级平行轴系参数并保存为MAT文件。\n');
        return;
    end
    
    % 加载预生成的一级参数
    load(first_stage_params_file, 'first_stage_valid_params');
    if isempty(first_stage_valid_params) || height(first_stage_valid_params) == 0
        fprintf('错误：预生成的一级平行轴系参数文件中没有有效参数！\n');
        return;
    end
    
    % 询问用户是否进行三级齿轮系参数优化过程
    fprintf('\n=== 二三级行星轮系优化阶段 ===\n');
    fprintf('将使用预生成的一级平行轴系参数优化二三级行星轮系\n');
    
    % 提前加载聚类信息
    clustered_file_check = fullfile('Results', '一级平行轴系聚类后参数.mat');
    clustered_count = 0;
    if exist(clustered_file_check, 'file')
        clustered_info = load(clustered_file_check);
        if isfield(clustered_info, 'clustered_params')
            clustered_count = height(clustered_info.clustered_params);
        end
    end
    
    fprintf('已找到 %d 组满足安全系数要求的一级平行轴系参数，聚类后得到 %d 组代表性参数\n', height(first_stage_valid_params), clustered_count);
    
    opt_choice = input('是否进行三级齿轮系参数优化过程? (1:是, 0:否, 默认:1): ');
    if isempty(opt_choice)
        opt_choice = 1;
    end
    
    if opt_choice == 0
        fprintf('优化过程已终止，请查看生成的报告\n');
        return;
    end
    
    % 默认使用自动聚类后的所有参数
    fprintf('\n=== 一级参数聚类处理 ===\n');
    addpath('Model');  % 确保可以找到聚类函数
    
    % 执行聚类
    clustered_params = ClusterFirstStageParams(first_stage_valid_params);
    
    % 不限制使用的参数组数量
    selected_params = clustered_params;
    
    % 保存聚类结果到文件
    clustered_file = fullfile(results_dir, '一级平行轴系聚类后参数.csv');
    writetable(clustered_params, clustered_file);
    
    % 保存聚类结果到MAT文件
    clustered_mat = fullfile(results_dir, '一级平行轴系聚类后参数.mat');
    save(clustered_mat, 'clustered_params');
    
    % 显示聚类结果
    fprintf('完成聚类：%d 组原始参数 → %d 组代表性参数\n', height(first_stage_valid_params), height(clustered_params));
    fprintf('聚类结果已保存\n');
    fprintf('将使用这些聚类后的代表性参数进行二三级行星轮系优化\n');
    
    % 多组参数情况
    use_fixed_first_stage = true;
    use_multiple_first_stage = true;
    fixed_first_stage_params_array = cell(1, height(selected_params));
    
    for i = 1:height(selected_params)
        curr_params = selected_params(i,:);
        first_stage_struct = struct();
        first_stage_struct.m = curr_params.('模数(mm)');
        first_stage_struct.z1 = curr_params.('小齿轮齿数');
        first_stage_struct.z2 = curr_params.('大齿轮齿数');
        first_stage_struct.beta = curr_params.('螺旋角(°)');
        first_stage_struct.alpha = curr_params.('压力角(°)');
        first_stage_struct.x1 = curr_params.('小齿轮变位系数');
        first_stage_struct.x2 = curr_params.('大齿轮变位系数');
        first_stage_struct.k_h = curr_params.('齿宽系数');
        first_stage_struct.b = curr_params.('齿宽(mm)');
        first_stage_struct.a = curr_params.('实际中心距(mm)');
        fixed_first_stage_params_array{i} = first_stage_struct;
        
        % 显示当前参数组
        % fprintf('\n一级平行轴系参数组 #%d:\n', i);
        % fprintf('模数: %.1f mm, 小齿轮齿数: %d, 大齿轮齿数: %d\n', first_stage_struct.m, first_stage_struct.z1, first_stage_struct.z2);
        % fprintf('总质量: %.2f kg, 接触安全系数: %.3f\n', curr_params.('总质量(kg)'), curr_params.('接触安全系数'));
    end
    fprintf('\n');
    
    % 修改优化变量范围，只包括二三级行星轮系的参数
    % 原始变量：[m1, z1, z2, mn2, zs2, zp2, k_h2, mn3, zs3, zp3, k_h3, planets_count_2, planets_count_3, 
    %           pressure_angle, helix_angle_1, helix_angle_2, helix_angle_3, x1, x2, xs2, xp2, xs3, xp3, pressure_angle_3_choice, k_h1]
    % 新变量：  [mn2, zs2, zp2, k_h2, mn3, zs3, zp3, k_h3, planets_count_2, planets_count_3, 
    %           pressure_angle, helix_angle_2, helix_angle_3, xs2, xp2, xs3, xp3, pressure_angle_3_choice]
    
    % 设置二三级参数的优化范围
    % 太阳轮和行星轮齿数约束在17-100，内齿圈通过几何关系约束在17-120
    lb_fixed = [5, 17, 17, 0.6, 5, 17, 17, 0.6, 3, 3, 20, 0, 0, 0.3, 0.2, 0.3, 0.2, 0];
    ub_fixed = [20, 100, 100, 1.0, 20, 100, 100, 1.0, 5, 5, 20, 0, 0, 0.6, 0.5, 0.6, 0.5, 1];
    
    fprintf('已设置二三级行星轮系参数的优化范围\n');
    fprintf('优化将使用固定的一级平行轴系参数\n\n');
else
    fprintf('继续进行完整的优化过程...\n\n');
    use_fixed_first_stage = false;
end

% 设计变量使用默认范围
% 完整变量列表：
% [m1, z1, z2, mn2, zs2, zp2, k_h2, mn3, zs3, zp3, k_h3, planets_count_2, planets_count_3, 
%  pressure_angle, helix_angle_1, helix_angle_2, helix_angle_3, x1, x2, xs2, xp2, xs3, xp3, pressure_angle_3_choice, k_h1]

% 变位系数范围更新，根据工程实践经验设置
% x1(小齿轮): 0.3-0.8, x2(大齿轮): 0-0.5
% xs2(二级太阳轮): 0.3-0.6, xp2(二级行星轮): 0.2-0.5
% xs3(三级太阳轮): 0.3-0.6, xp3(三级行星轮): 0.2-0.5

% 为了达到总传动比79.98，需要扩大齿数范围
% 一级传动比目标: 2.5-3.5, 二级传动比目标: 3-8, 三级传动比目标: 3-8
% 按照用户要求：二三级太阳轮行星轮都在17-100之间，内齿圈在17-120之间
default_lb = [7, 17, 50, 5, 17, 17, 0.6, 5, 17, 17, 0.6, 3, 3, 14, 0, 0, 0, 0.3, 0.0, 0.3, 0.2, 0.3, 0.2, 0, 0.28]; % 增大一级大齿轮下限到50
default_ub = [13, 30, 120, 20, 100, 100, 1.0, 20, 100, 100, 1.0, 8, 8, 25, 30, 20, 20, 0.8, 0.5, 0.6, 0.5, 0.6, 0.5, 1, 0.4]; % 太阳轮和行星轮上限设为100

% 设置科学合理的变位系数初始范围，外啮合齿轮变位系数必须为正值
% 变位系数范围设置为科学合理的值，外啮合齿轮变位系数必须为正值以防止根切
default_lb(18:23) = [0.1, 0.1, 0.1, 0.1, 0.1, 0.1]; % 变位系数下限设为0.1（外啮合齿轮变位应为正值）
default_ub(18:23) = [1.0, 1.0, 1.0, 1.0, 1.0, 1.0]; % 变位系数上限设为1.0，避免齿顶过尖

lb = default_lb;
ub = default_ub;

% 根据新的优化前提更新变量范围
% 1. 一级可以是螺旋角（7°-13°）
% 2. 二级和三级行星轮系为直齿轮（β₂ = β₃ = 0°）
% 3. 一级和二级压力角固定为20°
% 4. 三级行星轮系压力角只能是20°或25°（离散选择）

% 更新螺旋角范围
lb(15) = 8;    % 一级螺旋角下限 (8°)
ub(15) = 13;   % 一级螺旋角上限 (13°)
lb(16) = 0;    % 二级螺旋角下限 (0°)
ub(16) = 0;    % 二级螺旋角上限 (0°)
lb(17) = 0;    % 三级螺旋角下限 (0°)
ub(17) = 0;    % 三级螺旋角上限 (0°)

% 更新压力角范围
% 注意：第14个变量原来是表示所有级的压力角，现在表示一级和二级压力角
% 三级压力角将单独使用第24个变量，作为二元离散值
lb(14) = 20;   % 一二级压力角下限 (20°)
ub(14) = 20;   % 一二级压力角上限 (20°)

% 新增第24个变量表示三级压力角（纯离散值）
lb(24) = 0;    % 离散值索引下限
ub(24) = 1;    % 离散值索引上限
% 0表示选择20°，1表示选择25°

% 三级压力角的处理：
% - 由于压力角(第14项)现在固定为20°，
% - 在CostFunctionWrapper.m中，我们会将优化算法生成的值转化为离散选择：
%   * 若变量值≤20，使用20°压力角
%   * 若变量值>20，使用25°压力角
fprintf('注意：三级行星轮系的压力角现在是真正的离散变量 - 仅取值20°或25°\n');

% 更新行星轮数量为离散值
% 将行星轮数量设置为只能取3、4或5这三个离散值
% 变量限制范围不变，但在CostFunctionWrapper.m中进行特殊处理
fprintf('注意：二级和三级行星轮数量现在被设为离散变量 - 仅取值3、4或5\n');

% 螺旋角在Model/CostFunctionWrapper.m中会自动处理为整数值

% 显示所有优化参数的范围
disp('=== 优化参数范围 ===');
fprintf('%-25s %-10s %-10s\n', '参数', '下限', '上限');
fprintf('%-25s %-10.2f %-10.2f\n', '一级模数 (m1)', lb(1), ub(1));
fprintf('注意：一级模数现在只能从以下离散值中选取: 7, 8, 9, 10, 11, 12, 13\n');
fprintf('%-25s %-10d %-10d\n', '一级小齿轮齿数 (z1)', lb(2), ub(2));
fprintf('%-25s %-10d %-10d\n', '一级大齿轮齿数 (z2)', lb(3), ub(3));
fprintf('%-25s %-10.2f %-10.2f\n', '二级模数 (mn2)', lb(4), ub(4));
fprintf('%-25s %-10d %-10d\n', '二级太阳轮齿数 (zs2)', lb(5), ub(5));
fprintf('%-25s %-10d %-10d\n', '二级行星轮齿数 (zp2)', lb(6), ub(6));
fprintf('%-25s %-10.2f %-10.2f\n', '二级齿宽系数 (k_h2)', lb(7), ub(7));
fprintf('%-25s %-10.2f %-10.2f\n', '三级模数 (mn3)', lb(8), ub(8));
fprintf('%-25s %-10d %-10d\n', '三级太阳轮齿数 (zs3)', lb(9), ub(9));
fprintf('%-25s %-10d %-10d\n', '三级行星轮齿数 (zp3)', lb(10), ub(10));
fprintf('%-25s %-10.2f %-10.2f\n', '三级齿宽系数 (k_h3)', lb(11), ub(11));
fprintf('%-25s %-10d %-10d\n', '二级行星轮数量', lb(12), ub(12));
fprintf('%-25s %-10d %-10d\n', '三级行星轮数量', lb(13), ub(13));
fprintf('%-25s %-10.2f %-10.2f\n', '压力角 (度)', lb(14), ub(14));
fprintf('%-25s %-10.2f %-10.2f\n', '一级螺旋角 (度)', lb(15), ub(15));
fprintf('%-25s %-10.2f %-10.2f\n', '二级螺旋角 (度)', lb(16), ub(16));
fprintf('%-25s %-10.2f %-10.2f\n', '三级螺旋角 (度)', lb(17), ub(17));
fprintf('%-25s %-10.2f %-10.2f\n', '一级小齿轮变位系数 (x1)', lb(18), ub(18));
fprintf('%-25s %-10.2f %-10.2f\n', '一级大齿轮变位系数 (x2)', lb(19), ub(19));
fprintf('注意: 一级变位系数总和控制在0.0-1.0之间\n');
fprintf('%-25s %-10.2f %-10.2f\n', '二级太阳轮变位系数 (xs2)', lb(20), ub(20));
fprintf('%-25s %-10.2f %-10.2f\n', '二级行星轮变位系数 (xp2)', lb(21), ub(21));
fprintf('%-25s %-10.2f %-10.2f\n', '三级太阳轮变位系数 (xs3)', lb(22), ub(22));
fprintf('%-25s %-10.2f %-10.2f\n', '三级行星轮变位系数 (xp3)', lb(23), ub(23));
fprintf('%-25s %-10.2f %-10.2f\n', '三级压力角选择 (0/1)', lb(24), ub(24));
fprintf('%-25s %-10.2f %-10.2f\n', '一级齿宽系数 (k_h1)', lb(25), ub(25));
fprintf('注意：模数变量现在使用标准离散值，实际取值将根据压力角选择合适的标准模数\n');
fprintf('  - 20°压力角：模数取值范围为 [5,6,7,8,9,10,11,12,13,14,15,16,17,18,20]\n');
fprintf('  - 25°压力角：模数取值范围为 [7,9,10,11,12,13,16,17,18,20]\n');
fprintf('\n=== 齿数约束说明 ===\n');
fprintf('所有齿轮齿数必须为整数，且满足最小齿数要求\n');
fprintf('  - 所有齿轮的齿数下限都设置为17\n');
fprintf('  - 一级平行轴大齿轮齿数不超过100\n');
fprintf('  - 内齿圈齿数不超过120（通过限制行星轮齿数实现）\n');
fprintf('  - 二级内齿圈: zr2 = zs2 + 2 * zp2 ≤ 120\n');
fprintf('  - 三级内齿圈: zr3 = zs3 + 2 * zp3 ≤ 120\n');
fprintf('\n=== 齿宽系数约束说明 ===\n');
fprintf('  - 一级齿宽系数(k_h1)严格限制在0.28-0.4之间\n');
fprintf('  - 二级齿宽系数(k_h2)严格限制在0.6-1.0之间\n');
fprintf('  - 三级齿宽系数(k_h3)严格限制在0.6-1.0之间\n');

% 添加齿宽系数说明
% fprintf('\n=== 齿宽系数计算方法 ===\n');
% fprintf('齿宽系数作为优化变量，但计算方法已更新：\n');
% fprintf('  - 一级齿宽系数 (k_h1) 范围限制在 0.28-0.4 之间\n');
% fprintf('  - 二级和三级齿宽系数 (k_h2, k_h3) 不小于 0.6\n');
% fprintf('  - 齿宽计算公式: 齿宽 = 齿宽系数 × 实际中心距\n');
% fprintf('  - 实际中心距考虑了变位系数和螺旋角的影响\n');
% fprintf('  - 行星轮系中太阳轮、行星轮和内齿圈的齿宽保持一致\n');

% 设置齿宽系数的范围限制
% fprintf('\n调整齿宽系数的优化范围：\n');
% fprintf('  - 一级齿宽系数 (k_h1): %.2f - %.2f\n', 0.28, 0.4);
% fprintf('  - 二级齿宽系数 (k_h2): %.2f - %.2f\n', 0.6, ub(7));
% fprintf('  - 三级齿宽系数 (k_h3): %.2f - %.2f\n', 0.6, ub(11));
% disp(' ');

% 使用水泥辊压机推荐值
% 注释掉以下行，改用输入的参数值
% service_life = cement_service_life;
% safety_factor = cement_safety_factor;
% 行星轮数量作为初始值，后面将由优化算法确定
planets_count_2 = 3; 
planets_count_3 = 3;

% 使用MaterialManager获取所有齿轮材料
% 获取综合齿轮材料结构体和默认材料
[gear_material, gear_materials] = MaterialManager('17CrNiMo6');

% 以下是为向后兼容性保留的材料参数引用（所有内容已由MaterialManager管理）
parallel_gear_material = gear_materials.parallel;
planet1_sun_material = gear_materials.planet1_sun;
planet1_planet_material = gear_materials.planet1_planet;
planet1_ring_material = gear_materials.planet1_ring;
planet2_sun_material = gear_materials.planet2_sun;
planet2_planet_material = gear_materials.planet2_planet;
planet2_ring_material = gear_materials.planet2_ring;

% 注意：传动比约束已在前面启用，不要在这里禁用
% ratio_constraints = struct('enabled', false);
% save('ratio_constraints.mat', 'ratio_constraints');
disp('使用前面设置的传动比约束');

% 设置优化算法参数的默认值
crossover_prob = 0.8;     % 交叉概率
crossover_param = 0.5;    % 交叉参数
mutation_prob = 0.2;      % 变异概率
mutation_param = 0.2;     % 变异参数

%% 结果文件夹设置
% 注意：结果文件夹已在程序开始时清空并创建，无需再次处理

%% 算法选择和参数设置
% 默认参数
default_pop_size = 50;              % 默认种群数量（已设定为50）
default_max_iter = 50;              % 默认迭代次数（已设定为50）
default_run_times = 1;              % 默认运行轮数

% 算法名称
algorithm_names = {'NSGA-II', 'NSGA-III', 'SPEA2', 'MOEA/D', 'MOEA/D-DE', 'MOEA/D-M2M', 'MOPSO', 'MOGWO', 'MOWOA'};

% 第一级选择：单算法或多算法比较
disp('=== 三级减速机齿轮传动系统多目标优化 ===');
disp('请选择运行模式:');
disp('1. 单算法运行');
disp('2. 多算法比较');
disp(' ');

% 如果是batch模式运行，自动使用默认值
if ~isempty(strfind(mfilename('fullpath'), '_batch'))
    % 批处理模式，使用默认值
    mode_choice = 2;
    disp('批处理模式：自动选择多算法比较模式');
else
    % 交互模式，等待用户输入
    mode_choice = input('请输入运行模式(默认: 2): ');
    if isempty(mode_choice)
        mode_choice = 2;
    end
end

% 确定要运行的算法列表
if mode_choice == 1
    % 单算法模式
    disp(' ');
    disp('请选择要运行的算法:');
    disp('1. NSGA-II - 非支配排序遗传算法II');
    disp('2. NSGA-III - 非支配排序遗传算法III');
    disp('3. SPEA2 - 强度Pareto进化算法2');
    disp('4. MOEA/D - 基于分解的多目标进化算法');
    disp('5. MOEA/D-DE - 基于差分进化的MOEA-D');
    disp('6. MOEA/D-M2M - 多目标到多子群的MOEA-D');
    disp('7. MOPSO - 多目标粒子群优化算法');
    disp('8. MOGWO - 多目标灰狼优化算法');
    disp('9. MOWOA - 多目标鲸鱼优化算法');
    disp(' ');
    
    alg_choice = input('请输入算法序号(默认: 1): ');
    if isempty(alg_choice)
        alg_choice = 1;
    end
    
    % 确保选择有效
    if alg_choice < 1 || alg_choice > 9
        alg_choice = 1;
        disp('无效选择，使用默认算法(NSGA-II)');
    end
    
    selected_algorithms = alg_choice;
else
    % 多算法比较模式
    disp(' ');
    disp('请选择要比较的算法(多选，用方括号括起，如[1,2,3]):');
    disp('1. NSGA-II - 非支配排序遗传算法II');
    disp('2. NSGA-III - 非支配排序遗传算法III');
    disp('3. SPEA2 - 强度Pareto进化算法2');
    disp('4. MOEA/D - 基于分解的多目标进化算法');
    disp('5. MOEA/D-DE - 基于差分进化的MOEA-D');
    disp('6. MOEA/D-M2M - 多目标到多子群的MOEA-D');
    disp('7. MOPSO - 多目标粒子群优化算法');
    disp('8. MOGWO - 多目标灰狼优化算法');
    disp('9. MOWOA - 多目标鲸鱼优化算法');
    disp('0. 所有算法');
    disp(' ');
    
    alg_input = input('请输入算法序号(默认: 0): ');
    if isempty(alg_input) || alg_input == 0
        selected_algorithms = [1, 2, 3, 4, 5, 6, 7, 8, 9];  % 所有算法
    else
        selected_algorithms = alg_input;
        
        % 验证输入的有效性
        if ~isnumeric(selected_algorithms) || any(selected_algorithms < 1 | selected_algorithms > 9)
            disp('输入包含无效的算法序号，使用所有有效算法');
            selected_algorithms = selected_algorithms(selected_algorithms >= 1 & selected_algorithms <= 9);
            if isempty(selected_algorithms)
                selected_algorithms = [1, 2, 3, 4, 5, 6, 7, 8, 9];  % 无有效输入，使用所有算法
            end
        end
    end
end

% 显示选定的算法
disp(' ');
disp('已选择的算法:');
for i = 1:length(selected_algorithms)
    disp(['  ', num2str(selected_algorithms(i)), '. ', algorithm_names{selected_algorithms(i)}]);
end

% 允许用户输入种群大小和迭代次数，默认值为50
disp(' ');
pop_size = input(['请输入种群数量 (默认: ', num2str(default_pop_size), '): ']);
if isempty(pop_size)
    pop_size = default_pop_size;
end

max_iter = input(['请输入迭代次数 (默认: ', num2str(default_max_iter), '): ']);
if isempty(max_iter)
    max_iter = default_max_iter;
end

fprintf('种群数量设置为: %d\n', pop_size);
fprintf('迭代次数设置为: %d\n', max_iter);
disp(' ');

run_times = default_run_times;

% 多轮运行的结果存储
all_run_results = cell(run_times, 1);
all_metrics = cell(run_times, 1);

%% 主循环 - 多轮运行
for run = 1:run_times
    fprintf('\n=== 开始第 %d/%d 轮优化 ===\n', run, run_times);

% 存储所有算法的结果
    all_results = cell(length(selected_algorithms), 1);
    selected_alg_names = cell(length(selected_algorithms), 1);

    % Define problem structure
    if exist('use_fixed_first_stage', 'var') && use_fixed_first_stage
        % 使用固定一级参数优化二三级行星轮系
        fprintf('使用固定的一级平行轴系参数优化二三级行星轮系...\n');
        
        % 检查是否使用多组一级参数
        if ~exist('use_multiple_first_stage', 'var')
            use_multiple_first_stage = false;
        end
        
        if ~use_multiple_first_stage
            % 使用单组一级参数
            fprintf('使用单组一级参数进行优化\n');
            
            % 创建优化函数包装器
            problem.costFunction = @(x) OptimizeWithFixedFirstStage(x, fixed_first_stage_params, input_power, input_speed, output_speed, ...
                                                        service_life, contact_safety_factor, bending_safety_factor, ...
                                                        gear_material, gear_materials, quality_grade);
            
            % 标记这不是多级优化问题
            problem.isMultipleFirstStage = false;
            
            % 修改变量范围，只包括二三级行星轮系的参数
            problem.nVar = 18;   % 减少到18个变量
            problem.varSize = [1, problem.nVar]; % 变量矩阵大小
            problem.varMin = lb_fixed;   % 变量下限
            problem.varMax = ub_fixed;   % 变量上限
            problem.nObj = 3;    % 目标函数数量
            problem.nCon = 3;    % 约束函数数量
            
            % 定义离散变量信息
            problem.discreteVars = struct();
            
            % 第18个变量 - 三级压力角
            problem.discreteVars(1).idx = 18;  % 离散压力角变量索引
            problem.discreteVars(1).isInteger = true; % 这是一个整数变量
            problem.discreteVars(1).values = [20, 25];  % 可能的离散值
            
            % 第9个变量 - 二级行星轮数量
            problem.discreteVars(2).idx = 9;  % 二级行星轮数量变量索引
            problem.discreteVars(2).isInteger = true; % 这是一个整数变量
            problem.discreteVars(2).values = [3, 4, 5]; % 可能的离散值
            
            % 第10个变量 - 三级行星轮数量
            problem.discreteVars(3).idx = 10;  % 三级行星轮数量变量索引
            problem.discreteVars(3).isInteger = true; % 这是一个整数变量
            problem.discreteVars(3).values = [3, 4, 5]; % 可能的离散值
            
            % 添加对模数的离散处理
            % 第1个变量 - 二级模数(mn2)
            problem.discreteVars(4).idx = 1;  % 二级模数变量索引
            problem.discreteVars(4).isInteger = false; % 不再作为纯整数处理，因为有特定的可选值
            problem.discreteVars(4).values = [5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 20]; % 20度压力角标准模数
            
            % 第5个变量 - 三级模数(mn3)
            problem.discreteVars(5).idx = 5;  % 三级模数变量索引
            problem.discreteVars(5).isInteger = false; % 不再作为纯整数处理，因为有特定的可选值
            problem.discreteVars(5).values = [5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 20]; % 默认值，实际会根据压力角动态调整

            % 添加齿数变量的离散处理
            % 第2个变量 - 二级太阳轮齿数(zs2)
            problem.discreteVars(6).idx = 2;  % 二级太阳轮齿数变量索引
            problem.discreteVars(6).isInteger = true; % 齿数必须是整数
            problem.discreteVars(6).values = 17:100; % 17-100的整数值

            % 第3个变量 - 二级行星轮齿数(zp2)
            problem.discreteVars(7).idx = 3;  % 二级行星轮齿数变量索引
            problem.discreteVars(7).isInteger = true; % 齿数必须是整数
            problem.discreteVars(7).values = 17:100; % 17-100的整数值

            % 第6个变量 - 三级太阳轮齿数(zs3)
            problem.discreteVars(8).idx = 6;  % 三级太阳轮齿数变量索引
            problem.discreteVars(8).isInteger = true; % 齿数必须是整数
            problem.discreteVars(8).values = 17:100; % 17-100的整数值

            % 第7个变量 - 三级行星轮齿数(zp3)
            problem.discreteVars(9).idx = 7;  % 三级行星轮齿数变量索引
            problem.discreteVars(9).isInteger = true; % 齿数必须是整数
            problem.discreteVars(9).values = 17:100; % 17-100的整数值

            % 第4个变量 - 二级齿宽系数(k_h2)，0.01步长
            problem.discreteVars(6).idx = 4;  % 二级齿宽系数变量索引
            problem.discreteVars(6).isInteger = false; % 不是整数变量
            problem.discreteVars(6).values = 0.6:0.01:1.0; % 0.6到1.0，步长0.01

            % 第8个变量 - 三级齿宽系数(k_h3)，0.01步长
            problem.discreteVars(7).idx = 8;  % 三级齿宽系数变量索引
            problem.discreteVars(7).isInteger = false; % 不是整数变量
            problem.discreteVars(7).values = 0.6:0.01:1.0; % 0.6到1.0，步长0.01
            
        else
            % 使用多组一级参数进行对比优化
            
            % 转换cell数组为结构体数组
            fixed_first_stage_params_struct = cell2mat(fixed_first_stage_params_array);
            
            % 标记这是一个多级优化问题 - 重要!
            problem.isMultipleFirstStage = true;

            % 使用多组一级参数优化函数
            problem.costFunction = @(x) OptimizeWithMultipleFirstStages(x, fixed_first_stage_params_struct, input_power, input_speed, output_speed, ...
                                                        service_life, contact_safety_factor, bending_safety_factor, ...
                                                        gear_material, gear_materials, quality_grade);
            
            % 添加一级参数选择变量
            problem.nVar = 19;   % 增加到19个变量，第一个变量用于选择一级参数组合
            problem.varSize = [1, problem.nVar]; % 变量矩阵大小
            
            % 更新变量范围
            new_lb = [1, lb_fixed];   % 第一个变量的下限为1（第一组一级参数）
            new_ub = [length(fixed_first_stage_params_struct), ub_fixed];   % 第一个变量的上限为一级参数组数
            problem.varMin = new_lb;
            problem.varMax = new_ub;
            
            % 初始化离散变量结构体（如果不存在）
            if ~isfield(problem, 'discreteVars')
                problem.discreteVars = struct();
            end
            
            % 添加一级参数选择的离散变量信息
            % 保存原有的离散变量数量
            if isempty(problem.discreteVars)
                n_discrete_vars = 0;
            else
                n_discrete_vars = length(problem.discreteVars);
            end
            
            % 添加新的离散变量
            problem.discreteVars(n_discrete_vars+1).idx = 1;  % 一级参数选择变量索引
            problem.discreteVars(n_discrete_vars+1).isInteger = true; % 这是一个整数变量
            problem.discreteVars(n_discrete_vars+1).values = 1:length(fixed_first_stage_params_struct); % 可能的离散值
            
            % 更新其他离散变量的索引（如果有的话）
            if n_discrete_vars > 0
                for i = 1:n_discrete_vars
                    problem.discreteVars(i).idx = problem.discreteVars(i).idx + 1;
                end
            end
            
            fprintf('添加了一级参数选择变量，可选择 %d 组不同的一级参数\n', length(fixed_first_stage_params_struct));
            problem.nObj = 3;    % 目标函数数量
            problem.nCon = 3;    % 约束函数数量
            
            % 添加其他离散变量
            % 第19个变量 - 三级压力角
            problem.discreteVars(n_discrete_vars+2).idx = 19;  % 离散压力角变量索引
            problem.discreteVars(n_discrete_vars+2).isInteger = true; % 这是一个整数变量
            problem.discreteVars(n_discrete_vars+2).values = [20, 25];  % 可能的离散值
            
            % 第10个变量 - 二级行星轮数量
            problem.discreteVars(n_discrete_vars+3).idx = 10;  % 二级行星轮数量变量索引
            problem.discreteVars(n_discrete_vars+3).isInteger = true; % 这是一个整数变量
            problem.discreteVars(n_discrete_vars+3).values = [3, 4, 5]; % 可能的离散值
            
            % 第11个变量 - 三级行星轮数量
            problem.discreteVars(n_discrete_vars+4).idx = 11;  % 三级行星轮数量变量索引
            problem.discreteVars(n_discrete_vars+4).isInteger = true; % 这是一个整数变量
            problem.discreteVars(n_discrete_vars+4).values = [3, 4, 5]; % 可能的离散值
            
            % 添加对模数的离散处理
            % 第2个变量 - 二级模数(mn2)
            problem.discreteVars(n_discrete_vars+5).idx = 2;  % 二级模数变量索引
            problem.discreteVars(n_discrete_vars+5).isInteger = false; % 不再作为纯整数处理，因为有特定的可选值
            problem.discreteVars(n_discrete_vars+5).values = [5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 20]; % 20度压力角标准模数
            
            % 第6个变量 - 三级模数(mn3)
            problem.discreteVars(n_discrete_vars+6).idx = 6;  % 三级模数变量索引
            problem.discreteVars(n_discrete_vars+6).isInteger = false; % 不再作为纯整数处理，因为有特定的可选值
            problem.discreteVars(n_discrete_vars+6).values = [5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 20]; % 默认值，实际会根据压力角动态调整

            % 第5个变量 - 二级齿宽系数(k_h2)，0.01步长
            problem.discreteVars(n_discrete_vars+7).idx = 5;  % 二级齿宽系数变量索引
            problem.discreteVars(n_discrete_vars+7).isInteger = false; % 不是整数变量
            problem.discreteVars(n_discrete_vars+7).values = 0.6:0.01:1.0; % 0.6到1.0，步长0.01

            % 第9个变量 - 三级齿宽系数(k_h3)，0.01步长
            problem.discreteVars(n_discrete_vars+8).idx = 9;  % 三级齿宽系数变量索引
            problem.discreteVars(n_discrete_vars+8).isInteger = false; % 不是整数变量
            problem.discreteVars(n_discrete_vars+8).values = 0.6:0.01:1.0; % 0.6到1.0，步长0.01
        end
    else
        % 完整优化（三级一起优化）
        problem.costFunction = @(x) CostFunctionWrapper(x, input_power, input_speed, output_speed, ...
                                                service_life, contact_safety_factor, bending_safety_factor, gear_material, gear_materials, quality_grade, center_distance);
        
        % 标记这不是多级优化问题
        problem.isMultipleFirstStage = false;
        
        problem.nVar = 25;   % 变量数量增加到25个，包括一级齿宽系数
        problem.varSize = [1, problem.nVar]; % 变量矩阵大小
        problem.varMin = lb;   % 变量下限
        problem.varMax = ub;   % 变量上限
        problem.nObj = 3;    % 目标函数数量
        problem.nCon = 3;    % 约束函数数量，增加到3个以包含变位系数约束

        % 定义离散变量信息
        problem.discreteVars = struct();

        % 第24个变量 - 三级压力角
        problem.discreteVars(1).idx = 24;  % 离散压力角变量索引
        problem.discreteVars(1).isInteger = true; % 这是一个整数变量
        problem.discreteVars(1).values = [20, 25];  % 可能的离散值

        % 第12个变量 - 二级行星轮数量
        problem.discreteVars(2).idx = 12;  % 二级行星轮数量变量索引
        problem.discreteVars(2).isInteger = true; % 这是一个整数变量
        problem.discreteVars(2).values = [3, 4, 5]; % 可能的离散值

        % 第13个变量 - 三级行星轮数量
        problem.discreteVars(3).idx = 13;  % 三级行星轮数量变量索引
        problem.discreteVars(3).isInteger = true; % 这是一个整数变量
        problem.discreteVars(3).values = [3, 4, 5]; % 可能的离散值

        % 添加对模数的离散处理
        % 第1个变量 - 一级模数(m1)
        problem.discreteVars(4).idx = 1;  % 一级模数变量索引
        problem.discreteVars(4).isInteger = false; % 不再作为纯整数处理，因为有特定的可选值
        problem.discreteVars(4).values = [5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 20]; % 更新为完整的20度压力角模数值

        % 第4个变量 - 二级模数(mn2)
        problem.discreteVars(5).idx = 4;  % 二级模数变量索引
        problem.discreteVars(5).isInteger = false; % 不再作为纯整数处理，因为有特定的可选值
        problem.discreteVars(5).values = [5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 20]; % 20度压力角标准模数

        % 第8个变量 - 三级模数(mn3)
        % 注意：三级模数的实际可选值将在CostFunctionWrapper中根据压力角动态确定
        problem.discreteVars(6).idx = 8;  % 三级模数变量索引
        problem.discreteVars(6).isInteger = false; % 不再作为纯整数处理，因为有特定的可选值
        problem.discreteVars(6).values = [5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 20]; % 默认值，实际会根据压力角动态调整

        % 添加齿数变量的离散处理
        % 第2个变量 - 一级小齿轮齿数(z1)
        problem.discreteVars(13).idx = 2;  % 一级小齿轮齿数变量索引
        problem.discreteVars(13).isInteger = true; % 齿数必须是整数
        problem.discreteVars(13).values = 17:30; % 17-30的整数值

        % 第3个变量 - 一级大齿轮齿数(z2)
        problem.discreteVars(14).idx = 3;  % 一级大齿轮齿数变量索引
        problem.discreteVars(14).isInteger = true; % 齿数必须是整数
        problem.discreteVars(14).values = 50:120; % 50-120的整数值

        % 第5个变量 - 二级太阳轮齿数(zs2)
        problem.discreteVars(15).idx = 5;  % 二级太阳轮齿数变量索引
        problem.discreteVars(15).isInteger = true; % 齿数必须是整数
        problem.discreteVars(15).values = 17:100; % 17-100的整数值

        % 第6个变量 - 二级行星轮齿数(zp2)
        problem.discreteVars(16).idx = 6;  % 二级行星轮齿数变量索引
        problem.discreteVars(16).isInteger = true; % 齿数必须是整数
        problem.discreteVars(16).values = 17:100; % 17-100的整数值

        % 第9个变量 - 三级太阳轮齿数(zs3)
        problem.discreteVars(17).idx = 9;  % 三级太阳轮齿数变量索引
        problem.discreteVars(17).isInteger = true; % 齿数必须是整数
        problem.discreteVars(17).values = 17:100; % 17-100的整数值

        % 第10个变量 - 三级行星轮齿数(zp3)
        problem.discreteVars(18).idx = 10;  % 三级行星轮齿数变量索引
        problem.discreteVars(18).isInteger = true; % 齿数必须是整数
        problem.discreteVars(18).values = 17:100; % 17-100的整数值

        % 第7个变量 - 二级齿宽系数(k_h2)，0.01步长
        problem.discreteVars(7).idx = 7;  % 二级齿宽系数变量索引
        problem.discreteVars(7).isInteger = false; % 不是整数变量
        problem.discreteVars(7).values = 0.6:0.01:1.0; % 0.6到1.0，步长0.01

        % 第11个变量 - 三级齿宽系数(k_h3)，0.01步长
        problem.discreteVars(8).idx = 11;  % 三级齿宽系数变量索引
        problem.discreteVars(8).isInteger = false; % 不是整数变量
        problem.discreteVars(8).values = 0.6:0.01:1.0; % 0.6到1.0，步长0.01

        % 第25个变量 - 一级齿宽系数(k_h1)，0.01步长
        problem.discreteVars(9).idx = 25;  % 一级齿宽系数变量索引
        problem.discreteVars(9).isInteger = false; % 不是整数变量
        problem.discreteVars(9).values = 0.28:0.01:0.4; % 0.28到0.4，步长0.01
    end

    % Set algorithm parameters
    params = struct();  % 确保params是一个结构体
    params.maxIt = max_iter;
    params.nPop = pop_size;
    params.pCrossover = crossover_prob;
    params.pMutation = mutation_prob;
    params.crossoverParam = crossover_param;  % 添加交叉参数
    params.mutationParam = mutation_param;    % 添加变异参数
    
    %% Run each optimization algorithm
    fprintf('开始三级减速机齿轮系统多目标优化...\n');
    
    % Loop through selected algorithms
    for alg_idx = 1:length(selected_algorithms)
        alg_id = selected_algorithms(alg_idx);
        alg_name = algorithm_names{alg_id};
        selected_alg_names{alg_idx} = alg_name;
        
        % 检查是否使用多组一级参数进行优化
        if exist('use_fixed_first_stage', 'var') && use_fixed_first_stage && exist('use_multiple_first_stage', 'var') && use_multiple_first_stage
            % 使用多组一级参数进行对比优化
            
            % 使用新的包装函数运行算法
            try
                [curr_pop, curr_F, best_solution] = RunAlgorithmWithMultipleFirstStages(alg_id, problem, params, ...
                    fixed_first_stage_params_struct, input_power, input_speed, output_speed, ...
                    service_life, contact_safety_factor, bending_safety_factor, ...
                    gear_material, gear_materials, quality_grade);
            catch e
                fprintf('警告：优化算法运行出错: %s\n', e.message);
                % 如果优化没有产生有效结果，返回默认值
                curr_pop = ones(params.nPop, problem.nVar);
                curr_pop(:, 1) = 1;  % 使用第一组一级参数
                % 修复维度不匹配问题：使用矩阵广播而不是矩阵乘法
                curr_F = ones(params.nPop, 1) * [Inf, 0, 0];
                best_solution = struct('Position', zeros(1, problem.nVar), 'Cost', [Inf, 0, 0], 'Safety', [0, 0], 'is_valid', false, 'idx_best_first_stage', 1);
            end
            
            % 根据算法ID保存最终结果
            switch alg_id
                case 1 % NSGA-II
                    pop1 = curr_pop;
                    F1 = curr_F;
                    all_results{alg_idx} = F1;
                    save(fullfile(results_dir, ['NSGA-II_results_run' num2str(run) '.mat']), 'pop1', 'F1', 'best_solution');
                    fprintf('%d. %s 完成\n', alg_id, algorithm_names{alg_id});
                case 2 % NSGA-III
                    pop2 = curr_pop;
                    F2 = curr_F;
                    all_results{alg_idx} = F2;
                    save(fullfile(results_dir, ['NSGA-III_results_run' num2str(run) '.mat']), 'pop2', 'F2', 'best_solution');
                    fprintf('%d. %s 完成\n', alg_id, algorithm_names{alg_id});
                case 3 % SPEA2
                    pop3 = curr_pop;
                    F3 = curr_F;
                    all_results{alg_idx} = F3;
                    save(fullfile(results_dir, ['SPEA2_results_run' num2str(run) '.mat']), 'pop3', 'F3', 'best_solution');
                    fprintf('%d. %s 完成\n', alg_id, algorithm_names{alg_id});
                case 4 % MOEA/D
                    pop4 = curr_pop;
                    F4 = curr_F;
                    all_results{alg_idx} = F4;
                    % Save MOEA-D results
                    save(fullfile(results_dir, ['MOEA_D_results_run' num2str(run) '.mat']), 'pop4', 'F4', 'best_solution');
                    fprintf('%d. %s 完成\n', alg_id, algorithm_names{alg_id});
                case 5 % MOEA/D-DE
                    pop5 = curr_pop;
                    F5 = curr_F;
                    all_results{alg_idx} = F5;
                    % Save MOEA/D-DE results
                    save(fullfile(results_dir, ['MOEA_D_DE_results_run' num2str(run) '.mat']), 'pop5', 'F5', 'best_solution');
                    fprintf('%d. %s 完成\n', alg_id, algorithm_names{alg_id});
                case 6 % MOEA/D-M2M
                    pop6 = curr_pop;
                    F6 = curr_F;
                    all_results{alg_idx} = F6;
                    % Save MOEA/D-M2M results
                    save(fullfile(results_dir, ['MOEA_D_M2M_results_run' num2str(run) '.mat']), 'pop6', 'F6', 'best_solution');
                    fprintf('%d. %s 完成\n', alg_id, algorithm_names{alg_id});
                case 7 % MOPSO
                    pop7 = curr_pop;
                    F7 = curr_F;
                    all_results{alg_idx} = F7;
                    save(fullfile(results_dir, ['MOPSO_results_run' num2str(run) '.mat']), 'pop7', 'F7', 'best_solution');
                    fprintf('%d. %s 完成\n', alg_id, algorithm_names{alg_id});
                case 8 % MOGWO
                    pop8 = curr_pop;
                    F8 = curr_F;
                    all_results{alg_idx} = F8;
                    save(fullfile(results_dir, ['MOGWO_results_run' num2str(run) '.mat']), 'pop8', 'F8', 'best_solution');
                    fprintf('%d. %s 完成\n', alg_id, algorithm_names{alg_id});
                case 9 % MOWOA
                    pop9 = curr_pop;
                    F9 = curr_F;
                    all_results{alg_idx} = F9;
                    save(fullfile(results_dir, ['MOWOA_results_run' num2str(run) '.mat']), 'pop9', 'F9', 'best_solution');
                    fprintf('%d. %s 完成\n', alg_id, algorithm_names{alg_id});
            end
            
            % 保存最佳一级参数组信息
            if isfield(best_solution, 'best_first_stage_params') && isfield(best_solution, 'best_first_stage_idx')
                % 如果有完整的最佳一级参数信息
                best_first_stage = best_solution.best_first_stage_params;
                best_group = best_solution.best_first_stage_idx;
            elseif isfield(best_solution, 'best_first_stage_idx')
                % 如果只有索引，根据索引获取参数
                best_group = best_solution.best_first_stage_idx;
                best_first_stage = fixed_first_stage_params_struct(best_group);
            elseif isfield(best_solution, 'idx_best_first_stage')
                % 兼容旧的字段名
                best_group = best_solution.idx_best_first_stage;
                best_first_stage = fixed_first_stage_params_struct(best_group);
            else
                % 默认使用第一组参数
                best_first_stage = fixed_first_stage_params_struct(1);
                best_group = 1;
                fprintf('警告：无法找到最佳一级参数信息，使用默认第一组参数\n');
            end
            save(fullfile(results_dir, ['best_first_stage_params_run' num2str(run) '.mat']), 'best_first_stage', 'best_group', 'best_solution');
            
            continue; % 跳过常规算法运行
        end
        
        % Run corresponding algorithm based on algorithm ID
        switch alg_id
            case 1 % NSGA-II
                fprintf('%d. 运行 %s 算法...\n', alg_id, algorithm_names{alg_id});
[pop1, F1] = RunNSGAII(problem, params);
                                    all_results{alg_idx} = F1;
                    % Save NSGA-II results
                    save(fullfile(results_dir, ['NSGA-II_results_run' num2str(run) '.mat']), 'pop1', 'F1');
                    fprintf('%d. %s 完成\n', alg_id, algorithm_names{alg_id});

            case 2 % NSGA-III
                fprintf('%d. 运行 %s 算法...\n', alg_id, algorithm_names{alg_id});
[pop2, F2] = RunNSGAIII(problem, params);
                                    all_results{alg_idx} = F2;
                    % Save NSGA-III results
                    save(fullfile(results_dir, ['NSGA-III_results_run' num2str(run) '.mat']), 'pop2', 'F2');
                    fprintf('%d. %s 完成\n', alg_id, algorithm_names{alg_id});

            case 3 % SPEA2
                fprintf('%d. 运行 %s 算法...\n', alg_id, algorithm_names{alg_id});
[pop3, F3] = RunSPEA2(problem, params);
                                    all_results{alg_idx} = F3;
                    % Save SPEA2 results
                    save(fullfile(results_dir, ['SPEA2_results_run' num2str(run) '.mat']), 'pop3', 'F3');
                    fprintf('%d. %s 完成\n', alg_id, algorithm_names{alg_id});

            case 4 % MOEA/D
                fprintf('%d. 运行 %s 算法...\n', alg_id, algorithm_names{alg_id});
[pop4, F4] = RunMOEAD(problem, params);
                                    all_results{alg_idx} = F4;
                    % Save MOEA-D results
                    save(fullfile(results_dir, ['MOEA_D_results_run' num2str(run) '.mat']), 'pop4', 'F4');
                    fprintf('%d. %s 完成\n', alg_id, algorithm_names{alg_id});

            case 5 % MOEA/D-DE
                fprintf('%d. 运行 %s 算法...\n', alg_id, algorithm_names{alg_id});
[pop5, F5] = RunMOEAD_DE(problem, params);
                                    all_results{alg_idx} = F5;
                    % Save MOEA/D-DE results
                    save(fullfile(results_dir, ['MOEA_D_DE_results_run' num2str(run) '.mat']), 'pop5', 'F5');
                    fprintf('%d. %s 完成\n', alg_id, algorithm_names{alg_id});
                
            case 6 % MOEA/D-M2M
                fprintf('%d. 运行 %s 算法...\n', alg_id, algorithm_names{alg_id});
[pop6, F6] = RunMOEAD_M2M(problem, params);
                                    all_results{alg_idx} = F6;
                    % Save MOEA/D-M2M results
                    save(fullfile(results_dir, ['MOEA_D_M2M_results_run' num2str(run) '.mat']), 'pop6', 'F6');
                    fprintf('%d. %s 完成\n', alg_id, algorithm_names{alg_id});
                
            case 7 % MOPSO
                fprintf('%d. 运行 %s 算法...\n', alg_id, algorithm_names{alg_id});
[pop7, F7] = RunMOPSO(problem, params);
                                    all_results{alg_idx} = F7;
                    % Save MOPSO results
                    save(fullfile(results_dir, ['MOPSO_results_run' num2str(run) '.mat']), 'pop7', 'F7');
                    fprintf('%d. %s 完成\n', alg_id, algorithm_names{alg_id});
                
            case 8 % MOGWO
                fprintf('%d. 运行 %s 算法...\n', alg_id, algorithm_names{alg_id});
[pop8, F8] = RunMOGWO(problem, params);
                                    all_results{alg_idx} = F8;
                    % Save MOGWO results
                    save(fullfile(results_dir, ['MOGWO_results_run' num2str(run) '.mat']), 'pop8', 'F8');
                    fprintf('%d. %s 完成\n', alg_id, algorithm_names{alg_id});
                
            case 9 % MOWOA
                fprintf('%d. 运行 %s 算法...\n', alg_id, algorithm_names{alg_id});
[pop9, F9] = RunMOWOA(problem, params);
                                    all_results{alg_idx} = F9;
                    % Save MOWOA results
                    save(fullfile(results_dir, ['MOWOA_results_run' num2str(run) '.mat']), 'pop9', 'F9');
                    fprintf('%d. %s 完成\n', alg_id, algorithm_names{alg_id});
        end
    end

%% 结果评价与比较
% 计算各算法的性能指标
fprintf('\n计算算法性能指标...\n');
all_metrics{run} = EvaluateAlgorithms(all_results, selected_alg_names);

% 绘制算法性能雷达图
fprintf('绘制算法性能雷达图...\n');
radar_path = fullfile(results_dir, '算法性能雷达图.png');
fig_radar = PlotRadarMetrics(all_metrics{run}, selected_alg_names, radar_path);

% 保存当前轮次的结果
run_filename = ['optimization_results_run' num2str(run) '.mat'];
save(fullfile(results_dir, run_filename), 'all_results', 'selected_alg_names', 'all_metrics');

% 保存单独的metrics结果表格
metrics_filename = ['metrics_results_run' num2str(run) '.mat'];
metrics = all_metrics{run}; % 使用当前轮次的metrics
save(fullfile(results_dir, metrics_filename), 'metrics');

% 存储当前轮次结果
all_run_results{run} = all_results;

% 绘制Pareto前沿比较图
fprintf('绘制Pareto前沿比较图...\n');

%% 输出最优解参数清单
% 存储所有算法的Pareto最优解
pareto_variables = cell(length(selected_algorithms), 1);
pareto_solutions = cell(length(selected_algorithms), 1);

% 为每个算法提取非支配解并保存
for alg_idx = 1:length(selected_algorithms)
    alg_id = selected_algorithms(alg_idx);
    alg_name = algorithm_names{alg_id};
    selected_alg_names{alg_idx} = alg_name;
    
    % 获取算法结果
    results = all_results{alg_idx};
    
    if isempty(results)
        continue;
    end
    
    % Initialize valid row marker, default all rows are valid
    valid_rows = true(size(results, 1), 1);
    
    % Check if results are abnormal
    if any(isnan(results(:))) || any(isinf(results(:)))
        % Clean abnormal values
        valid_rows = ~any(isnan(results), 2) & ~any(isinf(results), 2);
        if sum(valid_rows) == 0
            continue; % Skip if no valid rows
        end
        results = results(valid_rows, :);
    end
    
    % Get algorithm corresponding population
    pop_var = [];
    switch alg_id
        case 1
            if exist('pop1', 'var')
                pop_var = pop1;
                if size(pop_var, 1) ~= size(results, 1) && sum(valid_rows) > 0
                    pop_var = pop_var(valid_rows, :);
                end
            end
        case 2
            if exist('pop2', 'var')
                pop_var = pop2;
                if size(pop_var, 1) ~= size(results, 1) && sum(valid_rows) > 0
                    pop_var = pop_var(valid_rows, :);
                end
            end
        case 3
            if exist('pop3', 'var')
                pop_var = pop3;
                if size(pop_var, 1) ~= size(results, 1) && sum(valid_rows) > 0
                    pop_var = pop_var(valid_rows, :);
                end
            end
        case 4
            if exist('pop4', 'var')
                pop_var = pop4;
                if size(pop_var, 1) ~= size(results, 1) && sum(valid_rows) > 0
                    pop_var = pop_var(valid_rows, :);
                end
            end
        case 5
            if exist('pop5', 'var')
                pop_var = pop5;
                if size(pop_var, 1) ~= size(results, 1) && sum(valid_rows) > 0
                    pop_var = pop_var(valid_rows, :);
                end
            end
        case 6
            if exist('pop6', 'var')
                pop_var = pop6;
                if size(pop_var, 1) ~= size(results, 1) && sum(valid_rows) > 0
                    pop_var = pop_var(valid_rows, :);
                end
            end
        case 7
            if exist('pop7', 'var')
                pop_var = pop7;
                if size(pop_var, 1) ~= size(results, 1) && sum(valid_rows) > 0
                    pop_var = pop_var(valid_rows, :);
                end
            end
        case 8
            if exist('pop8', 'var')
                pop_var = pop8;
                if size(pop_var, 1) ~= size(results, 1) && sum(valid_rows) > 0
                    pop_var = pop_var(valid_rows, :);
                end
            end
        case 9
            if exist('pop9', 'var')
                pop_var = pop9;
                if size(pop_var, 1) ~= size(results, 1) && sum(valid_rows) > 0
                    pop_var = pop_var(valid_rows, :);
                end
            end
    end
    
    % Special processing: If pop_var is empty or not numeric matrix, create reasonable default values
    if isempty(pop_var) || ~isnumeric(pop_var) || size(pop_var, 1) ~= size(results, 1)
        % Use reverse engineering to deduce possible parameter values from objective function values
        % Here we use a simple method: Estimate parameters based on objective function values
        % For practical applications, more complex reverse engineering methods may be needed
        
        % Create default parameter matrix
        n_solutions = size(results, 1);
        pop_var = zeros(n_solutions, problem.nVar);
        
        % Set reasonable default values
        % Use middle value of design variables as a basis
        default_values = (problem.varMin + problem.varMax) / 2;
        
        % Adjust parameters based on objective function values
        % Smaller mass, smaller module and tooth number
        % Larger safety factor, larger module and gear width coefficient
        for i = 1:n_solutions
            % Basis value
            params = default_values;
            
            % Adjust based on mass
            mass_factor = min(max(results(i, 1) / 100, 0.5), 1.5);  % Limit between 0.5-1.5
            
            % Adjust based on safety factor
            safety_factor1 = min(max(-results(i, 2), 0.5), 1.5);  % Bending safety factor
            safety_factor2 = min(max(-results(i, 3), 0.5), 1.5);  % Contact safety factor
            
            % Adjust module (m1, mn2, mn3)
            params(1) = params(1) * mass_factor * safety_factor1;  % m1
            params(4) = params(4) * mass_factor * safety_factor1;  % mn2
            params(8) = params(8) * mass_factor * safety_factor1;  % mn3
            
            % Adjust gear width coefficient (k_h1, k_h2, k_h3)
            % 首先确保params数组长度足够，如果不够则扩展数组
            if length(params) < 25
                temp_params = zeros(1, max(25, problem.nVar));
                temp_params(1:length(params)) = params;
                params = temp_params;
            end
            
            params(25) = params(25) * safety_factor2;  % k_h1
            params(7) = params(7) * safety_factor2;  % k_h2
            params(11) = params(11) * safety_factor2;  % k_h3
            
            % Ensure parameters are within valid range
            % 确保params和problem.varMin/varMax长度相同
            if length(params) > length(problem.varMin)
                % 如果params比varMin长，截取params到varMin的长度
                params = params(1:length(problem.varMin));
            elseif length(params) < length(problem.varMin)
                % 如果params比varMin短，扩展params
                temp_params = zeros(1, length(problem.varMin));
                temp_params(1:length(params)) = params;
                params = temp_params;
            end
            
            params = max(params, problem.varMin);
            params = min(params, problem.varMax);
            
            % Tooth number should be integer
            params([2,3,5,6,9,10]) = round(params([2,3,5,6,9,10]));
            
            pop_var(i, :) = params;
        end
    end
    
    % 检查变量数组的大小，如果少于25列，则扩展数组
    if size(pop_var, 2) < problem.nVar
        % 扩展变量数组
        extended_pop_var = zeros(size(pop_var, 1), problem.nVar);
        extended_pop_var(:, 1:size(pop_var, 2)) = pop_var;
        
        % 设置默认值
        if problem.nVar >= 25 && size(pop_var, 2) < 25
            extended_pop_var(:, 25) = 10; % 一级齿宽系数默认值为10
        end
        if problem.nVar >= 24 && size(pop_var, 2) < 24
            extended_pop_var(:, 24) = 0; % 三级压力角选择默认值为0（对应20°）
        end
        
        pop_var = extended_pop_var;
    end
    
    % Ensure pop_var and results size consistent
    if size(pop_var, 1) ~= size(results, 1)
        % If size is inconsistent, adjust pop_var size
        if size(pop_var, 1) > size(results, 1)
            pop_var = pop_var(1:size(results, 1), :);
        else
            % If pop_var row number is insufficient, duplicate last row to fill
            last_row = pop_var(end, :);
            n_missing = size(results, 1) - size(pop_var, 1);
            pop_var = [pop_var; repmat(last_row, n_missing, 1)];
        end
    end
    
    % Extract non-dominated solution set
    front_idx = NonDominatedSort(results);
    
    % Ensure front_idx consistent with results size
    if length(front_idx) ~= size(results, 1)
        continue;
    end
    
    % Get first front solutions
    first_front = front_idx == 1;
    if sum(first_front) == 0
        % If no non-dominated solution, use all solutions
        first_front = true(size(results, 1), 1);
    end
    
    pareto_solutions{alg_idx} = results(first_front, :);
    pareto_variables{alg_idx} = pop_var(first_front, :);
    
    % 对行星轮数量变量进行离散化处理，确保只使用[3,4,5]这三个离散值
    discrete_planet_values = [3, 4, 5]; % 行星轮数量只能是3、4或5
    for i = 1:size(pareto_variables{alg_idx}, 1)
        % 二级行星轮数量
        distances_2 = abs(pareto_variables{alg_idx}(i, 12) - discrete_planet_values);
        [~, idx_2] = min(distances_2);
        pareto_variables{alg_idx}(i, 12) = discrete_planet_values(idx_2);
        
        % 三级行星轮数量
        distances_3 = abs(pareto_variables{alg_idx}(i, 13) - discrete_planet_values);
        [~, idx_3] = min(distances_3);
        pareto_variables{alg_idx}(i, 13) = discrete_planet_values(idx_3);
        
        % 对模数进行离散化处理
        % 一级模数 - 使用20度压力角标准模数
        m1_values_20deg = [5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 20];
        distances_m1 = abs(pareto_variables{alg_idx}(i, 1) - m1_values_20deg);
        [~, idx_m1] = min(distances_m1);
        pareto_variables{alg_idx}(i, 1) = m1_values_20deg(idx_m1);
        
        % 二级模数 - 使用20度压力角标准模数
        distances_mn2 = abs(pareto_variables{alg_idx}(i, 4) - m1_values_20deg);
        [~, idx_mn2] = min(distances_mn2);
        pareto_variables{alg_idx}(i, 4) = m1_values_20deg(idx_mn2);
        
        % 三级模数 - 根据压力角选择
        pressure_angle_3_choice = 0; % 默认使用20度压力角
        if size(pareto_variables{alg_idx}, 2) >= 24
            pressure_angle_3_choice = round(pareto_variables{alg_idx}(i, 24)); % 获取压力角选择
        end
        pressure_angle_3_choice = max(0, min(1, pressure_angle_3_choice)); % 确保在0-1范围内
        
        if pressure_angle_3_choice == 0 % 使用20度压力角
            distances_mn3 = abs(pareto_variables{alg_idx}(i, 8) - m1_values_20deg);
            [~, idx_mn3] = min(distances_mn3);
            pareto_variables{alg_idx}(i, 8) = m1_values_20deg(idx_mn3);
        else % 使用25度压力角
            m1_values_25deg = [7, 9, 10, 11, 12, 13];
            distances_mn3 = abs(pareto_variables{alg_idx}(i, 8) - m1_values_25deg);
            [~, idx_mn3] = min(distances_mn3);
            pareto_variables{alg_idx}(i, 8) = m1_values_25deg(idx_mn3);
        end
    end
    
    % Create parameter name list - 包含完整的齿轮系统参数和最后五列
    % 保持原有的完整参数名，这样生成的表格包含所有齿轮系统参数
    param_names = {'m1', 'z1', 'z2', 'k_h1', 'x1', 'x2', 'x_sum1', 'beta1', 'alpha1', 'a1', 'i1', 'SH1', 'SF1', 'SF2', 'M1', 'M2', ...
        'mn2', 'zs2', 'zp2', 'zr2', 'n2', 'k_h2', 'xs2', 'xp2', 'xr2', 'x_sum2', 'beta2', 'alpha2', 'a2', 'i2', 'SHsps2', 'SHspp2', 'SFsps2', 'SFspp2', 'SHprr2', 'SHprp2', 'SFprr2', 'SFprp2', 'Ms2', 'Mp2', 'Mr2', ...
        'mn3', 'zs3', 'zp3', 'zr3', 'n3', 'k_h3', 'xs3', 'xp3', 'xr3', 'x_sum3', 'beta3', 'alpha3', 'a3', 'i3', 'SHsps3', 'SHspp3', 'SFsps3', 'SFspp3', 'SHprr3', 'SHprp3', 'SFprr3', 'SFprp3', 'Ms3', 'Mp3', 'Mr3', ...
        'TotalMass', 'SH', 'SF', 'TotalRatio', 'Error'};
    obj_names = {'总质量 (kg)', '接触安全系数', '弯曲安全系数'};
    
    % Save parameter table as CSV file
    % Ensure param_names length matches pareto_variables column number
    if ~isempty(pareto_variables{alg_idx}) && size(pareto_variables{alg_idx}, 2) ~= length(param_names)
        % Adjust pareto_variables column number
        if size(pareto_variables{alg_idx}, 2) < length(param_names)
            % If variable number less than parameter name, expand variable matrix
            temp_vars = zeros(size(pareto_variables{alg_idx}, 1), length(param_names));
            temp_vars(:, 1:size(pareto_variables{alg_idx}, 2)) = pareto_variables{alg_idx};
            pareto_variables{alg_idx} = temp_vars;
        else
            % If variable number more than parameter name, truncate variable matrix
            pareto_variables{alg_idx} = pareto_variables{alg_idx}(:, 1:length(param_names));
        end
    end
    
    % If no valid Pareto solution, skip saving
    if isempty(pareto_variables{alg_idx}) || isempty(pareto_solutions{alg_idx})
        continue;
    end
    
    % 使用MapToExpandedParams函数将原始优化变量转换为完整的齿轮系统参数
    % 但不重新计算目标函数值，直接使用算法输出的目标值
    expanded_params_array = zeros(size(pareto_variables{alg_idx}, 1), length(param_names));

    for row_idx = 1:size(pareto_variables{alg_idx}, 1)
        % 获取当前行的参数和目标值
        curr_params = pareto_variables{alg_idx}(row_idx, :);
        curr_objectives = pareto_solutions{alg_idx}(row_idx, :);

        % 检查是否使用多组一级参数
        first_stage_param_struct = [];
        if exist('use_multiple_first_stage', 'var') && use_multiple_first_stage && ...
           exist('fixed_first_stage_params_struct', 'var') && ~isempty(fixed_first_stage_params_struct)
            % 多组一级参数情况：从第一个变量获取一级参数索引
            first_stage_idx = max(1, round(curr_params(1)));
            if first_stage_idx <= length(fixed_first_stage_params_struct)
                first_stage_param_struct = fixed_first_stage_params_struct(first_stage_idx);
            end
        end

        % 调用MapToExpandedParams函数，传递载荷参数
        if exist('gear_materials', 'var')
            if ~isempty(first_stage_param_struct)
                expanded_params_row = MapToExpandedParams(curr_params, curr_objectives, gear_materials, first_stage_param_struct, input_power, input_speed, output_speed, service_life);
            else
                expanded_params_row = MapToExpandedParams(curr_params, curr_objectives, gear_materials, [], input_power, input_speed, output_speed, service_life);
            end
        else
            % 如果gear_materials不存在，创建一个空结构体
            temp_gear_materials = struct();
            if ~isempty(first_stage_param_struct)
                expanded_params_row = MapToExpandedParams(curr_params, curr_objectives, temp_gear_materials, first_stage_param_struct, input_power, input_speed, output_speed, service_life);
            else
                expanded_params_row = MapToExpandedParams(curr_params, curr_objectives, temp_gear_materials, [], input_power, input_speed, output_speed, service_life);
            end
        end

        % 截取需要的部分以匹配param_names的长度
        expanded_params_array(row_idx, 1:min(length(expanded_params_row), length(param_names))) = ...
            expanded_params_row(1:min(length(expanded_params_row), length(param_names)));
    end

    % 创建扩展参数表
    pareto_table = array2table(expanded_params_array, 'VariableNames', param_names);

    % 对变位系数列进行四位小数处理
    shift_coeff_columns = {'x1', 'x2', 'x_sum1', 'xs2', 'xp2', 'xr2', 'xs3', 'xp3', 'xr3'};
    for i = 1:length(shift_coeff_columns)
        col_name = shift_coeff_columns{i};
        if ismember(col_name, pareto_table.Properties.VariableNames)
            pareto_table.(col_name) = round(pareto_table.(col_name), 4);
        end
    end

    % 对齿轮质量列进行两位小数处理
    mass_columns = {'M1', 'M2', 'Ms2', 'Mp2', 'Mr2', 'Ms3', 'Mp3', 'Mr3', 'TotalMass'};
    for i = 1:length(mass_columns)
        col_name = mass_columns{i};
        if ismember(col_name, pareto_table.Properties.VariableNames)
            pareto_table.(col_name) = round(pareto_table.(col_name), 2);
        end
    end

    % 对传动比列进行三位小数处理
    ratio_columns = {'i1', 'i2', 'i3', 'TotalRatio'};
    for i = 1:length(ratio_columns)
        col_name = ratio_columns{i};
        if ismember(col_name, pareto_table.Properties.VariableNames)
            pareto_table.(col_name) = round(pareto_table.(col_name), 3);
        end
    end

    % 对安全系数列进行三位小数处理
    safety_columns = {'SH1', 'SF1', 'SF2', 'SHsps2', 'SHspp2', 'SFsps2', 'SFspp2', 'SHprr2', 'SHprp2', 'SFprr2', 'SFprp2', ...
                     'SHsps3', 'SHspp3', 'SFsps3', 'SFspp3', 'SHprr3', 'SHprp3', 'SFprr3', 'SFprp3', 'SH', 'SF'};
    for i = 1:length(safety_columns)
        col_name = safety_columns{i};
        if ismember(col_name, pareto_table.Properties.VariableNames)
            pareto_table.(col_name) = round(pareto_table.(col_name), 3);
        end
    end

    % 对误差列进行两位小数处理
    error_columns = {'Error'};
    for i = 1:length(error_columns)
        col_name = error_columns{i};
        if ismember(col_name, pareto_table.Properties.VariableNames)
            pareto_table.(col_name) = round(pareto_table.(col_name), 2);
        end
    end

    % 对中心距列进行两位小数处理
    center_distance_columns = {'a1', 'a2', 'a3'};
    for i = 1:length(center_distance_columns)
        col_name = center_distance_columns{i};
        if ismember(col_name, pareto_table.Properties.VariableNames)
            pareto_table.(col_name) = round(pareto_table.(col_name), 2);
        end
    end

    % 按照第一个目标函数值（通常是质量）排序
    [~, sort_idx] = sort(pareto_solutions{alg_idx}(:, 1));
    pareto_table = pareto_table(sort_idx, :);
    
    % 处理算法名中的特殊字符，替换不能用于文件名的字符
    safe_alg_name = strrep(alg_name, '/', '_');
    
    % Save file
    csv_filename = fullfile(results_dir, [safe_alg_name '_最优解_run' num2str(run) '.csv']);
    writetable(pareto_table, csv_filename);
    mat_filename = fullfile(results_dir, [safe_alg_name '_最优解_run' num2str(run) '.mat']);
    % 不要修改pareto_solutions和pareto_variables的结构
    save(mat_filename, 'pareto_variables', 'pareto_solutions', 'param_names', 'obj_names');
end

% Ensure parameter names are defined
if ~exist('param_names', 'var') || isempty(param_names)
    param_names = {'m1', 'z1', 'z2', 'k_h1', 'x1', 'x2', 'x_sum1', 'beta1', 'alpha1', 'a1', 'i1', 'SH1', 'SF1', 'SF2', 'M1', 'M2', ...
        'mn2', 'zs2', 'zp2', 'zr2', 'n2', 'k_h2', 'xs2', 'xp2', 'xr2', 'x_sum2', 'beta2', 'alpha2', 'a2', 'i2', 'SHsps2', 'SHspp2', 'SFsps2', 'SFspp2', 'SHprr2', 'SHprp2', 'SFprr2', 'SFprp2', 'Ms2', 'Mp2', 'Mr2', ...
        'mn3', 'zs3', 'zp3', 'zr3', 'n3', 'k_h3', 'xs3', 'xp3', 'xr3', 'x_sum3', 'beta3', 'alpha3', 'a3', 'i3', 'SHsps3', 'SHspp3', 'SFsps3', 'SFspp3', 'SHprr3', 'SHprp3', 'SFprr3', 'SFprp3', 'Ms3', 'Mp3', 'Mr3', ...
        'TotalMass', 'SH', 'SF', 'TotalRatio', 'Error'};
end

% Check data validity
valid_data = true;
for i = 1:length(selected_algorithms)
    if isempty(all_results{i})
        valid_data = false;
    elseif any(isnan(all_results{i}(:))) || any(isinf(all_results{i}(:)))
        valid_data = false;
    elseif any(all_results{i}(:) > 1e8) || any(all_results{i}(:) < -1e8)
        valid_data = false;
    end
end

% Create structure, including all necessary parameters
optimization_results = struct();
optimization_results.all_results = all_results;
optimization_results.all_var_results = pareto_variables;
optimization_results.alg_names = algorithm_names(selected_algorithms);
optimization_results.selected_vars = param_names;

% 生成Pareto前沿图
fprintf('生成Pareto前沿图...\n');
PlotParetoFronts(optimization_results, metrics, results_dir, false);

% 最后生成HTML报告
% fprintf('所有算法运行完成，生成HTML报告\n');
CreateSummaryTables(selected_alg_names, pareto_variables, pareto_solutions, all_metrics, param_names, results_dir, input_speed, output_speed, problem, input_torque);
% fprintf('HTML报告已生成: %s\n', fullfile(results_dir, '优化结果综合报告.html'));

% 打开HTML报告
html_file = fullfile(results_dir, '优化结果综合报告.html');
try
    web(html_file, '-browser');
    % fprintf('HTML报告已在浏览器中打开\n');
catch
    fprintf('无法自动打开HTML报告，请手动打开文件: %s\n', html_file);
end

% 显示优化结果
% disp('=== 优化结果 ===');
% disp('最佳设计变量:');

% 从所有算法结果中找到最佳解
best_mass = Inf;
best_idx = 1;
best_alg = 1;

% 获取目标传动比
target_ratio = input_speed / output_speed;
ratio_tolerance = 0.02; % 2%的误差容忍度

% 检查所有算法的结果
for i = 1:length(pareto_solutions)
    if ~isempty(pareto_solutions{i}) && ~isempty(pareto_variables{i})
        for j = 1:size(pareto_variables{i}, 1)
            % 提取当前解的变量
            x_current = pareto_variables{i}(j, :);
            
            % 计算当前解的传动比
            z1_curr = round(x_current(2));
            z2_curr = round(x_current(3));
            zs2_curr = round(x_current(5));
            zp2_curr = round(x_current(6));
            zs3_curr = round(x_current(9));
            zp3_curr = round(x_current(10));
            
            % 计算内齿圈齿数
            zr2_curr = zs2_curr + 2 * zp2_curr;
            zr3_curr = zs3_curr + 2 * zp3_curr;
            
            % 计算传动比
            i1_curr = z2_curr / z1_curr;
            i2_curr = (1 + zr2_curr / zs2_curr);
            i3_curr = (1 + zr3_curr / zs3_curr);
            total_ratio_curr = i1_curr * i2_curr * i3_curr;
            
            % 检查传动比是否满足要求
            if abs(total_ratio_curr - target_ratio) / target_ratio <= ratio_tolerance
                % 如果传动比满足要求，检查质量
                if pareto_solutions{i}(j, 1) < best_mass
                    best_mass = pareto_solutions{i}(j, 1);
                    best_idx = j;
                    best_alg = i;
                end
            end
        end
    end
end

% 如果没有找到满足传动比要求的解，放宽容忍度再试一次
if best_mass == Inf
    % fprintf('未找到满足传动比要求(%.2f±%.1f%%)的解，放宽容忍度至±5%%\n', target_ratio, ratio_tolerance*100);
    ratio_tolerance = 0.05; % 放宽到5%
    
    % 再次检查所有算法的结果
    for i = 1:length(pareto_solutions)
        if ~isempty(pareto_solutions{i}) && ~isempty(pareto_variables{i})
            for j = 1:size(pareto_variables{i}, 1)
                % 提取当前解的变量
                x_current = pareto_variables{i}(j, :);
                
                % 计算当前解的传动比
                z1_curr = round(x_current(2));
                z2_curr = round(x_current(3));
                zs2_curr = round(x_current(5));
                zp2_curr = round(x_current(6));
                zs3_curr = round(x_current(9));
                zp3_curr = round(x_current(10));
                
                % 计算内齿圈齿数
                zr2_curr = zs2_curr + 2 * zp2_curr;
                zr3_curr = zs3_curr + 2 * zp3_curr;
                
                % 计算传动比
                i1_curr = z2_curr / z1_curr;
                i2_curr = (1 + zr2_curr / zs2_curr);
                i3_curr = (1 + zr3_curr / zs3_curr);
                total_ratio_curr = i1_curr * i2_curr * i3_curr;
                
                % 检查传动比是否满足要求
                if abs(total_ratio_curr - target_ratio) / target_ratio <= ratio_tolerance
                    % 如果传动比满足要求，检查质量
                    if pareto_solutions{i}(j, 1) < best_mass
                        best_mass = pareto_solutions{i}(j, 1);
                        best_idx = j;
                        best_alg = i;
                    end
                end
            end
        end
    end
end

% 如果仍然没有找到满足要求的解，选择传动比最接近的解
if best_mass == Inf
    % fprintf('未找到满足传动比要求的解，选择传动比最接近的解\n');
    
    best_ratio_diff = Inf;
    
    % 检查所有算法的结果
    for i = 1:length(pareto_solutions)
        if ~isempty(pareto_solutions{i}) && ~isempty(pareto_variables{i})
            for j = 1:size(pareto_variables{i}, 1)
                % 提取当前解的变量
                x_current = pareto_variables{i}(j, :);
                
                % 计算当前解的传动比
                z1_curr = round(x_current(2));
                z2_curr = round(x_current(3));
                zs2_curr = round(x_current(5));
                zp2_curr = round(x_current(6));
                zs3_curr = round(x_current(9));
                zp3_curr = round(x_current(10));
                
                % 计算内齿圈齿数
                zr2_curr = zs2_curr + 2 * zp2_curr;
                zr3_curr = zs3_curr + 2 * zp3_curr;
                
                % 计算传动比
                i1_curr = z2_curr / z1_curr;
                i2_curr = (1 + zr2_curr / zs2_curr);
                i3_curr = (1 + zr3_curr / zs3_curr);
                total_ratio_curr = i1_curr * i2_curr * i3_curr;
                
                % 计算传动比差异
                ratio_diff = abs(total_ratio_curr - target_ratio) / target_ratio;
                
                % 找到传动比最接近的解
                if ratio_diff < best_ratio_diff
                    best_ratio_diff = ratio_diff;
                    best_mass = pareto_solutions{i}(j, 1);
                    best_idx = j;
                    best_alg = i;
                end
            end
        end
    end
end

% 提取最佳解的变量
if ~isempty(pareto_variables) && ~isempty(pareto_variables{best_alg})
    x_best = pareto_variables{best_alg}(best_idx, :);
else
    disp('警告：未找到有效的优化结果');
    return;
end

% 检查x_best是否为向量
if ~isvector(x_best)
    disp('警告：最佳解不是向量格式');
    return;
end

% 确保有足够的变量
if length(x_best) < 24
    % 如果变量不足，使用默认值填充
    temp = zeros(1, 24);
    temp(1:length(x_best)) = x_best;
    x_best = temp;
end

% 提取设计变量并计算相关参数（用于后续数据处理，但不输出）
% 获取标准模数值
m1_values_20deg = [5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 20]; % 一级模数限定标准值
m1_values_25deg = [7, 9, 10, 11, 12, 13]; % 25度压力角标准模数

% 处理一级模数 - 使用限定的模数值
distances_m1 = abs(x_best(1) - m1_values_20deg);
[~, idx_m1] = min(distances_m1);
m1 = m1_values_20deg(idx_m1);

% 处理二级模数 - 20度压力角
distances_mn2 = abs(x_best(4) - m1_values_20deg);
[~, idx_mn2] = min(distances_mn2);
mn2 = m1_values_20deg(idx_mn2);

% 处理三级模数 - 根据压力角选择
pressure_angle_3_choice = round(x_best(24)); % 获取压力角选择
pressure_angle_3_choice = max(0, min(1, pressure_angle_3_choice)); % 确保在0-1范围内

if pressure_angle_3_choice == 0 % 使用20度压力角
    distances_mn3 = abs(x_best(8) - m1_values_20deg);
    [~, idx_mn3] = min(distances_mn3);
    mn3 = m1_values_20deg(idx_mn3);
else % 使用25度压力角
    distances_mn3 = abs(x_best(8) - m1_values_25deg);
    [~, idx_mn3] = min(distances_mn3);
    mn3 = m1_values_25deg(idx_mn3);
end

z1 = round(x_best(2));
z2 = round(x_best(3));
zs2 = round(x_best(5));
zp2 = round(x_best(6));
k_h2 = x_best(7);
zs3 = round(x_best(9));
zp3 = round(x_best(10));
k_h3 = x_best(11);
planets_count_2 = round(x_best(12));
planets_count_3 = round(x_best(13));
pressure_angle = x_best(14);
helix_angle_1 = x_best(15);
helix_angle_2 = x_best(16);
helix_angle_3 = x_best(17);
x1 = x_best(18);
x2 = x_best(19);
xs2 = x_best(20);
xp2 = x_best(21);
xs3 = x_best(22);
xp3 = x_best(23);

% 计算内齿圈齿数和其他必要参数（用于后续数据处理）
zr2 = zs2 + 2 * zp2;
zr3 = zs3 + 2 * zp3;
i1 = z2 / z1;
i2 = (1 + zr2 / zs2);
i3 = (1 + zr3 / zs3);
total_ratio = i1 * i2 * i3;

% 显示齿轮系统参数
% fprintf('=== 齿轮系统参数 ===\n');
% fprintf('总传动比: %.3f (目标: %.3f)\n', total_ratio, input_speed / output_speed);
% fprintf('一级传动比: %.2f\n', i1);
% fprintf('二级传动比: %.2f\n', i2);
% fprintf('三级传动比: %.2f\n', i3);
% fprintf('\n');

% fprintf('=== 齿数搭配 ===\n');
% fprintf('一级小齿轮齿数: %d\n', z1);
% fprintf('一级大齿轮齿数: %d\n', z2);
% fprintf('二级太阳轮齿数: %d\n', zs2);
% fprintf('二级行星轮齿数: %d\n', zp2);
% fprintf('二级内齿圈齿数: %d\n', zr2);
% fprintf('三级太阳轮齿数: %d\n', zs3);
% fprintf('三级行星轮齿数: %d\n', zp3);
% fprintf('三级内齿圈齿数: %d\n', zr3);
% fprintf('\n');

% fprintf('=== 模数 ===\n');
% fprintf('一级传动模数: %.3f mm\n', m1);
% fprintf('二级行星系模数: %.3f mm\n', mn2);
% fprintf('三级行星系模数: %.3f mm\n', mn3);
% fprintf('\n');

% fprintf('=== 压力角和螺旋角 ===\n');
% fprintf('压力角: %.1f°\n', pressure_angle);
% fprintf('一级传动螺旋角: %.1f°\n', helix_angle_1);
% fprintf('二级行星系螺旋角: %.1f°\n', helix_angle_2);
% fprintf('三级行星系螺旋角: %.1f°\n', helix_angle_3);
% fprintf('\n');

% fprintf('=== 变位系数 ===\n');
% fprintf('一级小齿轮变位系数: %.3f\n', x1);
% fprintf('一级大齿轮变位系数: %.3f\n', x2);
% fprintf('二级太阳轮变位系数: %.3f\n', xs2);
% fprintf('二级行星轮变位系数: %.3f\n', xp2);
% fprintf('二级内齿圈变位系数: %.3f\n', xr2);
% fprintf('三级太阳轮变位系数: %.3f\n', xs3);
% fprintf('三级行星轮变位系数: %.3f\n', xp3);
% fprintf('三级内齿圈变位系数: %.3f\n', xr3);
% fprintf('\n');

% fprintf('=== 齿宽 ===\n');
% fprintf('一级传动齿宽: %.2f mm\n', b1);
% fprintf('二级行星系齿宽: %.2f mm\n', b2);
% fprintf('三级行星系齿宽: %.2f mm\n', b3);
% fprintf('\n');

% fprintf('=== 其他参数 ===\n');
% fprintf('二级行星轮数量: %d\n', planets_count_2);
% fprintf('三级行星轮数量: %d\n', planets_count_3);
% fprintf('齿轮精度等级: %d\n', quality_grade);
% fprintf('\n');

% fprintf('=== 优化目标 ===\n');
% fprintf('总质量: %.2f kg\n', pareto_solutions{best_alg}(best_idx, 1));
% fprintf('最小弯曲安全系数: %.3f\n', -pareto_solutions{best_alg}(best_idx, 2));
% fprintf('最小接触安全系数: %.3f\n', -pareto_solutions{best_alg}(best_idx, 3));
% fprintf('\n');

% 保存所有结果
optimization_results.algorithms = selected_alg_names;
optimization_results.results = all_results;
optimization_results.metrics = all_metrics{run};
optimization_results.run_times = run;

% 添加变位系数约束信息
problem.shiftConstraints = struct();
problem.shiftConstraints.enabled = true;
problem.shiftConstraints.min_sum = 0.6;  % 变位系数和的下限
problem.shiftConstraints.max_sum = 1.2;  % 变位系数和的上限

% 移除齿宽系数参数的优化，因为我们现在使用中心距计算齿宽系数
% 更新参数说明
% fprintf('注意: 齿宽系数计算方法已更新，但仍作为优化变量\n');
% fprintf('  - 一级齿宽系数 (k_h1) 范围限制在 0.28-0.4 之间\n');
% fprintf('  - 二级和三级齿宽系数 (k_h2, k_h3) 不小于 0.6\n');
% fprintf('  - 齿宽计算公式: 齿宽 = 齿宽系数 × 实际中心距\n');
% fprintf('  - 实际中心距考虑了变位系数和螺旋角的影响\n');
% fprintf('  - 行星轮系中太阳轮、行星轮和内齿圈的齿宽保持一致\n\n');

% 注释掉齿宽系数的范围显示
% fprintf('%-25s %-10.2f %-10.2f\n', '二级齿宽系数 (k_h2)', lb(7), ub(7));
% fprintf('%-25s %-10.2f %-10.2f\n', '三级齿宽系数 (k_h3)', lb(11), ub(11));
% fprintf('%-25s %-10.2f %-10.2f\n', '一级齿宽系数 (k_h1)', lb(25), ub(25));

end  % End for run = 1:run_times loop

%% 多轮运行结果汇总
if run_times > 1
    % 汇总性能指标
    avg_metrics = struct();
    avg_metrics.GD = zeros(length(selected_algorithms), 1);
    avg_metrics.IGD = zeros(length(selected_algorithms), 1);
    avg_metrics.Spread = zeros(length(selected_algorithms), 1);
    avg_metrics.HV = zeros(length(selected_algorithms), 1);
    
    % 计算平均性能
    for r = 1:run_times
        avg_metrics.GD = avg_metrics.GD + all_metrics{r}.GD';
        avg_metrics.IGD = avg_metrics.IGD + all_metrics{r}.IGD';
        avg_metrics.Spread = avg_metrics.Spread + all_metrics{r}.Spread';
        avg_metrics.HV = avg_metrics.HV + all_metrics{r}.HV';
    end
    
    avg_metrics.GD = avg_metrics.GD / run_times;
    avg_metrics.IGD = avg_metrics.IGD / run_times;
    avg_metrics.Spread = avg_metrics.Spread / run_times;
    avg_metrics.HV = avg_metrics.HV / run_times;
    
    % 准备表格数据
    avg_table_data = cell(length(selected_algorithms), 5);
    
    for i = 1:length(selected_algorithms)
        % 填充表格数据
        avg_table_data{i, 1} = selected_alg_names{i};
        avg_table_data{i, 2} = avg_metrics.GD(i);
        avg_table_data{i, 3} = avg_metrics.IGD(i);
        avg_table_data{i, 4} = avg_metrics.Spread(i);
        avg_table_data{i, 5} = avg_metrics.HV(i);
    end
    
    % 保存平均性能结果
    avg_metrics.AlgorithmNames = selected_alg_names;
    save(fullfile(results_dir, 'average_metrics_results.mat'), 'avg_metrics');
end

fprintf('\n三级减速机齿轮系统优化完成！\n');

%% 辅助函数
% 注意：辅助函数已移至单独的文件中
% NonDominatedSort.m 和 CostFunctionWrapper.m 