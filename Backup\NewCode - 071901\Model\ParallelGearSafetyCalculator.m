function [SF1, SF2, SH] = ParallelGearSafetyCalculator(gear_params, load_params, material_params, calculation_method, safety_factors)
% ParallelGearSafetyCalculator 专门用于平行轴系外啮合齿轮安全系数计算
%   该函数根据ISO 6336标准计算平行轴齿轮的弯曲和接触安全系数
%   改进版：同时计算小齿轮和大齿轮的齿根弯曲安全系数
%
%   输入参数:
%   - gear_params: 齿轮几何参数结构体
%     .m: 模数 (mm)
%     .z: 齿数
%     .alpha: 压力角 (度)
%     .beta: 螺旋角 (度)
%     .b: 齿宽 (mm)
%     .x: 变位系数
%     .d: 分度圆直径 (可选，如未提供则计算)
%     .mating_z: 啮合齿轮齿数 (可选)
%     .mating_x: 啮合齿轮变位系数 (可选)
%     .mating_b: 啮合齿轮齿宽 (可选)
%     .mating_material: 啮合齿轮材料 (可选)
%   - load_params: 载荷参数结构体
%     .T: 扭矩 (N·m)
%     .n: 转速 (rpm)
%     .KA: 应用系数 (通常为1.25-1.5)
%     .service_life: 设计寿命 (h)
%   - material_params: 材料参数结构体 (从MaterialManager获取)
%   - calculation_method: 计算方法
%     'ISO6336': 使用ISO 6336标准计算 (默认)
%   - safety_factors: 最小安全系数结构体 (可选)
%     .SHmin: 接触强度的最小安全系数 (默认: 1.2)
%     .SFmin: 弯曲强度的最小安全系数 (默认: 1.2)
%
%   输出:
%   - SF1: 小齿轮弯曲安全系数
%   - SF2: 大齿轮弯曲安全系数
%   - SH: 接触安全系数

% 默认值检查
if nargin < 4 || isempty(calculation_method)
    calculation_method = 'ISO6336';
end

% 检查最小安全系数参数
if nargin < 5 || isempty(safety_factors)
    % 使用默认值
    SHmin = 1.2;  % 接触强度的最小安全系数
    SFmin = 1.2;  % 弯曲强度的最小安全系数
else
    % 从输入参数中获取
    if isfield(safety_factors, 'SHmin') && ~isempty(safety_factors.SHmin)
        SHmin = safety_factors.SHmin;
    else
        SHmin = 1.2;  % 默认值
    end

    if isfield(safety_factors, 'SFmin') && ~isempty(safety_factors.SFmin)
        SFmin = safety_factors.SFmin;
    else
        SFmin = 1.2;  % 默认值
    end
end

%% 1. 提取和准备基本参数
%% 1.1 提取基本几何参数
m = gear_params.m;
z = gear_params.z;
alpha = gear_params.alpha * pi / 180;  % 转换为弧度
beta = gear_params.beta * pi / 180;    % 转换为弧度
b = gear_params.b;
x = gear_params.x;

% 检查是否提供了分度圆直径，如果没有则计算
if isfield(gear_params, 'd') && ~isempty(gear_params.d)
    d = gear_params.d;
else
    if beta == 0
        d = m * z;  % 直齿轮
    else
        mt = m / cos(beta);  % 端面模数
        d = mt * z;  % 斜齿轮
    end
end

% 检查是否提供了啮合齿轮参数
if isfield(gear_params, 'mating_z') && ~isempty(gear_params.mating_z)
    z_mate = gear_params.mating_z;
else
    error('啮合齿轮参数缺失：未提供mating_z');
end

if isfield(gear_params, 'mating_x') && ~isempty(gear_params.mating_x)
    x_mate = gear_params.mating_x;
else
    x_mate = 0;  % 默认为无变位
end

if isfield(gear_params, 'mating_b') && ~isempty(gear_params.mating_b)
    b_mate = gear_params.mating_b;
else
    b_mate = b;  % 默认与主动轮相同
end

%% 1.2 提取材料参数
% 检查材料结构体格式，提取必要的属性
if isfield(material_params, 'iso_params')
    % 使用ISO格式的参数
    material = material_params.iso_params;
elseif isfield(material_params, 'bending_strength') && isfield(material_params, 'contact_strength')
    % 直接使用材料属性字段
    material = struct('sigmaFlim', material_params.bending_strength, ...
                     'sigmaHlim', material_params.contact_strength, ...
                     'E', material_params.youngs_modulus, ...
                     'poisson', material_params.poissons_ratio);
else
    % 直接使用传入的参数
    material = material_params;
end

% 对啮合齿轮材料进行同样的处理
if isfield(gear_params, 'mating_material') && ~isempty(gear_params.mating_material)
    mating_material_params = gear_params.mating_material;
    if isfield(mating_material_params, 'iso_params')
        mating_material = mating_material_params.iso_params;
    elseif isfield(mating_material_params, 'bending_strength') && isfield(mating_material_params, 'contact_strength')
        % 直接使用材料属性字段
        mating_material = struct('sigmaFlim', mating_material_params.bending_strength, ...
                                'sigmaHlim', mating_material_params.contact_strength, ...
                                'E', mating_material_params.youngs_modulus, ...
                                'poisson', mating_material_params.poissons_ratio);
    else
        mating_material = mating_material_params;
    end
else
    mating_material = material;  % 默认与主动轮相同
end

%% 1.3 提取载荷参数与基本计算
T = load_params.T;
n = load_params.n;
% KA = load_params.KA;  % 不再从输入参数中提取KA
KA = 1.75;  % 直接设置应用系数为固定值1.75
service_life = load_params.service_life;

% 计算啮合齿轮分度圆直径
if beta == 0
    d_mate = m * z_mate;  % 直齿轮
else
    mt = m / cos(beta);  % 端面模数
    d_mate = mt * z_mate;  % 斜齿轮
end

% 计算传动比
i = z_mate / z;
u = i;  % 传动比 (大齿轮/小齿轮)

% 计算切向力
Ft = 2000 * T / d;  % 切向力(N)，2000是单位转换因子
T_mate = T * i;
Ft_mate = 2000 * T_mate / d_mate;  % 大齿轮受到的切向力

% 计算线速度
v = pi * d * n / 60000;  % 线速度(m/s)

%% 2. 计算基础几何参数和重合度
% 计算基本几何参数
da1 = d + 2*m;  % 小齿轮齿顶圆直径
da2 = d_mate + 2*m;  % 大齿轮齿顶圆直径
db1 = d * cos(alpha);  % 小齿轮基圆直径
db2 = d_mate * cos(alpha);  % 大齿轮基圆直径

% 计算工作压力角 alpha_w
inv_alpha = tan(alpha) - alpha;  % 渐开线函数
inv_alpha_w = inv_alpha + 2*(x + x_mate)*tan(alpha)/(z + z_mate);
alpha_w = fzero(@(a) tan(a) - a - inv_alpha_w, alpha);  % 求解工作压力角

% 计算工作中心距
a_w = (d + d_mate) * cos(alpha) / (2 * cos(alpha_w));

% 计算端面重合度
epsilon_alpha = (sqrt(da1^2 - db1^2) + sqrt(da2^2 - db2^2) - 2*a_w*sin(alpha_w)) / (2*pi*m*cos(alpha));

% 计算轴向重合度 epsilon_beta
if beta == 0
    epsilon_beta = 0;  % 直齿轮的轴向重合度为0
else
    epsilon_beta = b * tan(beta) / (pi * m);
end

% 计算总重合度
epsilon_gamma = epsilon_alpha + epsilon_beta;

%% 3. 计算共用的修正系数
%% 3.1 载荷修正系数
% 计算动载系数 Kv (ISO 6336-1)
% 简化：使用固定值
Kv = 1.1;

% 计算载荷分布系数 KHbeta (ISO 6336-1)
% 按用户要求，将KHbeta统一设置为固定值1.3
KHbeta = 1.3;

% 计算齿向载荷分布系数 KFbeta (ISO 6336-1)
% 使用公式(7-73): KFβ = (KHβ)^N
h = 2.25 * m;  % 标准齿高约为2.25倍模数
N = (b/h)^2 / (1 + (b/h) + (b/h)^2);  % 计算幂指数N
KFbeta = KHbeta^N;  % 计算KFbeta

% 计算齿间载荷分布系数 KHalpha和KFalpha
KHalpha = 1.1;  % 对于一级平行轴系统（斜齿轮），固定KHalpha = 1.1
KFalpha = KHalpha;  % KFα = KHα

%% 4. 计算接触安全系数
%% 4.1 接触应力计算 (ISO 6336-2)
% 计算区域系数 ZH
% 1. 计算端面压力角 alpha_t
alpha_t = atan(tan(alpha) / cos(beta));  % 在此代码中，alpha已经是端面压力角

% 2. 计算基圆螺旋角 beta_b
beta_b = atan(tan(beta) * cos(alpha_t));

% 3. 使用工作压力角 alpha_w 作为端面啮合角 alpha_t_prime
alpha_t_prime = alpha_w;  % 已在上面计算了工作压力角

% 4. 使用完整公式计算ZH - 公式(7-61)
ZH = sqrt((2 * cos(beta_b) * cos(alpha_t_prime)) / (cos(alpha_t)^2 * sin(alpha_t_prime)));

% 计算弹性系数 ZE (ISO 6336-2) - 公式(7-53)
E1 = material.E;  % 小齿轮弹性模量 (Pa)
v1 = material.poisson;  % 小齿轮泊松比
E2 = mating_material.E;  % 大齿轮弹性模量 (Pa)
v2 = mating_material.poisson;  % 大齿轮泊松比
ZE = sqrt(1 / (pi * ((1-v1^2)/E1 + (1-v2^2)/E2))) / 1e3;  % 转换为MPa^0.5

% 计算接触比系数 Zepsilon (Contact Ratio Factor) (ISO 6336-2)
if beta == 0
    % 直齿轮 - 公式(7-63)
    Zepsilon = sqrt((4 - epsilon_alpha) / 3);
else
    % 斜齿轮
    if epsilon_beta < 1
        % 公式(7-64)
        Zepsilon = sqrt((4 - epsilon_alpha) / 3) * (1 - epsilon_beta) + sqrt(1 / epsilon_alpha) * (epsilon_beta / epsilon_alpha);
    else
        % 公式(7-65)
        Zepsilon = sqrt(1 / epsilon_alpha);
    end
end

% 计算螺旋角系数 Zbeta (Spiral Angle Factor) (ISO 6336-2)
if beta == 0
    Zbeta = 1.0;  % 直齿轮
else
    Zbeta = sqrt(cos(beta));  % 斜齿轮
end

% 计算尺寸系数 ZX (根据ISO 6336-2标准，与YX计算方法相同)
ZX = 1.05 - 0.01 * m;  % 适用于模数在5-25mm范围内

% 计算基本接触应力 sigma_H0 (MPa) - 公式(7-52)
sigma_H0 = ZH * ZE * Zepsilon * Zbeta * sqrt(Ft / (b * d) * (u + 1) / u);

% 计算实际接触应力 (MPa) - 公式(7-51)
sigmaH = sigma_H0 * sqrt(KA * Kv * KHbeta * KHalpha);

%% 4.2 许用接触应力计算 (MPa) - 公式(7-54)
% 获取材料的接触疲劳极限强度
sigmaHlim_MPa = material.sigmaHlim / 1e6;  % 转换单位为MPa

% 设置寿命系数ZNT
ZNT = 1.0;

% 简化计算 - 使用表7-14中的值
% 设置ZL*ZV*ZR的简化乘积为0.92
ZLZVZR = 0.92;

% 设置硬度比系数ZW
ZW = 1.0;

% 计算许用接触应力 (MPa) - 公式(7-54)
% 注意：SHmin已在函数开头根据输入参数设置
sigmaHP = (sigmaHlim_MPa / SHmin) * ZNT * ZLZVZR * ZW * ZX;

%% 4.3 接触安全系数计算
% 计算接触安全系数
SH = sigmaHP / sigmaH;

%% 5. 计算齿轮弯曲安全系数
% 设置弯曲强度共用系数
% 计算尺寸系数 YX (根据ISO 6336-3标准，适用于渗碳淬火钢)
YX = 1.05 - 0.01 * m;  % 适用于模数在5-25mm范围内

% 计算相对表面状况系数和相对齿根圆角敏感系数
YdrelT = 1.0;  % 相对齿根圆角敏感系数，简化取值为1.0
YRrelT = 1.0;  % 相对齿根表面状况系数，简化取值为1.0

% 设置寿命系数 YNT
YNT = 1.0;  % 计算弯曲强度的寿命系数

% 设置试验齿轮的应力修正系数
YST = 2.0;  % 试验齿轮的应力修正系数
% 注意：SFmin已在函数开头根据输入参数设置

% 计算重合度系数 Yepsilon
% 注意：Yepsilon是啮合齿轮对共用的系数，小齿轮和大齿轮使用相同的值
if beta == 0
    % 直齿轮的重合度系数
    Yepsilon = 0.25 + 0.75/epsilon_alpha;
else
    % 斜齿轮的重合度系数 - 更新为公式(7-75)
    % 计算基圆螺旋角 beta_b
    beta_b = acos(sqrt(1 - (sin(beta) * cos(alpha))^2));

    % 计算修正后的端面重合度 epsilon_alpha_n
    epsilon_alpha_n = epsilon_alpha / (cos(beta_b)^2);

    % 计算重合度系数
    Yepsilon = 0.25 + 0.75/epsilon_alpha_n;
end

%% 5.1 小齿轮(主动轮)弯曲系数计算
%% 5.1.1 齿根弯曲应力计算
% 计算齿形系数 YFa1 (ISO 6336-3)
YFa1 = calculateYFa(z);

% 计算应力修正系数 YSa1 (ISO 6336-3)
YSa1 = calculateYSa(z);

% 计算螺旋角系数 Ybeta1 (ISO 6336-3)
% 注意：Ybeta系数需要为每个齿轮单独计算，不同于Yepsilon是啮合对共用的
if beta == 0
    Ybeta1 = 1.0;  % 直齿轮
else
    % 对纵向重合度进行限制
    epsilon_beta_limited = epsilon_beta;
    if epsilon_beta > 1
        epsilon_beta_limited = 1;  % εβ > 1时，按εβ = 1计算
    end

    % 计算螺旋角系数 - 公式(7-76)
    Ybeta1 = 1 - epsilon_beta_limited * beta / 120;

    % 计算最小值限制 - 公式(7-77)
    Ybeta_min = 1 - 0.25 * epsilon_beta_limited;
    if Ybeta_min < 0.75
        Ybeta_min = 0.75;  % 保证Yβmin ≥ 0.75
    end

    % 应用最小值限制
    if Ybeta1 < Ybeta_min
        Ybeta1 = Ybeta_min;
    end
end

% 计算小齿轮的齿根应力基本值 sigma_F0 (MPa) - 公式(7-70)
sigma_F0_1 = (Ft / (b * m)) * YFa1 * YSa1 * Yepsilon * Ybeta1;

% 计算小齿轮的齿根弯曲应力 (MPa) - 公式(7-69)
sigmaF1 = sigma_F0_1 * KA * Kv * KFbeta * KFalpha;

%% 5.1.2 许用齿根弯曲应力计算
sigmaFlim_MPa1 = material.sigmaFlim / 1e6;  % 转换单位为MPa
sigmaFP1 = (sigmaFlim_MPa1 * YST * YNT) / SFmin * YdrelT * YRrelT * YX;

%% 5.1.3 弯曲安全系数计算
SF1 = sigmaFP1 / sigmaF1;

%% 5.2 大齿轮(从动轮)弯曲系数计算
%% 5.2.1 齿根弯曲应力计算
% 计算大齿轮的齿形系数 YFa2
YFa2 = calculateYFa(z_mate);

% 计算大齿轮的应力修正系数 YSa2
YSa2 = calculateYSa(z_mate);

% 计算大齿轮的螺旋角系数 Ybeta2
% 注意：每个齿轮都需要单独计算Ybeta系数
if beta == 0
    Ybeta2 = 1.0;  % 直齿轮
else
    % 对纵向重合度进行限制
    epsilon_beta_limited = epsilon_beta;
    if epsilon_beta > 1
        epsilon_beta_limited = 1;  % εβ > 1时，按εβ = 1计算
    end

    % 计算螺旋角系数 - 公式(7-76)
    Ybeta2 = 1 - epsilon_beta_limited * beta / 120;

    % 计算最小值限制 - 公式(7-77)
    Ybeta_min = 1 - 0.25 * epsilon_beta_limited;
    if Ybeta_min < 0.75
        Ybeta_min = 0.75;  % 保证Yβmin ≥ 0.75
    end

    % 应用最小值限制
    if Ybeta2 < Ybeta_min
        Ybeta2 = Ybeta_min;
    end
end

% 计算大齿轮的齿根应力基本值 sigma_F0 (MPa) - 公式(7-70)
sigma_F0_2 = (Ft_mate / (b_mate * m)) * YFa2 * YSa2 * Yepsilon * Ybeta2;

% 计算大齿轮的齿根弯曲应力 (MPa) - 公式(7-69)
sigmaF2 = sigma_F0_2 * KA * Kv * KFbeta * KFalpha;

%% 5.2.2 许用齿根弯曲应力计算
sigmaFlim_MPa2 = mating_material.sigmaFlim / 1e6;  % 转换单位为MPa
sigmaFP2 = (sigmaFlim_MPa2 * YST * YNT) / SFmin * YdrelT * YRrelT * YX;

%% 5.2.3 弯曲安全系数计算
SF2 = sigmaFP2 / sigmaF2;

end

function YSa = calculateYSa(z)
    % 基于表格数据的YSa计算函数
    % 根据齿数z返回对应的YSa值

    % 定义表格中的齿数和对应的YSa值
    z_table = [17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 35, 40, 45, 50, 60, 70, 80, 90, 100, 150, 200, 1000];
    YSa_table = [1.52, 1.53, 1.54, 1.55, 1.56, 1.57, 1.575, 1.58, 1.59, 1.595, 1.60, 1.61, 1.62, 1.625, 1.65, 1.67, 1.68, 1.70, 1.73, 1.75, 1.77, 1.78, 1.79, 1.83, 1.865, 1.97];

    % 限制z的范围
    if z < 17
        % 对于小于17的齿数，使用保守值
        YSa = 1.52;
    elseif z > 1000
        % 对于极大的齿数，使用极限值
        YSa = 1.97;
    else
        % 使用线性插值计算YSa
        YSa = interp1(z_table, YSa_table, z);
    end
end

function YFa = calculateYFa(z)
    % 基于表格数据的YFa计算函数
    % 根据齿数z返回对应的YFa值
    % 表10-5数据拟合

    % 定义表格中的齿数和对应的YFa值
    z_table = [17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 35, 40, 45, 50, 60, 70, 80, 90, 100, 150, 200, 1000];
    YFa_table = [2.97, 2.91, 2.85, 2.80, 2.76, 2.72, 2.69, 2.65, 2.62, 2.60, 2.57, 2.55, 2.53, 2.52, 2.45, 2.40, 2.35, 2.32, 2.28, 2.24, 2.22, 2.20, 2.18, 2.14, 2.12, 2.06];

    % 限制z的范围
    if z < 17
        % 对于小于17的齿数，使用保守值
        YFa = 2.97;
    elseif z > 1000
        % 对于极大的齿数，使用极限值
        YFa = 2.06;
    else
        % 使用线性插值计算YFa
        YFa = interp1(z_table, YFa_table, z);
    end
end
