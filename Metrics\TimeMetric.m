function time_data = TimeMetric(action, varargin)
% TimeMetric - 时间测量工具函数
% 用于测量算法执行时间的工具函数
%
% 用法:
%   timer_id = TimeMetric('start')              % 开始计时
%   elapsed = TimeMetric('stop', timer_id)      % 停止计时并返回耗时
%   TimeMetric('reset')                         % 重置所有计时器
%   times = TimeMetric('get_all')               % 获取所有记录的时间
%
% 输入:
%   action - 操作类型: 'start', 'stop', 'reset', 'get_all'
%   varargin - 可选参数，根据action不同而不同
%
% 输出:
%   time_data - 根据action返回不同类型的数据
%
% 示例:
%   % 测量单个算法时间
%   timer_id = TimeMetric('start');
%   % ... 运行算法 ...
%   elapsed_time = TimeMetric('stop', timer_id);
%
%   % 测量多个算法时间
%   TimeMetric('reset');
%   for i = 1:n_algorithms
%       timer_id = TimeMetric('start');
%       % ... 运行第i个算法 ...
%       times(i) = TimeMetric('stop', timer_id);
%   end

persistent timers;
persistent timer_count;

% 初始化
if isempty(timers)
    timers = containers.Map('KeyType', 'int32', 'ValueType', 'double');
    timer_count = 0;
end

switch lower(action)
    case 'start'
        % 开始计时
        timer_count = timer_count + 1;
        timers(timer_count) = tic;
        time_data = timer_count;

    case 'stop'
        % 停止计时
        if nargin < 2
            error('TimeMetric: stop操作需要提供timer_id');
        end
        timer_id = varargin{1};

        if ~timers.isKey(timer_id)
            error('TimeMetric: 无效的timer_id');
        end

        start_time = timers(timer_id);
        time_data = toc(start_time);
        timers.remove(timer_id);

    case 'reset'
        % 重置所有计时器
        timers = containers.Map('KeyType', 'int32', 'ValueType', 'double');
        timer_count = 0;
        time_data = [];

    case 'get_all'
        % 获取所有活跃的计时器
        keys = cell2mat(timers.keys);
        if isempty(keys)
            time_data = [];
        else
            time_data = struct();
            for i = 1:length(keys)
                time_data.(sprintf('timer_%d', keys(i))) = toc(timers(keys(i)));
            end
        end

    otherwise
        error('TimeMetric: 未知的操作类型 "%s"', action);
end
end