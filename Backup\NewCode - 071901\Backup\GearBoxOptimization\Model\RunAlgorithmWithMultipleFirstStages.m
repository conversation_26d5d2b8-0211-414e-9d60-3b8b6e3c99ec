function [pop, F, best_solution] = RunAlgorithmWithMultipleFirstStages(algorithm_id, problem, params, fixed_first_stage_params_struct, input_power, input_speed, output_speed, service_life, contact_safety_factor, bending_safety_factor, gear_material, gear_materials, quality_grade)
% RunAlgorithmWithMultipleFirstStages 使用多组一级参数运行优化算法
%
%   输入参数:
%   - algorithm_id: 算法ID (1-9)
%   - problem: 优化问题结构体
%   - params: 算法参数
%   - fixed_first_stage_params_struct: 一级平行轴系固定参数结构体数组
%   - input_power: 输入功率 (kW)
%   - input_speed: 输入转速 (rpm)
%   - output_speed: 输出转速 (rpm)
%   - service_life: 设计寿命 (h)
%   - contact_safety_factor: 接触安全系数要求
%   - bending_safety_factor: 弯曲安全系数要求
%   - gear_material: 齿轮材料参数
%   - gear_materials: 详细的齿轮材料参数
%   - quality_grade: 齿轮精度等级
%
%   输出:
%   - pop: 最优种群
%   - F: 最优目标值
%   - best_solution: 最佳解信息，包含最佳一级参数索引

% 获取算法名称
algorithm_names = {'NSGA-II', 'NSGA-III', 'SPEA2', 'MOEA/D', 'MOEA/D-DE', 'MOEA/D-M2M', 'MOPSO', 'MOGWO', 'MOWOA'};
alg_name = algorithm_names{algorithm_id};

fprintf('\n==================== %d. %s ====================\n', algorithm_id, alg_name);
fprintf('共有 %d 组一级参数可供选择\n', length(fixed_first_stage_params_struct));

% 检查参数结构体是否有效
if ~isstruct(fixed_first_stage_params_struct) || isempty(fixed_first_stage_params_struct)
    error('一级参数结构体数组无效或为空');
end

% 构建有效参数索引列表
valid_params_indices = [];
invalid_params_indices = [];

% 检查一级参数组合是否完整并进行修复
for i = 1:length(fixed_first_stage_params_struct)
    % 检查必要的字段是否存在
    required_fields = {'m', 'z1', 'z2', 'beta', 'alpha', 'x1', 'x2', 'k_h', 'b', 'a'};
    missing_fields = false;
    
    for j = 1:length(required_fields)
        if ~isfield(fixed_first_stage_params_struct(i), required_fields{j})
            fprintf('警告：第%d组一级参数缺少字段 "%s"\n', i, required_fields{j});
            missing_fields = true;
        elseif ~isscalar(fixed_first_stage_params_struct(i).(required_fields{j}))
            fprintf('警告：第%d组一级参数字段 "%s" 不是标量\n', i, required_fields{j});
            missing_fields = true;
        elseif isnan(fixed_first_stage_params_struct(i).(required_fields{j})) || ...
               isinf(fixed_first_stage_params_struct(i).(required_fields{j}))
            fprintf('警告：第%d组一级参数字段 "%s" 值无效\n', i, required_fields{j});
            missing_fields = true;
        end
    end
    
    % 如果发现问题，修复该参数组
    if missing_fields
        fprintf('尝试修复第%d组一级参数...\n', i);
        
        % 查找有效的参数组作为模板
        template_found = false;
        for k = 1:length(valid_params_indices)
            template_idx = valid_params_indices(k);
            template_found = true;
            break;
        end
        
        if template_found
            % 使用之前有效的参数组作为模板
            fprintf('使用第%d组参数的值作为模板进行修复\n', template_idx);
            
            % 创建一个新结构体来保存修复后的值
            temp_struct = struct();
            
            % 复制所有必需字段
            for j = 1:length(required_fields)
                field_name = required_fields{j};
                temp_struct.(field_name) = fixed_first_stage_params_struct(template_idx).(field_name);
            end
            
            % 更新结构体
            fixed_first_stage_params_struct(i) = temp_struct;
            
            % 添加到有效参数列表
            valid_params_indices = [valid_params_indices, i];
        else
            % 如果没有有效模板，使用默认值
            fprintf('使用默认值进行修复\n');
            
            % 创建一个新结构体
            temp_struct = struct();
            
            % 设置默认值
            temp_struct.m = 10;      % 默认模数
            temp_struct.z1 = 20;     % 默认小齿轮齿数
            temp_struct.z2 = 60;     % 默认大齿轮齿数
            temp_struct.beta = 10;   % 默认螺旋角
            temp_struct.alpha = 20;  % 默认压力角
            temp_struct.x1 = 0.4;    % 默认小齿轮变位系数
            temp_struct.x2 = 0.2;    % 默认大齿轮变位系数
            temp_struct.k_h = 0.35;  % 默认齿宽系数
            temp_struct.b = 100;     % 默认齿宽
            temp_struct.a = 400;     % 默认中心距
            
            % 更新结构体
            fixed_first_stage_params_struct(i) = temp_struct;
            
            % 添加到有效参数列表
            valid_params_indices = [valid_params_indices, i];
        end
    else
        % 参数有效，添加到有效参数列表
        valid_params_indices = [valid_params_indices, i];
    end
end

% 修改原始问题定义，添加错误处理
original_cost_function = problem.costFunction;
problem.costFunction = @robust_cost_function;

% 添加错误处理的代价函数包装
function [objectives, constraints] = robust_cost_function(x)
    try
        % 确保x的第一个元素是有效的参数索引
        first_stage_idx = round(x(1));
        first_stage_idx = max(1, min(first_stage_idx, length(fixed_first_stage_params_struct)));
        
        % 创建输入向量的副本
        x_copy = x;
        x_copy(1) = first_stage_idx;
        
        % 调用原始的cost function
        [objectives, constraints] = original_cost_function(x_copy);
    catch e
        fprintf('警告：使用一级参数组 #%d 评估时出错: %s\n', round(x(1)), e.message);
        
        % 返回一个无效的结果
        objectives = [Inf, 0, 0];
        constraints = Inf;
    end
    
    % 检查结果的有效性
    if any(isnan(objectives)) || any(isinf(objectives)) || ~isequal(size(objectives), [1, 3])
        fprintf('警告：使用一级参数组 #%d 得到无效结果，使用默认值\n', round(x(1)));
        objectives = [Inf, 0, 0];
        constraints = Inf;
    end
end

try
    % 运行优化算法
    switch algorithm_id
        case 1 % NSGA-II
            [pop, F] = RunNSGAII(problem, params);
        case 2 % NSGA-III
            [pop, F] = RunNSGAIII(problem, params);
        case 3 % SPEA2
            [pop, F] = RunSPEA2(problem, params);
        case 4 % MOEA/D
            [pop, F] = RunMOEAD(problem, params);
        case 5 % MOEA/D-DE
            [pop, F] = RunMOEAD_DE(problem, params);
        case 6 % MOEA/D-M2M
            [pop, F] = RunMOEAD_M2M(problem, params);
        case 7 % MOPSO
            [pop, F] = RunMOPSO(problem, params);
        case 8 % MOGWO
            [pop, F] = RunMOGWO(problem, params);
        case 9 % MOWOA
            [pop, F] = RunMOWOA(problem, params);
    end
catch e
    % 捕获并显示错误
    fprintf('优化算法运行出错: %s\n', e.message);
    pop = [];
    F = [];
end

% 检查结果并生成默认值
if isempty(pop) || isempty(F) || size(pop, 1) == 0 || size(F, 1) == 0
    fprintf('警告：优化算法未返回有效结果，将使用默认值\n');
    
    % 创建默认的结果
    pop = zeros(1, problem.nVar);
    pop(1) = 1; % 使用第一组一级参数
    F = [Inf, 0, 0]; % 默认目标值
    
    % 创建默认的最佳解信息
    best_solution = struct();
    best_solution.best_first_stage_idx = 1;
    best_solution.best_first_stage_params = fixed_first_stage_params_struct(1);
    best_solution.best_objectives = [Inf, 0, 0];
    
    return;
end

% 修正种群数据
for i = 1:size(pop, 1)
    % 确保一级参数索引是有效的整数
    pop(i, 1) = max(1, min(round(pop(i, 1)), length(fixed_first_stage_params_struct)));
end

% 确保F的维度正确
if size(F, 2) ~= 3
    fprintf('警告：目标值矩阵维度不正确，尝试修复\n');
    
    % 如果F的列数不是3，尝试修复
    if size(F, 2) > 3
        % 如果有多余的列，截断
        F = F(:, 1:3);
    else
        % 如果列不足，补充默认值
        F_fixed = zeros(size(F, 1), 3);
        F_fixed(:, 1:size(F, 2)) = F;
        for i = size(F, 2)+1:3
            F_fixed(:, i) = 0; % 使用0填充缺失的目标值
        end
        F = F_fixed;
    end
end

% 统计每个一级参数组合的使用频率
first_stage_indices = round(pop(:, 1));
first_stage_indices = max(1, min(first_stage_indices, length(fixed_first_stage_params_struct)));
[unique_indices, ~, ic] = unique(first_stage_indices);
counts = accumarray(ic, 1);

% 找出使用最多的一级参数组合
[max_count, max_idx] = max(counts);
most_used_idx = unique_indices(max_idx);
fprintf('最常用的一级参数组合是 #%d，在 %d 个解中使用了 %d 次\n', most_used_idx, size(pop, 1), max_count);

% 创建最佳解信息
best_solution = struct();
best_solution.best_first_stage_idx = most_used_idx;
best_solution.best_first_stage_params = fixed_first_stage_params_struct(most_used_idx);

% 找出使用最常用一级参数组合的最佳解
best_idx = find(first_stage_indices == most_used_idx);
if ~isempty(best_idx)
    [~, min_idx] = min(F(best_idx, 1));
    best_solution.best_objectives = F(best_idx(min_idx), :);
    best_solution.best_position = pop(best_idx(min_idx), :);
else
    best_solution.best_objectives = [Inf, 0, 0];
    best_solution.best_position = zeros(1, size(pop, 2));
end

% 不再输出算法完成信息，由主程序统一处理
end 