function [population, objectives] = RunMOPSO(problem, params)
% RunMOPSO - 运行多目标粒子群优化算法
%
% 输入:
%   problem - 包含问题信息的结构体
%   params  - 算法参数
%
% 输出:
%   population - 最终种群
%   objectives - 最终的非支配解集目标函数值
%
% 参考文献:
% <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON> <PERSON><PERSON>, Handling multiple
% objectives with particle swarm optimization, IEEE Transactions on
% Evolutionary Computation, 2004, 8(3): 256-279.

%% 初始化参数
nVar = problem.nVar;
varSize = [1, nVar];
varMin = problem.varMin;
varMax = problem.varMax;
nPop = params.nPop;
maxIt = params.maxIt;
nObj = problem.nObj;

% PSO特有参数
w = 0.5;          % 惯性权重
wdamp = 0.99;     % 惯性权重衰减
c1 = 1;           % 个体学习因子
c2 = 2;           % 全局学习因子
nRep = 100;       % 存储库大小
nGrid = 7;        % 网格数量

% 确保有评价次数计数器
if ~isfield(problem, 'FE')
    problem.FE = 0;
end

%% 粒子结构定义
empty_particle.Position = [];
empty_particle.Velocity = [];
empty_particle.Cost = [];
empty_particle.Best.Position = [];
empty_particle.Best.Cost = [];
empty_particle.GridIndex = [];
empty_particle.GridSubIndex = [];

%% 初始化粒子群
particle = repmat(empty_particle, nPop, 1);

for i = 1:nPop
    particle(i).Position = unifrnd(varMin, varMax, varSize);
    particle(i).Velocity = zeros(varSize);
    
    % 对离散变量特殊处理
    if isfield(problem, 'discreteVars')
        for j = 1:length(problem.discreteVars)
            idx = problem.discreteVars(j).idx;
            if problem.discreteVars(j).isInteger
                particle(i).Position(idx) = round(particle(i).Position(idx));
            else
                % 找到最接近的离散值
                values = problem.discreteVars(j).values;
                [~, closest_idx] = min(abs(particle(i).Position(idx) - values));
                particle(i).Position(idx) = values(closest_idx);
            end
        end
    end
    
    particle(i).Cost = problem.costFunction(particle(i).Position);
    problem.FE = problem.FE + 1;
    
    % 初始化个体最优
    particle(i).Best.Position = particle(i).Position;
    particle(i).Best.Cost = particle(i).Cost;
end

% 确定非支配解
particle = DetermineDomination(particle);

% 初始化存储库
rep = particle(~[particle.IsDominated]);

% 创建网格
Grid = CreateGrid(rep, nGrid, 0.1);

% 更新网格索引
for i = 1:numel(rep)
    rep(i) = FindGridIndex(rep(i), Grid);
end

%% 优化主循环
for it = 1:maxIt
    for i = 1:nPop
        % 选择全局最优（从存储库中选择）
        leader = SelectLeader(rep, 2);
        
        % 更新速度
        particle(i).Velocity = w * particle(i).Velocity ...
            + c1 * rand(varSize) .* (particle(i).Best.Position - particle(i).Position) ...
            + c2 * rand(varSize) .* (leader.Position - particle(i).Position);
        
        % 更新位置
        particle(i).Position = particle(i).Position + particle(i).Velocity;
        
        % 边界处理
        particle(i).Position = max(particle(i).Position, varMin);
        particle(i).Position = min(particle(i).Position, varMax);
        
        % 对离散变量特殊处理
        if isfield(problem, 'discreteVars')
            for j = 1:length(problem.discreteVars)
                idx = problem.discreteVars(j).idx;
                if problem.discreteVars(j).isInteger
                    particle(i).Position(idx) = round(particle(i).Position(idx));
                else
                    values = problem.discreteVars(j).values;
                    [~, closest_idx] = min(abs(particle(i).Position(idx) - values));
                    particle(i).Position(idx) = values(closest_idx);
                end
            end
        end
        
        % 评估
        particle(i).Cost = problem.costFunction(particle(i).Position);
        problem.FE = problem.FE + 1;
        
        % 更新个体最优
        if Dominates(particle(i), particle(i).Best) || ...
           (~Dominates(particle(i).Best, particle(i)) && rand < 0.5)
            particle(i).Best.Position = particle(i).Position;
            particle(i).Best.Cost = particle(i).Cost;
        end
    end
    
    % 确定非支配解
    particle = DetermineDomination(particle);
    
    % 添加非支配粒子到存储库
    non_dominated_particle = particle(~[particle.IsDominated]);
    rep = [rep; non_dominated_particle];
    
    % 确定存储库中的非支配解
    rep = DetermineDomination(rep);
    rep = rep(~[rep.IsDominated]);
    
    % 更新网格
    Grid = CreateGrid(rep, nGrid, 0.1);
    
    % 更新网格索引
    for i = 1:numel(rep)
        rep(i) = FindGridIndex(rep(i), Grid);
    end
    
    % 如果存储库过大，删除一些解
    if numel(rep) > nRep
        Extra = numel(rep) - nRep;
        for e = 1:Extra
            rep = DeleteOneRepMember(rep);
        end
    end
    
    % 更新惯性权重
    w = w * wdamp;
    
    % 显示迭代信息
    if mod(it, 10) == 0 || it == maxIt
        disp(['MOPSO: 迭代 ' num2str(it) '/' num2str(maxIt) ', 存储库大小 = ' num2str(numel(rep)) ', 评价次数 = ' num2str(problem.FE)]);
    end
end

%% 提取最终结果
% 将结构体数组转换为数值矩阵
if ~isempty(rep)
    population = vertcat(rep.Position);  % 提取位置矩阵
    objectives = vertcat(rep.Cost);      % 提取目标函数值矩阵
else
    population = [];
    objectives = [];
end

end

%% ========== 辅助函数 ==========

function pop = DetermineDomination(pop)
% 确定支配关系
nPop = numel(pop);

for i = 1:nPop
    pop(i).IsDominated = false;
end

for i = 1:nPop-1
    for j = i+1:nPop
        if Dominates(pop(i), pop(j))
            pop(j).IsDominated = true;
        elseif Dominates(pop(j), pop(i))
            pop(i).IsDominated = true;
        end
    end
end
end

function Grid = CreateGrid(pop, nGrid, alpha)
% 创建网格
if isempty(pop)
    Grid = [];
    return;
end

costs = vertcat(pop.Cost);
nObj = size(costs, 2);

cmin = min(costs, [], 1);
cmax = max(costs, [], 1);

dc = cmax - cmin;
cmin = cmin - alpha * dc;
cmax = cmax + alpha * dc;

nGrid = ones(1, nObj) * nGrid;

Grid.LowerBounds = cmin;
Grid.UpperBounds = cmax;
Grid.nGrid = nGrid;
Grid.Delta = (cmax - cmin) ./ nGrid;
end

function particle = FindGridIndex(particle, Grid)
% 找到网格索引
if isempty(Grid)
    particle.GridIndex = [];
    particle.GridSubIndex = [];
    return;
end

nObj = numel(particle.Cost);

nGrid = Grid.nGrid;
LowerBounds = Grid.LowerBounds;
Delta = Grid.Delta;

particle.GridSubIndex = 1 + floor((particle.Cost - LowerBounds) ./ Delta);
particle.GridSubIndex = max(particle.GridSubIndex, 1);
particle.GridSubIndex = min(particle.GridSubIndex, nGrid);

particle.GridIndex = 1;
for j = 1:nObj
    particle.GridIndex = particle.GridIndex + (particle.GridSubIndex(j) - 1) * prod(nGrid(1:j-1));
end
end

function leader = SelectLeader(rep, beta)
% 选择领导者
if isempty(rep)
    leader = [];
    return;
end

% 计算每个网格的占用数量
GridIndices = [rep.GridIndex];
OccupiedCells = unique(GridIndices);
N = zeros(size(OccupiedCells));

for k = 1:numel(OccupiedCells)
    N(k) = sum(GridIndices == OccupiedCells(k));
end

% 计算选择概率（偏向于较少占用的网格）
P = exp(-beta * N);
P = P / sum(P);

% 轮盘赌选择网格
sc = cumsum(P);
r = rand;
SelectedCellIndex = find(sc >= r, 1, 'first');
SelectedCell = OccupiedCells(SelectedCellIndex);

% 从选中的网格中随机选择一个解
InSelectedCell = find(GridIndices == SelectedCell);
n = numel(InSelectedCell);

SelectedIndex = InSelectedCell(randi([1 n]));

leader = rep(SelectedIndex);
end

function rep = DeleteOneRepMember(rep)
% 删除一个存储库成员
if isempty(rep)
    return;
end

% 计算每个网格的占用数量
GridIndices = [rep.GridIndex];
OccupiedCells = unique(GridIndices);
N = zeros(size(OccupiedCells));

for k = 1:numel(OccupiedCells)
    N(k) = sum(GridIndices == OccupiedCells(k));
end

% 找到占用最多的网格
[~, MaxIndex] = max(N);
SelectedCell = OccupiedCells(MaxIndex);

% 从该网格中随机删除一个解
InSelectedCell = find(GridIndices == SelectedCell);
n = numel(InSelectedCell);

SelectedIndex = InSelectedCell(randi([1 n]));

rep(SelectedIndex) = [];
end

function b = Dominates(p, q)
% 判断p是否支配q
if isfield(p, 'Cost')
    p_cost = p.Cost;
else
    p_cost = p;
end

if isfield(q, 'Cost')
    q_cost = q.Cost;
else
    q_cost = q;
end

b = all(p_cost <= q_cost) && any(p_cost < q_cost);
end
