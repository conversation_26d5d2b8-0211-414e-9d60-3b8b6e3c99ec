function [population, objectives] = RunSPEA2(problem, params)
% RunSPEA2 - 运行SPEA2算法用于多目标优化
%
% 输入:
%   problem - 包含问题信息的结构体
%   params  - 算法参数
%
% 输出:
%   population - 最终种群
%   objectives - 最终的非支配解集目标函数值
%
% 参考文献:
% <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, SPEA2: Improving the strength
% Pareto evolutionary algorithm, TIK-Report 103, 2001.

%% 初始化参数
nVar = problem.nVar;
varSize = [1, nVar];
varMin = problem.varMin;
varMax = problem.varMax;
nPop = params.nPop;
maxIt = params.maxIt;
nObj = problem.nObj;
pCrossover = params.pCrossover;
pMutation = params.pMutation;

% SPEA2特有参数
nArchive = nPop;  % 档案大小
K = floor(sqrt(nPop + nArchive));  % k-最近邻参数

% 确保有评价次数计数器
if ~isfield(problem, 'FE')
    problem.FE = 0;
end

%% 个体结构定义
empty_individual.Position = [];
empty_individual.Cost = [];
empty_individual.S = [];      % 强度值
empty_individual.R = [];      % 原始适应度
empty_individual.D = [];      % 密度
empty_individual.F = [];      % 最终适应度

%% 初始化种群
pop = repmat(empty_individual, nPop, 1);

for i = 1:nPop
    pop(i).Position = unifrnd(varMin, varMax, varSize);
    
    % 对离散变量特殊处理
    if isfield(problem, 'discreteVars')
        for j = 1:length(problem.discreteVars)
            idx = problem.discreteVars(j).idx;
            if problem.discreteVars(j).isInteger
                pop(i).Position(idx) = round(pop(i).Position(idx));
            else
                % 找到最接近的离散值
                values = problem.discreteVars(j).values;
                [~, closest_idx] = min(abs(pop(i).Position(idx) - values));
                pop(i).Position(idx) = values(closest_idx);
            end
        end
    end
    
    pop(i).Cost = problem.costFunction(pop(i).Position);
    problem.FE = problem.FE + 1;
end

% 初始化档案
archive = [];

%% 优化主循环
for it = 1:maxIt
    % 合并种群和档案
    union = [pop; archive];
    nUnion = numel(union);
    
    % 计算强度值S(i)
    for i = 1:nUnion
        union(i).S = 0;
        for j = 1:nUnion
            if i ~= j && Dominates(union(i), union(j))
                union(i).S = union(i).S + 1;
            end
        end
    end
    
    % 计算原始适应度R(i)
    for i = 1:nUnion
        union(i).R = 0;
        for j = 1:nUnion
            if i ~= j && Dominates(union(j), union(i))
                union(i).R = union(i).R + union(j).S;
            end
        end
    end
    
    % 计算密度D(i)
    if nUnion > 1
        PopObj = vertcat(union.Cost);
        Distance = pdist2(PopObj, PopObj);
        Distance = Distance + diag(inf(nUnion, 1));  % 排除自身距离
        Distance = sort(Distance, 2);
        
        for i = 1:nUnion
            if K <= size(Distance, 2)
                union(i).D = 1 / (Distance(i, K) + 2);
            else
                union(i).D = 1 / (Distance(i, end) + 2);
            end
        end
    else
        union(1).D = 0;
    end
    
    % 计算最终适应度F(i)
    for i = 1:nUnion
        union(i).F = union(i).R + union(i).D;
    end
    
    % 环境选择
    archive = EnvironmentalSelection(union, nArchive);
    
    % 如果是最后一次迭代，跳过交叉变异
    if it == maxIt
        break;
    end
    
    % 交叉变异生成新种群
    pop = repmat(empty_individual, nPop, 1);
    for i = 1:nPop/2
        % 锦标赛选择
        p1 = TournamentSelection(archive);
        p2 = TournamentSelection(archive);
        
        % 交叉
        [pop(2*i-1).Position, pop(2*i).Position] = Crossover(p1.Position, p2.Position, pCrossover, varMin, varMax);
        
        % 变异
        pop(2*i-1).Position = Mutate(pop(2*i-1).Position, pMutation, varMin, varMax);
        pop(2*i).Position = Mutate(pop(2*i).Position, pMutation, varMin, varMax);
        
        % 离散变量处理
        if isfield(problem, 'discreteVars')
            for k = [2*i-1, 2*i]
                for j = 1:length(problem.discreteVars)
                    idx = problem.discreteVars(j).idx;
                    if problem.discreteVars(j).isInteger
                        pop(k).Position(idx) = round(pop(k).Position(idx));
                    else
                        values = problem.discreteVars(j).values;
                        [~, closest_idx] = min(abs(pop(k).Position(idx) - values));
                        pop(k).Position(idx) = values(closest_idx);
                    end
                end
            end
        end
        
        % 评估
        pop(2*i-1).Cost = problem.costFunction(pop(2*i-1).Position);
        pop(2*i).Cost = problem.costFunction(pop(2*i).Position);
        problem.FE = problem.FE + 2;
    end
    
    % 显示迭代信息
    if mod(it, 10) == 0 || it == maxIt
        % 计算非支配解数量
        n_nondom = sum([archive.R] == 0);
        disp(['SPEA2: 迭代 ' num2str(it) '/' num2str(maxIt) ', 非支配解数量 = ' num2str(n_nondom) ', 评价次数 = ' num2str(problem.FE)]);
    end
end

%% 提取最终结果
% 选择非支配解
nondom_indices = find([archive.R] == 0);
if ~isempty(nondom_indices)
    population = archive(nondom_indices);
    objectives = vertcat(population.Cost);
else
    population = archive;
    objectives = vertcat(archive.Cost);
end

end

%% ========== 辅助函数 ==========

function archive = EnvironmentalSelection(union, nArchive)
% 环境选择
nUnion = numel(union);

% 首先选择所有非支配解
nondom_indices = find([union.R] == 0);
nondom = union(nondom_indices);

if numel(nondom) < nArchive
    % 非支配解不足，添加支配解
    dom_indices = find([union.R] > 0);
    dom = union(dom_indices);
    
    % 按适应度排序
    [~, sorted_indices] = sort([dom.F]);
    dom = dom(sorted_indices);
    
    % 添加到档案
    needed = nArchive - numel(nondom);
    if numel(dom) >= needed
        archive = [nondom; dom(1:needed)];
    else
        archive = [nondom; dom];
    end
    
elseif numel(nondom) == nArchive
    % 非支配解数量正好
    archive = nondom;
    
else
    % 非支配解过多，需要截断
    archive = TruncateArchive(nondom, nArchive);
end
end

function archive = TruncateArchive(pop, nArchive)
% 截断档案
nPop = numel(pop);

if nPop <= nArchive
    archive = pop;
    return;
end

% 计算距离矩阵
PopObj = vertcat(pop.Cost);
Distance = pdist2(PopObj, PopObj);
Distance = Distance + diag(inf(nPop, 1));

% 逐个移除个体
while nPop > nArchive
    % 找到距离最近的一对个体
    [min_dist, min_idx] = min(Distance(:));
    [i, j] = ind2sub(size(Distance), min_idx);
    
    % 移除其中一个个体（随机选择）
    if rand < 0.5
        remove_idx = i;
    else
        remove_idx = j;
    end
    
    % 更新种群和距离矩阵
    pop(remove_idx) = [];
    Distance(remove_idx, :) = [];
    Distance(:, remove_idx) = [];
    nPop = nPop - 1;
end

archive = pop;
end

function i = TournamentSelection(pop)
% 锦标赛选择
nPop = numel(pop);
a = randi([1 nPop]);
b = randi([1 nPop]);

if pop(a).F < pop(b).F
    i = a;
else
    i = b;
end
end

function [y1, y2] = Crossover(x1, x2, pCrossover, varMin, varMax)
% SBX交叉
if rand <= pCrossover
    alpha = rand(size(x1));
    y1 = alpha .* x1 + (1 - alpha) .* x2;
    y2 = alpha .* x2 + (1 - alpha) .* x1;
else
    y1 = x1;
    y2 = x2;
end

% 边界处理
y1 = max(y1, varMin);
y1 = min(y1, varMax);
y2 = max(y2, varMin);
y2 = min(y2, varMax);
end

function y = Mutate(x, pMutation, varMin, varMax)
% 多项式变异
y = x;
flag = rand(size(x)) <= pMutation;
y(flag) = x(flag) + 0.1 * (varMax - varMin) .* randn(size(x(flag)));

% 边界处理
y = max(y, varMin);
y = min(y, varMax);
end

function b = Dominates(p, q)
% 判断p是否支配q
b = all(p.Cost <= q.Cost) && any(p.Cost < q.Cost);
end
