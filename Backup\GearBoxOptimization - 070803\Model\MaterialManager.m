function materials = MaterialManager(material_name)
% MaterialManager 统一管理齿轮材料属性
%   该函数根据材料名称返回标准化的材料属性结构体
%
%   输入参数:
%   - material_name: 材料名称，如'17CrNiMo6'、'20CrNi2MoA'、'42CrMoA'等
%
%   输出:
%   - materials: 包含材料属性的结构体
%
%   材料属性包括:
%   - name: 材料名称
%   - density: 密度 (kg/m^3)
%   - youngs_modulus: 杨氏模量 (Pa)
%   - poissons_ratio: 泊松比
%   - yield_strength: 屈服强度 (Pa)
%   - tensile_strength: 拉伸强度 (Pa)
%   - bending_strength: 弯曲疲劳极限 (Pa)
%   - contact_strength: 接触疲劳极限 (Pa)
%   - HB: 布氏硬度 (可选)

% 默认值
materials = struct('name', material_name, ...
                  'density', 7850, ...
                  'youngs_modulus', 206e9, ...
                  'poissons_ratio', 0.3, ...
                  'yield_strength', 785e6, ...
                  'tensile_strength', 1080e6, ...
                  'bending_strength', 430e6, ...
                  'contact_strength', 1500e6, ...
                  'HB', 300);

% 根据材料名称设置特定属性
switch upper(material_name)
    case '17CRNIMO6'
        % 17CrNiMo6 - 高强度渗碳钢
        materials.name = '17CrNiMo6';
        materials.density = 7850;
        materials.youngs_modulus = 206e9;
        materials.poissons_ratio = 0.3;
        materials.yield_strength = 785e6;
        materials.tensile_strength = 1080e6;
        materials.bending_strength = 430e6;
        materials.contact_strength = 1500e6;
        materials.HB = 300;
        
    case '20CRNI2MOA'
        % 20CrNi2MoA - 中强度渗碳钢
        materials.name = '20CrNi2MoA';
        materials.density = 7870;
        materials.youngs_modulus = 210e9;
        materials.poissons_ratio = 0.275;
        materials.yield_strength = 785e6;
        materials.tensile_strength = 980e6;
        materials.bending_strength = 430e6;
        materials.contact_strength = 1500e6;
        materials.HB = 300;
        
    case '42CRMOA'
        % 42CrMoA - 调质钢
        materials.name = '42CrMoA';
        materials.density = 7800;
        materials.youngs_modulus = 200e9;
        materials.poissons_ratio = 0.3;
        materials.yield_strength = 930e6;
        materials.tensile_strength = 1080e6;
        materials.bending_strength = 182e6;
        materials.contact_strength = 490e6;
        materials.HB = 320;
        
    otherwise
        warning('未知材料名称: %s，使用默认值', material_name);
end

% 创建ISO 6336计算所需的材料参数格式
materials.iso_params = struct('E', materials.youngs_modulus, ...
                             'poisson', materials.poissons_ratio, ...
                             'HB', materials.HB, ...
                             'sigmaFlim', materials.bending_strength, ...
                             'sigmaHlim', materials.contact_strength, ...
                             'yield_strength', materials.yield_strength, ...
                             'tensile_strength', materials.tensile_strength);
end 