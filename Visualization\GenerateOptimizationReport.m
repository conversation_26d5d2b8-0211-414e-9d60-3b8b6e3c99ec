function GenerateOptimizationReport(optimization_results, first_stage_params, system_params)
% GenerateOptimizationReport 生成优化结果的HTML报告
%
% 输入参数:
%   optimization_results - 优化结果结构体数组
%   first_stage_params - 一级参数表格
%   system_params - 系统参数结构体
%
% 输出:
%   生成HTML报告文件

if nargin < 3
    system_params = struct();
end

% 确保Results目录存在
results_dir = 'Results';
if ~exist(results_dir, 'dir')
    mkdir(results_dir);
end

% 生成HTML文件名
html_filename = fullfile(results_dir, '齿轮传动系统优化结果报告.html');

try
    % 打开文件写入
    fid = fopen(html_filename, 'w', 'n', 'UTF-8');
    if fid == -1
        error('无法创建HTML文件: %s', html_filename);
    end
    
    % 写入HTML头部
    writeHTMLHeader(fid);
    
    % 写入系统参数信息
    writeSystemParameters(fid, system_params);
    
    % 写入一级参数信息
    writeFirstStageParameters(fid, first_stage_params);
    
    % 写入优化结果
    writeOptimizationResults(fid, optimization_results);
    
    % 写入HTML尾部
    writeHTMLFooter(fid);
    
    % 关闭文件
    fclose(fid);
    
    fprintf('优化结果HTML报告已生成: %s\n', html_filename);
    
catch e
    if exist('fid', 'var') && fid ~= -1
        fclose(fid);
    end
    fprintf('生成HTML报告时出错: %s\n', e.message);
end

end

function writeHTMLHeader(fid)
% 写入HTML头部
fprintf(fid, '<!DOCTYPE html>\n');
fprintf(fid, '<html lang="zh-CN">\n');
fprintf(fid, '<head>\n');
fprintf(fid, '    <meta charset="UTF-8">\n');
fprintf(fid, '    <meta name="viewport" content="width=device-width, initial-scale=1.0">\n');
fprintf(fid, '    <title>齿轮传动系统多目标优化结果报告</title>\n');

% 引入DataTables库
fprintf(fid, '    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.6/css/jquery.dataTables.min.css">\n');
fprintf(fid, '    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">\n');
fprintf(fid, '    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>\n');
fprintf(fid, '    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>\n');

fprintf(fid, '    <style>\n');
fprintf(fid, '        @import url("https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap");\n');
fprintf(fid, '        body { font-family: "Microsoft YaHei", "Roboto", Arial, sans-serif; margin: 0; color: #333; background-color: #f5f7fa; line-height: 1.5; }\n');
fprintf(fid, '        h1, h2, h3 { color: #2c3e50; margin-top: 0.5em; margin-bottom: 0.5em; }\n');
fprintf(fid, '        h1 { text-align: center; padding: 15px 10px; background: linear-gradient(135deg, #3498db, #2c3e50); color: white; font-weight: 500; letter-spacing: 1px; box-shadow: 0 2px 6px rgba(0,0,0,0.1); }\n');
fprintf(fid, '        h2 { border-bottom: 2px solid #3498db; padding-bottom: 5px; font-weight: 500; }\n');
fprintf(fid, '        .main-container { max-width: 1200px; margin: 0 auto; padding: 20px; padding-bottom: 40px; }\n');
fprintf(fid, '        .container { background-color: white; padding: 20px; border-radius: 5px; box-shadow: 0 1px 3px rgba(0,0,0,0.08); margin-bottom: 25px; }\n');

% DataTables样式
fprintf(fid, '        table.dataTable { width: 100%%; border-collapse: collapse; margin: 15px 0; font-size: 14px; }\n');
fprintf(fid, '        table.dataTable thead th { background: linear-gradient(to bottom, #5a7b9c, #7b96ae); color: white; text-align: center; padding: 10px 5px; vertical-align: middle; font-weight: 500; border: 1px solid #4a6b8c; }\n');
fprintf(fid, '        table.dataTable tbody td { border: 1px solid #e0e6ed; text-align: center; padding: 8px 5px; }\n');
fprintf(fid, '        table.dataTable tbody tr:nth-child(even) { background-color: #f8fafc; }\n');
fprintf(fid, '        table.dataTable tbody tr:hover { background-color: #eef7fb !important; transition: all 0.2s ease; }\n');

% 参数分组颜色
fprintf(fid, '        .group-geometry { background-color: #e6f2ff; }\n');
fprintf(fid, '        .group-shift { background-color: #fff0e6; }\n');
fprintf(fid, '        .group-result { background-color: #f0f0f0; }\n');
fprintf(fid, '        .group-safety { background-color: #e6ffec; }\n');

% DataTables控件样式
fprintf(fid, '        .dataTables_wrapper .dataTables_filter input { margin-left: 10px; padding: 5px; border: 1px solid #ddd; border-radius: 4px; }\n');
fprintf(fid, '        .dataTables_wrapper .dataTables_length select { padding: 5px; margin: 0 5px; border: 1px solid #ddd; border-radius: 4px; }\n');
fprintf(fid, '        .dataTables_info, .dataTables_paginate { margin-top: 15px; }\n');
fprintf(fid, '        .dataTables_paginate .paginate_button { padding: 5px 10px; margin: 0 2px; border: 1px solid #ddd; border-radius: 4px; cursor: pointer; }\n');
fprintf(fid, '        .dataTables_paginate .paginate_button.current { background: #3498db; color: white !important; border-color: #3498db; }\n');

% 其他样式
fprintf(fid, '        .summary-box { background-color: #edf7ff; border-left: 5px solid #3498db; padding: 15px; margin: 15px 0; border-radius: 5px; }\n');
fprintf(fid, '        .algorithm-section { margin: 20px 0; padding: 15px; background-color: white; border-radius: 5px; box-shadow: 0 1px 3px rgba(0,0,0,0.08); }\n');
fprintf(fid, '        .timestamp { text-align: right; color: #7f8c8d; font-size: 12px; margin-top: 20px; }\n');
fprintf(fid, '        .table-responsive { overflow-x: auto; margin-bottom: 15px; }\n');
fprintf(fid, '    </style>\n');
fprintf(fid, '</head>\n');
fprintf(fid, '<body>\n');
fprintf(fid, '    <div class="main-container">\n');
fprintf(fid, '        <h1>齿轮传动系统多目标优化结果报告</h1>\n');
end

function writeSystemParameters(fid, system_params)
% 写入系统参数信息
fprintf(fid, '        <h2>系统参数</h2>\n');
fprintf(fid, '        <div class="summary-box">\n');
fprintf(fid, '            <table class="param-table">\n');
fprintf(fid, '                <tr><th>参数名称</th><th>数值</th><th>单位</th></tr>\n');

if isfield(system_params, 'input_power')
    fprintf(fid, '                <tr><td>输入功率</td><td>%.2f</td><td>kW</td></tr>\n', system_params.input_power);
end
if isfield(system_params, 'input_speed')
    fprintf(fid, '                <tr><td>输入转速</td><td>%.0f</td><td>rpm</td></tr>\n', system_params.input_speed);
end
if isfield(system_params, 'output_speed')
    fprintf(fid, '                <tr><td>输出转速</td><td>%.2f</td><td>rpm</td></tr>\n', system_params.output_speed);
end
if isfield(system_params, 'center_distance')
    fprintf(fid, '                <tr><td>中心距</td><td>%.0f</td><td>mm</td></tr>\n', system_params.center_distance);
end
if isfield(system_params, 'service_life')
    fprintf(fid, '                <tr><td>设计寿命</td><td>%.0f</td><td>h</td></tr>\n', system_params.service_life);
end
if isfield(system_params, 'contact_safety_factor')
    fprintf(fid, '                <tr><td>接触安全系数要求</td><td>%.1f</td><td>-</td></tr>\n', system_params.contact_safety_factor);
end
if isfield(system_params, 'bending_safety_factor')
    fprintf(fid, '                <tr><td>弯曲安全系数要求</td><td>%.1f</td><td>-</td></tr>\n', system_params.bending_safety_factor);
end

fprintf(fid, '            </table>\n');
fprintf(fid, '        </div>\n');
end

function writeFirstStageParameters(fid, first_stage_params)
% 写入一级参数信息
fprintf(fid, '        <h2>一级平行轴系参数</h2>\n');

if isempty(first_stage_params)
    fprintf(fid, '        <p>未提供一级参数信息</p>\n');
    return;
end

fprintf(fid, '        <div class="summary-box">\n');
fprintf(fid, '            <p><strong>聚类后的一级参数组数量:</strong> %d 组</p>\n', height(first_stage_params));
fprintf(fid, '            <p>每组参数代表一类具有相似核心特征（模数、齿数、螺旋角等）的齿轮参数组合</p>\n');
fprintf(fid, '        </div>\n');

% 显示所有参数组合
fprintf(fid, '        <h3>一级参数组合详细表格</h3>\n');
fprintf(fid, '        <div class="table-responsive">\n');
fprintf(fid, '            <table id="firstStageTable" class="display compact stripe hover">\n');
fprintf(fid, '                <thead>\n');
fprintf(fid, '                    <tr>\n');
fprintf(fid, '                        <th>组号</th>\n');
fprintf(fid, '                        <th class="group-geometry">模数<br>(mm)</th>\n');
fprintf(fid, '                        <th class="group-geometry">小齿轮<br>齿数</th>\n');
fprintf(fid, '                        <th class="group-geometry">大齿轮<br>齿数</th>\n');
fprintf(fid, '                        <th class="group-geometry">螺旋角<br>(°)</th>\n');
fprintf(fid, '                        <th class="group-geometry">齿宽系数</th>\n');
fprintf(fid, '                        <th class="group-result">总质量<br>(kg)</th>\n');
fprintf(fid, '                        <th class="group-safety">接触安全系数</th>\n');
fprintf(fid, '                    </tr>\n');
fprintf(fid, '                </thead>\n');
fprintf(fid, '                <tbody>\n');

for i = 1:height(first_stage_params)
    fprintf(fid, '                    <tr>\n');
    fprintf(fid, '                        <td>%d</td>\n', i);

    % 获取各列数据
    if ismember('模数(mm)', first_stage_params.Properties.VariableNames)
        fprintf(fid, '                        <td>%.1f</td>\n', first_stage_params{i, '模数(mm)'});
    else
        fprintf(fid, '                        <td>-</td>\n');
    end

    if ismember('小齿轮齿数', first_stage_params.Properties.VariableNames)
        fprintf(fid, '                        <td>%d</td>\n', first_stage_params{i, '小齿轮齿数'});
    else
        fprintf(fid, '                        <td>-</td>\n');
    end

    if ismember('大齿轮齿数', first_stage_params.Properties.VariableNames)
        fprintf(fid, '                        <td>%d</td>\n', first_stage_params{i, '大齿轮齿数'});
    else
        fprintf(fid, '                        <td>-</td>\n');
    end

    if ismember('螺旋角(°)', first_stage_params.Properties.VariableNames)
        fprintf(fid, '                        <td>%.1f</td>\n', first_stage_params{i, '螺旋角(°)'});
    else
        fprintf(fid, '                        <td>-</td>\n');
    end

    if ismember('齿宽系数', first_stage_params.Properties.VariableNames)
        fprintf(fid, '                        <td>%.3f</td>\n', first_stage_params{i, '齿宽系数'});
    else
        fprintf(fid, '                        <td>-</td>\n');
    end

    if ismember('总质量(kg)', first_stage_params.Properties.VariableNames)
        fprintf(fid, '                        <td>%.2f</td>\n', first_stage_params{i, '总质量(kg)'});
    else
        fprintf(fid, '                        <td>-</td>\n');
    end

    if ismember('接触安全系数', first_stage_params.Properties.VariableNames)
        fprintf(fid, '                        <td>%.3f</td>\n', first_stage_params{i, '接触安全系数'});
    else
        fprintf(fid, '                        <td>-</td>\n');
    end

    fprintf(fid, '                    </tr>\n');
end

fprintf(fid, '                </tbody>\n');
fprintf(fid, '            </table>\n');
fprintf(fid, '        </div>\n');
end

function writeOptimizationResults(fid, optimization_results)
% 写入优化结果
fprintf(fid, '        <h2>多目标优化结果</h2>\n');

if isempty(optimization_results)
    fprintf(fid, '        <p>未提供优化结果</p>\n');
    return;
end

for i = 1:length(optimization_results)
    result = optimization_results(i);
    
    fprintf(fid, '        <div class="algorithm-section">\n');
    fprintf(fid, '            <h3>算法: %s</h3>\n', result.algorithm);
    
    if isfield(result, 'objectives') && ~isempty(result.objectives)
        fprintf(fid, '            <div class="summary-box">\n');
        fprintf(fid, '                <p><strong>非支配解数量:</strong> %d</p>\n', size(result.objectives, 1));
        fprintf(fid, '                <p><strong>运行时间:</strong> %.2f 秒</p>\n', result.elapsed_time);
        fprintf(fid, '                <p><strong>评价次数:</strong> %d</p>\n', result.num_evaluations);
        
        % 显示目标函数统计信息
        fprintf(fid, '                <table class="param-table">\n');
        fprintf(fid, '                    <tr><th>目标函数</th><th>最小值</th><th>最大值</th><th>平均值</th></tr>\n');
        fprintf(fid, '                    <tr><td>总质量 (kg)</td><td>%.2f</td><td>%.2f</td><td>%.2f</td></tr>\n', ...
                min(result.objectives(:,1)), max(result.objectives(:,1)), mean(result.objectives(:,1)));
        fprintf(fid, '                    <tr><td>弯曲安全系数</td><td>%.3f</td><td>%.3f</td><td>%.3f</td></tr>\n', ...
                min(-result.objectives(:,2)), max(-result.objectives(:,2)), mean(-result.objectives(:,2)));
        fprintf(fid, '                    <tr><td>接触安全系数</td><td>%.3f</td><td>%.3f</td><td>%.3f</td></tr>\n', ...
                min(-result.objectives(:,3)), max(-result.objectives(:,3)), mean(-result.objectives(:,3)));
        fprintf(fid, '                </table>\n');
        fprintf(fid, '            </div>\n');
    else
        fprintf(fid, '            <p>该算法未产生有效结果</p>\n');
    end
    
    fprintf(fid, '        </div>\n');
end
end

function writeHTMLFooter(fid)
% 写入HTML尾部
fprintf(fid, '        <div class="timestamp">\n');
fprintf(fid, '            <p>报告生成时间: %s</p>\n', char(datetime("now", 'Format', 'yyyy-MM-dd HH:mm:ss')));
fprintf(fid, '        </div>\n');
fprintf(fid, '    </div>\n');

% 添加DataTables初始化JavaScript
fprintf(fid, '    <script>\n');
fprintf(fid, '    $(document).ready(function() {\n');
fprintf(fid, '        // 初始化一级参数表格\n');
fprintf(fid, '        if ($("#firstStageTable").length) {\n');
fprintf(fid, '            $("#firstStageTable").DataTable({\n');
fprintf(fid, '                "language": {\n');
fprintf(fid, '                    "search": "搜索:",\n');
fprintf(fid, '                    "lengthMenu": "每页显示 _MENU_ 条记录",\n');
fprintf(fid, '                    "zeroRecords": "没有找到匹配的记录",\n');
fprintf(fid, '                    "info": "显示第 _START_ 至 _END_ 条记录，共 _TOTAL_ 条",\n');
fprintf(fid, '                    "infoEmpty": "显示第 0 至 0 条记录，共 0 条",\n');
fprintf(fid, '                    "infoFiltered": "(从 _MAX_ 条记录过滤)",\n');
fprintf(fid, '                    "paginate": {\n');
fprintf(fid, '                        "first": "首页",\n');
fprintf(fid, '                        "last": "末页",\n');
fprintf(fid, '                        "next": "下一页",\n');
fprintf(fid, '                        "previous": "上一页"\n');
fprintf(fid, '                    }\n');
fprintf(fid, '                },\n');
fprintf(fid, '                "pageLength": 10,\n');
fprintf(fid, '                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "全部"]],\n');
fprintf(fid, '                "order": [[6, "asc"]],\n');
fprintf(fid, '                "scrollX": true,\n');
fprintf(fid, '                "autoWidth": false\n');
fprintf(fid, '            });\n');
fprintf(fid, '        }\n');
fprintf(fid, '    });\n');
fprintf(fid, '    </script>\n');

fprintf(fid, '</body>\n');
fprintf(fid, '</html>\n');
end
