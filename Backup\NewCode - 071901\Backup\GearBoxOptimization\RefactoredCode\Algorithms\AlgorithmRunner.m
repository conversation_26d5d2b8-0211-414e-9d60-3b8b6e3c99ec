function [pareto_variables, pareto_solutions, execution_time] = AlgorithmRunner(algorithm_name, problem, algorithm_params)
% AlgorithmRunner 统一的算法运行器
% 调用重构后的优化算法，保持接口一致性
%
% 输入:
%   algorithm_name - 算法名称
%   problem - 优化问题结构体
%   algorithm_params - 算法参数
%
% 输出:
%   pareto_variables - Pareto最优解的变量值
%   pareto_solutions - Pareto最优解的目标函数值
%   execution_time - 算法执行时间

%% 1. 参数验证
if nargin < 3
    algorithm_params = struct();
end

% 设置默认算法参数
if ~isfield(algorithm_params, 'pop_size')
    algorithm_params.pop_size = 50;
end
if ~isfield(algorithm_params, 'max_iter')
    algorithm_params.max_iter = 50;
end
if ~isfield(algorithm_params, 'pCrossover')
    algorithm_params.pCrossover = 0.9;
end
if ~isfield(algorithm_params, 'pMutation')
    algorithm_params.pMutation = 0.1;
end

%% 2. 准备算法参数
params = struct();
params.nPop = algorithm_params.pop_size;
params.maxIt = algorithm_params.max_iter;
params.pCrossover = algorithm_params.pCrossover;
params.pMutation = algorithm_params.pMutation;

%% 3. 预处理问题结构
% 确保问题结构包含必要字段
if ~isfield(problem, 'varMin')
    problem.varMin = problem.lb;
end
if ~isfield(problem, 'varMax')
    problem.varMax = problem.ub;
end
if ~isfield(problem, 'costFunction')
    problem.costFunction = problem.objective_function;
end

%% 4. 根据算法名称调用相应的算法
fprintf('正在运行算法: %s\n', algorithm_name);
tic;

try
    % 添加算法路径
    addpath('NSGA-II');
    addpath('NSGA-III');
    addpath('SPEA2');
    addpath('MOEA-D');
    addpath('MOEA-D-DE');
    addpath('MOEA-D-M2M');
    addpath('MOPSO');
    addpath('MOGWO');
    addpath('MOWOA');

    switch algorithm_name
        case 'NSGA-II'
            [population, pareto_solutions] = RunNSGAII(problem, params);
            pareto_variables = vertcat(population.Position);

        case 'NSGA-III'
            [population, pareto_solutions] = RunNSGAIII(problem, params);
            pareto_variables = vertcat(population.Position);

        case 'SPEA2'
            [population, pareto_solutions] = RunSPEA2(problem, params);
            pareto_variables = vertcat(population.Position);

        case 'MOEA/D'
            [population, pareto_solutions] = RunMOEAD(problem, params);
            pareto_variables = vertcat(population.Position);

        case 'MOEA/D-DE'
            [population, pareto_solutions] = RunMOEAD_DE(problem, params);
            pareto_variables = vertcat(population.Position);

        case 'MOEA/D-M2M'
            [population, pareto_solutions] = RunMOEAD_M2M(problem, params);
            pareto_variables = vertcat(population.Position);

        case 'MOPSO'
            [population, pareto_solutions] = RunMOPSO(problem, params);
            pareto_variables = vertcat(population.Position);

        case 'MOGWO'
            [population, pareto_solutions] = RunMOGWO(problem, params);
            pareto_variables = vertcat(population.Position);

        case 'MOWOA'
            [population, pareto_solutions] = RunMOWOA(problem, params);
            pareto_variables = vertcat(population.Position);

        otherwise
            error('未知的算法名称: %s', algorithm_name);
    end

    execution_time = toc;
    fprintf('算法 %s 运行完成，耗时: %.2f 秒\n', algorithm_name, execution_time);
    fprintf('获得 %d 个非支配解\n', size(pareto_solutions, 1));

catch e
    execution_time = toc;
    fprintf('算法 %s 运行出错: %s\n', algorithm_name, e.message);

    % 重新抛出错误，不提供默认值
    rethrow(e);
end

end
