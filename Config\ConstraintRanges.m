function constraints = ConstraintRanges()
% ConstraintRanges 简化的约束范围配置
% 只保留重构后代码实际使用的配置项
%
% 输出:
%   constraints - 约束范围结构体

%% 系统默认参数配置（用于用户输入的默认值）
constraints.system_defaults = struct();
constraints.system_defaults.operating_conditions = struct();
constraints.system_defaults.operating_conditions.input_power = 55;              % 输入功率 (kW)
constraints.system_defaults.operating_conditions.input_speed = 1500;           % 输入转速 (rpm)
constraints.system_defaults.operating_conditions.output_speed = 18.75;         % 输出转速 (rpm)
constraints.system_defaults.operating_conditions.service_life = 20000;         % 设计寿命 (h)

% 安全系数要求（默认值，用户可以在控制台输入时修改）
constraints.system_defaults.safety_factors = struct();
constraints.system_defaults.safety_factors.contact_safety_factor = 1.2;        % 接触安全系数默认值（用户可修改）
constraints.system_defaults.safety_factors.bending_safety_factor = 1.8;        % 弯曲安全系数默认值（用户可修改）

% 几何约束参数
constraints.system_defaults.geometric_constraints = struct();
constraints.system_defaults.geometric_constraints.center_distance = 400;       % 一级中心距约束 (mm)

% 齿轮精度等级
constraints.system_defaults.quality = struct();
constraints.system_defaults.quality.grade = 6;                                 % 齿轮精度等级 (ISO 1328)

% 计算参数
constraints.system_defaults.calculation = struct();
constraints.system_defaults.calculation.pressure_angle = 20;                   % 标准压力角 (度)

%% 算法配置
constraints.algorithms = {'NSGA-II', 'NSGA-III', 'SPEA2', 'MOEA/D', 'MOEA/D-DE', 'MOEA/D-M2M', 'MOPSO', 'MOGWO', 'MOWOA'};

%% 显示用的约束范围（仅用于参数范围显示）
constraints.display_ranges = struct();
constraints.display_ranges.first_stage = struct();
constraints.display_ranges.first_stage.z1_min = 18;
constraints.display_ranges.first_stage.z1_max = 100;
constraints.display_ranges.first_stage.z2_min = 18;
constraints.display_ranges.first_stage.z2_max = 100;
% 添加FirstStageGenerator需要的字段
constraints.display_ranges.first_stage.module_values = [7, 8, 9, 10, 11, 12, 13];
constraints.display_ranges.first_stage.ratio_min = 3.0;
constraints.display_ranges.first_stage.ratio_max = 3.5;
constraints.display_ranges.first_stage.width_coefficient_min = 0.28;
constraints.display_ranges.first_stage.width_coefficient_max = 0.4;

constraints.display_ranges.geometry = struct();
constraints.display_ranges.geometry.min_tooth_count = 18;
constraints.display_ranges.geometry.max_tooth_count = 100;

%% 为了兼容原有代码，添加engineering字段（按照原始main函数的约束范围）
constraints.engineering = struct();

% 几何约束
constraints.engineering.geometry = struct();
constraints.engineering.geometry.min_tooth_count = 18;
constraints.engineering.geometry.max_tooth_count = 100;
constraints.engineering.geometry.max_ring_tooth_count = 130;  % 二级内齿圈最大齿数

% 一级齿轮约束
constraints.engineering.first_stage = struct();
constraints.engineering.first_stage.z1_min = 18;             % 一级小齿轮最小齿数
constraints.engineering.first_stage.z1_max = 30;             % 一级小齿轮最大齿数
constraints.engineering.first_stage.z2_min = 50;             % 一级大齿轮最小齿数
constraints.engineering.first_stage.z2_max = 100;            % 一级大齿轮最大齿数

% 传动比约束
constraints.engineering.transmission_ratio = struct();
constraints.engineering.transmission_ratio.target_total_ratio = 79.98; % 目标总传动比
constraints.engineering.transmission_ratio.ratio_tolerance = 0.05;  % 传动比容差
constraints.engineering.transmission_ratio.stage1_min = 3.0;   % 一级传动比最小值
constraints.engineering.transmission_ratio.stage1_max = 3.5;   % 一级传动比最大值
constraints.engineering.transmission_ratio.stage2_min = 5.0;   % 二级传动比最小值（根据实际数据调整）
constraints.engineering.transmission_ratio.stage2_max = 12.5;  % 二级传动比最大值
constraints.engineering.transmission_ratio.stage3_min = 3.0;   % 三级传动比最小值
constraints.engineering.transmission_ratio.stage3_max_3planet = 12.5; % 三级传动比最大值（三行星轮）
constraints.engineering.transmission_ratio.stage3_max_4planet = 5.7;  % 三级传动比最大值（四行星轮）

% 中心距约束
constraints.engineering.center_distance = struct();
constraints.engineering.center_distance.stage1_fixed = 400;         % 一级中心距固定值 (mm)
constraints.engineering.center_distance.stage2_min = 300;           % 二级最小中心距 (mm)
constraints.engineering.center_distance.stage2_max = 350;           % 二级最大中心距 (mm)
constraints.engineering.center_distance.stage3_min = 350;           % 三级最小中心距 (mm)
constraints.engineering.center_distance.stage3_max = 400;           % 三级最大中心距 (mm)

% 变位系数约束 - 扩大范围以改善接触安全系数
constraints.engineering.shift_coefficient = struct();
constraints.engineering.shift_coefficient.sun_min = 0.0;            % 太阳轮变位系数最小值
constraints.engineering.shift_coefficient.sun_max = 0.8;            % 太阳轮变位系数最大值（扩大以改善接触条件）
constraints.engineering.shift_coefficient.planet_min = 0.0;         % 行星轮变位系数最小值
constraints.engineering.shift_coefficient.planet_max = 0.8;         % 行星轮变位系数最大值（扩大以改善接触条件）
constraints.engineering.shift_coefficient.ring_min = -1.2;          % 内齿圈变位系数最小值（适当扩大）
constraints.engineering.shift_coefficient.ring_max = 1.2;           % 内齿圈变位系数最大值（适当扩大）

end
