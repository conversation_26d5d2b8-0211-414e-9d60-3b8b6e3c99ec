# 齿轮安全系数计算与材料管理系统

本文档介绍了齿轮传动系统中安全系数计算和材料管理的统一接口，旨在简化代码结构，提高可维护性和一致性。

## 系统组成

系统主要由以下几个核心文件组成：

1. `MaterialManager.m` - 材料属性管理器
2. `ParallelGearSafetyCalculator.m` - 专用于平行轴齿轮的安全系数计算
3. `PlanetarySystemSafetyCalculator.m` - 专用于行星齿轮系统的安全系数计算

> 注意：为了简化代码结构，已将原有的 `ISO6336Calculator.m` 和 `ISO6336PlanetaryCalculator.m` 的功能整合到上述两个文件中，避免重复计算。

## 使用方法

### 1. 材料属性管理

使用 `MaterialManager` 函数获取材料属性：

```matlab
% 获取17CrNiMo6材料属性
material = MaterialManager('17CrNiMo6');

% 获取20CrNi2MoA材料属性
material = MaterialManager('20CrNi2MoA');

% 获取42CrMoA材料属性
material = MaterialManager('42CrMoA');
```

### 2. 平行轴齿轮安全系数计算

使用 `ParallelGearSafetyCalculator` 函数计算平行轴齿轮的弯曲和接触安全系数：

```matlab
% 准备齿轮参数
gear_params = struct('m', 10, ...      % 模数 (mm)
                    'z', 20, ...       % 齿数
                    'alpha', 20, ...   % 压力角 (度)
                    'beta', 0, ...     % 螺旋角 (度)
                    'b', 100, ...      % 齿宽 (mm)
                    'x', 0.5, ...      % 变位系数
                    'mating_z', 40);   % 啮合齿轮齿数

% 准备载荷参数
load_params = struct('T', 1000, ...        % 扭矩 (N·m)
                    'n', 1000, ...         % 转速 (rpm)
                    'KA', 1.25, ...        % 应用系数
                    'service_life', 10000); % 设计寿命 (h)

% 获取材料参数
material = MaterialManager('17CrNiMo6');

% 计算安全系数
[SF, SH] = ParallelGearSafetyCalculator(gear_params, load_params, material, 'ISO6336');
fprintf('弯曲安全系数: %.3f\n', SF);
fprintf('接触安全系数: %.3f\n', SH);
```

### 3. 行星齿轮系统安全系数计算

使用 `PlanetarySystemSafetyCalculator` 函数计算行星齿轮系统的安全系数：

```matlab
% 创建行星齿轮系统参数结构体
gear_params_planet = struct();
% 太阳轮参数
gear_params_planet.sun = struct('m', 8, ...    % 模数 (mm)
                              'z', 20, ...     % 齿数
                              'alpha', 20, ... % 压力角 (度)
                              'beta', 0, ...   % 螺旋角 (度)
                              'b', 80, ...     % 齿宽 (mm)
                              'x', 0.5);       % 变位系数

% 行星轮参数
gear_params_planet.planet = struct('m', 8, ...   % 模数 (mm)
                                 'z', 30, ...    % 齿数
                                 'alpha', 20, ... % 压力角 (度)
                                 'beta', 0, ...   % 螺旋角 (度)
                                 'b', 80, ...     % 齿宽 (mm)
                                 'x', 0.3);       % 变位系数

% 内齿圈参数
gear_params_planet.ring = struct('m', 8, ...     % 模数 (mm)
                               'z', 80, ...      % 齿数
                               'alpha', 20, ...  % 压力角 (度)
                               'beta', 0, ...    % 螺旋角 (度)
                               'b', 80, ...      % 齿宽 (mm)
                               'x', -0.8);       % 变位系数

% 行星轮数量
gear_params_planet.planets_count = 3;

% 准备载荷参数
load_params = struct('T', 2000, ...        % 太阳轮输入扭矩 (N·m)
                    'n', 800, ...          % 太阳轮转速 (rpm)
                    'KA', 1.25, ...        % 应用系数
                    'service_life', 10000); % 设计寿命 (h)

% 准备材料参数
materials = struct('sun', MaterialManager('17CrNiMo6'), ...
                  'planet', MaterialManager('20CrNi2MoA'), ...
                  'ring', MaterialManager('42CrMoA'));

% 计算行星系统安全系数
[SF_min, SH_min] = PlanetarySystemSafetyCalculator(gear_params_planet, load_params, materials, 'ISO6336');
fprintf('最小弯曲安全系数: %.3f\n', SF_min);
fprintf('最小接触安全系数: %.3f\n', SH_min);
```

## 计算方法

系统使用以下计算方法：

1. `'ISO6336'` - 使用ISO 6336标准计算（默认）

## 注意事项

1. 为了保持代码一致性，所有平行轴齿轮安全系数计算应使用 `ParallelGearSafetyCalculator`，所有行星系统安全系数计算应使用 `PlanetarySystemSafetyCalculator`。
2. 不要直接在代码中使用硬编码的材料属性，应通过 `MaterialManager` 获取。
3. 当添加新材料时，只需在 `MaterialManager` 中添加即可。 