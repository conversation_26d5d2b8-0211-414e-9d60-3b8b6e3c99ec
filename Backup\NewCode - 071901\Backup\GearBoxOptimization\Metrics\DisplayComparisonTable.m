function DisplayComparisonTable(data, column_names, title_text)
% DisplayComparisonTable 显示算法比较结果的表格
%   DisplayComparisonTable(data, column_names, title_text) 创建一个表格窗口显示比较数据
%
% 输入:
%   data - 表格数据，cell数组
%   column_names - 列名，cell数组
%   title_text - 表格标题，字符串
%
% 示例:
%   DisplayComparisonTable(data, {'算法', 'GD', 'Spread', 'MS', 'IGD', 'HV', '计算时间(秒)'}, '多目标优化算法性能对比');

% 如果未提供标题，使用默认标题
if nargin < 3
    title_text = '算法比较结果';
end

% 创建表格数据
table_data = cell2table(data, 'VariableNames', column_names);

% 在命令窗口中显示表格
disp(' ');
disp(['=== ', title_text, ' ===']);
disp(table_data);

% 不再创建图形窗口，只在命令窗口显示表格
end 