function html_output = GenerateFirstStageHTML(gear_combinations)
% GenerateFirstStageHTML 生成一级平行轴系组合的HTML表格
%   输入: 
%   - gear_combinations: 一级平行轴系组合参数表格
%   输出:
%   - html_output: 包含表格的HTML文本

% 检查输入是否为空
if isempty(gear_combinations) || height(gear_combinations) == 0
    html_output = '<div class="alert alert-warning">未找到满足约束的一级平行轴系组合</div>';
    return;
end

% 开始生成HTML - 使用cell数组存储HTML片段，避免'\n'字符问题
html = {};
html{end+1} = '<!DOCTYPE html>';
html{end+1} = '<html lang="zh-CN">';
html{end+1} = '<head>';
html{end+1} = '    <meta charset="UTF-8">';
html{end+1} = '    <meta name="viewport" content="width=device-width, initial-scale=1.0">';
html{end+1} = '    <title>一级平行轴系有效参数组合</title>';

% 引入外部库
html{end+1} = '    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.6/css/jquery.dataTables.min.css">';
html{end+1} = '    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">';
html{end+1} = '    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>';
html{end+1} = '    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>';

% CSS样式，采用CreateSummaryTables.m的风格
html{end+1} = '    <style>';
html{end+1} = '        @import url("https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap");';
html{end+1} = '        body { font-family: "Microsoft YaHei", "Roboto", Arial, sans-serif; margin: 0; color: #333; background-color: #f5f7fa; line-height: 1.5; }';
html{end+1} = '        h1, h2, h3 { color: #2c3e50; margin-top: 0.5em; margin-bottom: 0.5em; }';
html{end+1} = '        h1 { text-align: center; padding: 15px 10px; background: linear-gradient(135deg, #3498db, #2c3e50); color: white; font-weight: 500; letter-spacing: 1px; box-shadow: 0 2px 6px rgba(0,0,0,0.1); }';
html{end+1} = '        h2 { border-bottom: 2px solid #3498db; padding-bottom: 5px; font-weight: 500; }';
html{end+1} = '        .main-container { max-width: 1200px; margin: 0 auto; padding: 20px; }';
html{end+1} = '        .container { background-color: white; padding: 20px; border-radius: 5px; box-shadow: 0 1px 3px rgba(0,0,0,0.08); margin-bottom: 20px; }';
html{end+1} = '        table.dataTable { width: 100%; border-collapse: collapse; margin: 15px 0; font-size: 14px; }';
html{end+1} = '        table.dataTable thead th { background: linear-gradient(to bottom, #5a7b9c, #7b96ae); color: white; text-align: center; padding: 10px 5px; vertical-align: middle; font-weight: 500; border: 1px solid #4a6b8c; }';
html{end+1} = '        table.dataTable tbody td { border: 1px solid #e0e6ed; text-align: center; padding: 8px 5px; }';
html{end+1} = '        table.dataTable tbody tr:nth-child(even) { background-color: #f8fafc; }';
html{end+1} = '        table.dataTable tbody tr:hover { background-color: #eef7fb !important; transition: all 0.2s ease; }';
html{end+1} = '        .highlight-row { background-color: #c3e6cb !important; }';
html{end+1} = '        .highlight-row:hover { background-color: #b1dfbb !important; }';
html{end+1} = '        /* 参数分组颜色 - 更鲜明的浅色系 */';
html{end+1} = '        .group-geometry { background-color: #e6f2ff; }'; % 几何参数 - 更鲜明的浅蓝色
html{end+1} = '        .group-shift { background-color: #fff0e6; }';    % 变位系数 - 浅橙色
html{end+1} = '        .group-result { background-color: #f0f0f0; }';    % 结果参数 - 浅灰色
html{end+1} = '        .group-safety { background-color: #e6ffec; }';    % 安全系数 - 浅绿色
html{end+1} = '        .summary { margin: 15px 0; padding: 15px; background-color: #edf7ff; border-radius: 5px; border-left: 5px solid #3498db; }';
html{end+1} = '        .dataTables_wrapper .dataTables_filter input { margin-left: 10px; padding: 5px; border: 1px solid #ddd; border-radius: 4px; }';
html{end+1} = '        .dataTables_wrapper .dataTables_length select { padding: 5px; margin: 0 5px; border: 1px solid #ddd; border-radius: 4px; }';
html{end+1} = '        .dataTables_info, .dataTables_paginate { margin-top: 15px; }';
html{end+1} = '        .dataTables_paginate .paginate_button { padding: 5px 10px; margin: 0 2px; border: 1px solid #ddd; border-radius: 4px; cursor: pointer; }';
html{end+1} = '        .dataTables_paginate .paginate_button.current { background: #3498db; color: white !important; border-color: #3498db; }';
html{end+1} = '        .explanation { background-color: white; border-left: 4px solid #3498db; padding: 15px; margin: 15px 0; }';
html{end+1} = '        .explanation h4 { margin-top: 0; color: #2980b9; font-weight: 500; }';
html{end+1} = '        .footnote { font-size: 13px; color: #666; margin-top: 20px; text-align: center; }';
html{end+1} = '        /* 颜色图例 */';
html{end+1} = '        .color-legend { display: flex; flex-wrap: wrap; gap: 15px; margin: 15px 0; }';
html{end+1} = '        .color-item { display: flex; align-items: center; }';
html{end+1} = '        .color-box { width: 20px; height: 20px; margin-right: 8px; border: 1px solid #ddd; }';
html{end+1} = '        .recommended-box { background-color: #c3e6cb; }';
html{end+1} = '        .geometry-box { background-color: #e6f2ff; }';
html{end+1} = '        .shift-box { background-color: #fff0e6; }';
html{end+1} = '        .result-box { background-color: #f0f0f0; }';
html{end+1} = '        .safety-box { background-color: #e6ffec; }';
html{end+1} = '    </style>';
html{end+1} = '</head>';
html{end+1} = '<body>';
html{end+1} = sprintf('<h1 style="position: relative;">一级平行轴系有效参数组合<span style="position: absolute; right: 20px; bottom: 15px; font-size: 14px; font-weight: normal; opacity: 0.9;">生成时间: %s</span></h1>', datestr(now, 'yyyy-mm-dd HH:MM:SS'));
html{end+1} = '<div class="main-container">';

% 添加概览信息部分
html{end+1} = '    <div class="container">';
html{end+1} = '        <h2 style="margin-top:0;">参数组合概览</h2>';
html{end+1} = '        <div class="summary">';
html{end+1} = sprintf('            <p><i class="fas fa-info-circle"></i> <strong>找到符合条件的组合数量: </strong>%s</p>', num2str(height(gear_combinations)));
html{end+1} = '            <p><i class="fas fa-ruler"></i> <strong>目标中心距: </strong>400 mm (误差控制在±0.01%以内)</p>';
html{end+1} = '            <h4 style="margin-top:15px;">约束条件:</h4>';
html{end+1} = '            <ul>';
html{end+1} = '                <li><strong>模数:</strong> 限制在离散值: 7, 8, 9, 10, 11, 12, 13 mm</li>';
html{end+1} = '                <li><strong>齿数:</strong> 不小于17，不大于100</li>';
html{end+1} = '                <li><strong>变位系数:</strong>';
html{end+1} = '                    <ul>';
html{end+1} = '                        <li>综合变位系数: 0.4-1.0</li>';
html{end+1} = '                        <li>主动轮变位系数: 0.3-0.8</li>';
html{end+1} = '                        <li>从动轮变位系数: 0-0.5</li>';
html{end+1} = '                    </ul>';
html{end+1} = '                </li>';
html{end+1} = '                <li><strong>螺旋角:</strong> 8°-13°</li>';
html{end+1} = '                <li><strong>压力角:</strong> 20°</li>';
html{end+1} = '            </ul>';
html{end+1} = '        </div>';

% 添加颜色图例
html{end+1} = '        <div class="explanation">';
html{end+1} = '            <h4>表格颜色说明</h4>';
html{end+1} = '            <div class="color-legend">';
html{end+1} = '                <div class="color-item"><div class="color-box recommended-box"></div><span>推荐配置</span></div>';
html{end+1} = '                <div class="color-item"><div class="color-box geometry-box"></div><span>几何参数</span></div>';
html{end+1} = '                <div class="color-item"><div class="color-box shift-box"></div><span>变位系数</span></div>';
html{end+1} = '                <div class="color-item"><div class="color-box result-box"></div><span>结果参数</span></div>';
html{end+1} = '                <div class="color-item"><div class="color-box safety-box"></div><span>安全系数</span></div>';
html{end+1} = '            </div>';
html{end+1} = '        </div>';
html{end+1} = '    </div>';

% 表格部分
html{end+1} = '    <div class="container">';
html{end+1} = '        <h2>有效参数组合表</h2>';
html{end+1} = '        <div class="table-responsive">';
html{end+1} = '            <table id="gearTable" class="display compact stripe hover">';
html{end+1} = '                <thead>';
html{end+1} = '                    <tr>';
html{end+1} = '                        <th>序号</th>';
html{end+1} = '                        <th class="group-geometry">模数<br>(mm)</th>';
html{end+1} = '                        <th class="group-geometry">小齿轮<br>齿数</th>';
html{end+1} = '                        <th class="group-geometry">大齿轮<br>齿数</th>';
html{end+1} = '                        <th class="group-geometry">传动比</th>';
html{end+1} = '                        <th class="group-geometry">螺旋角<br>(°)</th>';
html{end+1} = '                        <th class="group-geometry">压力角<br>(°)</th>';
html{end+1} = '                        <th class="group-geometry">齿宽系数</th>';
html{end+1} = '                        <th class="group-shift">小齿轮<br>变位系数</th>';
html{end+1} = '                        <th class="group-shift">大齿轮<br>变位系数</th>';
html{end+1} = '                        <th class="group-shift">综合<br>变位系数</th>';
html{end+1} = '                        <th class="group-result">实际中心距<br>(mm)</th>';
html{end+1} = '                        <th class="group-result">齿宽<br>(mm)</th>';
html{end+1} = '                        <th class="group-result">估计质量<br>(kg)</th>';
html{end+1} = '                        <th class="group-safety">接触<br>安全系数</th>';
html{end+1} = '                        <th class="group-safety">小齿轮<br>弯曲安全系数</th>';
html{end+1} = '                        <th class="group-safety">大齿轮<br>弯曲安全系数</th>';
html{end+1} = '                    </tr>';
html{end+1} = '                </thead>';
html{end+1} = '                <tbody>';

% 获取表格列名
col_names = gear_combinations.Properties.VariableNames;

% 添加数据行
for i = 1:height(gear_combinations)
    row = gear_combinations(i,:);
    
    % 确定是否为推荐行（质量适中且安全系数最高的选项）
    is_recommended = false;
    if i <= 10 && row.(col_names{15}) >= 1.5 && row.(col_names{14}) >= 1.2 && row.(col_names{16}) >= 1.2
        is_recommended = true;
    end
    
    % 设置行样式
    if is_recommended
        html{end+1} = '                    <tr class="highlight-row">';
    else
        html{end+1} = '                    <tr>';
    end
    
    % 添加序号列
    html{end+1} = sprintf('                        <td>%d</td>', i);
    
    % 添加数据列 - 直接使用列索引，确保数据正确显示
    % 几何参数组
    html{end+1} = sprintf('                        <td class="group-geometry">%.1f</td>', row{1, 1});  % 模数
    html{end+1} = sprintf('                        <td class="group-geometry">%d</td>', row{1, 2});    % 小齿轮齿数
    html{end+1} = sprintf('                        <td class="group-geometry">%d</td>', row{1, 3});    % 大齿轮齿数
    html{end+1} = sprintf('                        <td class="group-geometry">%.3f</td>', row{1, 4});  % 传动比
    html{end+1} = sprintf('                        <td class="group-geometry">%.1f</td>', row{1, 5});  % 螺旋角
    html{end+1} = sprintf('                        <td class="group-geometry">%.1f</td>', row{1, 6});  % 压力角
    html{end+1} = sprintf('                        <td class="group-geometry">%.3f</td>', row{1, 7});  % 齿宽系数
    
    % 变位系数组 - 精确到4位小数
    html{end+1} = sprintf('                        <td class="group-shift">%.4f</td>', row{1, 8});     % 小齿轮变位系数
    html{end+1} = sprintf('                        <td class="group-shift">%.4f</td>', row{1, 9});     % 大齿轮变位系数
    html{end+1} = sprintf('                        <td class="group-shift">%.4f</td>', row{1, 10});    % 综合变位系数
    
    % 结果参数组
    html{end+1} = sprintf('                        <td class="group-result">%.2f</td>', row{1, 11});   % 实际中心距
    html{end+1} = sprintf('                        <td class="group-result">%.2f</td>', row{1, 12});   % 齿宽
    html{end+1} = sprintf('                        <td class="group-result">%.2f</td>', row{1, 13});   % 估计质量
    
    % 安全系数组 - 接触安全系数和两个弯曲安全系数
    html{end+1} = sprintf('                        <td class="group-safety">%.3f</td>', row{1, 16});   % 接触安全系数
    html{end+1} = sprintf('                        <td class="group-safety">%.3f</td>', row{1, 14});   % 小齿轮弯曲安全系数
    html{end+1} = sprintf('                        <td class="group-safety">%.3f</td>', row{1, 15});   % 大齿轮弯曲安全系数
    
    html{end+1} = '                    </tr>';
end

html{end+1} = '                </tbody>';
html{end+1} = '            </table>';
html{end+1} = '        </div>';

% 添加说明信息
html{end+1} = '        <div class="explanation">';
html{end+1} = '            <h4>参数说明</h4>';
html{end+1} = '            <ul>';
html{end+1} = '                <li><strong>绿色背景的行</strong>为推荐配置 (质量较轻且安全系数较高)</li>';
html{end+1} = '                <li><strong>中心距:</strong> 实际中心距误差控制在±0.01%以内，确保与目标值(400 mm)精确匹配</li>';
html{end+1} = '                <li><strong>变位系数:</strong> 精确到小数点后4位，综合变位系数为小齿轮和大齿轮变位系数之和</li>';
html{end+1} = '                <li><strong>安全系数:</strong> 包括接触安全系数和弯曲安全系数，计算方法与优化算法保持一致</li>';
html{end+1} = '                <li><strong>接触安全系数:</strong> 反映齿面接触疲劳强度裕度，防止齿面点蚀</li>';
html{end+1} = '                <li><strong>弯曲安全系数:</strong> 分别显示小齿轮和大齿轮的齿根弯曲疲劳强度裕度，防止齿根断裂</li>';
html{end+1} = '                <li><strong>压力角:</strong> 标准压力角固定为20°，用于计算齿轮几何参数和啮合特性</li>';
html{end+1} = '            </ul>';
html{end+1} = '        </div>';
html{end+1} = '    </div>';

% 添加页脚信息
html{end+1} = '    <div class="footnote">';
html{end+1} = sprintf('        生成时间: %s | 三级减速机齿轮传动系统优化', datestr(now, 'yyyy-mm-dd HH:MM:SS'));
html{end+1} = '    </div>';
html{end+1} = '</div>';

% DataTables初始化脚本
html{end+1} = '<script>';
html{end+1} = '    $(document).ready(function() {';
html{end+1} = '        var table = $("#gearTable").DataTable({';
html{end+1} = '            "language": {';
html{end+1} = '                "search": "搜索:",';
html{end+1} = '                "lengthMenu": "每页显示 _MENU_ 条记录",';
html{end+1} = '                "zeroRecords": "没有找到匹配的记录",';
html{end+1} = '                "info": "显示第 _START_ 至 _END_ 条记录，共 _TOTAL_ 条",';
html{end+1} = '                "infoEmpty": "显示第 0 至 0 条记录，共 0 条",';
html{end+1} = '                "infoFiltered": "(从 _MAX_ 条记录过滤)",';
html{end+1} = '                "paginate": {';
html{end+1} = '                    "first": "首页",';
html{end+1} = '                    "last": "末页",';
html{end+1} = '                    "next": "下一页",';
html{end+1} = '                    "previous": "上一页"';
html{end+1} = '                }';
html{end+1} = '            },';
html{end+1} = '            "pageLength": 25,';
html{end+1} = '            "lengthMenu": [[10, 25, 50, 100, -1], [10, 25, 50, 100, "全部"]],';
html{end+1} = '            "order": [[13, "asc"]], // 默认按质量排序';
html{end+1} = '            "responsive": true,';
html{end+1} = '            "columnDefs": [';
html{end+1} = '                { className: "group-geometry", targets: [1, 2, 3, 4, 5, 6, 7] },';
html{end+1} = '                { className: "group-shift", targets: [8, 9, 10] },';
html{end+1} = '                { className: "group-result", targets: [11, 12, 13] },';
html{end+1} = '                { className: "group-safety", targets: [14, 15, 16] }';
html{end+1} = '            ]';
html{end+1} = '        });';
html{end+1} = '    });';
html{end+1} = '</script>';
html{end+1} = '</body>';
html{end+1} = '</html>';

% 将cell数组连接为字符串，使用换行符连接
html_output = strjoin(html, '\n');
end 