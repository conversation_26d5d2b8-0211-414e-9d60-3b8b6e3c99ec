function [shift_ranges, optimal_shifts] = CalculatePlanetaryShiftCoefficients(gear_params)
% CalculatePlanetaryShiftCoefficients 计算行星轮系的科学变位系数范围和最优值
%
%   输入参数:
%   - gear_params: 齿轮几何参数结构体，包含字段:
%     - z1: 太阳轮齿数 (或外齿轮1)
%     - z2: 行星轮齿数 (或外齿轮2)
%     - zr: 内齿圈齿数 (可选，内啮合时需要)
%     - alpha: 压力角 (度)
%     - beta: 螺旋角 (度)
%     - module: 模数 (mm)
%     - center_distance: 中心距 (mm，可选)
%     - is_planetary: 是否为行星轮系 (布尔值)
%     - is_internal: 是否为内啮合 (布尔值)
%
%   输出:
%   - shift_ranges: 变位系数范围结构体，包含字段:
%     - x1_min, x1_max: 第一个齿轮变位系数范围
%     - x2_min, x2_max: 第二个齿轮变位系数范围
%     - xr_min, xr_max: 内齿圈变位系数范围 (内啮合时)
%     - sum_min, sum_max: 变位系数和的范围
%   - optimal_shifts: 最优变位系数结构体

% 提取参数
z1 = gear_params.z1;  % 太阳轮齿数 (或外齿轮1)
z2 = gear_params.z2;  % 行星轮齿数 (或外齿轮2)

% 确保压力角和螺旋角是弧度
alpha_deg = gear_params.alpha;
alpha = deg2rad(alpha_deg);
beta_deg = gear_params.beta;
beta = deg2rad(beta_deg);

% 计算端面压力角
alpha_t = atan(tan(alpha) / cos(beta));

% 模数
module = gear_params.module;

% 获取内齿圈齿数(如果有)
if isfield(gear_params, 'zr')
    zr = gear_params.zr;
else
    zr = 0;  % 非内啮合情况下不使用
end

% 是否为行星轮系和内啮合
is_planetary = false;
if isfield(gear_params, 'is_planetary')
    is_planetary = gear_params.is_planetary;
end

is_internal = false;
if isfield(gear_params, 'is_internal')
    is_internal = gear_params.is_internal;
end

% 初始化变位系数范围
shift_ranges = struct();
optimal_shifts = struct();

% 1. 根据齿数计算防止根切的最小变位系数
% 对于20度压力角，无变位时最小齿数为17，对于较小的齿数需要正变位
x1_min_undercut = max(0, (17 - z1) / 17);
x2_min_undercut = max(0, (17 - z2) / 17);

% 2. 根据齿顶厚度要求计算最大变位系数
% 齿顶厚度不小于0.4模数
x1_max_tip = calc_max_x_for_tip_thickness(z1, alpha_t);
x2_max_tip = calc_max_x_for_tip_thickness(z2, alpha_t);

% 3. 计算中心距
if isfield(gear_params, 'center_distance') && ~isempty(gear_params.center_distance)
    actual_center_distance = gear_params.center_distance;
    % 计算标准中心距用于后续计算
    normal_center_distance = module * (z1 + z2) / (2 * cos(beta));
else
    % 计算标准中心距
    normal_center_distance = module * (z1 + z2) / (2 * cos(beta));
    actual_center_distance = normal_center_distance;
end

% 4. 计算变位系数和的范围，以满足中心距要求
if ~is_internal
    % 外啮合
    center_sum = (z1 + z2)/2;  % 外啮合中心距
    % 计算啮合角余弦值
    cos_alpha_w = normal_center_distance * cos(alpha_t) / actual_center_distance;
    alpha_w = acos(cos_alpha_w);  % 啮合压力角
    
    % 计算渐开线函数
    inv_alpha = tan(alpha_t) - alpha_t;
    inv_alpha_w = tan(alpha_w) - alpha_w;
    
    % 计算满足中心距要求的变位系数和
    sum_x_sp = (z1 + z2) * (inv_alpha_w - inv_alpha) / (2 * tan(alpha_t));
    
    % 5. 计算平衡滑移变位系数分配
    u_sp = z2 / z1; % 太阳轮-行星轮传动比
    
    % 6. 考虑重合度要求，重合度不小于1.2
    % 为保证足够的重合度，计算最小变位系数
    [x1_min_eps, x2_min_eps] = get_min_shifts_for_contact_ratio(z1, z2, alpha_t, module, false);
    
    % 7. 考虑啮合干涉，计算干涉限制下的变位系数范围
    [x1_min_interf, x2_max_interf] = check_interference(z1, z2, alpha_t, false);
    
    % 8. 综合考虑各种约束，确定最终变位系数范围
    x1_min = max([x1_min_undercut, x1_min_eps, x1_min_interf, 0]); % 不小于0
    x1_max = min([x1_max_tip, sum_x_sp, 1.0]); % 不大于1.0
    
    x2_min = max([x2_min_undercut, x2_min_eps, 0]); % 不小于0
    x2_max = min([x2_max_tip, sum_x_sp - x1_min, x2_max_interf, 1.0]); % 不大于1.0
    
    % 确保变位系数和在合理范围内
    sum_min = max(0.6, x1_min + x2_min);
    sum_max = min(1.2, x1_max + x2_max);
    
    % 如果是行星轮系，还需要考虑内齿圈变位系数
    if is_planetary && zr > 0
        % 9. 根据太阳轮和行星轮的变位系数计算内齿圈变位系数
        % 内齿圈变位系数 xr = -(x1*z1 + x2*z2) / zr
        xr_min = -(x1_max * z1 + x2_max * z2) / zr;
        xr_max = -(x1_min * z1 + x2_min * z2) / zr;
        
        % 如果内齿圈变位系数超出合理范围，调整x1和x2
        if xr_min < -0.5 || xr_max > 0.3
            % 调整变位系数和的范围
            if xr_min < -0.5
                xr_calc = -(x1_min * z1 + x2_min * z2) / zr;
                if xr_calc < -0.5
                    % 需要减小变位系数和
                    adj_factor = (-0.5 * zr) / (x1_min * z1 + x2_min * z2);
                    x1_min = adj_factor * x1_min;
                    x2_min = adj_factor * x2_min;
                end
            end
            
            if xr_max > 0.3
                xr_calc = -(x1_max * z1 + x2_max * z2) / zr;
                if xr_calc > 0.3
                    % 需要增大变位系数和
                    adj_factor = (0.3 * zr) / (x1_max * z1 + x2_max * z2);
                    x1_max = adj_factor * x1_max;
                    x2_max = adj_factor * x2_max;
                end
            end
            
            % 重新计算内齿圈变位系数范围
            xr_min = -(x1_max * z1 + x2_max * z2) / zr;
            xr_max = -(x1_min * z1 + x2_min * z2) / zr;
        end
        
        % 添加内齿圈变位系数范围
        shift_ranges.xr_min = xr_min;
        shift_ranges.xr_max = xr_max;
    end
else
    % 内啮合(行星轮-内齿圈)
    % 内齿圈变位系数通过力平衡约束确定，这里主要计算行星轮变位系数范围
    
    % 增加针对内齿圈的特殊处理
    % 对于内啮合，防止根切的最小变位系数计算有所不同
    if z2 < 85
        x2_min_undercut = max(0, (85 - z2) / 85);
    end
    
    % 内啮合中，变位系数有特殊约束
    x1_min = max(x1_min_undercut, 0);  % 行星轮变位系数下限
    x1_max = min(x1_max_tip, 0.8);    % 行星轮变位系数上限
    
    x2_min = -0.5;  % 内齿圈变位系数下限
    x2_max = 0.3;   % 内齿圈变位系数上限
    
    % 根据中心距约束计算变位系数和
    % 对于内啮合：a = (z2 - z1) * m / 2
    if abs(actual_center_distance - module * (z2 - z1) / 2) > 0.01
        % 如果实际中心距与标准中心距不同，需要调整变位系数和
        % 计算内啮合的啮合压力角
        cos_alpha_w = module * (z2 - z1) * cos(alpha_t) / (2 * actual_center_distance);
        alpha_w = acos(cos_alpha_w);
        
        inv_alpha = tan(alpha_t) - alpha_t;
        inv_alpha_w = tan(alpha_w) - alpha_w;
        
        % 内啮合的变位系数和
        sum_x_pr = (z2 - z1) * (inv_alpha_w - inv_alpha) / (2 * tan(alpha_t));
        
        % 根据变位系数和调整范围
        x1_min = max(x1_min, sum_x_pr - x2_max);
        x1_max = min(x1_max, sum_x_pr - x2_min);
    end
    
    sum_min = x1_min + x2_min;
    sum_max = x1_max + x2_max;
end

% 存储变位系数范围
shift_ranges.x1_min = x1_min;
shift_ranges.x1_max = x1_max;
shift_ranges.x2_min = x2_min;
shift_ranges.x2_max = x2_max;
shift_ranges.sum_min = sum_min;
shift_ranges.sum_max = sum_max;

% 10. 计算最优变位系数
if ~is_internal
    % 外啮合使用Maag公式
    % x1 = sum_x * (u - 1) / (u + 1)
    % x2 = sum_x - x1
    sum_x_opt = (sum_min + sum_max) / 2; % 使用变位系数和的中间值
    
    % 使用改进的Maag公式，考虑中心距和重合度要求
    x1_opt = sum_x_opt * (u_sp - 1) / (u_sp + 1);
    x2_opt = sum_x_opt - x1_opt;
    
    % 确保在计算的范围内
    x1_opt = max(x1_min, min(x1_max, x1_opt));
    x2_opt = max(x2_min, min(x2_max, x2_opt));
    
    % 重新计算变位系数和
    sum_x_opt = x1_opt + x2_opt;
    
    optimal_shifts.x1_opt = x1_opt;
    optimal_shifts.x2_opt = x2_opt;
    optimal_shifts.sum_opt = sum_x_opt;
    
    % 如果是行星轮系，还需要计算内齿圈的最优变位系数
    if is_planetary && zr > 0
        optimal_shifts.xr_opt = -(optimal_shifts.x1_opt * z1 + optimal_shifts.x2_opt * z2) / zr;
        
        % 11. 检查重合度
        eps_sp = calculate_contact_ratio(z1, z2, optimal_shifts.x1_opt, optimal_shifts.x2_opt, alpha_t, module, false);
        
        % 如果重合度不足，调整变位系数
        if eps_sp < 1.2
            % 可以增大变位系数和来提高重合度
            for test_sum = sum_x_opt:0.01:sum_max
                % 按比例调整两个齿轮的变位系数
                test_x1 = max(x1_min, min(x1_max, test_sum * (u_sp - 1) / (u_sp + 1)));
                test_x2 = test_sum - test_x1;
                
                % 检查内齿圈变位系数是否在合理范围内
                test_xr = -(test_x1 * z1 + test_x2 * z2) / zr;
                if test_xr < -0.5 || test_xr > 0.3
                    continue;  % 跳过不符合要求的变位系数组合
                end
                
                new_eps_sp = calculate_contact_ratio(z1, z2, test_x1, test_x2, alpha_t, module, false);
                
                if new_eps_sp >= 1.2
                    optimal_shifts.x1_opt = test_x1;
                    optimal_shifts.x2_opt = test_x2;
                    optimal_shifts.sum_opt = test_sum;
                    optimal_shifts.xr_opt = test_xr;
                    break;
                end
            end
        end
    end
else
    % 内啮合使用内啮合特定的最优变位系数计算
    % 对于内啮合，我们更关注避免干涉，而非平衡滑移
    u_pr = -z2 / z1; % 内啮合传动比(负值)
    
    % 计算中心距并确定工作压力角
    a_ac = module * (z2 - z1) / 2; % 行星轮与内齿圈的中心距
    
    % 对于内啮合，可以考虑使用总和为零的变位系数组合
    % 这样可以保持标准中心距
    sum_x_opt = 0;
    
    % 但如果有中心距要求，需要调整变位系数和
    if abs(actual_center_distance - a_ac) > 0.01
        % 计算需要的变位系数和
        cos_alpha_w = a_ac * cos(alpha_t) / actual_center_distance;
        alpha_w = acos(cos_alpha_w);
        
        inv_alpha = tan(alpha_t) - alpha_t;
        inv_alpha_w = tan(alpha_w) - alpha_w;
        
        sum_x_opt = (z2 - z1) * (inv_alpha_w - inv_alpha) / (2 * tan(alpha_t));
    end
    
    % 确保变位系数和在合理范围内
    sum_x_opt = max(sum_min, min(sum_max, sum_x_opt));
    
    % 分配变位系数 - 内啮合Maag公式
    if u_pr < -1 % 如果内齿圈齿数大于行星轮齿数的2倍以上
        x1_opt = sum_x_opt * (abs(u_pr) + 1) / (abs(u_pr) - 1);
    else
        x1_opt = sum_x_opt / 2; % 平均分配
    end
    x2_opt = sum_x_opt - x1_opt;
    
    % 确保在计算的范围内
    x1_opt = max(x1_min, min(x1_max, x1_opt));
    x2_opt = max(x2_min, min(x2_max, x2_opt));
    
    optimal_shifts.x1_opt = x1_opt;
    optimal_shifts.x2_opt = x2_opt;
    optimal_shifts.sum_opt = x1_opt + x2_opt;
    
    % 行星轮系中，二级内齿圈的变位系数也需要满足一级的约束
    if is_planetary && zr > 0
        optimal_shifts.xr_opt = -(optimal_shifts.x1_opt * z1 + optimal_shifts.x2_opt * z2) / zr;
    end
end

% 返回优化后的变位系数和范围
end

% 辅助函数：计算满足最小齿顶厚度要求的最大变位系数
function x_max = calc_max_x_for_tip_thickness(z, alpha_t)
    % 基于ISO 6336，齿顶厚度不应小于0.4模数
    % 使用简化公式：
    % sa* = 1 - 2*x/z * tan(alpha_t) - inv(alpha_t) + inv(alphat)
    % 其中sa*是相对齿顶厚度(sa/m)
    
    % 最小齿顶厚度系数 (sa/m)
    sa_min = 0.4;
    
    % 计算渐开线函数
    inv_alpha = tan(alpha_t) - alpha_t;
    
    % 反向计算满足最小齿顶厚度要求的最大变位系数
    % 注意：这是一个简化的公式，实际情况可能更复杂
    x_max = z * (0.5 - sa_min * tan(alpha_t) / z);
    
    % 限制在合理范围内
    x_max = min(max(0, x_max), 1.0);
end

% 辅助函数：计算最小变位系数以满足重合度要求
function [x1_min, x2_min] = get_min_shifts_for_contact_ratio(z1, z2, alpha_t, module, is_internal)
    % 初始估计
    x1_min = 0;
    x2_min = 0;
    
    % 如果有必要，根据重合度要求调整
    if ~is_internal
        % 对于外啮合，可以增加变位系数来提高重合度
        eps_min = 1.2; % 最小重合度
        
        % 简化计算，假设变位系数对重合度的影响线性相关
        % 实际应该使用齿轮几何计算公式
        
        % 默认值
        x1_min = 0;
        x2_min = 0;
    else
        % 对于内啮合，增加行星轮变位系数可能会降低重合度
        x1_min = 0;
        x2_min = 0;
    end
end

% 辅助函数：检查干涉并计算限制变位系数
function [x1_min, x2_max] = check_interference(z1, z2, alpha_t, is_internal)
    % 初始估计
    x1_min = 0;
    x2_max = 1.0;
    
    % 如果有必要，根据干涉条件调整
    if ~is_internal
        % 外啮合干涉检查
        % 以下是一个简化的计算，实际应根据齿轮几何学计算
        
        % 干涉条件：x1 + x2 <= (z1 + z2) * sin(alpha)^2 / 2
        interf_limit = (z1 + z2) * sin(alpha_t)^2 / 2;
        
        % 假设x1_min为0，则x2_max为干涉限制
        x1_min = 0;
        x2_max = interf_limit;
    else
        % 内啮合干涉检查
        % 对于内啮合，变位系数存在更复杂的约束
        % 这里使用简化模型
        
        % 内啮合特有的干涉条件
        x1_min = 0;
        x2_max = 0.3; % 内齿圈一般使用负变位，上限较低
    end
end

% 辅助函数：计算重合度
function eps = calculate_contact_ratio(z1, z2, x1, x2, alpha_t, module, is_internal)
    % 这是一个简化的重合度计算
    % 实际应使用更复杂的齿轮几何公式
    
    if ~is_internal
        % 外啮合重合度计算
        % 根据ISO 6336，外啮合重合度计算公式近似为：
        % epsilon_alpha = (sqrt(da1^2 - db1^2) + sqrt(da2^2 - db2^2) - 2*a*sin(alpha_t)) / (2*pi*m*cos(alpha_t))
        
        % 分度圆半径
        r1 = module * z1 / 2;
        r2 = module * z2 / 2;
        
        % 基圆半径
        rb1 = r1 * cos(alpha_t);
        rb2 = r2 * cos(alpha_t);
        
        % 齿顶圆半径(考虑变位)
        ha_star = 1.0; % 标准齿顶高系数
        da1 = 2 * (r1 + module * (ha_star + x1));
        da2 = 2 * (r2 + module * (ha_star + x2));
        
        % 中心距
        a = (r1 + r2) * (1 + (x1 + x2) / (z1 + z2) * 2 * tan(alpha_t));
        
        % 计算重合度
        eps = (sqrt(da1^2/4 - rb1^2) + sqrt(da2^2/4 - rb2^2) - a * sin(alpha_t)) / (pi * module * cos(alpha_t));
    else
        % 内啮合重合度计算
        % 对于内啮合，重合度计算更复杂
        % 这里提供一个近似值
        eps = 1.5; % 内啮合通常有更高的重合度
    end
end 