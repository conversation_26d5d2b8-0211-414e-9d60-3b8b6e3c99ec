function [pop, F1] = NSGA3(CostFunction, nVar, Var<PERSON>in, VarMax, MaxIt, nPop)
% NSGA-III: Non-dominated Sorting Genetic Algorithm III
%
% Inputs:
%   CostFunction: Function handle to the objective function
%   nVar: Number of decision variables
%   VarMin, VarMax: Lower and upper bounds of variables
%   MaxIt: Maximum number of iterations
%   nPop: Population size
%
% Outputs:
%   pop: Final population
%   F1: First front solutions (Pareto front)
%
% Reference:
%   <PERSON><PERSON> and <PERSON><PERSON>, "An Evolutionary Many-Objective Optimization Algorithm 
%   Using Reference-Point-Based Nondominated Sorting Approach, Part I: Solving
%   Problems With Box Constraints," IEEE Transactions on Evolutionary Computation,
%   vol. 18, no. 4, pp. 577-601, 2014.

    % Default parameter values if not provided
    if nargin < 6
        nPop = 80;
    end
    if nargin < 5
        MaxIt = 50;
    end
    if nargin < 4
        VarMax = 1;
    end
    if nargin < 3
        VarMin = 0;
    end
    if nargin < 2
        nVar = 5;
    end
    
    % Decision variables matrix size
    VarSize = [1 nVar];
    
    % Get number of objectives
    nObj = numel(CostFunction(unifrnd(VarMin, VarMax, VarSize)));
    
    %% NSGA-III Parameters
    
    % Generating Reference Points
    nDivision = 12;
    Zr = GenerateReferencePoints(nObj, nDivision);
    
    pCrossover = 0.5;                        % Crossover percentage
    nCrossover = 2*round(pCrossover*nPop/2); % Number of parents (offsprings)
    
    pMutation = 0.5;                         % Mutation percentage
    nMutation = round(pMutation*nPop);       % Number of mutants
    
    mu = 0.02;                               % Mutation rate
    sigma = 0.1*(VarMax-VarMin);             % Mutation step size
    
    %% Collect Parameters
    params.nPop = nPop;
    params.Zr = Zr;
    params.nZr = size(Zr, 2);
    params.zmin = [];
    params.zmax = [];
    params.smin = [];
    
    %% Initialization
    empty_individual.Position = [];
    empty_individual.Cost = [];
    empty_individual.Rank = [];
    empty_individual.DominationSet = [];
    empty_individual.DominatedCount = [];
    empty_individual.NormalizedCost = [];
    empty_individual.AssociatedRef = [];
    empty_individual.DistanceToAssociatedRef = [];
    
    pop = repmat(empty_individual, nPop, 1);
    for i = 1:nPop
        pop(i).Position = unifrnd(VarMin, VarMax, VarSize);
        pop(i).Cost = CostFunction(pop(i).Position);
    end
    
    % Sort Population and Perform Selection
    [pop, F, params] = SortAndSelectPopulation(pop, params);
    
    %% NSGA-III Main Loop
    for it = 1:MaxIt
        
        % Crossover
        popc = repmat(empty_individual, nCrossover/2, 2);
        for k = 1:nCrossover/2
            
            i1 = randi([1 nPop]);
            p1 = pop(i1);
            
            i2 = randi([1 nPop]);
            p2 = pop(i2);
            
            [popc(k, 1).Position, popc(k, 2).Position] = Crossover(p1.Position, p2.Position);
            
            popc(k, 1).Cost = CostFunction(popc(k, 1).Position);
            popc(k, 2).Cost = CostFunction(popc(k, 2).Position);
            
        end
        popc = popc(:);
        
        % Mutation
        popm = repmat(empty_individual, nMutation, 1);
        for k = 1:nMutation
            
            i = randi([1 nPop]);
            p = pop(i);
            
            popm(k).Position = Mutate(p.Position, mu, sigma);
            
            popm(k).Cost = CostFunction(popm(k).Position);
            
        end
        
        % Merge
        pop = [pop; popc; popm];
        
        % Sort Population and Perform Selection
        [pop, F, params] = SortAndSelectPopulation(pop, params);
        
        % Store F1
        F1 = pop(F{1});
    end
end

function [pop, F, params] = SortAndSelectPopulation(pop, params)
    % Non-Dominated Sorting
    [pop, F] = NonDominatedSorting(pop);

    % Update Ideal Point
    params = UpdateIdealPoint(pop, params);
    
    % Normalize Population
    [pop, params] = NormalizePopulation(pop, params);
    
    % Sort Population
    pop = SortAndSelectPopulationNSGA3(pop, F, params);
end

function Zr = GenerateReferencePoints(M, p)
    % Generate reference points for NSGA-III
    % M: Number of objectives
    % p: Number of divisions
    
    if M == 1
        Zr = 1;
        return;
    end
    
    if M == 2
        Zr = (0:p)/p;
        Zr = [Zr; 1-Zr];
        return;
    end
    
    Zr = [];
    for i = 0:p
        Zrm1 = GenerateReferencePoints(M-1, p-i);
        Zrm1 = [Zrm1; ones(1, size(Zrm1, 2))*(i/p)];
        Zr = [Zr, Zrm1];
    end
end

function params = UpdateIdealPoint(pop, params)
    % Update ideal point
    if isempty(params.zmin)
        params.zmin = min([pop.Cost], [], 2)';
    else
        params.zmin = min(params.zmin, min([pop.Cost], [], 2)');
    end
    
    % Update maximum point (optional)
    if isempty(params.zmax)
        params.zmax = max([pop.Cost], [], 2)';
    else
        params.zmax = max(params.zmax, max([pop.Cost], [], 2)');
    end
end

function [pop, params] = NormalizePopulation(pop, params)
    % Get costs and normalize them
    nPop = numel(pop);
    costs = reshape([pop.Cost], [], nPop)';
    
    % Apply normalization (NSGA-III style)
    zmin = params.zmin;
    normalized_costs = costs - zmin;
    
    % Find extreme points and calculate intercepts
    nObj = size(costs, 2);
    extreme_points = zeros(nObj, nObj);
    
    for j = 1:nObj
        weight = ones(1, nObj) * 0.00001;
        weight(j) = 1;
        
        % Find the solution with minimum weighted sum
        [~, idx] = min(normalized_costs * weight');
        extreme_points(j, :) = normalized_costs(idx, :);
    end
    
    % Calculate intercepts using extreme points
    if det(extreme_points) ~= 0
        b = ones(nObj, 1);
        a = extreme_points\b;
        intercepts = 1./a;
    else
        % Fallback if extreme points matrix is singular
        intercepts = max(normalized_costs, [], 1);
    end
    
    % Scale using intercepts
    for i = 1:nPop
        pop(i).NormalizedCost = normalized_costs(i, :) ./ intercepts;
    end
    
    params.smin = intercepts;
end

function pop = SortAndSelectPopulationNSGA3(pop, F, params)
    % Get existing population size
    nPop = params.nPop;
    
    % Current population size
    popSize = numel(pop);
    
    % Number of fronts
    nF = numel(F);
    
    % Select from F1 to F_{l-1}
    Selected = [];
    for i = 1:nF
        if numel(Selected) + numel(F{i}) <= nPop
            Selected = [Selected; F{i}];
        else
            Last = i;
            nToSelect = nPop - numel(Selected);
            break;
        end
    end
    
    % Associate each solution with a reference point
    pop = AssociateToReferencePoint(pop, params);
    
    % Select solutions from last front
    if numel(Selected) < nPop
        % Calculate niche counts for each reference point
        [pop, F{Last}] = PerformScalarizing(pop, F{Last}, nToSelect, params);
        Selected = [Selected; F{Last}];
    end
    
    % Select final population
    pop = pop(Selected);
end

function pop = AssociateToReferencePoint(pop, params)
    % Associate each solution with a reference point based on perpendicular distance
    Zr = params.Zr;
    nZr = params.nZr;
    
    for i = 1:numel(pop)
        z = pop(i).NormalizedCost;
        
        % Calculate distances to reference lines
        d = zeros(1, nZr);
        for j = 1:nZr
            w = Zr(:, j)';
            d(j) = norm(z - (dot(z, w)/dot(w, w))*w);
        end
        
        % Find nearest reference point
        [d_min, idx] = min(d);
        
        pop(i).AssociatedRef = idx;
        pop(i).DistanceToAssociatedRef = d_min;
    end
end

function [pop, LastFront] = PerformScalarizing(pop, LastFront, nToSelect, params)
    % Calculate niche count for each reference point
    Zr = params.Zr;
    nZr = size(Zr, 2);
    
    % Get niche count
    niche_count = zeros(1, nZr);
    for i = 1:numel(pop)
        j = pop(i).AssociatedRef;
        niche_count(j) = niche_count(j) + 1;
    end
    
    % Select solutions from last front
    Selected = [];
    
    % First, select from reference points with no associated solutions
    while numel(Selected) < nToSelect
        % Find reference points with minimum niche count
        min_niche_count = min(niche_count);
        min_niche_ref = find(niche_count == min_niche_count);
        
        if isempty(min_niche_ref)
            break;
        end
        
        % Randomly select one reference point
        j_min = min_niche_ref(randi(numel(min_niche_ref)));
        
        % Find solutions associated with this reference point
        associated_solutions = [];
        for i = LastFront
            if pop(i).AssociatedRef == j_min
                associated_solutions = [associated_solutions, i];
            end
        end
        
        if isempty(associated_solutions)
            % If no solution is associated, exclude this reference point
            niche_count(j_min) = inf;
        else
            % Select the solution with minimum perpendicular distance
            min_dist = inf;
            min_idx = 0;
            for i = associated_solutions
                if pop(i).DistanceToAssociatedRef < min_dist
                    min_dist = pop(i).DistanceToAssociatedRef;
                    min_idx = i;
                end
            end
            
            % Add to selected solutions and update niche count
            Selected = [Selected, min_idx];
            LastFront(LastFront == min_idx) = [];
            niche_count(j_min) = niche_count(j_min) + 1;
            
            % Break if we have selected enough solutions
            if numel(Selected) >= nToSelect
                break;
            end
        end
    end
    
    % If not enough solutions selected, randomly select the rest
    if numel(Selected) < nToSelect
        remaining = nToSelect - numel(Selected);
        if numel(LastFront) > remaining
            % Randomly select the remaining solutions
            idx = randperm(numel(LastFront), remaining);
            Selected = [Selected, LastFront(idx)];
            LastFront(idx) = [];
        else
            % Select all remaining solutions
            Selected = [Selected, LastFront];
            LastFront = [];
        end
    end
    
    LastFront = Selected;
end

function [pop, F] = NonDominatedSorting(pop)
    nPop = numel(pop);
    
    % Initialize
    for i = 1:nPop
        pop(i).DominationSet = [];
        pop(i).DominatedCount = 0;
    end
    
    F{1} = [];
    
    for i = 1:nPop
        for j = i+1:nPop
            p = pop(i);
            q = pop(j);
            
            if Dominates(p.Cost, q.Cost)
                p.DominationSet = [p.DominationSet j];
                q.DominatedCount = q.DominatedCount + 1;
            end
            
            if Dominates(q.Cost, p.Cost)
                q.DominationSet = [q.DominationSet i];
                p.DominatedCount = p.DominatedCount + 1;
            end
            
            pop(i) = p;
            pop(j) = q;
        end
        
        if pop(i).DominatedCount == 0
            F{1} = [F{1} i];
            pop(i).Rank = 1;
        end
    end
    
    k = 1;
    while true
        Q = [];
        for i = F{k}
            p = pop(i);
            for j = p.DominationSet
                q = pop(j);
                q.DominatedCount = q.DominatedCount - 1;
                if q.DominatedCount == 0
                    Q = [Q j];
                    q.Rank = k + 1;
                end
                pop(j) = q;
            end
        end
        
        if isempty(Q)
            break;
        end
        
        F{k+1} = Q;
        k = k + 1;
    end
    
end

function [y1, y2] = Crossover(x1, x2)
    alpha = rand(size(x1));
    y1 = alpha.*x1 + (1-alpha).*x2;
    y2 = alpha.*x2 + (1-alpha).*x1;
end

function y = Mutate(x, mu, sigma)
    nVar = numel(x);
    nMu = ceil(mu*nVar);
    j = randsample(nVar, nMu);
    y = x;
    y(j) = x(j) + sigma*randn(size(j));
end

function b = Dominates(x, y)
    % Check if x dominates y
    b = all(x <= y) && any(x < y);
end 