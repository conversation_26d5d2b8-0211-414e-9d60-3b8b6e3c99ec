function [objectives, constraints] = OptimizeWithMultipleFirstStages(x, first_stage_params_array, input_power, input_speed, output_speed, ...
                                             service_life, contact_safety_factor, bending_safety_factor, gear_material, gear_materials, quality_grade)
% OptimizeWithMultipleFirstStages 使用多组一级平行轴系参数进行优化
%
%   输入参数:
%   - x: 优化变量 [first_stage_idx, mn2, zs2, zp2, k_h2, mn3, zs3, zp3, k_h3, planets_count_2, planets_count_3, 
%                  pressure_angle, helix_angle_2, helix_angle_3, xs2, xp2, xs3, xp3, pressure_angle_3_choice]
%   - first_stage_params_array: 一级平行轴系固定参数数组，每个元素包含以下字段:
%       - m: 模数 (mm)
%       - z1: 小齿轮齿数
%       - z2: 大齿轮齿数
%       - beta: 螺旋角 (度)
%       - alpha: 压力角 (度)
%       - x1: 小齿轮变位系数
%       - x2: 大齿轮变位系数
%       - k_h: 齿宽系数
%       - b: 齿宽 (mm)
%       - a: 中心距 (mm)
%   - input_power: 输入功率 (kW)
%   - input_speed: 输入转速 (rpm)
%   - output_speed: 输出转速 (rpm)
%   - service_life: 设计寿命 (h)
%   - contact_safety_factor: 接触安全系数要求
%   - bending_safety_factor: 弯曲安全系数要求
%   - gear_material: 齿轮材料参数
%   - gear_materials: 详细的齿轮材料参数
%   - quality_grade: 齿轮精度等级
%
%   输出:
%   - objectives: 优化目标值 [总质量, 最小弯曲安全系数, 最小接触安全系数]
%   - constraints: 约束违反程度

% 检查输入参数
if isempty(first_stage_params_array)
    error('一级参数数组为空');
end

% 检查x是否为有效输入
if isempty(x) || any(isnan(x)) || any(isinf(x))
    objectives = [Inf, 0, 0];
    constraints = Inf;
    return;
end

% 获取一级参数组合的索引（第一个变量）
first_stage_idx = round(x(1));

% 确保索引在有效范围内
num_first_stage_params = length(first_stage_params_array);
first_stage_idx = max(1, min(num_first_stage_params, first_stage_idx));

% 选择对应的一级参数组合
first_stage_params = first_stage_params_array(first_stage_idx);

% 检查是否应该显示详细调试信息
debug_output = false;
try
    % 尝试从主工作区获取debug_output标志
    debug_output = evalin('base', 'debug_output');
catch
    % 如果不存在，保持为false
    debug_output = false;
end

% 添加调试输出，显示所选一级参数的详细信息
if debug_output
    fprintf('DEBUG: 选择一级参数组合 #%d\n', first_stage_idx);
    if isfield(first_stage_params, 'm') && isfield(first_stage_params, 'z1') && isfield(first_stage_params, 'z2')
        fprintf('DEBUG: 模数 = %.1f mm, 小齿轮齿数 = %d, 大齿轮齿数 = %d\n', ...
                first_stage_params.m, first_stage_params.z1, first_stage_params.z2);
    end
end

% 提取二三级参数（从第二个变量开始）
second_third_stage_params = x(2:end);

% 调用OptimizeWithFixedFirstStage函数进行评估
try
    [objectives, constraints] = OptimizeWithFixedFirstStage(second_third_stage_params, first_stage_params, input_power, input_speed, output_speed, ...
                                                 service_life, contact_safety_factor, bending_safety_factor, ...
                                                 gear_material, gear_materials, quality_grade);
catch e
    % 捕获并处理错误
    fprintf('警告：使用一级参数组 #%d 评估时出错: %s\n', first_stage_idx, e.message);
    objectives = [Inf, 0, 0];
    constraints = Inf;
end

% 检查结果是否有效
if any(isnan(objectives)) || any(isinf(objectives))
    fprintf('警告：使用一级参数组 #%d 得到无效结果，使用默认值\n', first_stage_idx);
    objectives = [Inf, 0, 0];
    constraints = Inf;
end

end 

function [best_objectives, best_constraints, best_solution, best_first_stage_idx] = OptimizeWithMultipleFirstStagesEvaluator(x, first_stage_params_array, input_power, input_speed, output_speed, ...
                                             service_life, contact_safety_factor, bending_safety_factor, gear_material, gear_materials, quality_grade)
% OptimizeWithMultipleFirstStagesEvaluator 评估多组一级参数并返回最佳结果
%
%   输入参数:
%   - x: 优化变量 [mn2, zs2, zp2, k_h2, mn3, zs3, zp3, k_h3, planets_count_2, planets_count_3, 
%                  pressure_angle, helix_angle_2, helix_angle_3, xs2, xp2, xs3, xp3, pressure_angle_3_choice]
%   - first_stage_params_array: 一级平行轴系固定参数数组，每个元素包含以下字段
%   - 其他参数同OptimizeWithMultipleFirstStages
%
%   输出:
%   - best_objectives: 最佳优化目标值 [总质量, 最小弯曲安全系数, 最小接触安全系数]
%   - best_constraints: 最佳约束违反程度
%   - best_solution: 最佳解的完整参数
%   - best_first_stage_idx: 最佳一级参数的索引

% 初始化最佳结果
best_objectives = [Inf, 0, 0]; % 初始化为最差情况
best_constraints = Inf;
best_solution = [];
best_first_stage_idx = 0;

% 记录所有可行解
feasible_solutions = struct('objectives', {}, 'constraints', {}, 'solution', {}, 'first_stage_idx', {});
feasible_count = 0;

% 遍历每组一级参数
num_params = length(first_stage_params_array);
fprintf('评估 %d 组一级参数...\n', num_params);

for i = 1:num_params
    try
        % 获取当前一级参数
        current_params = first_stage_params_array(i);
        
        % 确保当前参数是有效的结构体
        if ~isstruct(current_params)
            fprintf('警告：第%d组一级参数不是有效的结构体，跳过\n', i);
            continue;
        end
        
        % 检查必要字段
        required_fields = {'m', 'z1', 'z2', 'beta', 'alpha', 'x1', 'x2', 'k_h', 'b', 'a'};
        missing_field = false;
        for j = 1:length(required_fields)
            if ~isfield(current_params, required_fields{j})
                fprintf('警告：第%d组一级参数缺少字段 %s，跳过\n', i, required_fields{j});
                missing_field = true;
                break;
            end
        end
        
        if missing_field
            continue;
        end
        
        % 使用当前一级参数进行优化
        [objectives, constraints] = OptimizeWithFixedFirstStage(x, current_params, input_power, input_speed, output_speed, ...
                                                     service_life, contact_safety_factor, bending_safety_factor, ...
                                                     gear_material, gear_materials, quality_grade);
        
        % 构建完整解
        full_solution = struct('first_stage', current_params, 'second_stage_third_stage', x, 'objectives', objectives);
        
        % 检查是否为可行解（约束违反程度为0）
        if constraints <= 0
            % 记录可行解
            feasible_count = feasible_count + 1;
            feasible_solutions(feasible_count).objectives = objectives;
            feasible_solutions(feasible_count).constraints = constraints;
            feasible_solutions(feasible_count).solution = full_solution;
            feasible_solutions(feasible_count).first_stage_idx = i;
            
            % 检查是否为最佳解（按总质量比较）
            if objectives(1) < best_objectives(1)
                best_objectives = objectives;
                best_constraints = constraints;
                best_solution = full_solution;
                best_first_stage_idx = i;
            end
        elseif best_constraints > 0 && constraints < best_constraints
            % 如果还没有找到可行解，则记录约束违反程度最小的解
            best_objectives = objectives;
            best_constraints = constraints;
            best_solution = full_solution;
            best_first_stage_idx = i;
        end
    catch e
        fprintf('警告：评估第%d组一级参数时出错: %s\n', i, e.message);
    end
    
    % 显示进度
    if mod(i, 5) == 0 || i == num_params
        fprintf('已评估 %d/%d 组参数，找到 %d 个可行解\n', i, num_params, feasible_count);
    end
end

% 如果找到了可行解，输出最佳结果
if feasible_count > 0
    fprintf('\n找到 %d 个可行解，最佳解使用第 %d 组一级参数\n', feasible_count, best_first_stage_idx);
    fprintf('最佳解: 总质量 = %.2f kg, 最小弯曲安全系数 = %.3f, 最小接触安全系数 = %.3f\n', ...
        best_objectives(1), -best_objectives(2), -best_objectives(3));
else
    fprintf('\n未找到可行解，最佳约束违反度的解使用第 %d 组一级参数\n', best_first_stage_idx);
    if ~isempty(best_objectives)
        fprintf('最佳解: 总质量 = %.2f kg, 最小弯曲安全系数 = %.3f, 最小接触安全系数 = %.3f, 约束违反度 = %.3f\n', ...
            best_objectives(1), -best_objectives(2), -best_objectives(3), best_constraints);
    end
end

end 