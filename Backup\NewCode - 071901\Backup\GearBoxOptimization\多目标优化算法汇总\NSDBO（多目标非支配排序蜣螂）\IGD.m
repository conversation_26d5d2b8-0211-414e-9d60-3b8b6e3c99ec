function Score = IGD(PopObj,PF)
% <metric> <min>
% Inverted generational distance

%------------------------------- Reference --------------------------------
% <PERSON><PERSON> <PERSON><PERSON> and <PERSON><PERSON> <PERSON><PERSON>, Solving multiobjective optimization
% problems using an artificial immune system, Genetic Programming and
% Evolvable Machines, 2005, 6(2): 163-190.
%------------------------------- Copyright --------------------------------
% Copyright (c) 2018-2019 BIMK Group.

    Distance = min(pdist2(PF,PopObj),[],2);
    Score    = mean(Distance);
end