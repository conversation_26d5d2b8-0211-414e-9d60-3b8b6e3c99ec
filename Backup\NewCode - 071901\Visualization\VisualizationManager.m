function VisualizationManager(optimization_results, algorithm_names, param_names, system_params)
% VisualizationManager 可视化管理器
% 保持原有可视化功能，提供统一的可视化接口
%
% 输入:
%   optimization_results - 优化结果结构体
%   algorithm_names - 算法名称列表
%   param_names - 参数名称列表
%   system_params - 系统参数

%% 1. 参数验证
if nargin < 4
    system_params = struct();
end

fprintf('开始创建可视化界面...\n');
fprintf('算法数量: %d\n', length(algorithm_names));

%% 2. 提取结果数据
[all_results, pareto_variables, pareto_solutions] = extractResultsData(optimization_results, algorithm_names);

%% 3. 创建主界面
fig = createMainFigure();

%% 4. 创建标签页
tab_group = uitabgroup(fig);

% Pareto前沿标签页
pareto_tab = uitab(tab_group, 'Title', 'Pareto前沿');
createParetoFrontPanel(pareto_tab, all_results, algorithm_names);

% 算法比较标签页
comparison_tab = uitab(tab_group, 'Title', '算法比较');
createAlgorithmComparisonPanel(comparison_tab, optimization_results, algorithm_names);

% 详细结果标签页
for i = 1:length(algorithm_names)
    if ~isempty(pareto_variables{i})
        alg_tab = uitab(tab_group, 'Title', algorithm_names{i});
        createAlgorithmDetailPanel(alg_tab, pareto_variables{i}, pareto_solutions{i}, param_names, algorithm_names{i});
    end
end

fprintf('可视化界面创建完成\n');

end

%% ========== 辅助函数 ==========

function [all_results, pareto_variables, pareto_solutions] = extractResultsData(optimization_results, algorithm_names)
% 提取结果数据
all_results = cell(length(algorithm_names), 1);
pareto_variables = cell(length(algorithm_names), 1);
pareto_solutions = cell(length(algorithm_names), 1);

for i = 1:length(algorithm_names)
    alg_name = algorithm_names{i};
    if isfield(optimization_results, alg_name)
        result = optimization_results.(alg_name);
        
        if isfield(result, 'pareto_solutions') && ~isempty(result.pareto_solutions)
            all_results{i} = result.pareto_solutions;
            pareto_solutions{i} = result.pareto_solutions;
        end
        
        if isfield(result, 'pareto_variables') && ~isempty(result.pareto_variables)
            pareto_variables{i} = result.pareto_variables;
        end
    end
end
end

function fig = createMainFigure()
% 创建主窗口
fig = uifigure('Name', '齿轮传动系统优化结果可视化', ...
               'Position', [100, 100, 1400, 800], ...
               'Resize', 'on');
end

function createParetoFrontPanel(parent_tab, all_results, algorithm_names)
% 创建Pareto前沿面板
try
    % 创建坐标轴
    ax = uiaxes(parent_tab, 'Position', [50, 50, 1100, 600]);
    
    % 绘制Pareto前沿
    hold(ax, 'on');
    colors = lines(length(all_results));
    markers = {'o', 's', 'd', '^', 'v', '>', '<', 'p', 'h', '*'};
    
    legend_items = {};
    valid_results_count = 0;
    
    for i = 1:length(all_results)
        if ~isempty(all_results{i})
            % 检查数据有效性
            if any(all_results{i}(:) > 1e8) || any(all_results{i}(:) < -1e8)
                fprintf('算法 %s 的结果包含异常值，跳过绘图\n', algorithm_names{i});
                continue;
            end
            
            % 绘制3D Pareto前沿
            if size(all_results{i}, 2) >= 3
                scatter3(ax, all_results{i}(:,1), -all_results{i}(:,2), -all_results{i}(:,3), ...
                        50, colors(i,:), 'filled', 'Marker', markers{mod(i-1, length(markers))+1});
                legend_items{end+1} = algorithm_names{i};
                valid_results_count = valid_results_count + 1;
            end
        end
    end
    
    % 设置坐标轴标签
    xlabel(ax, '总质量 (kg)');
    ylabel(ax, '弯曲安全系数');
    zlabel(ax, '接触安全系数');
    title(ax, 'Pareto前沿对比');
    
    % 添加图例
    if valid_results_count > 0
        legend(ax, legend_items, 'Location', 'best');
    end
    
    hold(ax, 'off');
    
catch e
    fprintf('创建Pareto前沿面板时出错: %s\n', e.message);
    % 添加错误信息标签
    uilabel(parent_tab, 'Text', '创建Pareto前沿图时出错', ...
           'Position', [400, 350, 300, 30], ...
           'FontSize', 14, 'FontWeight', 'bold');
end
end

function createAlgorithmComparisonPanel(parent_tab, optimization_results, algorithm_names)
% 创建算法比较面板
try
    % 创建表格数据
    table_data = {};
    row_names = {};
    
    for i = 1:length(algorithm_names)
        alg_name = algorithm_names{i};
        if isfield(optimization_results, alg_name)
            result = optimization_results.(alg_name);
            
            % 提取性能指标
            execution_time = 0;
            if isfield(result, 'execution_time')
                execution_time = result.execution_time;
            end
            
            best_mass = Inf;
            best_bending = 0;
            best_contact = 0;
            
            if isfield(result, 'pareto_solutions') && ~isempty(result.pareto_solutions)
                solutions = result.pareto_solutions;
                [best_mass, idx] = min(solutions(:, 1));
                best_bending = -solutions(idx, 2);
                best_contact = -solutions(idx, 3);
            end
            
            table_data{end+1} = {alg_name, sprintf('%.2f', execution_time), ...
                               sprintf('%.2f', best_mass), sprintf('%.3f', best_bending), ...
                               sprintf('%.3f', best_contact)};
            row_names{end+1} = alg_name;
        end
    end
    
    % 创建表格
    if ~isempty(table_data)
        col_names = {'算法', '执行时间(s)', '最佳质量(kg)', '最佳弯曲安全系数', '最佳接触安全系数'};
        
        % 转换为表格格式
        table_matrix = cell(length(table_data), length(col_names));
        for i = 1:length(table_data)
            table_matrix(i, :) = table_data{i};
        end
        
        % 创建UI表格
        uit = uitable(parent_tab, 'Data', table_matrix, ...
                     'ColumnName', col_names, ...
                     'Position', [50, 200, 1100, 400], ...
                     'ColumnWidth', {150, 120, 120, 150, 150});
    else
        uilabel(parent_tab, 'Text', '没有可用的算法比较数据', ...
               'Position', [400, 350, 300, 30], ...
               'FontSize', 14, 'FontWeight', 'bold');
    end
    
catch e
    fprintf('创建算法比较面板时出错: %s\n', e.message);
    uilabel(parent_tab, 'Text', '创建算法比较表时出错', ...
           'Position', [400, 350, 300, 30], ...
           'FontSize', 14, 'FontWeight', 'bold');
end
end

function createAlgorithmDetailPanel(parent_tab, variables, solutions, param_names, algorithm_name)
% 创建算法详细结果面板
try
    if isempty(variables) || isempty(solutions)
        uilabel(parent_tab, 'Text', [algorithm_name, ' - 没有有效结果'], ...
               'Position', [400, 350, 300, 30], ...
               'FontSize', 14, 'FontWeight', 'bold');
        return;
    end
    
    % 创建解的数量信息
    info_text = sprintf('%s - 共 %d 个Pareto最优解', algorithm_name, size(variables, 1));
    uilabel(parent_tab, 'Text', info_text, ...
           'Position', [50, 750, 500, 30], ...
           'FontSize', 12, 'FontWeight', 'bold');
    
    % 创建变量表格
    if size(variables, 2) <= length(param_names)
        var_table_data = num2cell(variables);
        var_col_names = param_names(1:size(variables, 2));
        
        uit_var = uitable(parent_tab, 'Data', var_table_data, ...
                         'ColumnName', var_col_names, ...
                         'Position', [50, 400, 1100, 300], ...
                         'ColumnWidth', repmat({80}, 1, length(var_col_names)));
    end
    
    % 创建目标函数值表格
    obj_table_data = num2cell(solutions);
    obj_col_names = {'总质量(kg)', '弯曲安全系数', '接触安全系数'};
    
    % 转换安全系数为正值显示
    if size(solutions, 2) >= 3
        obj_table_data(:, 2) = num2cell(-cell2mat(obj_table_data(:, 2)));
        obj_table_data(:, 3) = num2cell(-cell2mat(obj_table_data(:, 3)));
    end
    
    uit_obj = uitable(parent_tab, 'Data', obj_table_data, ...
                     'ColumnName', obj_col_names, ...
                     'Position', [50, 50, 600, 300], ...
                     'ColumnWidth', {150, 150, 150});
    
catch e
    fprintf('创建算法详细面板时出错: %s\n', e.message);
    uilabel(parent_tab, 'Text', [algorithm_name, ' - 创建详细面板时出错'], ...
           'Position', [400, 350, 300, 30], ...
           'FontSize', 14, 'FontWeight', 'bold');
end
end
