function PlotParetoFronts(all_results, algorithm_names, save_plots)
% PlotParetoFronts 绘制多目标优化算法的Pareto前沿对比图
%
% 输入参数:
%   all_results - 包含所有算法结果的cell数组，每个元素是一个N×3的矩阵
%   algorithm_names - 算法名称的cell数组
%   save_plots - 是否保存图片（可选，默认为true）
%
% 输出:
%   无返回值，直接显示图形并可选保存

if nargin < 3
    save_plots = true;
end

% 验证输入参数
if length(all_results) ~= length(algorithm_names)
    error('算法结果数量与算法名称数量不匹配');
end

% 过滤空结果和异常数据
valid_indices = [];
for i = 1:length(all_results)
    if ~isempty(all_results{i}) && size(all_results{i}, 2) >= 3
        % 检查质量数据是否合理（第一列是质量）
        masses = all_results{i}(:, 1);
        if max(masses) <= 10000 && min(masses) > 0  % 质量应该在合理范围内
            valid_indices = [valid_indices, i];
            fprintf('✓ %s: %d个解, 质量范围: %.2f - %.2f kg\n', ...
                algorithm_names{i}, size(all_results{i}, 1), min(masses), max(masses));
        else
            fprintf('❌ %s: 质量数据异常 (%.2f - %.2f kg)，已过滤\n', ...
                algorithm_names{i}, min(masses), max(masses));
        end
    end
end

if isempty(valid_indices)
    fprintf('警告：没有有效的算法结果可以绘制\n');
    return;
end

% 只保留有效结果
all_results = all_results(valid_indices);
algorithm_names = algorithm_names(valid_indices);

fprintf('最终使用%d个算法的数据进行绘图\n', length(algorithm_names));

% 设置默认字体为宋体，确保所有文字统一
set(0, 'DefaultAxesFontName', 'SimSun');
set(0, 'DefaultTextFontName', 'SimSun');
set(0, 'DefaultUicontrolFontName', 'SimSun');
set(0, 'DefaultAxesFontSize', 12);
set(0, 'DefaultTextFontSize', 12);

% 定义更加鲜艳活泼的颜色方案
colors = [
    [1.0000, 0.0000, 0.0000];  % 红色 - NSGA-II
    [0.0000, 0.0000, 1.0000];  % 蓝色 - NSGA-III
    [0.0000, 0.7000, 0.0000];  % 鲜绿色 - SPEA2
    [0.8000, 0.0000, 0.8000];  % 亮紫色 - MOEA-D
    [0.0000, 0.8000, 0.8000];  % 青绿色 - MOEA-D-DE
    [1.0000, 0.6000, 0.0000];  % 橙色 - MOEA-D-M2M
    [0.5000, 0.0000, 1.0000];  % 紫色 - MOPSO
    [1.0000, 1.0000, 0.0000];  % 鲜黄色 - MOGWO
    [1.0000, 0.0000, 0.5000];  % 品红色 - MOWOA
];

% 定义边线颜色 - 增强立体感
edgeColors = [
    [0.7000, 0.0000, 0.0000];  % 深红色边线
    [0.0000, 0.0000, 0.7000];  % 深蓝色边线
    [0.0000, 0.5000, 0.0000];  % 深绿色边线
    [0.6000, 0.0000, 0.6000];  % 深紫色边线
    [0.0000, 0.6000, 0.6000];  % 深青绿色边线
    [0.7000, 0.4000, 0.0000];  % 深橙色边线
    [0.3500, 0.0000, 0.7000];  % 深紫色边线
    [0.7500, 0.7500, 0.0000];  % 深黄色边线
    [0.7000, 0.0000, 0.4000];  % 深品红色边线
];

% 定义标记样式 - 使用更清晰区分的形状和大小
markers = {'o', 's', 'd', '^', 'v', 'p', '>', 'h', '<'};
markerSizes = [60, 65, 55, 70, 60, 65, 55, 70, 60]; % 增大标记尺寸，提高可见性

% 创建图形窗口 - 居中显示
screenSize = get(0, 'ScreenSize');
figWidth = 1400;
figHeight = 500;
figX = (screenSize(3) - figWidth) / 2;
figY = (screenSize(4) - figHeight) / 2;
fig_main = figure('Name', 'Pareto前沿图', 'NumberTitle', 'off', 'Color', 'white', 'Position', [figX, figY, figWidth, figHeight]);

% 提取目标函数值
all_f1 = []; all_f2 = []; all_f3 = [];

for i = 1:length(all_results)
    if ~isempty(all_results{i})
        all_f1 = [all_f1; all_results{i}(:, 1)];
        all_f2 = [all_f2; abs(all_results{i}(:, 2))];  % 取绝对值确保为正数
        all_f3 = [all_f3; abs(all_results{i}(:, 3))];  % 取绝对值确保为正数
    end
end

% 三维情况：3个子图在同一行，增加间距
subplot(1, 3, 1);
Plot3DParetoFront(all_results, algorithm_names, all_f1, all_f2, all_f3, 'southeast'); % 图例位置设为右下角
title('三维Pareto前沿', 'FontSize', 12, 'FontWeight', 'normal', 'FontName', 'SimSun');

subplot(1, 3, 2);
PlotMassVsContactSafety(all_results, algorithm_names, all_f1, all_f3);
title('质量-接触安全系数', 'FontSize', 12, 'FontWeight', 'normal', 'FontName', 'SimSun');

subplot(1, 3, 3);
PlotMassVsBendingSafety(all_results, algorithm_names, all_f1, all_f2);
title('质量-弯曲安全系数', 'FontSize', 12, 'FontWeight', 'normal', 'FontName', 'SimSun');

% 调整子图布局，增加间距避免拥挤
set(fig_main, 'Units', 'normalized');
% 调整子图间距，确保标题不重叠且有足够间隔
p1 = get(subplot(1,3,1), 'Position');
p2 = get(subplot(1,3,2), 'Position');
p3 = get(subplot(1,3,3), 'Position');

% 调整为合理的大小和位置，均匀分布间距
p1(1) = 0.05;    % 左侧图位置
p1(2) = 0.18;    % 底部位置
p1(3) = 0.26;    % 左侧图宽度
p1(4) = 0.68;    % 图高度

p2(1) = 0.37;    % 中间图位置（均匀间距）
p2(2) = 0.18;    % 底部位置
p2(3) = 0.26;    % 中间图宽度
p2(4) = 0.68;    % 图高度

p3(1) = 0.69;    % 右侧图位置（均匀间距）
p3(2) = 0.18;    % 底部位置
p3(3) = 0.26;    % 右侧图宽度
p3(4) = 0.68;    % 图高度

set(subplot(1,3,1), 'Position', p1);
set(subplot(1,3,2), 'Position', p2);
set(subplot(1,3,3), 'Position', p3);

% 添加总标题
sgtitle('多目标优化算法Pareto前沿比较', 'FontSize', 12, 'FontWeight', 'normal', 'FontName', 'SimSun');



% 保存前沿图
if save_plots
    try
        % 确保Results目录存在
        results_dir = 'Results';
        if ~exist(results_dir, 'dir')
            mkdir(results_dir);
        end

        % 保存为PNG格式 - 使用print函数更适合批处理模式
        png_filename = fullfile(results_dir, 'pareto_fronts.png');
        try
            print(fig_main, png_filename, '-dpng', '-r300');
            fprintf('已保存图表: %s\n', png_filename);
        catch
            % 备选方案
            saveas(fig_main, png_filename, 'png');
            fprintf('已保存图表: %s (备选方案)\n', png_filename);
        end

        % 保存为高质量的EPS格式
        eps_filename = fullfile(results_dir, 'pareto_fronts.eps');
        try
            print(fig_main, eps_filename, '-depsc', '-r300');
            fprintf('已保存EPS格式: %s\n', eps_filename);
        catch
            % 备选方案
            saveas(fig_main, eps_filename, 'epsc');
            fprintf('已保存EPS格式: %s (备选方案)\n', eps_filename);
        end

    catch e
        fprintf('保存图片时出错: %s\n', e.message);
    end
end

fprintf('完成! 所有Pareto前沿图表已保存到Results文件夹\n');
end

function [varargout] = Plot3DParetoFront(all_results, alg_names, f1_all, f2_all, f3_all, legend_position)
    % 绘制三维Pareto前沿
    % 如果未指定图例位置，默认为东北角
    if nargin < 6
        legend_position = 'northeast';
    end

    % 设置统一字体
    chineseFont = 'SimSun';
    fontSize = 12;

    % 定义更加鲜艳活泼的颜色方案
    colors = [
        [1.0000, 0.0000, 0.0000];  % 红色 - NSGA-II
        [0.0000, 0.0000, 1.0000];  % 蓝色 - NSGA-III
        [0.0000, 0.7000, 0.0000];  % 鲜绿色 - SPEA2
        [0.8000, 0.0000, 0.8000];  % 亮紫色 - MOEA-D
        [0.0000, 0.8000, 0.8000];  % 青绿色 - MOEA-D-DE
        [1.0000, 0.6000, 0.0000];  % 橙色 - MOEA-D-M2M
        [0.5000, 0.0000, 1.0000];  % 紫色 - MOPSO
        [1.0000, 1.0000, 0.0000];  % 鲜黄色 - MOGWO
        [1.0000, 0.0000, 0.5000];  % 品红色 - MOWOA
    ];

    % 定义边线颜色 - 增强立体感
    edgeColors = [
        [0.7000, 0.0000, 0.0000];  % 深红色边线
        [0.0000, 0.0000, 0.7000];  % 深蓝色边线
        [0.0000, 0.5000, 0.0000];  % 深绿色边线
        [0.6000, 0.0000, 0.6000];  % 深紫色边线
        [0.0000, 0.6000, 0.6000];  % 深青绿色边线
        [0.7000, 0.4000, 0.0000];  % 深橙色边线
        [0.3500, 0.0000, 0.7000];  % 深紫色边线
        [0.7500, 0.7500, 0.0000];  % 深黄色边线
        [0.7000, 0.0000, 0.4000];  % 深品红色边线
    ];

    % 定义标记样式 - 使用更清晰区分的形状和大小
    markers = {'o', 's', 'd', '^', 'v', 'p', '>', 'h', '<'};
    markerSizes = [50, 55, 45, 60, 50, 55, 45, 60, 50]; % 不同大小的标记

    % 获取当前figure
    fig = gcf;

    % 创建轴并保持
    hold on;
    grid on;
    box on;

    % 图例条目
    legendEntries = {};

    % 绘制每个算法的Pareto前沿（仅使用散点）
    for i = 1:length(all_results)
        if isempty(all_results{i})
            continue;
        end

        % 提取目标函数值
        f1 = all_results{i}(:, 1);        % 质量
        f2 = abs(all_results{i}(:, 2));   % 弯曲安全系数（取绝对值确保为正）
        f3 = abs(all_results{i}(:, 3));   % 接触安全系数（取绝对值确保为正）

        % 当前算法的颜色和标记
        colorIdx = mod(i-1, size(colors, 1))+1;
        markerIdx = mod(i-1, length(markers))+1;

        % 使用散点图绘制Pareto前沿 - 修改坐标轴顺序，使z轴表示质量
        % 添加少量随机抖动以减少重叠
        jitter = 0.01; % 抖动量
        f2_jitter = f2 + (rand(size(f2))-0.5)*jitter*mean(f2);
        f3_jitter = f3 + (rand(size(f3))-0.5)*jitter*mean(f3);

        scatter3(f2_jitter, f3_jitter, f1, markerSizes(colorIdx), ...  % 使用不同大小的标记
               'filled', ...
               markers{markerIdx}, ...
               'MarkerEdgeColor', edgeColors(colorIdx,:), ...  % 使用更深的边线颜色增强立体感
               'LineWidth', 1.2, ...         % 增加线条粗细，增强立体感
               'MarkerFaceColor', colors(colorIdx, :), ...
               'MarkerFaceAlpha', 0.9, ... % 减少透明度，提高可见性
               'DisplayName', alg_names{i});

        legendEntries{end+1} = alg_names{i};
    end

    % 设置坐标轴范围，确保数据分布均匀美观
    f1_range = max(f1_all) - min(f1_all);
    f2_range = max(f2_all) - min(f2_all);
    f3_range = max(f3_all) - min(f3_all);

    % 使用适当的边距，确保视觉平衡
    margin_percent = 0.08;  % 8%边距

    xlim([min(f2_all) - f2_range*margin_percent, max(f2_all) + f2_range*margin_percent]);
    ylim([min(f3_all) - f3_range*margin_percent, max(f3_all) + f3_range*margin_percent]);
    zlim([min(f1_all) - f1_range*margin_percent, max(f1_all) + f1_range*margin_percent]);

    % 设置合适的刻度间隔 - 安全系数以0.05为间隔，质量以0.5千克为间隔
    % 弯曲安全系数轴（X轴）：以0.05为间隔
    bend_safety_min = floor(min(f2_all)*20)/20;    % 向下取整到最近的0.05
    bend_safety_max = ceil(max(f2_all)*20)/20;     % 向上取整到最近的0.05
    xticks(bend_safety_min:0.05:bend_safety_max);

    % 接触安全系数轴（Y轴）：以0.05为间隔
    contact_safety_min = floor(min(f3_all)*20)/20;    % 向下取整到最近的0.05
    contact_safety_max = ceil(max(f3_all)*20)/20;     % 向上取整到最近的0.05
    yticks(contact_safety_min:0.05:contact_safety_max);

    % 质量轴（Z轴）：以0.5千克为间隔
    mass_min = floor(min(f1_all)/500)*500;  % 向下取整到最近的500
    mass_max = ceil(max(f1_all)/500)*500;   % 向上取整到最近的500
    zticks(mass_min:500:mass_max);

    % 设置固定视角，调整为更好地查看
    view([-30, 25]);

    % 设置坐标轴标签 - 使用宋体
    xlabel('弯曲安全系数', 'FontName', chineseFont, 'FontSize', fontSize);
    ylabel('接触安全系数', 'FontName', chineseFont, 'FontSize', fontSize);
    zlabel('总质量 (kg)', 'FontName', chineseFont, 'FontSize', fontSize);

    % 使用线性坐标，显示更直观
    set(gca, 'ZScale', 'linear');

    % 自定义Z轴刻度标签，显示小数值
    zticks_vals = get(gca, 'ZTick');
    ztick_labels = cell(size(zticks_vals));
    for i = 1:length(zticks_vals)
        ztick_labels{i} = sprintf('%.1f', zticks_vals(i)/1000);
    end
    set(gca, 'ZTickLabel', ztick_labels);

    % 在Z轴末端添加科学计数法标注 - 在Z轴上方
    zlim_vals = get(gca, 'ZLim');
    xlim_vals = get(gca, 'XLim');
    ylim_vals = get(gca, 'YLim');
    text(min(xlim_vals), min(ylim_vals), max(zlim_vals)*1.08, ...
         '×10³', 'FontName', chineseFont, 'FontSize', fontSize-1, ...
         'HorizontalAlignment', 'center', 'VerticalAlignment', 'bottom');

    % 美化坐标轴 - 学术论文风格
    ax = gca;
    ax.FontName = chineseFont;
    ax.FontSize = fontSize;
    ax.LineWidth = 1.5;         % 统一边框线条粗细
    ax.GridLineStyle = ':';
    ax.GridAlpha = 0.1;         % 更低的网格线透明度，不干扰数据点
    ax.TickLength = [0.01 0.01];
    ax.TickDir = 'in';          % 内向刻度
    ax.XColor = [0.2 0.2 0.2];  % 深灰色坐标轴，更专业
    ax.YColor = [0.2 0.2 0.2];
    ax.ZColor = [0.2 0.2 0.2];
    % 设置背景为白色
    ax.Color = 'white';

    % 添加图例，放在指定位置，设置为学术论文风格
    lgd = legend(legendEntries, 'Location', legend_position, 'FontName', chineseFont, 'FontSize', fontSize-1);
    lgd.Box = 'on';
    lgd.LineWidth = 0.5;  % 更细的边框线条
    % 设置图例中的标记大小为适中值
    for i = 1:length(lgd.ItemTokenSize)
        lgd.ItemTokenSize(i) = 6;  % 减小图例中的标记
    end

    % 返回图形句柄
    if nargout > 0
        varargout{1} = fig;
    end
end

function [varargout] = PlotMassVsBendingSafety(all_results, alg_names, f1_all, f2_all)
    % 绘制质量-弯曲安全系数图

    % 设置统一字体
    chineseFont = 'SimSun';
    fontSize = 12;

    % 定义更加鲜艳活泼的颜色方案
    colors = [
        [1.0000, 0.0000, 0.0000];  % 红色 - NSGA-II
        [0.0000, 0.0000, 1.0000];  % 蓝色 - NSGA-III
        [0.0000, 0.7000, 0.0000];  % 鲜绿色 - SPEA2
        [0.8000, 0.0000, 0.8000];  % 亮紫色 - MOEA-D
        [0.0000, 0.8000, 0.8000];  % 青绿色 - MOEA-D-DE
        [1.0000, 0.6000, 0.0000];  % 橙色 - MOEA-D-M2M
        [0.5000, 0.0000, 1.0000];  % 紫色 - MOPSO
        [1.0000, 1.0000, 0.0000];  % 鲜黄色 - MOGWO
        [1.0000, 0.0000, 0.5000];  % 品红色 - MOWOA
    ];

    % 定义边线颜色 - 增强立体感
    edgeColors = [
        [0.7000, 0.0000, 0.0000];  % 深红色边线
        [0.0000, 0.0000, 0.7000];  % 深蓝色边线
        [0.0000, 0.5000, 0.0000];  % 深绿色边线
        [0.6000, 0.0000, 0.6000];  % 深紫色边线
        [0.0000, 0.6000, 0.6000];  % 深青绿色边线
        [0.7000, 0.4000, 0.0000];  % 深橙色边线
        [0.3500, 0.0000, 0.7000];  % 深紫色边线
        [0.7500, 0.7500, 0.0000];  % 深黄色边线
        [0.7000, 0.0000, 0.4000];  % 深品红色边线
    ];

    % 定义标记样式 - 使用更清晰区分的形状和大小
    markers = {'o', 's', 'd', '^', 'v', 'p', '>', 'h', '<'};
    markerSizes = [50, 55, 45, 60, 50, 55, 45, 60, 50]; % 不同大小的标记

    % 获取当前figure
    fig = gcf;

    % 创建轴并保持
    hold on;
    grid on;
    box on;

    % 图例条目
    legendEntries = {};

    % 绘制每个算法的Pareto前沿（仅使用散点）
    for i = 1:length(all_results)
        if isempty(all_results{i})
            continue;
        end

        % 提取目标函数值
        f1 = all_results{i}(:, 1);        % 质量
        f2 = abs(all_results{i}(:, 2));   % 弯曲安全系数（取绝对值确保为正）

        % 当前算法的颜色和标记
        colorIdx = mod(i-1, size(colors, 1))+1;
        markerIdx = mod(i-1, length(markers))+1;

        % 添加少量随机抖动以减少重叠
        jitter = 0.01; % 抖动量
        f1_jitter = f1 + (rand(size(f1))-0.5)*jitter*mean(f1);
        f2_jitter = f2 + (rand(size(f2))-0.5)*jitter*mean(f2);

        % 使用散点图绘制Pareto前沿
        scatter(f1_jitter, f2_jitter, markerSizes(colorIdx), ...  % 使用不同大小的标记
               'filled', ...
               markers{markerIdx}, ...
               'MarkerEdgeColor', edgeColors(colorIdx,:), ...  % 使用更深的边线颜色增强立体感
               'LineWidth', 1.2, ...         % 增加线条粗细，增强立体感
               'MarkerFaceColor', colors(colorIdx, :), ...
               'MarkerFaceAlpha', 0.9, ... % 减少透明度，提高可见性
               'DisplayName', alg_names{i});

        legendEntries{end+1} = alg_names{i};
    end

    % 设置坐标轴范围，确保数据分布均匀美观
    f1_range = max(f1_all) - min(f1_all);
    f2_range = max(f2_all) - min(f2_all);

    % 使用适当的边距
    margin_percent = 0.08;  % 8%边距

    xlim([min(f1_all) - f1_range*margin_percent, max(f1_all) + f1_range*margin_percent]);
    % 弯曲安全系数Y轴范围设置为显示到1.6
    ylim([min(f2_all) - f2_range*margin_percent, max(1.6, max(f2_all) + f2_range*margin_percent)]);

    % 设置合适的刻度间隔 - 质量以0.5千克为间隔，安全系数以0.1为间隔
    % 质量轴（X轴）：以0.5千克为间隔
    mass_min = floor(min(f1_all)/500)*500;  % 向下取整到最近的500
    mass_max = ceil(max(f1_all)/500)*500;   % 向上取整到最近的500
    xticks(mass_min:500:mass_max);

    % 安全系数轴（Y轴）：以0.05为间隔，确保显示到1.6
    safety_min = floor(min(f2_all)*20)/20;    % 向下取整到最近的0.05
    safety_max = max(1.6, ceil(max(f2_all)*20)/20);     % 确保最大值至少为1.6
    yticks(safety_min:0.05:safety_max);

    % 设置坐标轴标签 - 使用宋体
    xlabel('总质量 (kg)', 'FontName', chineseFont, 'FontSize', fontSize);
    ylabel('弯曲安全系数', 'FontName', chineseFont, 'FontSize', fontSize);

    % 使用线性坐标，显示更直观
    set(gca, 'XScale', 'linear');

    % 自定义X轴刻度标签，显示小数值
    xticks_vals = get(gca, 'XTick');
    xtick_labels = cell(size(xticks_vals));
    for i = 1:length(xticks_vals)
        xtick_labels{i} = sprintf('%.1f', xticks_vals(i)/1000);
    end
    set(gca, 'XTickLabel', xtick_labels);

    % 在X轴末端添加科学计数法标注 - 在X轴右侧
    xlim_vals = get(gca, 'XLim');
    ylim_vals = get(gca, 'YLim');
    text(max(xlim_vals)*1.02, min(ylim_vals), '×10³', ...
         'FontName', chineseFont, 'FontSize', fontSize-1, ...
         'HorizontalAlignment', 'left', 'VerticalAlignment', 'bottom');

    % 美化坐标轴 - 学术论文风格
    ax = gca;
    ax.FontName = chineseFont;
    ax.FontSize = fontSize;
    ax.LineWidth = 1.5;         % 统一边框线条粗细
    ax.GridLineStyle = ':';
    ax.GridAlpha = 0.1;         % 更低的网格线透明度，不干扰数据点
    ax.TickLength = [0.01 0.01];
    ax.TickDir = 'in';          % 内向刻度
    ax.XColor = [0.2 0.2 0.2];  % 深灰色坐标轴，更专业
    ax.YColor = [0.2 0.2 0.2];
    % 设置背景为白色
    ax.Color = 'white';

    % 确保标签与坐标轴平行
    ax.XLabel.Rotation = 0;  % 确保X标签水平
    ax.YLabel.Rotation = 90; % 确保Y标签垂直

    % 添加图例，放在右下角，设置为学术论文风格
    lgd = legend(legendEntries, 'Location', 'southeast', 'FontName', chineseFont, 'FontSize', fontSize-1);
    lgd.Box = 'on';
    lgd.LineWidth = 0.5;  % 更细的边框线条
    % 设置图例中的标记大小为适中值
    for i = 1:length(lgd.ItemTokenSize)
        lgd.ItemTokenSize(i) = 6;  % 调整图例中的标记大小
    end

    % 返回图形句柄
    if nargout > 0
        varargout{1} = fig;
    end
end

function [varargout] = PlotMassVsContactSafety(all_results, alg_names, f1_all, f3_all)
    % 绘制质量-接触安全系数图

    % 设置统一字体
    chineseFont = 'SimSun';
    fontSize = 12;

    % 定义更加鲜艳活泼的颜色方案
    colors = [
        [1.0000, 0.0000, 0.0000];  % 红色 - NSGA-II
        [0.0000, 0.0000, 1.0000];  % 蓝色 - NSGA-III
        [0.0000, 0.7000, 0.0000];  % 鲜绿色 - SPEA2
        [0.8000, 0.0000, 0.8000];  % 亮紫色 - MOEA-D
        [0.0000, 0.8000, 0.8000];  % 青绿色 - MOEA-D-DE
        [1.0000, 0.6000, 0.0000];  % 橙色 - MOEA-D-M2M
        [0.5000, 0.0000, 1.0000];  % 紫色 - MOPSO
        [1.0000, 1.0000, 0.0000];  % 鲜黄色 - MOGWO
        [1.0000, 0.0000, 0.5000];  % 品红色 - MOWOA
    ];

    % 定义边线颜色 - 增强立体感
    edgeColors = [
        [0.7000, 0.0000, 0.0000];  % 深红色边线
        [0.0000, 0.0000, 0.7000];  % 深蓝色边线
        [0.0000, 0.5000, 0.0000];  % 深绿色边线
        [0.6000, 0.0000, 0.6000];  % 深紫色边线
        [0.0000, 0.6000, 0.6000];  % 深青绿色边线
        [0.7000, 0.4000, 0.0000];  % 深橙色边线
        [0.3500, 0.0000, 0.7000];  % 深紫色边线
        [0.7500, 0.7500, 0.0000];  % 深黄色边线
        [0.7000, 0.0000, 0.4000];  % 深品红色边线
    ];

    % 定义标记样式 - 使用更清晰区分的形状和大小
    markers = {'o', 's', 'd', '^', 'v', 'p', '>', 'h', '<'};
    markerSizes = [50, 55, 45, 60, 50, 55, 45, 60, 50]; % 不同大小的标记

    % 获取当前figure
    fig = gcf;

    % 创建轴并保持
    hold on;
    grid on;
    box on;

    % 图例条目
    legendEntries = {};

    % 绘制每个算法的Pareto前沿（仅使用散点）
    for i = 1:length(all_results)
        if isempty(all_results{i})
            continue;
        end

        % 提取目标函数值
        f1 = all_results{i}(:, 1);        % 质量
        f3 = abs(all_results{i}(:, 3));   % 接触安全系数（取绝对值确保为正）

        % 当前算法的颜色和标记
        colorIdx = mod(i-1, size(colors, 1))+1;
        markerIdx = mod(i-1, length(markers))+1;

        % 添加少量随机抖动以减少重叠
        jitter = 0.01; % 抖动量
        f1_jitter = f1 + (rand(size(f1))-0.5)*jitter*mean(f1);
        f3_jitter = f3 + (rand(size(f3))-0.5)*jitter*mean(f3);

        % 使用散点图绘制Pareto前沿
        scatter(f1_jitter, f3_jitter, markerSizes(colorIdx), ...  % 使用不同大小的标记
               'filled', ...
               markers{markerIdx}, ...
               'MarkerEdgeColor', edgeColors(colorIdx,:), ...  % 使用更深的边线颜色增强立体感
               'LineWidth', 1.2, ...         % 增加线条粗细，增强立体感
               'MarkerFaceColor', colors(colorIdx, :), ...
               'MarkerFaceAlpha', 0.9, ... % 减少透明度，提高可见性
               'DisplayName', alg_names{i});

        legendEntries{end+1} = alg_names{i};
    end

    % 设置坐标轴范围，确保数据分布均匀美观
    f1_range = max(f1_all) - min(f1_all);
    f3_range = max(f3_all) - min(f3_all);

    % 使用适当的边距
    margin_percent = 0.08;  % 8%边距

    xlim([min(f1_all) - f1_range*margin_percent, max(f1_all) + f1_range*margin_percent]);
    ylim([min(f3_all) - f3_range*margin_percent, max(f3_all) + f3_range*margin_percent]);

    % 设置合适的刻度间隔 - 质量以0.5千克为间隔，安全系数以0.1为间隔
    % 质量轴（X轴）：以0.5千克为间隔
    mass_min = floor(min(f1_all)/500)*500;  % 向下取整到最近的500
    mass_max = ceil(max(f1_all)/500)*500;   % 向上取整到最近的500
    xticks(mass_min:500:mass_max);

    % 安全系数轴（Y轴）：以0.05为间隔
    safety_min = floor(min(f3_all)*20)/20;    % 向下取整到最近的0.05
    safety_max = ceil(max(f3_all)*20)/20;     % 向上取整到最近的0.05
    yticks(safety_min:0.05:safety_max);

    % 设置坐标轴标签 - 使用宋体
    xlabel('总质量 (kg)', 'FontName', chineseFont, 'FontSize', fontSize);
    ylabel('接触安全系数', 'FontName', chineseFont, 'FontSize', fontSize);

    % 使用线性坐标，显示更直观
    set(gca, 'XScale', 'linear');

    % 自定义X轴刻度标签，显示小数值
    xticks_vals = get(gca, 'XTick');
    xtick_labels = cell(size(xticks_vals));
    for i = 1:length(xticks_vals)
        xtick_labels{i} = sprintf('%.1f', xticks_vals(i)/1000);
    end
    set(gca, 'XTickLabel', xtick_labels);

    % 在X轴末端添加科学计数法标注 - 在X轴右侧
    xlim_vals = get(gca, 'XLim');
    ylim_vals = get(gca, 'YLim');
    text(max(xlim_vals)*1.02, min(ylim_vals), '×10³', ...
         'FontName', chineseFont, 'FontSize', fontSize-1, ...
         'HorizontalAlignment', 'left', 'VerticalAlignment', 'bottom');

    % 美化坐标轴 - 学术论文风格
    ax = gca;
    ax.FontName = chineseFont;
    ax.FontSize = fontSize;
    ax.LineWidth = 1.5;         % 统一边框线条粗细
    ax.GridLineStyle = ':';
    ax.GridAlpha = 0.1;         % 更低的网格线透明度，不干扰数据点
    ax.TickLength = [0.01 0.01];
    ax.TickDir = 'in';          % 内向刻度
    ax.XColor = [0.2 0.2 0.2];  % 深灰色坐标轴，更专业
    ax.YColor = [0.2 0.2 0.2];
    % 设置背景为白色
    ax.Color = 'white';

    % 确保标签与坐标轴平行
    ax.XLabel.Rotation = 0;  % 确保X标签水平
    ax.YLabel.Rotation = 90; % 确保Y标签垂直

    % 添加图例，放在右下角，设置为学术论文风格
    lgd = legend(legendEntries, 'Location', 'southeast', 'FontName', chineseFont, 'FontSize', fontSize-1);
    lgd.Box = 'on';
    lgd.LineWidth = 0.5;  % 更细的边框线条
    % 设置图例中的标记大小为适中值
    for i = 1:length(lgd.ItemTokenSize)
        lgd.ItemTokenSize(i) = 6;  % 调整图例中的标记大小
    end

    % 返回图形句柄
    if nargout > 0
        varargout{1} = fig;
    end
end
