function C = Coverage(A, B)
% Coverage - 计算两个Pareto前沿集合之间的覆盖率指标
% A和B是两个解集，每行是一个解，每列是一个目标值
% C是A相对于B的覆盖率，表示B中被A支配的解的比例
%
% 注意：Coverage是一个非对称指标，C(A,B)与C(B,A)一般不相等
% 值越大表示A集相对于B集的优势越明显
% 若C(A,B)=1，表示A完全支配B；若C(A,B)=0，表示A不支配B中任何解

    count = 0;
    for i = 1:size(B, 1)
        for j = 1:size(A, 1)
            % 判断A中的解j是否支配B中的解i
            if all(A(j, :) <= B(i, :)) && any(A(j, :) < B(i, :))
                count = count + 1;
                break;
            end
        end
    end
    
    % 计算B中被A支配的解的比例
    C = count / size(B, 1);
end 