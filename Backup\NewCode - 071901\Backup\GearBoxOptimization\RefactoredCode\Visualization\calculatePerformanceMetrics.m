function metrics = calculatePerformanceMetrics(pareto_fronts, algorithm_names)
% calculatePerformanceMetrics 计算多目标优化算法的性能指标
% 基于原有的EvaluateAlgorithms.m实现
%
% 输入:
%   pareto_fronts - 包含各算法Pareto前沿的cell数组
%   algorithm_names - 算法名称的cell数组
%
% 输出:
%   metrics - 包含各种性能指标的结构体

% 添加Metrics路径
addpath('../Metrics');
addpath('Metrics');

% 设置默认算法名称（如果未提供）
if nargin < 2 || isempty(algorithm_names)
    algorithm_names = {'NSGA-II', 'NSGA-III', 'SPEA2', 'MOEA-D', 'MOEA-D-DE', 'MOEA-D-M2M', 'MOPSO', 'MOGWO', 'MOWOA'};
end

% 算法数量
n_algs = length(pareto_fronts);

% 初始化指标结构体
metrics = struct();
metrics.GD = zeros(1, n_algs);       % Generation Distance
metrics.IGD = zeros(1, n_algs);      % Inverted Generation Distance
metrics.Spread = zeros(1, n_algs);   % Spread (Diversity)
metrics.MS = zeros(1, n_algs);       % Maximum Spread
metrics.HV = zeros(1, n_algs);       % Hypervolume
metrics.Time = zeros(1, n_algs);     % Computation Time
metrics.Coverage = zeros(n_algs, n_algs); % Coverage matrix

% 找出所有算法结果中的非支配解，作为参考前沿
all_solutions = [];
for i = 1:n_algs
    if ~isempty(pareto_fronts{i})
        all_solutions = [all_solutions; pareto_fronts{i}];
    end
end

if isempty(all_solutions)
    fprintf('警告: 没有有效的解集用于计算指标\n');
    metrics.AlgorithmNames = algorithm_names(1:n_algs);
    return;
end

% 找出参考前沿（所有解的非支配集）
reference_front = findNonDominatedSolutions(all_solutions);

% 计算nadir点（用于超体积计算）
nadir_point = max(all_solutions) * 1.1;

fprintf('参考前沿包含 %d 个解\n', size(reference_front, 1));

% 为每个算法计算指标
for i = 1:n_algs
    if isempty(pareto_fronts{i})
        % 如果算法没有结果，设置默认值
        metrics.GD(i) = Inf;
        metrics.IGD(i) = Inf;
        metrics.Spread(i) = 0;
        metrics.MS(i) = 0;
        metrics.HV(i) = 0;
        metrics.Time(i) = 0;
        continue;
    end
    
    current_front = pareto_fronts{i};
    
    % 计算GD (Generation Distance)
    metrics.GD(i) = GenerationDistance(current_front, reference_front);
    
    % 计算IGD (Inverted Generation Distance)
    metrics.IGD(i) = InvertedGenerationDistance(reference_front, current_front);
    
    % 计算Spread (Diversity)
    metrics.Spread(i) = SpreadMetric(current_front);
    
    % 计算MS (Maximum Spread)
    metrics.MS(i) = MaximumSpread(current_front, reference_front);
    
    % 计算HV (Hypervolume)
    metrics.HV(i) = Hypervolume(current_front, nadir_point);
    
    % 模拟计算时间（实际应用中用实际测量值替换）
    metrics.Time(i) = rand() * 10 + 5;
end

% 计算Coverage指标
for i = 1:n_algs
    for j = 1:n_algs
        if i ~= j && ~isempty(pareto_fronts{i}) && ~isempty(pareto_fronts{j})
            metrics.Coverage(i, j) = CalculateCoverage(pareto_fronts{i}, pareto_fronts{j});
        end
    end
end

fprintf('性能指标计算完成\n');

% 保存指标名称和算法名称
metrics.IndicatorNames = {'GD', 'Spread', 'MS', 'IGD', 'HV', 'Time', 'Coverage'};
metrics.AlgorithmNames = algorithm_names(1:n_algs);
end

% ================ 以下是评价指标计算函数 ================

function non_dominated = findNonDominatedSolutions(solutions)
% 找出非支配解集
n = size(solutions, 1);
dominated = false(n, 1);

for i = 1:n
    for j = 1:n
        if i ~= j
            % 检查j是否支配i
            if all(solutions(j, :) <= solutions(i, :)) && any(solutions(j, :) < solutions(i, :))
                dominated(i) = true;
                break;
            end
        end
    end
end

non_dominated = solutions(~dominated, :);
end

function gd = GenerationDistance(approximation_front, reference_front)
% 计算生成距离(Generation Distance)指标
n_approx = size(approximation_front, 1);
total_dist = 0;

for i = 1:n_approx
    % 计算当前点到参考前沿的最小欧几里得距离
    min_dist = inf;
    for j = 1:size(reference_front, 1)
        dist = sqrt(sum((approximation_front(i,:) - reference_front(j,:)).^2));
        min_dist = min(min_dist, dist);
    end
    total_dist = total_dist + min_dist^2;
end

gd = sqrt(total_dist / n_approx);
end

function igd = InvertedGenerationDistance(reference_front, approximation_front)
% 计算反向生成距离(IGD)
if isempty(approximation_front) || isempty(reference_front)
    igd = Inf;
    return;
end

% 计算每个参考点到近似前沿的最小距离
n_ref = size(reference_front, 1);
min_distances = zeros(n_ref, 1);

for i = 1:n_ref
    ref_point = reference_front(i, :);
    min_dist = Inf;
    
    % 找到最近的近似前沿点
    for j = 1:size(approximation_front, 1)
        approx_point = approximation_front(j, :);
        dist = sqrt(sum((ref_point - approx_point).^2));
        min_dist = min(min_dist, dist);
    end
    
    min_distances(i) = min_dist;
end

% IGD是所有最小距离的平均值
igd = mean(min_distances);
end

function sp = SpreadMetric(front)
% 计算分布均匀度(Spread / Diversity)指标
n = size(front, 1);
if n <= 1
    sp = 0;
    return;
end

% 计算解之间的欧几里得距离
distances = zeros(n-1, 1);
for i = 1:n-1
    distances(i) = sqrt(sum((front(i,:) - front(i+1,:)).^2));
end

% 计算平均距离
mean_dist = mean(distances);

% 计算散布度指标
numerator = sum(abs(distances - mean_dist));
denominator = (n-1) * mean_dist;

if denominator == 0
    sp = 0;
else
    sp = numerator / denominator;
end
end

function ms = MaximumSpread(approximation_front, reference_front)
% 计算最大扩展度(Maximum Spread)指标
m = size(approximation_front, 2); % 目标数量

% 计算近似前沿和参考前沿在各目标上的极值
min_approx = min(approximation_front);
max_approx = max(approximation_front);
min_ref = min(reference_front);
max_ref = max(reference_front);

% 计算各目标上的归一化扩展度
spread_sum = 0;
for i = 1:m
    range_approx = max_approx(i) - min_approx(i);
    range_ref = max_ref(i) - min_ref(i);
    if range_ref == 0
        normalized_spread = 1;
    else
        normalized_spread = range_approx / range_ref;
    end
    spread_sum = spread_sum + normalized_spread^2;
end

ms = sqrt(spread_sum / m);
end

function hv = Hypervolume(front, reference_point)
% 计算超体积(Hypervolume)指标
% 简化版本，仅支持2-3维目标空间

% 确保参考点大于前沿中的所有点
if ~isempty(front)
    max_front = max(front);
    for i = 1:length(reference_point)
        if i <= length(max_front) && reference_point(i) <= max_front(i)
            reference_point(i) = max_front(i) * 1.1; % 增加10%的余量
        end
    end
end

n = size(front, 1);
m = size(front, 2);

if n == 0
    hv = 0;
    return;
end

if m == 2
    % 2维情况
    sorted_front = sortrows(front, 1);
    hv = 0;
    for i = 1:n
        if i == 1
            width = reference_point(1) - sorted_front(i, 1);
        else
            width = sorted_front(i-1, 1) - sorted_front(i, 1);
        end
        height = reference_point(2) - sorted_front(i, 2);
        if width > 0 && height > 0
            hv = hv + width * height;
        end
    end
elseif m == 3
    % 3维情况（简化计算）
    hv = 0;
    for i = 1:n
        volume = 1;
        for j = 1:m
            volume = volume * max(0, reference_point(j) - front(i, j));
        end
        hv = hv + volume;
    end
    hv = hv / n; % 归一化
else
    % 高维情况使用近似方法
    hv = 0;
    for i = 1:n
        volume = 1;
        for j = 1:m
            volume = volume * max(0, reference_point(j) - front(i, j));
        end
        hv = hv + volume;
    end
    hv = hv / n; % 归一化
end
end

function c = CalculateCoverage(A, B)
% 计算传统的C-metric覆盖率
% C(A,B) = |{b ∈ B | ∃a ∈ A: a ≼ b}| / |B|

if isempty(A) || isempty(B)
    c = 0;
    return;
end

n_B = size(B, 1);
n_dominated = 0;

for i = 1:n_B
    b = B(i, :);
    for j = 1:size(A, 1)
        a = A(j, :);
        % 检查a是否支配b
        % 对于最小化问题，a支配b意味着：a <= b（所有维度）且 a < b（至少一个维度）
        if all(a <= b) && any(a < b)
            n_dominated = n_dominated + 1;
            break;  % 如果b被支配，不需要检查其他的a
        end
    end
end

c = n_dominated / n_B;
end
