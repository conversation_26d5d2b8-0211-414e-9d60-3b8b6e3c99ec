function [objectives, constraints, gear_system] = OptimizationObjectiveFunction(x, system_params)
% OptimizationObjectiveFunction 重构的优化目标函数
% 统一的优化接口，保持原有计算逻辑不变
%
% 输入:
%   x - 优化变量向量
%   system_params - 系统参数结构体
%
% 输出:
%   objectives - 目标函数值 [总质量, -最小弯曲安全系数, -最小接触安全系数]
%   constraints - 约束违反值
%   gear_system - 完整的齿轮系统参数（可选输出）

%% 1. 参数验证和初始化
if nargin < 2
    system_params = struct();
end

% 获取参数定义
param_def = GearParameterDefinition();

% 验证优化变量维度
expected_dim = length(param_def.optimization_variables);
if length(x) ~= expected_dim
    error('优化变量维度不匹配。期望 %d 个变量，实际 %d 个', expected_dim, length(x));
end

%% 2. 调用统一计算器
try
    [gear_system, objectives, constraints] = GearSystemCalculator(x, system_params);
    
    % 确保目标函数格式正确
    if length(objectives) ~= 3
        objectives = [Inf, 0, 0];  % 默认无效解
        constraints = Inf;
    end
    
    % 确保约束值为标量
    if length(constraints) > 1
        constraints = max(constraints);  % 取最大约束违反值
    end
    
catch e
    fprintf('优化目标函数计算出错: %s\n', e.message);
    objectives = [Inf, 0, 0];
    constraints = Inf;
    
    % 创建空的齿轮系统结构体
    gear_system = struct();
    for i = 1:length(param_def.table_columns)
        gear_system.(param_def.table_columns{i}) = 0;
    end
    gear_system.is_valid = false;
end

%% 3. 结果验证
if any(isnan(objectives)) || any(isinf(objectives))
    objectives = [Inf, 0, 0];
    constraints = Inf;
end

if isnan(constraints) || constraints < 0
    constraints = Inf;
end

end
