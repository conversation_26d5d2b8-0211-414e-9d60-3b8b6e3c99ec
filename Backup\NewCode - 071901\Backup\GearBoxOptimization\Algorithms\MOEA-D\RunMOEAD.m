function [population, objectives] = RunMOEAD(problem, params)
% RunMOEAD - 运行基于分解的多目标进化算法 (MOEA/D)
%
% 输入:
%   problem - 包含问题信息的结构体
%   params  - 算法参数
%
% 输出:
%   population - 最终种群
%   objectives - 最终的非支配解集目标函数值
%
% 参考文献:
% <PERSON><PERSON> and <PERSON><PERSON>, MOEA/D: A multiobjective evolutionary algorithm based
% on decomposition, IEEE Transactions on Evolutionary Computation, 2007,
% 11(6): 712-731.

%% 初始化参数
nVar = problem.nVar;
varSize = problem.varSize;
varMin = problem.varMin;
varMax = problem.varMax;
nPop = params.nPop;
maxIt = params.maxIt;
nObj = problem.nObj;

% MOEA/D特定参数
type = 2;                         % 使用切比雪夫方法(Tchebycheff approach)
pCrossover = params.pCrossover;   % 交叉概率
pMutation = params.pMutation;     % 变异概率

% 确保有评价次数计数器
if ~isfield(problem, 'FE')
    problem.FE = 0;
end

fprintf('开始运行MOEA/D算法...\n');

%% 生成权重向量
[W, nPop] = UniformPoint(nPop, nObj);

%% 检测每个解的邻居
T = ceil(nPop/10);  % 邻居大小

% 计算权重向量之间的距离矩阵
B = pdist2(W, W);
[~, B] = sort(B, 2);
B = B(:, 1:T);      % 存储每个个体的T个最近邻居

%% 初始化种群
empty_individual.Position = [];
empty_individual.Cost = [];

pop = repmat(empty_individual, nPop, 1);

for i = 1:nPop
    % 生成随机解
    pop(i).Position = unifrnd(varMin, varMax, varSize);
    
    % 对离散变量特殊处理
    if isfield(problem, 'discreteVars')
        for j = 1:length(problem.discreteVars)
            idx = problem.discreteVars(j).idx;
            if problem.discreteVars(j).isInteger
                pop(i).Position(idx) = round(pop(i).Position(idx));
            else
                % 找到最接近的离散值
                values = problem.discreteVars(j).values;
                [~, closest_idx] = min(abs(pop(i).Position(idx) - values));
                pop(i).Position(idx) = values(closest_idx);
            end
        end
    end
    
    % 评估目标函数
    pop(i).Cost = problem.costFunction(pop(i).Position);
    problem.FE = problem.FE + 1;
end

% 初始化理想点
Z = min(reshape([pop.Cost], nObj, []), [], 2)';

%% 优化主循环
for it = 1:maxIt
    % 对每个解进行操作
    for i = 1:nPop
        % 从邻居中随机选择父代
        P = B(i, randperm(size(B, 2)));
        
        % 生成子代
        offspring = GAhalf(problem, pop(P(1:2)), pCrossover, pMutation, varMin, varMax);
        problem.FE = problem.FE + 1;
        
        % 更新理想点
        Z = min(Z, offspring.Cost);
        
        % 更新邻居解
        for j = 1:T
            neighbor_idx = P(j);
            
            % 根据聚合函数类型计算替换指标
            switch type
                case 1
                    % PBI方法
                    normW = sqrt(sum(W(neighbor_idx, :).^2));
                    normP = sqrt(sum((pop(neighbor_idx).Cost - Z).^2));
                    normO = sqrt(sum((offspring.Cost - Z).^2));
                    
                    CosineP = sum((pop(neighbor_idx).Cost - Z) .* W(neighbor_idx, :)) / (normW * normP);
                    CosineO = sum((offspring.Cost - Z) .* W(neighbor_idx, :)) / (normW * normO);
                    
                    g_old = normP * CosineP + 5 * normP * sqrt(1 - CosineP^2);
                    g_new = normO * CosineO + 5 * normO * sqrt(1 - CosineO^2);
                    
                case 2
                    % 切比雪夫方法
                    g_old = max(abs(pop(neighbor_idx).Cost - Z) .* W(neighbor_idx, :));
                    g_new = max(abs(offspring.Cost - Z) .* W(neighbor_idx, :));
                    
                case 3
                    % 归一化切比雪夫方法
                    Zmax = max(reshape([pop.Cost], nObj, []), [], 2)';
                    g_old = max(abs(pop(neighbor_idx).Cost - Z) ./ (Zmax - Z) .* W(neighbor_idx, :));
                    g_new = max(abs(offspring.Cost - Z) ./ (Zmax - Z) .* W(neighbor_idx, :));
                    
                case 4
                    % 修改的切比雪夫方法
                    g_old = max(abs(pop(neighbor_idx).Cost - Z) ./ W(neighbor_idx, :));
                    g_new = max(abs(offspring.Cost - Z) ./ W(neighbor_idx, :));
            end
            
            % 如果新解更好，进行替换
            if g_new <= g_old
                pop(neighbor_idx) = offspring;
            end
        end
    end
    
    % 显示迭代信息
    if mod(it, 10) == 0 || it == maxIt
        % 计算非支配解数量
        nonDominated = GetNonDominatedSet(pop);
        n_nondom = numel(nonDominated);
        disp(['迭代 ' num2str(it) '/' num2str(maxIt) ', 非支配解数量 = ' num2str(n_nondom) ', 评价次数 = ' num2str(problem.FE)]);
    end
end

% 提取非支配解集
nonDominated = GetNonDominatedSet(pop);

% 返回最终结果
n_pareto = numel(nonDominated);
population = zeros(n_pareto, nVar);
objectives = zeros(n_pareto, nObj);

for i = 1:n_pareto
    population(i, :) = nonDominated(i).Position;
    objectives(i, :) = nonDominated(i).Cost;
end

% 处理最大化目标
objectives(:, 2) = -objectives(:, 2);  % 取负值以便最大化弯曲安全系数
objectives(:, 3) = -objectives(:, 3);  % 取负值以便最大化接触安全系数

% 不再输出算法完成信息，由主程序统一处理
end

%% 辅助函数
function [W, N] = UniformPoint(N, M)
% 生成均匀分布的权重向量

if M == 2
    % 二目标直接均匀分布
    W = zeros(N, M);
    for i = 1:N
        W(i, 1) = (i-1)/(N-1);
        W(i, 2) = 1-W(i, 1);
    end
else
    % 多目标使用Das和Dennis方法
    H1 = 1;
    while nchoosek(H1+M-1, M-1) <= N
        H1 = H1 + 1;
    end
    W = nchoosek(1:H1+M-1, M-1) - repmat(0:M-2, nchoosek(H1+M-1, M-1), 1) - 1;
    W = [W, repmat(H1, size(W, 1), 1)] - [zeros(size(W, 1), 1), W];
    W = W./H1;
    
    if size(W, 1) > N
        W = W(1:N, :);
    else
        N = size(W, 1);
    end
end
end

function offspring = GAhalf(problem, parents, pc, pm, lb, ub)
% 遗传算法半均匀交叉

offspring = struct();

% 提取父代
parent1 = parents(1);
parent2 = parents(2);

% 交叉操作
if rand <= pc
    % 半均匀交叉
    alpha = rand(size(parent1.Position));
    offspring.Position = parent1.Position .* alpha + parent2.Position .* (1 - alpha);
else
    % 不交叉时，随机选择一个父代
    if rand < 0.5
        offspring.Position = parent1.Position;
    else
        offspring.Position = parent2.Position;
    end
end

% 变异操作
if rand <= pm
    % 多项式变异
    eta_m = 20;  % 变异分布指数
    
    for j = 1:length(offspring.Position)
        if rand < 1/length(offspring.Position)
            y = offspring.Position(j);
            low = lb(min(j, length(lb)));
            up = ub(min(j, length(ub)));
            
            delta1 = (y - low) / (up - low);
            delta2 = (up - y) / (up - low);
            
            rand_mut = rand();
            mut_pow = 1/(eta_m + 1);
            
            if rand_mut <= 0.5
                xy = 1 - delta1;
                val = 2 * rand_mut + (1 - 2 * rand_mut) * (xy^(eta_m + 1));
                delta_q = val^mut_pow - 1;
            else
                xy = 1 - delta2;
                val = 2 * (1 - rand_mut) + (2 * rand_mut - 1) * (xy^(eta_m + 1));
                delta_q = 1 - val^mut_pow;
            end
            
            offspring.Position(j) = y + delta_q * (up - low);
        end
    end
end

% 对离散变量特殊处理
if isfield(problem, 'discreteVars')
    for j = 1:length(problem.discreteVars)
        idx = problem.discreteVars(j).idx;
        if problem.discreteVars(j).isInteger
            offspring.Position(idx) = round(offspring.Position(idx));
        else
            % 找到最接近的离散值
            values = problem.discreteVars(j).values;
            [~, closest_idx] = min(abs(offspring.Position(idx) - values));
            offspring.Position(idx) = values(closest_idx);
        end
    end
end

% 边界处理
offspring.Position = max(offspring.Position, lb);
offspring.Position = min(offspring.Position, ub);

% 评估子代
offspring.Cost = problem.costFunction(offspring.Position);
end

function nonDominated = GetNonDominatedSet(pop)
% 获取非支配解集

objValues = reshape([pop.Cost], [], length(pop))';
N = size(objValues, 1);
dominated = false(1, N);

for i = 1:N-1
    for j = i+1:N
        if ~dominated(i) && ~dominated(j)
            if all(objValues(i,:) <= objValues(j,:)) && any(objValues(i,:) < objValues(j,:))
                dominated(j) = true;
            elseif all(objValues(j,:) <= objValues(i,:)) && any(objValues(j,:) < objValues(i,:))
                dominated(i) = true;
            end
        end
    end
end

nonDominated = pop(~dominated);
end 