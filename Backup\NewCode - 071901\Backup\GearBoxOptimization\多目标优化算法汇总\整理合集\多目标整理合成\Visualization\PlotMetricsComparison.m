function PlotMetricsComparison(metrics_data, algorithm_names, metric_names, problem_name, fig_num)
% PlotMetricsComparison - 绘制多个算法在不同评价指标上的比较图
%
% 输入:
%   metrics_data - 评价指标数据，矩阵，每行是一个算法，每列是一个指标
%   algorithm_names - 算法名称，元胞数组
%   metric_names - 指标名称，元胞数组
%   problem_name - 问题名称，字符串
%   fig_num - 图形编号，可选，默认为2
%
% 示例:
%   PlotMetricsComparison(metrics_data, {'NSGA2', 'MOPSO'}, {'GD', 'IGD', 'HV'}, 'ZDT1', 2)

    % 默认参数
    if nargin < 5
        fig_num = 2;
    end
    
    % 如果指标数目大于1，创建一个子图组
    num_metrics = length(metric_names);
    
    if num_metrics > 1
        figure(fig_num);
        clf;
        
        % 设置图形属性
        set(gcf, 'Position', [100, 100, 1000, 200*ceil(num_metrics/2)]);
        
        % 计算子图行列数
        num_rows = ceil(num_metrics / 2);
        num_cols = min(2, num_metrics);
        
        % 为每个指标创建子图
        for m = 1:num_metrics
            subplot(num_rows, num_cols, m);
            
            % 绘制条形图
            bar(metrics_data(:, m), 'FaceColor', 'flat');
            
            % 设置横坐标标签
            set(gca, 'XTick', 1:length(algorithm_names));
            set(gca, 'XTickLabel', algorithm_names);
            set(gca, 'XTickLabelRotation', 45);
            
            % 添加标题和标签
            title([metric_names{m}, ' - ', problem_name], 'FontSize', 12);
            ylabel(metric_names{m}, 'FontSize', 10);
            
            % 添加数值标签
            for i = 1:length(algorithm_names)
                text(i, metrics_data(i, m), sprintf('%.4f', metrics_data(i, m)), ...
                    'HorizontalAlignment', 'center', ...
                    'VerticalAlignment', 'bottom', ...
                    'FontSize', 8);
            end
            
            grid on;
        end
        
        % 调整子图之间的间隔
        if num_metrics > 1
            tight_subplot = true;
            if tight_subplot
                try
                    % 尝试添加紧凑布局（需要MATLAB新版本）
                    tiledlayout('flow');
                catch
                    % 旧版本的MATLAB不支持tiledlayout，使用传统的方法
                    set(gcf, 'Position', [100, 100, 1000, 200*ceil(num_metrics/2)]);
                    % 可以手动调整子图的位置
                end
            end
        end
    else
        % 只有一个指标时，创建单个图形
        figure(fig_num);
        clf;
        
        % 设置图形属性
        set(gcf, 'Position', [100, 100, 600, 400]);
        
        % 绘制条形图
        bar(metrics_data, 'FaceColor', 'flat');
        
        % 设置横坐标标签
        set(gca, 'XTick', 1:length(algorithm_names));
        set(gca, 'XTickLabel', algorithm_names);
        set(gca, 'XTickLabelRotation', 45);
        
        % 添加标题和标签
        title([metric_names{1}, ' - ', problem_name], 'FontSize', 14);
        ylabel(metric_names{1}, 'FontSize', 12);
        
        % 添加数值标签
        for i = 1:length(algorithm_names)
            text(i, metrics_data(i), sprintf('%.4f', metrics_data(i)), ...
                'HorizontalAlignment', 'center', ...
                'VerticalAlignment', 'bottom', ...
                'FontSize', 10);
        end
        
        grid on;
    end
    
    % 添加图形保存选项
    sgtitle(['指标比较 - ', problem_name], 'FontSize', 14, 'FontWeight', 'bold');
end

% 较旧版本的MATLAB不支持sgtitle函数，提供一个兼容函数
function sgtitle(titleText, varargin)
    try
        sgtitle(titleText, varargin{:});
    catch
        % 如果不支持sgtitle，使用传统方法添加标题
        axes('position', [0, 0.95, 1, 0.05], 'visible', 'off');
        text(0.5, 0, titleText, 'HorizontalAlignment', 'center', 'FontSize', 14, 'FontWeight', 'bold');
    end
end 