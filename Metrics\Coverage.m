function Score = Coverage(A, B)
% Coverage - C-metric覆盖率指标
% 计算解集A对解集B的覆盖率
%
% 输入:
%   A - 解集A
%   B - 解集B
%
% 输出:
%   Score - 覆盖率值，范围[0,1]，越大越好
%
% 公式: C(A,B) = |{b ∈ B | ∃a ∈ A: a ≼ b}| / |B|
% 表示集合A中有多少解支配了集合B中的解
%
% 参考文献:
% <PERSON>, E., & <PERSON>, L. (1998). Multiobjective optimization using
% evolutionary algorithms—a comparative case study.

if isempty(A) || isempty(B)
    Score = 0;
    return;
end

n_B = size(B, 1);
n_dominated = 0;

for i = 1:n_B
    b = B(i, :);
    for j = 1:size(A, 1)
        a = A(j, :);
        % 检查a是否支配b
        % 对于最小化问题，a支配b意味着：a <= b（所有维度）且 a < b（至少一个维度）
        if all(a <= b) && any(a < b)
            n_dominated = n_dominated + 1;
            break;  % 如果b被支配，不需要检查其他的a
        end
    end
end

Score = n_dominated / n_B;
end