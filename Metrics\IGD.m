function Score = IGD(reference_front, approximation_front)
% IGD - Inverted Generation Distance 反向生成距离指标
% 计算参考前沿到近似前沿的平均距离
%
% 输入:
%   reference_front - 参考前沿解集 (真实前沿或参考解)
%   approximation_front - 近似前沿解集 (算法求得的解)
%
% 输出:
%   Score - IGD值，越小越好
%
% 公式: IGD = 1/|P| * Σ(min_dist_i)
% 其中 min_dist_i 是参考前沿第i个点到近似前沿的最小距离
%
% 参考文献:
% Coello, C. A. C., & Reyes-Sierra, M. (2006). A study of the parallelization
% of a coevolutionary multi-objective evolutionary algorithm.

if isempty(approximation_front) || isempty(reference_front)
    Score = Inf;
    return;
end

% 计算每个参考点到近似前沿的最小距离
n_ref = size(reference_front, 1);
min_distances = zeros(n_ref, 1);

for i = 1:n_ref
    ref_point = reference_front(i, :);
    min_dist = Inf;

    % 找到最近的近似前沿点
    for j = 1:size(approximation_front, 1)
        approx_point = approximation_front(j, :);
        dist = sqrt(sum((ref_point - approx_point).^2));
        min_dist = min(min_dist, dist);
    end

    min_distances(i) = min_dist;
end

% IGD是所有最小距离的平均值
Score = mean(min_distances);
end