function materials = MaterialProperties()
% MaterialProperties 齿轮材料属性配置
% 严格按照原有MaterialManager.m中的参数定义
%
% 输出:
%   materials - 材料属性结构体

%% 1. 17CrNiMo6 材料属性（按照原有MaterialManager.m）
materials.material_17CrNiMo6 = struct();
materials.material_17CrNiMo6.name = '17CrNiMo6';
materials.material_17CrNiMo6.density = 7850;                    % 密度 (kg/m³)
materials.material_17CrNiMo6.youngs_modulus = 206e9;            % 杨氏模量 (Pa)
materials.material_17CrNiMo6.poissons_ratio = 0.3;              % 泊松比
materials.material_17CrNiMo6.yield_strength = 785e6;            % 屈服强度 (Pa)
materials.material_17CrNiMo6.tensile_strength = 1080e6;         % 拉伸强度 (Pa)
materials.material_17CrNiMo6.bending_strength = 430e6;          % 弯曲疲劳极限 (Pa)
materials.material_17CrNiMo6.contact_strength = 1500e6;         % 接触疲劳极限 (Pa)
materials.material_17CrNiMo6.HB = 300;                          % 布氏硬度

%% 2. 20CrNi2MoA 材料属性（按照原有MaterialManager.m）
materials.material_20CrNi2MoA = struct();
materials.material_20CrNi2MoA.name = '20CrNi2MoA';
materials.material_20CrNi2MoA.density = 7870;                   % 密度 (kg/m³)
materials.material_20CrNi2MoA.youngs_modulus = 210e9;           % 杨氏模量 (Pa)
materials.material_20CrNi2MoA.poissons_ratio = 0.275;           % 泊松比
materials.material_20CrNi2MoA.yield_strength = 785e6;           % 屈服强度 (Pa)
materials.material_20CrNi2MoA.tensile_strength = 980e6;         % 拉伸强度 (Pa)
materials.material_20CrNi2MoA.bending_strength = 410e6;         % 弯曲疲劳极限 (Pa)
materials.material_20CrNi2MoA.contact_strength = 1400e6;        % 接触疲劳极限 (Pa)
materials.material_20CrNi2MoA.HB = 300;                         % 布氏硬度

%% 3. 42CrMoA 材料属性（按照原有MaterialManager.m）
materials.material_42CrMoA = struct();
materials.material_42CrMoA.name = '42CrMoA';
materials.material_42CrMoA.density = 7800;                      % 密度 (kg/m³)
materials.material_42CrMoA.youngs_modulus = 200e9;              % 杨氏模量 (Pa)
materials.material_42CrMoA.poissons_ratio = 0.3;                % 泊松比
materials.material_42CrMoA.yield_strength = 930e6;              % 屈服强度 (Pa)
materials.material_42CrMoA.tensile_strength = 1080e6;           % 拉伸强度 (Pa)
materials.material_42CrMoA.bending_strength = 182e6;            % 弯曲疲劳极限 (Pa)
materials.material_42CrMoA.contact_strength = 490e6;            % 接触疲劳极限 (Pa)
materials.material_42CrMoA.HB = 300;                            % 布氏硬度

%% 4. 标准齿轮系统材料配置（按照原有getStandardGearMaterials函数）
materials.gear_materials = struct();

% 平行轴齿轮材料 - 17CrNiMo6
materials.gear_materials.parallel = struct();
materials.gear_materials.parallel.name = '17CrNiMo6';
materials.gear_materials.parallel.density = 7850;           % kg/m³
materials.gear_materials.parallel.youngs_modulus = 206e9;   % Pa
materials.gear_materials.parallel.poissons_ratio = 0.3;
materials.gear_materials.parallel.yield_strength = 785e6;   % Pa
materials.gear_materials.parallel.tensile_strength = 1080e6; % Pa
materials.gear_materials.parallel.bending_strength = 430e6; % Pa
materials.gear_materials.parallel.contact_strength = 1500e6; % Pa

% 二级太阳轮材料 - 17CrNiMo6
materials.gear_materials.planet1_sun = materials.gear_materials.parallel;

% 二级行星轮材料 - 20CrNi2MoA
materials.gear_materials.planet1_planet = struct();
materials.gear_materials.planet1_planet.name = '20CrNi2MoA';
materials.gear_materials.planet1_planet.density = 7870;     % kg/m³
materials.gear_materials.planet1_planet.youngs_modulus = 210e9; % Pa
materials.gear_materials.planet1_planet.poissons_ratio = 0.275;
materials.gear_materials.planet1_planet.yield_strength = 785e6; % Pa
materials.gear_materials.planet1_planet.tensile_strength = 980e6; % Pa
materials.gear_materials.planet1_planet.bending_strength = 410e6; % Pa
materials.gear_materials.planet1_planet.contact_strength = 1400e6; % Pa

% 二级内齿圈材料 - 42CrMoA
materials.gear_materials.planet1_ring = struct();
materials.gear_materials.planet1_ring.name = '42CrMoA';
materials.gear_materials.planet1_ring.density = 7800;       % kg/m³
materials.gear_materials.planet1_ring.youngs_modulus = 200e9; % Pa
materials.gear_materials.planet1_ring.poissons_ratio = 0.3;
materials.gear_materials.planet1_ring.yield_strength = 930e6; % Pa
materials.gear_materials.planet1_ring.tensile_strength = 1080e6; % Pa
materials.gear_materials.planet1_ring.bending_strength = 182e6; % Pa
materials.gear_materials.planet1_ring.contact_strength = 490e6; % Pa

% 三级材料（与二级相同）
materials.gear_materials.planet2_sun = materials.gear_materials.planet1_sun;
materials.gear_materials.planet2_planet = materials.gear_materials.planet1_planet;
materials.gear_materials.planet2_ring = materials.gear_materials.planet1_ring;

%% 5. 材料分配方案（与原有代码保持一致）
materials.assignment = struct();
materials.assignment.gear1_material = '17CrNiMo6';        % 一级小齿轮
materials.assignment.gear2_material = '17CrNiMo6';        % 一级大齿轮
materials.assignment.sun2_material = '17CrNiMo6';         % 二级太阳轮
materials.assignment.planet2_material = '20CrNi2MoA';     % 二级行星轮
materials.assignment.ring2_material = '42CrMoA';          % 二级内齿圈
materials.assignment.sun3_material = '17CrNiMo6';         % 三级太阳轮
materials.assignment.planet3_material = '20CrNi2MoA';     % 三级行星轮
materials.assignment.ring3_material = '42CrMoA';          % 三级内齿圈

%% 6. 材料查询函数（兼容原有MaterialManager接口）
materials.getMaterialByName = @(name) getMaterialProperties(materials, name);

end

%% ========== 辅助函数 ==========

function material_props = getMaterialProperties(materials, material_name)
% 根据材料名称获取材料属性（兼容原有MaterialManager接口）
switch upper(material_name)
    case '17CRNIMO6'
        material_props = materials.material_17CrNiMo6;
    case '20CRNI2MOA'
        material_props = materials.material_20CrNi2MoA;
    case '42CRMOA'
        material_props = materials.material_42CrMoA;
    otherwise
        % 返回默认材料（17CrNiMo6）
        warning('未知材料名称: %s，使用默认值', material_name);
        material_props = materials.material_17CrNiMo6;
        material_props.name = material_name;
end

% 添加ISO参数（兼容原有接口）
material_props.iso_params = struct();
material_props.iso_params.E = material_props.youngs_modulus;
material_props.iso_params.poisson = material_props.poissons_ratio;
material_props.iso_params.HB = material_props.HB;
material_props.iso_params.sigmaFlim = material_props.bending_strength;
material_props.iso_params.sigmaHlim = material_props.contact_strength;
material_props.iso_params.yield_strength = material_props.yield_strength;
material_props.iso_params.tensile_strength = material_props.tensile_strength;
end
