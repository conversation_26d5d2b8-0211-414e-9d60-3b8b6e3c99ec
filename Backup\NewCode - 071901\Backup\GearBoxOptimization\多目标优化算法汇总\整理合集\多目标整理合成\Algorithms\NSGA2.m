function [pop, F1] = NSGA2(Cost<PERSON>unction, nVar, Var<PERSON>in, VarMax, MaxIt, nPop)
% NSGA-II: Non-dominated Sorting Genetic Algorithm II
%
% Inputs:
%   CostFunction: Function handle to the objective function
%   nVar: Number of decision variables
%   VarMin, VarMax: Lower and upper bounds of variables
%   MaxIt: Maximum number of iterations
%   nPop: Population size
%
% Outputs:
%   pop: Final population
%   F1: First front solutions (Pareto front)

    % Default parameter values if not provided
    if nargin < 6
        nPop = 50;
    end
    if nargin < 5
        MaxIt = 100;
    end
    if nargin < 4
        VarMax = 1;
    end
    if nargin < 3
        VarMin = 0;
    end
    if nargin < 2
        nVar = 5;
    end
    
    % Decision variables matrix size
    VarSize = [1 nVar];
    
    % Get number of objectives
    nObj = numel(CostFunction(unifrnd(VarMin, VarMax, VarSize)));
    
    %% NSGA-II Parameters
    pCrossover = 0.7;                        % Crossover percentage
    nCrossover = 2*round(pCrossover*nPop/2); % Number of parents (offsprings)
    
    pMutation = 0.4;                         % Mutation percentage
    nMutation = round(pMutation*nPop);       % Number of mutants
    
    mu = 0.02;                               % Mutation rate
    sigma = 0.1*(VarMax-VarMin);             % Mutation step size
    
    %% Initialization
    empty_individual.Position = [];
    empty_individual.Cost = [];
    empty_individual.Rank = [];
    empty_individual.DominationSet = [];
    empty_individual.DominatedCount = [];
    empty_individual.CrowdingDistance = [];
    
    pop = repmat(empty_individual, nPop, 1);
    
    for i = 1:nPop
        pop(i).Position = unifrnd(VarMin, VarMax, VarSize);
        pop(i).Cost = CostFunction(pop(i).Position);
    end
    
    % Non-Dominated Sorting
    [pop, F] = NonDominatedSorting(pop);
    
    % Calculate Crowding Distance
    pop = CalcCrowdingDistance(pop, F);
    
    % Sort Population
    [pop, F] = SortPopulation(pop);
    
    %% NSGA-II Main Loop
    for it = 1:MaxIt
        
        % Crossover
        popc = repmat(empty_individual, nCrossover/2, 2);
        for k = 1:nCrossover/2
            
            i1 = randi([1 nPop]);
            p1 = pop(i1);
            
            i2 = randi([1 nPop]);
            p2 = pop(i2);
            
            [popc(k, 1).Position, popc(k, 2).Position] = Crossover(p1.Position, p2.Position);
            
            popc(k, 1).Cost = CostFunction(popc(k, 1).Position);
            popc(k, 2).Cost = CostFunction(popc(k, 2).Position);
            
        end
        popc = popc(:);
        
        % Mutation
        popm = repmat(empty_individual, nMutation, 1);
        for k = 1:nMutation
            
            i = randi([1 nPop]);
            p = pop(i);
            
            popm(k).Position = Mutate(p.Position, mu, sigma);
            
            popm(k).Cost = CostFunction(popm(k).Position);
            
        end
        
        % Merge
        pop = [pop; popc; popm];
        
        % Non-Dominated Sorting
        [pop, F] = NonDominatedSorting(pop);
        
        % Calculate Crowding Distance
        pop = CalcCrowdingDistance(pop, F);
        
        % Sort Population
        pop = SortPopulation(pop);
        
        % Truncate
        pop = pop(1:nPop);
        
        % Non-Dominated Sorting
        [pop, F] = NonDominatedSorting(pop);
        
        % Calculate Crowding Distance
        pop = CalcCrowdingDistance(pop, F);
        
        % Sort Population
        [pop, F] = SortPopulation(pop);
        
        % Store F1
        F1 = pop(F{1});
    end
end

function [pop, F] = NonDominatedSorting(pop)
    nPop = numel(pop);
    
    % Initialize
    for i = 1:nPop
        pop(i).DominationSet = [];
        pop(i).DominatedCount = 0;
    end
    
    F{1} = [];
    
    for i = 1:nPop
        for j = i+1:nPop
            p = pop(i);
            q = pop(j);
            
            if Dominates(p.Cost, q.Cost)
                p.DominationSet = [p.DominationSet j];
                q.DominatedCount = q.DominatedCount + 1;
            end
            
            if Dominates(q.Cost, p.Cost)
                q.DominationSet = [q.DominationSet i];
                p.DominatedCount = p.DominatedCount + 1;
            end
            
            pop(i) = p;
            pop(j) = q;
        end
        
        if pop(i).DominatedCount == 0
            F{1} = [F{1} i];
            pop(i).Rank = 1;
        end
    end
    
    k = 1;
    while true
        Q = [];
        for i = F{k}
            p = pop(i);
            for j = p.DominationSet
                q = pop(j);
                q.DominatedCount = q.DominatedCount - 1;
                if q.DominatedCount == 0
                    Q = [Q j];
                    q.Rank = k + 1;
                end
                pop(j) = q;
            end
        end
        
        if isempty(Q)
            break;
        end
        
        F{k+1} = Q;
        k = k + 1;
    end
    
end

function pop = CalcCrowdingDistance(pop, F)
    nF = numel(F);
    
    for k = 1:nF
        members = F{k};
        n = numel(members);
        
        if n <= 1
            continue;
        end
        
        for i = 1:n
            pop(members(i)).CrowdingDistance = 0;
        end
        
        nObj = numel(pop(1).Cost);
        for j = 1:nObj
            [~, so] = sort([pop(members).Cost]', j);
            
            pop(members(so(1))).CrowdingDistance = inf;
            pop(members(so(end))).CrowdingDistance = inf;
            
            minCost = pop(members(so(1))).Cost(j);
            maxCost = pop(members(so(end))).Cost(j);
            
            for i = 2:n-1
                if maxCost - minCost > 0
                    pop(members(so(i))).CrowdingDistance = ...
                        pop(members(so(i))).CrowdingDistance + ...
                        (pop(members(so(i+1))).Cost(j) - pop(members(so(i-1))).Cost(j))/(maxCost-minCost);
                end
            end
        end
    end
end

function [pop, F] = SortPopulation(pop)
    % Sort Based on Rank
    [~, so] = sort([pop.Rank]);
    pop = pop(so);
    
    % Get Fronts
    ranks = [pop.Rank];
    maxRank = max(ranks);
    F = cell(maxRank, 1);
    for r = 1:maxRank
        F{r} = find(ranks == r);
    end
    
    % Sort Based on Crowding Distance
    for r = 1:maxRank
        [~, so] = sort([pop(F{r}).CrowdingDistance], 'descend');
        F{r} = F{r}(so);
    end
    
    % Sort Population
    newpop = [];
    for r = 1:maxRank
        newpop = [newpop; pop(F{r})];
    end
    pop = newpop;
    
    % Get Fronts
    ranks = [pop.Rank];
    maxRank = max(ranks);
    F = cell(maxRank, 1);
    for r = 1:maxRank
        F{r} = find(ranks == r);
    end
end

function [y1, y2] = Crossover(x1, x2)
    alpha = rand(size(x1));
    y1 = alpha.*x1 + (1-alpha).*x2;
    y2 = alpha.*x2 + (1-alpha).*x1;
end

function y = Mutate(x, mu, sigma)
    nVar = numel(x);
    nMu = ceil(mu*nVar);
    j = randsample(nVar, nMu);
    y = x;
    y(j) = x(j) + sigma*randn(size(j));
end

function b = Dominates(x, y)
    % Check if x dominates y
    b = all(x <= y) && any(x < y);
end 