function front_idx = NonDominatedSort(objectives)
% 执行快速非支配排序
n = size(objectives, 1);
front_idx = ones(n, 1);  % 默认都是第一前沿

% 双重循环比较解的支配关系
for i = 1:n
    for j = i+1:n
        % 检查i是否支配j或j是否支配i
        if all(objectives(i, :) <= objectives(j, :)) && any(objectives(i, :) < objectives(j, :))
            % i支配j, j的前沿等级加1
            front_idx(j) = front_idx(j) + 1;
        elseif all(objectives(j, :) <= objectives(i, :)) && any(objectives(j, :) < objectives(i, :))
            % j支配i, i的前沿等级加1
            front_idx(i) = front_idx(i) + 1;
        end
    end
end
end 