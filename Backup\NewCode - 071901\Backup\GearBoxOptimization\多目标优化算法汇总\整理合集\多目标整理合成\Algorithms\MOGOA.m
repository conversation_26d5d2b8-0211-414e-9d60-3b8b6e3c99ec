function [Archive_X, Archive_F] = MOGOA(CostFunction, nVar, VarMin, VarMax, MaxIt, nPop)
% MOGOA: 多目标蝗虫优化算法 (Multi-objective Grasshopper Optimization Algorithm)
%
% 输入参数:
%   CostFunction: 目标函数句柄
%   nVar: 决策变量数量
%   VarMin, VarMax: 决策变量的上下界
%   MaxIt: 最大迭代次数
%   nPop: 种群大小
%
% 输出参数:
%   Archive_X: 非支配解的决策变量
%   Archive_F: 非支配解的目标函数值
%
% 参考文献:
%   <PERSON><PERSON> et al., "Multi-objective grasshopper optimization algorithm: 
%   A novel multi-objective optimization technique," 
%   Applied Intelligence, 2017.

    % 默认参数值
    if nargin < 6
        nPop = 200;
    end
    if nargin < 5
        MaxIt = 100;
    end
    if nargin < 4
        VarMax = 1;
    end
    if nargin < 3
        VarMin = 0;
    end
    if nargin < 2
        nVar = 5;
    end
    
    % 处理nVar为奇数的情况（算法要求偶数）
    flag = 0;
    if (rem(nVar, 2) ~= 0)
        nVar = nVar + 1;
        VarMax = [VarMax, 1];
        VarMin = [VarMin, 0];
        flag = 1;
    end
    
    % 算法参数
    ArchiveMaxSize = 100;        % 存档最大大小
    
    % 初始化存档
    Archive_X = zeros(ArchiveMaxSize, nVar);
    Archive_F = ones(ArchiveMaxSize, 2) * inf;  % 假设是双目标问题
    Archive_member_no = 0;
    
    % 初始化蝗虫位置
    GrassHopperPositions = initialization(nPop, nVar, VarMax, VarMin);
    
    % 目标参数初始化
    TargetPosition = zeros(nVar, 1);
    TargetFitness = inf * ones(1, 2);  % 假设是双目标问题
    
    % 控制参数
    cMax = 1;
    cMin = 0.00004;
    
    % 获取目标函数数量
    sample = unifrnd(VarMin, VarMax, [1, nVar]);
    if flag == 1
        sample = sample(1:nVar-1);  % 如果nVar是奇数，移除额外添加的变量
    end
    obj_values = CostFunction(sample);
    obj_no = numel(obj_values);
    
    % 主循环
    for iter = 1:MaxIt
        % 评估蝗虫位置的适应度
        GrassHopperFitness = zeros(nPop, obj_no);
        for i = 1:nPop
            % 边界检查
            Flag4ub = GrassHopperPositions(:, i) > VarMax';
            Flag4lb = GrassHopperPositions(:, i) < VarMin';
            GrassHopperPositions(:, i) = (GrassHopperPositions(:, i).*(~(Flag4ub + Flag4lb))) + VarMax'.*Flag4ub + VarMin'.*Flag4lb;
            
            % 评估蝗虫的适应度
            if flag == 1
                GrassHopperFitness(i, :) = CostFunction(GrassHopperPositions(1:nVar-1, i)');
            else
                GrassHopperFitness(i, :) = CostFunction(GrassHopperPositions(:, i)');
            end
            
            % 更新目标位置
            if dominates(GrassHopperFitness(i, :), TargetFitness)
                TargetFitness = GrassHopperFitness(i, :);
                TargetPosition = GrassHopperPositions(:, i);
            end
        end
        
        % 更新存档
        [Archive_X, Archive_F, Archive_member_no] = UpdateArchive(Archive_X, Archive_F, GrassHopperPositions, GrassHopperFitness, Archive_member_no, flag);
        
        % 处理存档满的情况
        if Archive_member_no > ArchiveMaxSize
            Archive_mem_ranks = RankingProcess(Archive_F, ArchiveMaxSize, obj_no);
            [Archive_X, Archive_F, Archive_mem_ranks, Archive_member_no] = HandleFullArchive(Archive_X, Archive_F, Archive_member_no, Archive_mem_ranks, ArchiveMaxSize);
        else
            Archive_mem_ranks = RankingProcess(Archive_F, ArchiveMaxSize, obj_no);
        end
        
        % 从存档中选择目标位置
        index = RouletteWheelSelection(1./Archive_mem_ranks);
        if index == -1
            index = 1;
        end
        TargetFitness = Archive_F(index, :);
        TargetPosition = Archive_X(index, :)';
        
        % 更新c参数（控制搜索范围）
        c = cMax - iter * ((cMax - cMin) / MaxIt);  % 公式 (3.8)
        
        % 更新蝗虫位置
        GrassHopperPositions_temp = zeros(nPop, nVar);
        
        for i = 1:nPop
            S_i_total = zeros(nVar, 1);
            
            % 计算第i个蝗虫的社会互动
            for k = 1:2:nVar
                if k+1 <= nVar  % 确保不越界
                    S_i = zeros(2, 1);
                    for j = 1:nPop
                        if i ~= j
                            % 计算蝗虫之间的距离
                            Dist = distance(GrassHopperPositions(k:k+1, j), GrassHopperPositions(k:k+1, i));
                            % 计算单位向量
                            r_ij_vec = (GrassHopperPositions(k:k+1, j) - GrassHopperPositions(k:k+1, i)) / (Dist + eps);
                            % 计算社会力
                            xj_xi = 2 + rem(Dist, 2);
                            % 公式 (3.2)
                            s_ij = ((VarMax(k:k+1)' - VarMin(k:k+1)') .* c/2) * S_func(xj_xi) .* r_ij_vec;
                            S_i = S_i + s_ij;
                        end
                    end
                    S_i_total(k:k+1, :) = S_i;
                end
            end
            
            % 更新蝗虫位置，公式 (3.7)
            X_new = c * S_i_total' + (TargetPosition)';
            GrassHopperPositions_temp(i, :) = X_new';
        end
        
        % 更新蝗虫位置
        GrassHopperPositions = GrassHopperPositions_temp';
    end
    
    % 返回非支配解
    if flag == 1
        % 如果nVar是奇数，移除额外添加的变量
        Archive_X = Archive_X(:, 1:nVar-1);
    end
    
    % 只返回存档中的成员
    Archive_X = Archive_X(1:Archive_member_no, :);
    Archive_F = Archive_F(1:Archive_member_no, :);
end

% 初始化种群
function Positions = initialization(pop_size, dim, ub, lb)
    Positions = zeros(dim, pop_size);
    for i = 1:dim
        Positions(i, :) = rand(1, pop_size) .* (ub(i) - lb(i)) + lb(i);
    end
end

% 计算两点之间的距离
function d = distance(x1, x2)
    d = norm(x1 - x2);
end

% 社会函数
function result = S_func(x)
    f = 0.5;
    l = 1.5;
    result = f * exp(-x/l) - exp(-x);  % 公式 (3.5)
end

% 检查支配关系
function dom = dominates(x, y)
    % x支配y，如果x的所有目标都不劣于y，且至少有一个目标严格优于y
    dom = all(x <= y) && any(x < y);
end

% 更新存档
function [Archive_X, Archive_F, Archive_member_no] = UpdateArchive(Archive_X, Archive_F, Particles_X, Particles_F, Archive_member_no, flag)
    % 输入:
    %   Archive_X, Archive_F: 当前存档
    %   Particles_X, Particles_F: 当前粒子位置和适应度
    %   Archive_member_no: 当前存档成员数量
    %   flag: 是否需要处理额外变量
    
    [N, dim] = size(Particles_X);
    
    for i = 1:N
        particle_position = Particles_X(:, i)';
        
                 % 获取粒子适应度
         particle_fitness = Particles_F(i, :);
        
        if Archive_member_no == 0
            % 如果存档为空，直接添加
            Archive_X(Archive_member_no + 1, :) = particle_position;
            Archive_F(Archive_member_no + 1, :) = particle_fitness;
            Archive_member_no = Archive_member_no + 1;
        else
            dominated = false;
            should_add = true;
            
            j = 1;
            while j <= Archive_member_no && ~dominated
                if dominates(Archive_F(j, :), particle_fitness)
                    dominated = true;
                    should_add = false;
                elseif dominates(particle_fitness, Archive_F(j, :))
                    % 移除被支配的存档成员
                    Archive_X(j:Archive_member_no-1, :) = Archive_X(j+1:Archive_member_no, :);
                    Archive_F(j:Archive_member_no-1, :) = Archive_F(j+1:Archive_member_no, :);
                    Archive_member_no = Archive_member_no - 1;
                    j = j - 1;
                end
                j = j + 1;
            end
            
            if should_add
                Archive_X(Archive_member_no + 1, :) = particle_position;
                Archive_F(Archive_member_no + 1, :) = particle_fitness;
                Archive_member_no = Archive_member_no + 1;
            end
        end
    end
end

% 对存档成员进行排名
function ranks = RankingProcess(Archive_F, ArchiveMaxSize, obj_no)
    [Archive_member_no, ~] = size(Archive_F);
    
    % 计算拥挤距离
    distance_matrix = zeros(Archive_member_no, Archive_member_no);
    for i = 1:Archive_member_no
        for j = i:Archive_member_no
            distance_matrix(i, j) = sqrt(sum((Archive_F(i, :) - Archive_F(j, :)).^2));
            distance_matrix(j, i) = distance_matrix(i, j);
        end
    end
    
    % 计算拥挤度
    crowding = zeros(Archive_member_no, 1);
    for i = 1:Archive_member_no
        sorted_dist = sort(distance_matrix(i, :));
        % 求k个最近邻居的距离之和
        k = min(3, Archive_member_no-1);
        if k > 0
            crowding(i) = sum(sorted_dist(2:k+1));
        end
    end
    
    % 归一化拥挤度
    if max(crowding) ~= min(crowding)
        crowding = (crowding - min(crowding)) / (max(crowding) - min(crowding));
    else
        crowding = ones(Archive_member_no, 1);
    end
    
    ranks = crowding;
end

% 处理存档满的情况
function [Archive_X, Archive_F, Archive_mem_ranks, Archive_member_no] = HandleFullArchive(Archive_X, Archive_F, Archive_member_no, Archive_mem_ranks, ArchiveMaxSize)
    % 根据拥挤度排序
    [~, indices] = sort(Archive_mem_ranks);
    
    % 保留排名靠前的解
    Archive_X = Archive_X(indices(1:ArchiveMaxSize), :);
    Archive_F = Archive_F(indices(1:ArchiveMaxSize), :);
    Archive_mem_ranks = Archive_mem_ranks(indices(1:ArchiveMaxSize));
    Archive_member_no = ArchiveMaxSize;
end

% 轮盘赌选择
function index = RouletteWheelSelection(weights)
    if sum(weights) == 0
        index = -1;
        return;
    end
    
    accumulation = cumsum(weights);
    p = rand() * accumulation(end);
    chosen_index = -1;
    for i = 1:length(accumulation)
        if accumulation(i) > p
            chosen_index = i;
            break;
        end
    end
    index = chosen_index;
end 