function FrontNo = NonDominatedSort(PopObj)
% NonDominatedSort - 非支配排序
% 对解集进行非支配排序，返回每个解所属的前沿层级
%
% 输入:
%   PopObj - 目标函数值矩阵，每行是一个解的目标函数值
%
% 输出:
%   FrontNo - 每个解所属的前沿层级（1表示第一层前沿，即非支配解）

[N, M] = size(PopObj);

% 初始化
FrontNo = inf(1, N);
MaxFNo = 0;

% 计算每个解被多少个解支配，以及每个解支配哪些解
nP = zeros(1, N);  % 支配当前解的解的数量
sP = cell(1, N);   % 当前解支配的解的集合

% 计算支配关系
for i = 1:N
    sP{i} = [];
    nP(i) = 0;
    for j = 1:N
        if i ~= j
            % 检查解i是否支配解j
            if Dominates(PopObj(i,:), PopObj(j,:))
                sP{i} = [sP{i}, j];  % i支配j
            elseif Dominates(PopObj(j,:), PopObj(i,:))
                nP(i) = nP(i) + 1;   % j支配i
            end
        end
    end
    
    % 如果没有解支配当前解，则它属于第一层前沿
    if nP(i) == 0
        MaxFNo = 1;
        FrontNo(i) = 1;
    end
end

% 构建后续前沿层
while MaxFNo < N
    for i = 1:N
        if FrontNo(i) == MaxFNo
            % 对于当前前沿层的每个解，更新它支配的解
            for j = sP{i}
                nP(j) = nP(j) - 1;
                if nP(j) == 0
                    FrontNo(j) = MaxFNo + 1;
                end
            end
        end
    end
    MaxFNo = MaxFNo + 1;
end

end

function flag = Dominates(obj1, obj2)
% 判断obj1是否支配obj2
% 对于最小化问题，obj1支配obj2当且仅当：
% obj1在所有目标上都不劣于obj2，且至少在一个目标上严格优于obj2

flag = all(obj1 <= obj2) && any(obj1 < obj2);
end
