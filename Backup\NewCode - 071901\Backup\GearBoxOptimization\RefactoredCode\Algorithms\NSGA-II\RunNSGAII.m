function [population, objectives] = RunNSGAII(problem, params)
% RunNSGAII - 运行NSGA-II算法用于多目标优化
%
% 输入:
%   problem - 包含问题信息的结构体
%   params  - 算法参数
%
% 输出:
%   population - 最终种群
%   objectives - 最终的非支配解集目标函数值
%
% 参考文献:
% <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, A fast and elitist
% multiobjective genetic algorithm: NSGA-II, IEEE Transactions on
% Evolutionary Computation, 2002, 6(2): 182-197.

%% 初始化参数
nVar = problem.nVar;
varSize = [1, nVar];
varMin = problem.varMin;
varMax = problem.varMax;
nPop = params.nPop;
maxIt = params.maxIt;
nObj = problem.nObj;
pCrossover = params.pCrossover;
pMutation = params.pMutation;

% 确保有评价次数计数器
if ~isfield(problem, 'FE')
    problem.FE = 0;
end

%% 个体结构定义
empty_individual.Position = [];
empty_individual.Cost = [];
empty_individual.Rank = [];
empty_individual.DominationSet = [];
empty_individual.DominatedCount = [];
empty_individual.CrowdingDistance = [];

%% 初始化种群
pop = repmat(empty_individual, nPop, 1);

for i = 1:nPop
    pop(i).Position = unifrnd(varMin, varMax, varSize);
    
    % 对离散变量特殊处理
    if isfield(problem, 'discreteVars')
        for j = 1:length(problem.discreteVars)
            idx = problem.discreteVars(j).idx;
            if problem.discreteVars(j).isInteger
                pop(i).Position(idx) = round(pop(i).Position(idx));
            else
                % 找到最接近的离散值
                values = problem.discreteVars(j).values;
                [~, closest_idx] = min(abs(pop(i).Position(idx) - values));
                pop(i).Position(idx) = values(closest_idx);
            end
        end
    end
    
    pop(i).Cost = problem.costFunction(pop(i).Position);
    problem.FE = problem.FE + 1;
end

% 非支配排序
[pop, F] = NonDominatedSorting(pop);

% 计算拥挤度
pop = CalcCrowdingDistance(pop, F);

% 排序
[pop, ~] = SortPopulation(pop);

%% 优化主循环
for it = 1:maxIt
    % 交叉
    popc = repmat(empty_individual, nPop, 1);
    for i = 1:nPop/2
        % 锦标赛选择
        p1 = TournamentSelection(pop);
        p2 = TournamentSelection(pop);
        
        % 交叉
        [popc(2*i-1).Position, popc(2*i).Position] = Crossover(p1.Position, p2.Position, pCrossover, varMin, varMax);
        
        % 变异
        popc(2*i-1).Position = Mutate(popc(2*i-1).Position, pMutation, varMin, varMax);
        popc(2*i).Position = Mutate(popc(2*i).Position, pMutation, varMin, varMax);
        
        % 离散变量处理
        if isfield(problem, 'discreteVars')
            for k = [2*i-1, 2*i]
                for j = 1:length(problem.discreteVars)
                    idx = problem.discreteVars(j).idx;
                    if problem.discreteVars(j).isInteger
                        popc(k).Position(idx) = round(popc(k).Position(idx));
                    else
                        values = problem.discreteVars(j).values;
                        [~, closest_idx] = min(abs(popc(k).Position(idx) - values));
                        popc(k).Position(idx) = values(closest_idx);
                    end
                end
            end
        end
        
        % 评估
        popc(2*i-1).Cost = problem.costFunction(popc(2*i-1).Position);
        popc(2*i).Cost = problem.costFunction(popc(2*i).Position);
        problem.FE = problem.FE + 2;
    end
    
    % 合并种群
    pop = [pop; popc];
    
    % 非支配排序
    [pop, F] = NonDominatedSorting(pop);
    
    % 计算拥挤度
    pop = CalcCrowdingDistance(pop, F);
    
    % 排序
    [pop, ~] = SortPopulation(pop);
    
    % 选择
    pop = pop(1:nPop);
    
    % 显示迭代信息
    if mod(it, 10) == 0 || it == maxIt
        disp(['NSGA-II: 迭代 ' num2str(it) '/' num2str(maxIt) ', 评价次数 = ' num2str(problem.FE)]);
    end
end

%% 提取最终结果
% 获取非支配解
[~, F] = NonDominatedSorting(pop);
if ~isempty(F)
    population = pop(F{1});
    objectives = vertcat(population.Cost);
else
    population = pop;
    objectives = vertcat(pop.Cost);
end

end

%% ========== 辅助函数 ==========

function [pop, F] = NonDominatedSorting(pop)
% 非支配排序
nPop = numel(pop);

for i = 1:nPop
    pop(i).DominatedCount = 0;
    pop(i).DominationSet = [];
end

F{1} = [];

for i = 1:nPop
    for j = i+1:nPop
        p = pop(i);
        q = pop(j);
        
        if Dominates(p, q)
            p.DominationSet = [p.DominationSet j];
        elseif Dominates(q, p)
            p.DominatedCount = p.DominatedCount + 1;
        end
        
        pop(i) = p;
    end
    
    if pop(i).DominatedCount == 0
        F{1} = [F{1} i];
        pop(i).Rank = 1;
    end
end

k = 1;
while true
    Q = [];
    
    for i = F{k}
        p = pop(i);
        
        for j = p.DominationSet
            pop(j).DominatedCount = pop(j).DominatedCount - 1;

            if pop(j).DominatedCount == 0
                Q = [Q j];
                pop(j).Rank = k + 1;
            end
        end
    end
    
    if isempty(Q)
        break;
    end
    
    F{k+1} = Q;
    k = k + 1;
end
end

function pop = CalcCrowdingDistance(pop, F)
% 计算拥挤度
nF = numel(F);

for k = 1:nF
    Costs = [pop(F{k}).Cost];
    
    nObj = size(Costs, 1);
    n = numel(F{k});
    
    d = zeros(n, nObj);
    
    for j = 1:nObj
        [cj, so] = sort(Costs(j, :));
        
        d(so(1), j) = inf;
        
        for i = 2:n-1
            d(so(i), j) = abs(cj(i+1) - cj(i-1)) / abs(cj(end) - cj(1));
        end
        
        d(so(end), j) = inf;
    end
    
    for i = 1:n
        pop(F{k}(i)).CrowdingDistance = sum(d(i, :));
    end
end
end

function [pop, so] = SortPopulation(pop)
% 排序种群
[~, so] = sort([pop.Rank]);
pop = pop(so);

% 在同一等级内按拥挤度排序
Ranks = [pop.Rank];
for r = min(Ranks):max(Ranks)
    Inds = find(Ranks == r);
    [~, so] = sort([pop(Inds).CrowdingDistance], 'descend');
    pop(Inds) = pop(Inds(so));
end
end

function i = TournamentSelection(pop)
% 锦标赛选择
nPop = numel(pop);
a = randi([1 nPop]);
b = randi([1 nPop]);

if pop(a).Rank < pop(b).Rank
    i = a;
elseif pop(a).Rank > pop(b).Rank
    i = b;
else
    if pop(a).CrowdingDistance > pop(b).CrowdingDistance
        i = a;
    else
        i = b;
    end
end
end

function [y1, y2] = Crossover(x1, x2, pCrossover, varMin, varMax)
% SBX交叉
alpha = rand(size(x1));
y1 = alpha .* x1 + (1 - alpha) .* x2;
y2 = alpha .* x2 + (1 - alpha) .* x1;

% 边界处理
y1 = max(y1, varMin);
y1 = min(y1, varMax);
y2 = max(y2, varMin);
y2 = min(y2, varMax);
end

function y = Mutate(x, pMutation, varMin, varMax)
% 多项式变异
y = x;
flag = rand(size(x)) <= pMutation;
y(flag) = x(flag) + 0.1 * (varMax - varMin) .* randn(size(x(flag)));

% 边界处理
y = max(y, varMin);
y = min(y, varMax);
end

function b = Dominates(p, q)
% 判断p是否支配q
b = all(p.Cost <= q.Cost) && any(p.Cost < q.Cost);
end
