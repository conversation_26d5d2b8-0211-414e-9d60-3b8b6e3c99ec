function [x1, x2] = CalculateParallelShiftCoefficients(z1, z2, m, alpha_deg, beta_deg, target_center_distance)
% CalculateParallelShiftCoefficients - 计算平行轴齿轮对的最佳变位系数
%
% 输入参数:
%   z1 - 小齿轮齿数
%   z2 - 大齿轮齿数
%   m - 模数 (mm)
%   alpha_deg - 压力角 (度)
%   beta_deg - 螺旋角 (度)
%   target_center_distance - 目标中心距 (mm)
%
% 输出参数:
%   x1 - 小齿轮变位系数
%   x2 - 大齿轮变位系数
%
% 此函数根据指定参数和目标中心距计算平行轴齿轮系统中齿轮对的最佳变位系数。
% 它分配总变位系数以平衡齿轮的滑动率。

% 将角度从度转换为弧度
alpha = alpha_deg * pi / 180;
beta = beta_deg * pi / 180;

% 1. 计算标准中心距 (a)
a_standard = m * (z1 + z2) / (2 * cos(beta));

% 2. 计算中心距系数
center_distance_factor = target_center_distance / a_standard;

% 3. 计算工作压力角 (alpha')
inv_alpha = tan(alpha) - alpha; % alpha的渐开线函数
cos_alpha_w = a_standard * cos(alpha) / target_center_distance;

% 将cos_alpha_w限制在有效范围内
cos_alpha_w = min(max(cos_alpha_w, -1), 1);
alpha_w = acos(cos_alpha_w);
inv_alpha_w = tan(alpha_w) - alpha_w;

% 4. 计算总变位系数
sum_x = (z1 + z2) * (inv_alpha_w - inv_alpha) / (2 * tan(alpha));

% 5. 检查sum_x是否在有效范围内 (0-1.0)
min_sum_x = 0.0;  % 总变位系数最小值
max_sum_x = 1.0;  % 总变位系数最大值

% 如果超出范围则调整sum_x
if sum_x < min_sum_x
    sum_x = min_sum_x;
elseif sum_x > max_sum_x
    sum_x = max_sum_x;
end

% 6. 计算传动比
u = z2 / z1;

% 7. 使用图片中的公式分配变位系数，以平衡滑动率
% 均衡滑动率的变位分配: x₁ = x_Σ/2 + ((z₂ - z₁)/(2(z₁ + z₂))) · x_Σ
x1 = sum_x/2 + (z2 - z1)/(2*(z1 + z2)) * sum_x;
x2 = sum_x - x1;

% 8. 检查防止根切的最小变位系数
% 计算避免根切所需的最小齿数
min_teeth_no_undercut = 2 / (sin(alpha)^2);

% 计算防止根切的最小变位系数
x1_min_undercut = (min_teeth_no_undercut - z1) / 2;
x2_min_undercut = (min_teeth_no_undercut - z2) / 2;

% 确保x1和x2不小于最小值
if x1 < x1_min_undercut
    x1 = x1_min_undercut;
    x2 = sum_x - x1;
end

if x2 < x2_min_undercut
    x2 = x2_min_undercut;
    x1 = sum_x - x2;
end

% 9. 检查齿顶厚度
% 齿顶厚度检查的常数
ha_star = 1.0; % 标准齿顶高系数
sa_min = 0.4;  % 最小齿顶厚度系数 (0.4*m)

% 计算确保足够齿顶厚度的最大变位系数
x1_max_tip = z1 * (0.5 + sa_min/m - ha_star/z1) - 0.5;
x2_max_tip = z2 * (0.5 + sa_min/m - ha_star/z2) - 0.5;

% 确保x1和x2不大于最大值
if x1 > x1_max_tip
    x1 = x1_max_tip;
    x2 = sum_x - x1;
end

if x2 > x2_max_tip
    x2 = x2_max_tip;
    x1 = sum_x - x2;
end

% 10. 确保x1和x2的和等于所需总和（可能由于约束条件而调整）
actual_sum = x1 + x2;
if abs(actual_sum - sum_x) > 1e-6
    % 如果约束导致总和变化，则按比例重新分配
    factor = sum_x / actual_sum;
    x1 = x1 * factor;
    x2 = x2 * factor;
end

% 11. 确保外啮合齿轮的变位系数为正值
x1 = max(0, x1);  % 最小为0的变位系数
x2 = max(0, x2);  % 最小为0的变位系数

% 12. 最终检查总变位系数约束
sum_x_final = x1 + x2;
if sum_x_final < min_sum_x
    % 按比例增加
    factor = min_sum_x / sum_x_final;
    x1 = x1 * factor;
    x2 = x2 * factor;
elseif sum_x_final > max_sum_x
    % 按比例减少
    factor = max_sum_x / sum_x_final;
    x1 = x1 * factor;
    x2 = x2 * factor;
end

end
