function [population, objectives] = RunMOEAD(problem, params)
% RunMOEAD - 运行MOEA/D算法用于多目标优化
%
% 输入:
%   problem - 包含问题信息的结构体
%   params  - 算法参数
%
% 输出:
%   population - 最终种群
%   objectives - 最终的非支配解集目标函数值
%
% 参考文献:
% <PERSON><PERSON> and <PERSON><PERSON>, MOEA/D: A multiobjective evolutionary algorithm based
% on decomposition, IEEE Transactions on Evolutionary Computation, 2007,
% 11(6): 712-731.

%% 初始化参数
nVar = problem.nVar;
varSize = [1, nVar];
varMin = problem.varMin;
varMax = problem.varMax;
nPop = params.nPop;
maxIt = params.maxIt;
nObj = problem.nObj;
pCrossover = params.pCrossover;
pMutation = params.pMutation;

% MOEA/D特有参数
T = 20;  % 邻域大小
nr = 2;  % 最大替换数量
delta = 0.9;  % 选择概率

% 确保有评价次数计数器
if ~isfield(problem, 'FE')
    problem.FE = 0;
end

%% 个体结构定义
empty_individual.Position = [];
empty_individual.Cost = [];

%% 生成权重向量
lambda = GenerateWeights(nPop, nObj);

%% 计算邻域
B = zeros(nPop, T);
for i = 1:nPop
    distances = sum((lambda - repmat(lambda(i, :), nPop, 1)).^2, 2);
    [~, indices] = sort(distances);
    B(i, :) = indices(1:T)';
end

%% 初始化种群
pop = repmat(empty_individual, nPop, 1);

for i = 1:nPop
    pop(i).Position = unifrnd(varMin, varMax, varSize);
    
    % 对离散变量特殊处理
    if isfield(problem, 'discreteVars')
        for j = 1:length(problem.discreteVars)
            idx = problem.discreteVars(j).idx;
            if problem.discreteVars(j).isInteger
                pop(i).Position(idx) = round(pop(i).Position(idx));
            else
                % 找到最接近的离散值
                values = problem.discreteVars(j).values;
                [~, closest_idx] = min(abs(pop(i).Position(idx) - values));
                pop(i).Position(idx) = values(closest_idx);
            end
        end
    end
    
    pop(i).Cost = problem.costFunction(pop(i).Position);
    problem.FE = problem.FE + 1;
end

%% 初始化理想点
z = min(vertcat(pop.Cost), [], 1);

%% 优化主循环
for it = 1:maxIt
    for i = 1:nPop
        % 选择父代
        if rand < delta
            % 从邻域中选择
            P = B(i, :);
        else
            % 从整个种群中选择
            P = 1:nPop;
        end
        
        % 随机选择两个父代
        parents = P(randperm(length(P), 2));
        p1 = parents(1);
        p2 = parents(2);
        
        % 交叉
        [y1, ~] = Crossover(pop(p1).Position, pop(p2).Position, pCrossover, varMin, varMax);
        
        % 变异
        y1 = Mutate(y1, pMutation, varMin, varMax);
        
        % 离散变量处理
        if isfield(problem, 'discreteVars')
            for j = 1:length(problem.discreteVars)
                idx = problem.discreteVars(j).idx;
                if problem.discreteVars(j).isInteger
                    y1(idx) = round(y1(idx));
                else
                    values = problem.discreteVars(j).values;
                    [~, closest_idx] = min(abs(y1(idx) - values));
                    y1(idx) = values(closest_idx);
                end
            end
        end
        
        % 评估
        y_cost = problem.costFunction(y1);
        problem.FE = problem.FE + 1;
        
        % 更新理想点
        z = min(z, y_cost);
        
        % 更新邻域解
        c = 0;
        for j = randperm(T)
            if c >= nr
                break;
            end
            
            k = B(i, j);
            
            % 计算聚合函数值
            g_old = max(lambda(k, :) .* abs(pop(k).Cost - z));
            g_new = max(lambda(k, :) .* abs(y_cost - z));
            
            if g_new <= g_old
                pop(k).Position = y1;
                pop(k).Cost = y_cost;
                c = c + 1;
            end
        end
    end
    
    % 显示迭代信息
    if mod(it, 10) == 0 || it == maxIt
        disp(['MOEA/D: 迭代 ' num2str(it) '/' num2str(maxIt) ', 评价次数 = ' num2str(problem.FE)]);
    end
end

%% 提取最终结果
% 获取非支配解
[pop_nd, ~] = NonDominatedSorting(pop);
if ~isempty(pop_nd)
    % 将结构体数组转换为数值矩阵
    population = vertcat(pop_nd.Position);
    objectives = vertcat(pop_nd.Cost);
else
    population = [];
    objectives = [];
end

end

%% ========== 辅助函数 ==========

function lambda = GenerateWeights(N, M)
% 生成权重向量
if M == 2
    lambda = zeros(N, M);
    for i = 1:N
        lambda(i, 1) = (i-1) / (N-1);
        lambda(i, 2) = 1 - lambda(i, 1);
    end
elseif M == 3
    % 对于3目标，使用Das and Dennis方法
    H = floor((N^(1/M) - 1) * M);
    lambda = [];
    for i = 0:H
        for j = 0:H-i
            k = H - i - j;
            lambda = [lambda; i/H, j/H, k/H];
        end
    end
    
    % 如果生成的权重向量数量不够，随机生成补充
    if size(lambda, 1) < N
        additional = N - size(lambda, 1);
        rand_weights = rand(additional, M);
        rand_weights = rand_weights ./ repmat(sum(rand_weights, 2), 1, M);
        lambda = [lambda; rand_weights];
    end
    
    % 如果生成的权重向量数量过多，随机选择N个
    if size(lambda, 1) > N
        indices = randperm(size(lambda, 1), N);
        lambda = lambda(indices, :);
    end
else
    % 对于更高维度，使用随机权重
    lambda = rand(N, M);
    lambda = lambda ./ repmat(sum(lambda, 2), 1, M);
end
end

function [pop_nd, F] = NonDominatedSorting(pop)
% 非支配排序
nPop = numel(pop);

for i = 1:nPop
    pop(i).DominatedCount = 0;
    pop(i).DominationSet = [];
end

F{1} = [];

for i = 1:nPop
    for j = i+1:nPop
        p = pop(i);
        q = pop(j);
        
        if Dominates(p, q)
            p.DominationSet = [p.DominationSet j];
        elseif Dominates(q, p)
            p.DominatedCount = p.DominatedCount + 1;
        end
        
        pop(i) = p;
    end
    
    if pop(i).DominatedCount == 0
        F{1} = [F{1} i];
        pop(i).Rank = 1;
    end
end

% 返回第一前沿的解
pop_nd = pop(F{1});
end

function [y1, y2] = Crossover(x1, x2, pCrossover, varMin, varMax)
% SBX交叉
if rand <= pCrossover
    alpha = rand(size(x1));
    y1 = alpha .* x1 + (1 - alpha) .* x2;
    y2 = alpha .* x2 + (1 - alpha) .* x1;
else
    y1 = x1;
    y2 = x2;
end

% 边界处理
y1 = max(y1, varMin);
y1 = min(y1, varMax);
y2 = max(y2, varMin);
y2 = min(y2, varMax);
end

function y = Mutate(x, pMutation, varMin, varMax)
% 多项式变异
y = x;
flag = rand(size(x)) <= pMutation;
if any(flag)
    % 确保维度匹配
    delta_range = (varMax - varMin);
    if length(delta_range) == length(x)
        y(flag) = x(flag) + 0.1 * delta_range(flag) .* randn(sum(flag), 1);
    else
        % 如果维度不匹配，使用标量变异
        y(flag) = x(flag) + 0.1 * randn(sum(flag), 1);
    end
end

% 边界处理
y = max(y, varMin);
y = min(y, varMax);
end

function b = Dominates(p, q)
% 判断p是否支配q
b = all(p.Cost <= q.Cost) && any(p.Cost < q.Cost);
end
