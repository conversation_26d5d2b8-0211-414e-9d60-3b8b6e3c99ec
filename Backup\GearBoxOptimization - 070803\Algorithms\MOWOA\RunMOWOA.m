function [pop, F] = RunMOWOA(problem, params)
% RunMOWOA - 运行改进的多目标鲸鱼优化算法 (基于NSWOA)
%
% 输入:
%   problem - 包含问题信息的结构体
%   params  - 算法参数
%
% 输出:
%   pop - 最终种群
%   F   - 最终的非支配解集目标函数值
%
% 参考自NSWOA (Non-dominated Sorting Whale Optimization Algorithm)
% 改进了标准WOA，融合了寄生向量策略和改进的种群交替进化机制

%% 初始化参数
nVar = problem.nVar;
varSize = problem.varSize;
varMin = problem.varMin;
varMax = problem.varMax;
nPop = params.nPop;
maxIt = params.maxIt;
nObj = problem.nObj;

% 确保有评价次数计数器
if ~isfield(problem, 'FE')
    problem.FE = 0;
end

% MOWOA特有参数
b = 1;      % 螺旋形状参数
SF = 2;     % 缩放因子，用于更好地覆盖搜索空间

fprintf('开始运行MOWOA算法...\n');

%% 初始化种群
empty_whale.Position = [];
empty_whale.Cost = [];
empty_whale.Rank = [];
empty_whale.CrowdingDistance = [];
empty_whale.DominationSet = [];
empty_whale.DominatedCount = [];

% 初始化鲸鱼种群
whales = repmat(empty_whale, nPop, 1);
whales_advanced = repmat(empty_whale, nPop, 1); % 高级种群(交替进化用)

% 随机生成初始鲸鱼位置
for i = 1:nPop
    whales(i).Position = unifrnd(varMin, varMax, varSize);
    
    % 对离散变量特殊处理
    if isfield(problem, 'discreteVars')
        for j = 1:length(problem.discreteVars)
            idx = problem.discreteVars(j).idx;
            if problem.discreteVars(j).isInteger
                whales(i).Position(idx) = round(whales(i).Position(idx));
            else
                % 找到最接近的离散值
                values = problem.discreteVars(j).values;
                [~, closest_idx] = min(abs(whales(i).Position(idx) - values));
                whales(i).Position(idx) = values(closest_idx);
            end
        end
    end
    
    whales(i).Cost = problem.costFunction(whales(i).Position);
    problem.FE = problem.FE + 1;
end

% 执行非支配排序
[whales, fronts] = NonDominatedSorting(whales);

% 计算拥挤度
whales = CrowdingDistanceCalculation(whales, fronts);

%% 主循环
for it = 1:maxIt
    % 更新搜索参数a(线性下降从2到0)
    a = 2 - it*(2/maxIt);
    a2 = -1 + it*((-1)/maxIt); % 用于螺旋更新的参数
    
    % 修改：限制每次迭代的函数评价次数
    evaluations_this_iteration = 0;
    max_evaluations_per_iteration = nPop; % 每次迭代最多评价nPop个解
    
    % 第一阶段：交替进化
    for i = 1:nPop
        % 如果达到本次迭代的评价上限，则跳出循环
        if evaluations_this_iteration >= max_evaluations_per_iteration
            break;
        end
        
        % 随机选择另一个鲸鱼
        if nPop <= 1
            % 如果种群只有一个或没有元素，使用当前索引
            j = i;
        else
            % 随机选择一个不同的鲸鱼
            j = randi(nPop);
            % 尝试一定次数找到一个不同的索引
            max_tries = 5;
            try_count = 0;
            while j == i && try_count < max_tries
                j = randi(nPop);
                try_count = try_count + 1;
            end
        end
        
        % 从第一前沿随机选择一个领导者
        first_front_indices = find([whales.Rank] == 1);
        if ~isempty(first_front_indices)
            leader_idx = first_front_indices(randi(length(first_front_indices)));
            leader = whales(leader_idx);
        else
            % 防止索引错误
            if nPop == 0
                % 如果种群为空，创建一个默认的领导者
                leader = empty_whale;
                leader.Position = (varMin + varMax) / 2;  % 使用设计空间的中点
                leader.Cost = problem.costFunction(leader.Position);
                problem.FE = problem.FE + 1;
                evaluations_this_iteration = evaluations_this_iteration + 1;
                
                % 如果种群为空，需要初始化种群
                if isempty(whales)
                    whales = repmat(empty_whale, nPop, 1);
                    for k = 1:nPop
                        if evaluations_this_iteration >= max_evaluations_per_iteration
                            break;
                        end
                        whales(k).Position = unifrnd(varMin, varMax, varSize);
                        whales(k).Cost = problem.costFunction(whales(k).Position);
                        problem.FE = problem.FE + 1;
                        evaluations_this_iteration = evaluations_this_iteration + 1;
                    end
                end
            elseif nPop == 1
                % 如果只有一个元素，直接使用它
                leader = whales(1);
            else
                % 正常情况：随机选择
                leader = whales(randi(nPop));
            end
        end
        
        % 如果达到本次迭代的评价上限，则跳出循环
        if evaluations_this_iteration >= max_evaluations_per_iteration
            break;
        end
        
        % 第一种变异策略 - 基于领导者的直接进化
        trial_whale1 = empty_whale;
        trial_whale1.Position = whales(i).Position + rand(varSize).*(leader.Position - SF.*whales(i).Position);
        
        % 对离散变量特殊处理
        if isfield(problem, 'discreteVars')
            for j = 1:length(problem.discreteVars)
                idx = problem.discreteVars(j).idx;
                if problem.discreteVars(j).isInteger
                    trial_whale1.Position(idx) = round(trial_whale1.Position(idx));
                else
                    % 找到最接近的离散值
                    values = problem.discreteVars(j).values;
                    [~, closest_idx] = min(abs(trial_whale1.Position(idx) - values));
                    trial_whale1.Position(idx) = values(closest_idx);
                end
            end
        end
        
        % 边界处理
        trial_whale1.Position = max(trial_whale1.Position, varMin);
        trial_whale1.Position = min(trial_whale1.Position, varMax);
        
        % 评估新位置
        trial_whale1.Cost = problem.costFunction(trial_whale1.Position);
        problem.FE = problem.FE + 1;
        evaluations_this_iteration = evaluations_this_iteration + 1;
        
        % 支配检查
        if dominates(trial_whale1.Cost, whales(i).Cost)
            % 如果新位置支配当前位置
            whales_advanced(i) = whales(i); % 将当前鲸鱼添加到高级种群
            whales(i) = trial_whale1; % 用新位置替换当前鲸鱼
        else
            % 否则将新位置添加到高级种群
            whales_advanced(i) = trial_whale1;
        end
        
        % 如果达到本次迭代的评价上限，则跳出循环
        if evaluations_this_iteration >= max_evaluations_per_iteration
            break;
        end
        
        % 第二种变异策略 - 基于标准WOA的行为
        r1 = rand();
        r2 = rand();
        A = 2*a*r1 - a;
        C = 2*r2;
        p = rand();
        
        trial_whale2 = empty_whale;
        
        if p < 0.5
            % 包围猎物或搜索猎物
            if abs(A) >= 1
                % 搜索猎物（全局搜索）
                trial_whale2.Position = whales(i).Position + leader.Position - A.*abs(C*leader.Position - whales(j).Position);
            else
                % 包围猎物（局部搜索）
                trial_whale2.Position = leader.Position - A.*abs(C*leader.Position - whales(i).Position);
            end
        else
            % 气泡网攻击（螺旋更新）
            t = (a2-1)*rand() + 1;
            trial_whale2.Position = abs(leader.Position - whales(j).Position)*exp(b*t)*cos(t*2*pi) + leader.Position;
        end
        
        % 对离散变量特殊处理
        if isfield(problem, 'discreteVars')
            for j = 1:length(problem.discreteVars)
                idx = problem.discreteVars(j).idx;
                if problem.discreteVars(j).isInteger
                    trial_whale2.Position(idx) = round(trial_whale2.Position(idx));
                else
                    % 找到最接近的离散值
                    values = problem.discreteVars(j).values;
                    [~, closest_idx] = min(abs(trial_whale2.Position(idx) - values));
                    trial_whale2.Position(idx) = values(closest_idx);
                end
            end
        end
        
        % 边界处理
        trial_whale2.Position = max(trial_whale2.Position, varMin);
        trial_whale2.Position = min(trial_whale2.Position, varMax);
        
        % 评估新位置
        trial_whale2.Cost = problem.costFunction(trial_whale2.Position);
        problem.FE = problem.FE + 1;
        evaluations_this_iteration = evaluations_this_iteration + 1;
        
        % 支配检查
        if dominates(trial_whale2.Cost, whales(i).Cost)
            % 如果新位置支配当前位置
            whales(i) = trial_whale2;
        elseif ~dominates(whales(i).Cost, trial_whale2.Cost)
            % 如果互不支配，随机选择
            if rand() < 0.5
                whales(i) = trial_whale2;
            end
        end
    end
    
    % 第二阶段：合并两个种群并选择下一代
    combined_whales = [whales; whales_advanced];
    
    % 对合并种群进行非支配排序
    [combined_whales, fronts] = NonDominatedSorting(combined_whales);
    
    % 计算拥挤度
    combined_whales = CrowdingDistanceCalculation(combined_whales, fronts);
    
    % 使用NSGA-II选择机制选择下一代
    whales = SelectionNSGAII(combined_whales, nPop);
    
    % 显示迭代信息
    if mod(it, 10) == 0 || it == maxIt
        % 计算非支配解数量
        [~, fronts] = NonDominatedSorting(whales);
        if ~isempty(fronts) && ~isempty(fronts{1})
            n_nondom = length(fronts{1});
        else
            n_nondom = 0;
        end
        disp(['迭代 ' num2str(it) '/' num2str(maxIt) ', 非支配解数量 = ' num2str(n_nondom) ', 评价次数 = ' num2str(problem.FE)]);
    end
end

% 提取非支配解
[whales, fronts] = NonDominatedSorting(whales);
first_front_indices = [];
if ~isempty(fronts)
    first_front_indices = fronts{1};
end

% 如果种群为空，创建一个默认解
if isempty(first_front_indices) || isempty(whales)
    pop = repmat(empty_whale, 1, 1);
    pop(1).Position = (varMin + varMax) / 2;
    pop(1).Cost = problem.costFunction(pop(1).Position);
    problem.FE = problem.FE + 1;
    F = reshape([pop.Cost], nObj, [])';
else
    pop = whales(first_front_indices);
    F = reshape([pop.Cost], nObj, [])';
end

% 处理最大化目标
F(:, 2) = -F(:, 2);  % 取负值以便最大化弯曲安全系数
F(:, 3) = -F(:, 3);  % 取负值以便最大化接触安全系数

fprintf('MOWOA算法完成!\n');
end

%% 辅助函数

function result = dominates(x, y)
% 判断x是否支配y - 改进版，处理大小不匹配和空数组的情况
    
    % 检查输入是否为空
    if isempty(x) || isempty(y)
        result = false;
        return;
    end
    
    % 检查维度是否匹配
    if length(x) ~= length(y)
        result = false;
        return;
    end
    
    % 标准支配关系检查
    result = all(x <= y) && any(x < y);
end

function [pop, fronts] = NonDominatedSorting(pop)
% 非支配排序（Fast Non-dominated Sorting）- 改进版，处理边缘情况
n = numel(pop);

% 处理空种群
if n == 0
    fronts = {};
    return;
end

% 处理单个体种群
if n == 1
    pop(1).Rank = 1;
    pop(1).DominationSet = [];
    pop(1).DominatedCount = 0;
    fronts = {1};
    return;
end

fronts = cell(n, 1);

% 初始化
for i = 1:n
    pop(i).DominationSet = [];
    pop(i).DominatedCount = 0;
end

% 检查是否有无效的目标函数值
valid_indices = true(1, n);
for i = 1:n
    if ~isfield(pop(i), 'Cost') || isempty(pop(i).Cost) || any(isnan(pop(i).Cost)) || any(isinf(pop(i).Cost))
        valid_indices(i) = false;
        pop(i).Rank = n+1; % 将无效解排在最后
    end
end

% 如果所有解都无效，返回空前沿
if ~any(valid_indices)
    fronts = {[]};
    return;
end

% 计算支配关系
for i = 1:n
    if ~valid_indices(i)
        continue; % 跳过无效解
    end
    
    for j = 1:n
        if i == j || ~valid_indices(j)
            continue; % 跳过自身和无效解
        end
        
        try
            if dominates(pop(i).Cost, pop(j).Cost)
                % i支配j
                pop(i).DominationSet = [pop(i).DominationSet j];
                pop(j).DominatedCount = pop(j).DominatedCount + 1;
            elseif dominates(pop(j).Cost, pop(i).Cost)
                % j支配i
                pop(j).DominationSet = [pop(j).DominationSet i];
                pop(i).DominatedCount = pop(i).DominatedCount + 1;
            end
        catch
            % 如果比较失败，忽略这对解
            continue;
        end
    end
    
    % 放入第一前沿的个体
    if pop(i).DominatedCount == 0
        pop(i).Rank = 1;
        fronts{1} = [fronts{1} i];
    end
end

% 确保至少有一个解在第一前沿
if isempty(fronts{1})
    % 如果没有解在第一前沿，选择一个有效解放入第一前沿
    valid_idx = find(valid_indices, 1);
    if ~isempty(valid_idx)
        pop(valid_idx).Rank = 1;
        fronts{1} = valid_idx;
    end
end

% 生成各个前沿
front = 1;
while front <= n && ~isempty(fronts{front})
    next_front = [];
    
    for i = fronts{front}
        for j = pop(i).DominationSet
            pop(j).DominatedCount = pop(j).DominatedCount - 1;
            
            if pop(j).DominatedCount == 0
                pop(j).Rank = front + 1;
                next_front = [next_front j];
            end
        end
    end
    
    front = front + 1;
    if front <= n
        fronts{front} = next_front;
    end
end

% 移除空前沿
fronts = fronts(1:min(front-1, n));
end

function pop = CrowdingDistanceCalculation(pop, fronts)
% 计算拥挤度距离 - 改进版，增强稳定性
n = numel(pop);

% 检查边界情况
if n == 0 || isempty(fronts)
    return;
end

% 确保第一个个体有Cost字段，如果没有则直接返回
if ~isfield(pop(1), 'Cost') || isempty(pop(1).Cost)
    return;
end

nObj = numel(pop(1).Cost);

% 初始化拥挤度
for i = 1:n
    pop(i).CrowdingDistance = 0;
end

% 对每个前沿计算拥挤度
for f = 1:numel(fronts)
    front = fronts{f};
    
    % 检查front是否为空
    if isempty(front)
        continue;
    end
    
    n_front = numel(front);
    
    if n_front <= 2
        for i = front
            pop(i).CrowdingDistance = inf;
        end
        continue;
    end
    
    % 对每个目标函数计算拥挤度
    for obj = 1:nObj
        % 提取该前沿的目标值
        costs = zeros(n_front, 1);
        valid_indices = true(n_front, 1);
        
        for i = 1:n_front
            if isfield(pop(front(i)), 'Cost') && length(pop(front(i)).Cost) >= obj && ...
               ~isnan(pop(front(i)).Cost(obj)) && ~isinf(pop(front(i)).Cost(obj))
                costs(i) = pop(front(i)).Cost(obj);
            else
                valid_indices(i) = false;
            end
        end
        
        % 如果没有有效值，跳过这个目标函数
        if ~any(valid_indices)
            continue;
        end
        
        % 只对有效值进行排序
        valid_front = front(valid_indices);
        valid_costs = costs(valid_indices);
        
        % 排序
        [sorted_costs, indices] = sort(valid_costs);
        sorted_indices = valid_front(indices);
        
        % 为边界点分配无穷拥挤度
        if length(sorted_indices) >= 1
            pop(sorted_indices(1)).CrowdingDistance = inf;
        end
        if length(sorted_indices) >= 2
            pop(sorted_indices(end)).CrowdingDistance = inf;
        end
        
        % 计算中间点的拥挤度
        range = sorted_costs(end) - sorted_costs(1);
        if range > 0 && length(sorted_indices) > 2
            for i = 2:length(sorted_indices)-1
                pop(sorted_indices(i)).CrowdingDistance = pop(sorted_indices(i)).CrowdingDistance + ...
                    (sorted_costs(i+1) - sorted_costs(i-1))/range;
            end
        end
    end
end
end

function selected_pop = SelectionNSGAII(pop, n)
% NSGA-II选择机制
% 检查种群大小
if isempty(pop)
    % 如果种群为空，返回空数组
    selected_pop = pop;
    return;
end

% 如果种群大小小于需要选择的数量，直接返回整个种群
if numel(pop) <= n
    selected_pop = pop;
    return;
end

% 按Rank和拥挤度对种群排序
ranks = zeros(numel(pop), 1);
distances = zeros(numel(pop), 1);
for i = 1:numel(pop)
    if isfield(pop(i), 'Rank')
        ranks(i) = pop(i).Rank;
    else
        ranks(i) = 1; % 默认排名
    end
    
    if isfield(pop(i), 'CrowdingDistance')
        distances(i) = pop(i).CrowdingDistance;
    else
        distances(i) = 0; % 默认拥挤度
    end
end

[~, sorted_indices] = sortrows([ranks -distances]);
pop = pop(sorted_indices);

% 选择前n个个体
selected_pop = pop(1:min(n, numel(pop)));
end 