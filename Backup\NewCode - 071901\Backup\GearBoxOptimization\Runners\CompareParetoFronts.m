function CompareParetoFronts()
    % 比较多个算法的Pareto前沿

    % 设置随机数种子，保证结果可重复
    rng(42);

    % 定义保存路径
    results_dir = 'Results';
    if ~exist(results_dir, 'dir')
        mkdir(results_dir);
    end
    
    % 算法名称
    alg_names = {'NSGA-II', 'NSGA-III', 'SPEA2', 'MOEA/D', 'MOEA/D-DE', 'MOEA/D-M2M', 'MOPSO', 'MOGWO', 'MOWOA'};
    
    % 参数名称
    param_names = {'m1', 'z1', 'z2', 'k_h1', 'x1', 'x2', 'x_sum1', 'beta1', 'alpha1', 'a1', 'i1', 'SH1', 'SF1', 'SF2', 'M1', 'M2', ...
     'mn2', 'zs2', 'zp2', 'zr2', 'n2', 'k_h2', 'xs2', 'xp2', 'xr2', 'x_sum2', 'beta2', 'alpha2', 'a2', 'i2', 'SHsps2', 'SHspp2', 'SFsps2', 'SFspp2', 'SHprr2', 'SHprp2', 'SFprr2', 'SFprp2', 'Ms2', 'Mp2', 'Mr2', ...
     'mn3', 'zs3', 'zp3', 'zr3', 'n3', 'k_h3', 'xs3', 'xp3', 'xr3', 'x_sum3', 'beta3', 'alpha3', 'a3', 'i3', 'SHsps3', 'SHspp3', 'SFsps3', 'SFspp3', 'SHprr3', 'SHprp3', 'SFprr3', 'SFprp3', 'Ms3', 'Mp3', 'Mr3', ...
     'TotalMass', 'SH', 'SF', 'TotalRatio', 'Error'};

    % 加载所有算法的结果
    fprintf('正在加载所有算法的优化结果...\n');
    all_results = cell(1, length(alg_names));
    all_variables = cell(1, length(alg_names));
    
    % 计数有效算法
    valid_alg_count = 0;
    
    % 逐个加载结果
    for i = 1:length(alg_names)
        % 处理文件名中的特殊字符
        alg_name_file = strrep(alg_names{i}, '-', '_');
        alg_name_file = strrep(alg_name_file, '/', '_');
        
        file_path = fullfile(results_dir, ['pareto_solutions_', alg_name_file, '.mat']);
        
        if exist(file_path, 'file')
            data = load(file_path);
            if isfield(data, 'pareto_solutions')
                all_results{i} = data.pareto_solutions;
                all_variables{i} = data.pareto_variables;
                valid_alg_count = valid_alg_count + 1;
            else
                fprintf('警告：%s 中没有找到 pareto_solutions 字段\n', file_path);
                all_results{i} = [];
                all_variables{i} = [];
            end
        else
            fprintf('警告：找不到文件 %s\n', file_path);
            all_results{i} = [];
            all_variables{i} = [];
        end
    end
    
    % 检查是否至少有一个有效的算法结果
    if valid_alg_count == 0
        error('没有找到任何有效的算法结果文件，请先运行优化算法');
    end
    
    % 评估算法性能
    fprintf('正在计算算法性能指标...\n');
    metrics = EvaluateAlgorithms(all_results, alg_names);
    
    % 保存评价指标
    save(fullfile(results_dir, 'metrics_results_run1.mat'), 'metrics');
    
    % 绘制Pareto前沿
    fprintf('正在绘制Pareto前沿...\n');
    pareto_fig = PlotParetoFronts();
    
    % 创建总结表格
    fprintf('正在创建结果总结表格...\n');
    % 使用默认的输入输出转速
    default_input_speed = 1490;
    default_output_speed = 18.63;
    % 创建一个空的problem结构体
    empty_problem = struct('isMultipleFirstStage', false);
    default_input_torque = 7500; % 默认输入扭矩
    CreateSummaryTables(alg_names, all_variables, all_results, {metrics}, param_names, results_dir, default_input_speed, default_output_speed, empty_problem, default_input_torque);
    
    % 绘制五个关键指标的雷达图
    fprintf('正在绘制算法性能雷达图...\n');
    radar_path = fullfile(results_dir, '算法性能雷达图.png');
    PlotRadarMetrics(metrics, alg_names, radar_path);
    
    % 显示结果
    fprintf('所有分析完成! 结果已保存到 %s 目录\n', results_dir);
    fprintf('===================================\n');
    fprintf('生成的文件:\n');
    fprintf('1. Pareto前沿图组合.png - 三维Pareto前沿和投影图\n');
    fprintf('2. 算法性能雷达图.png - 五个关键指标的雷达图对比\n');
    fprintf('3. 算法最优解综合表.xlsx - 每个算法的最优设计方案\n');
    fprintf('4. 算法性能指标综合表.xlsx - 各算法的性能指标对比\n');
    fprintf('5. 优化结果综合报告.html - 交互式HTML报告\n');
    fprintf('===================================\n');
end 