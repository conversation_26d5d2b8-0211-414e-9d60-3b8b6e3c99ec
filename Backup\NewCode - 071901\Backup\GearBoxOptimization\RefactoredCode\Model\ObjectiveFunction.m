function [objectives, constraints, gear_system] = ObjectiveFunction(x, system_params)
% ObjectiveFunction 优化目标函数
% 按照原有代码流程：固定一级参数，优化二三级行星轮系
%
% 输入:
%   x - 优化变量向量（19个变量：第1个为一级参数组索引，其余18个为二三级参数）
%   system_params - 系统参数结构体
%
% 输出:
%   objectives - 目标函数值 [总质量, -最小弯曲安全系数, -最小接触安全系数]
%   constraints - 约束违反值
%   gear_system - 完整的齿轮系统参数（可选输出）

%% 1. 参数验证
% 验证优化变量维度（19个变量）
if length(x) ~= 19
    error('优化变量维度不匹配。期望 19 个变量，实际 %d 个', length(x));
end

%% 2. 调用齿轮系统计算器
try
    [gear_system, objectives, constraints] = GearSystemCalculator(x, system_params);
    
    % 验证目标函数格式
    if length(objectives) ~= 3
        error('目标函数返回值格式错误：期望3个目标，实际得到%d个', length(objectives));
    end

    % 确保约束值为标量
    if length(constraints) > 1
        constraints = max(constraints);  % 取最大约束违反值
    end

catch e
    fprintf('优化目标函数计算出错: %s\n', e.message);
    rethrow(e);
end

%% 3. 结果验证
if any(isnan(objectives)) || any(isinf(objectives))
    error('目标函数计算结果包含NaN或Inf值');
end

if isnan(constraints) || constraints < 0
    error('约束值无效：NaN或负值');
end

end
