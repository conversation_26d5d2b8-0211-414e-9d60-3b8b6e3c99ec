function [population, objectives] = RunNSGAIII(problem, params)
% RunNSGAIII - 运行NSGA-III算法用于多目标优化
%
% 输入:
%   problem - 包含问题信息的结构体
%   params  - 算法参数
%
% 输出:
%   population - 最终种群
%   objectives - 最终的非支配解集目标函数值
%
% 参考文献:
% <PERSON><PERSON> and <PERSON><PERSON>, An evolutionary many-objective optimization algorithm
% using reference-point based non-dominated sorting approach, part I:
% Solving problems with box constraints, IEEE Transactions on Evolutionary
% Computation, 2014, 18(4): 577-601.

%% NSGA-III Parameters
nVar = problem.nVar;
varSize = problem.varSize;
varMin = problem.varMin;
varMax = problem.varMax;
nPop = params.nPop;
maxIt = params.maxIt;
nObj = problem.nObj;
pCrossover = params.pCrossover;
pMutation = params.pMutation;
nDivision = 12;  % 参考点划分数

% 确保有评价次数计数器
if ~isfield(problem, 'FE')
    problem.FE = 0;
end

% 生成参考点
Zr = GenerateReferencePoints(nObj, nDivision);
nZr = size(Zr, 2);

% 初始化算法参数
params.Zr = Zr;
params.nZr = nZr;
params.zmin = [];
params.zmax = [];

%% 初始化种群
empty_individual.Position = [];
empty_individual.Cost = [];
empty_individual.Rank = [];
empty_individual.DominationSet = [];
empty_individual.DominatedCount = [];
empty_individual.NormalizedCost = [];
empty_individual.AssociatedRef = [];
empty_individual.DistanceToAssociatedRef = [];

pop = repmat(empty_individual, nPop, 1);

for i = 1:nPop
    % 生成随机解
    pop(i).Position = unifrnd(varMin, varMax, varSize);
    
    % 对离散变量特殊处理
    if isfield(problem, 'discreteVars')
        for j = 1:length(problem.discreteVars)
            idx = problem.discreteVars(j).idx;
            if problem.discreteVars(j).isInteger
                pop(i).Position(idx) = round(pop(i).Position(idx));
            else
                % 找到最接近的离散值
                values = problem.discreteVars(j).values;
                [~, closest_idx] = min(abs(pop(i).Position(idx) - values));
                pop(i).Position(idx) = values(closest_idx);
            end
        end
    end
    
    % 评估目标函数
    pop(i).Cost = problem.costFunction(pop(i).Position);
    problem.FE = problem.FE + 1;
end

% 排序和选择种群
[pop, F, params] = SortAndSelectPopulation(pop, params);

%% 优化主循环
for it = 1:maxIt
    % 交叉
    popc = repmat(empty_individual, nPop, 1);
    for i = 1:nPop/2
        % 锦标赛选择
        p1 = TournamentSelection(pop);
        p2 = TournamentSelection(pop);
        
        % 交叉
        [popc(2*i-1).Position, popc(2*i).Position] = Crossover(p1.Position, p2.Position, pCrossover, varMin, varMax);
        
        % 变异
        popc(2*i-1).Position = Mutate(popc(2*i-1).Position, pMutation, varMin, varMax);
        popc(2*i).Position = Mutate(popc(2*i).Position, pMutation, varMin, varMax);
        
        % 对离散变量特殊处理
        if isfield(problem, 'discreteVars')
            for k = [2*i-1, 2*i]
                for j = 1:length(problem.discreteVars)
                    idx = problem.discreteVars(j).idx;
                    if problem.discreteVars(j).isInteger
                        popc(k).Position(idx) = round(popc(k).Position(idx));
                    else
                        % 找到最接近的离散值
                        values = problem.discreteVars(j).values;
                        [~, closest_idx] = min(abs(popc(k).Position(idx) - values));
                        popc(k).Position(idx) = values(closest_idx);
                    end
                end
            end
        end
        
        % 评估子代
        popc(2*i-1).Cost = problem.costFunction(popc(2*i-1).Position);
        popc(2*i).Cost = problem.costFunction(popc(2*i).Position);
        problem.FE = problem.FE + 2;
    end
    
    % 合并父代和子代
    pop = [pop; popc];
    
    % 排序和选择种群
    [pop, F, params] = SortAndSelectPopulation(pop, params);
    
    % 显示迭代信息
    if mod(it, 10) == 0 || it == maxIt
        % 计算非支配解数量
        n_nondom = numel(F{1});
        disp(['迭代 ' num2str(it) '/' num2str(maxIt) ', 非支配解数量 = ' num2str(n_nondom) ', 评价次数 = ' num2str(problem.FE)]);
    end
end

% 再次对最终种群进行非支配排序
[pop, F] = NonDominatedSorting(pop);

% 提取非支配解集
% 确保F{1}中的索引不超出pop的范围
valid_indices = F{1}(F{1} <= length(pop));
if isempty(valid_indices)
    % 如果没有有效的非支配解，使用整个种群
    nonDominated = pop;
else
    nonDominated = pop(valid_indices);
end

% 返回最终结果
n_pareto = numel(nonDominated);
population = zeros(n_pareto, nVar);
objectives = zeros(n_pareto, nObj);

for i = 1:n_pareto
    population(i, :) = nonDominated(i).Position;
    objectives(i, :) = nonDominated(i).Cost;
end

% 处理最大化目标
objectives(:, 2) = -objectives(:, 2);  % 取负值以便最大化弯曲安全系数
objectives(:, 3) = -objectives(:, 3);  % 取负值以便最大化接触安全系数

fprintf('NSGA-III算法完成!\n');
end

%% 辅助函数
function Zr = GenerateReferencePoints(M, p)
% 生成参考点

    if M == 1
        Zr = 1;
    elseif M == 2
        Zr = (0:p)'/p;
        Zr = [Zr, 1-Zr];
    else
        Zr = [];
        for i = 0:p
            Zr1 = GenerateReferencePoints(M-1, p-i);
            if size(Zr1, 2) == M-1  % 确保维度匹配
                new_points = [i/p*ones(size(Zr1, 1), 1), Zr1*(1-i/p)];
                Zr = [Zr; new_points];
            else
                % 如果维度不匹配，调整维度
                new_points = [i/p*ones(size(Zr1, 1), 1), Zr1(:, 1:M-2)*(1-i/p)];
                if size(new_points, 2) == M
                    Zr = [Zr; new_points];
                end
            end
        end
    end
    
    % 确保返回正确的维度
    if ~isempty(Zr)
        if size(Zr, 2) ~= M
            % 如果列数不等于M，填充或裁剪
            if size(Zr, 2) < M
                Zr = [Zr, zeros(size(Zr, 1), M-size(Zr, 2))];
            else
                Zr = Zr(:, 1:M);
            end
        end
    else
        % 如果生成的参考点为空，创建一个默认点
        Zr = ones(1, M) / M;
    end
    
    % 转置以匹配预期的格式
    Zr = Zr';
end

function [pop, params] = NormalizePopulation(pop, params)
% 归一化种群

    % 提取目标函数值
    costs = reshape([pop.Cost], [], length(pop))';
    
    % 更新理想点
    if isempty(params.zmin)
        params.zmin = min(costs, [], 1);
    else
        params.zmin = min([params.zmin; costs], [], 1);
    end
    
    % 更新极点
    if isempty(params.zmax)
        params.zmax = max(costs, [], 1);
    else
        params.zmax = max([params.zmax; costs], [], 1);
    end
    
    % 归一化
    for i = 1:numel(pop)
        if any(isnan(pop(i).Cost) | isinf(pop(i).Cost))
            pop(i).NormalizedCost = pop(i).Cost;
        else
            pop(i).NormalizedCost = (pop(i).Cost - params.zmin) ./ (params.zmax - params.zmin + eps);
        end
    end
end

function [pop, F] = NonDominatedSorting(pop)
% 非支配排序

    nPop = numel(pop);
    
    % 初始化
    for i = 1:nPop
        pop(i).DominationSet = [];
        pop(i).DominatedCount = 0;
    end
    
    F{1} = [];
    
    % 计算支配关系
    for i = 1:nPop
        for j = i+1:nPop
            p = pop(i);
            q = pop(j);
            
            % 检查目标函数值是否有效
            p_valid = ~any(isnan(p.Cost) | isinf(p.Cost));
            q_valid = ~any(isnan(q.Cost) | isinf(q.Cost));
            
            if p_valid && q_valid
                if Dominates(p.Cost, q.Cost)
                    p.DominationSet = [p.DominationSet j];
                    q.DominatedCount = q.DominatedCount + 1;
                elseif Dominates(q.Cost, p.Cost)
                    q.DominationSet = [q.DominationSet i];
                    p.DominatedCount = p.DominatedCount + 1;
                end
            elseif p_valid && ~q_valid
                % 如果p有效而q无效，则p支配q
                p.DominationSet = [p.DominationSet j];
                q.DominatedCount = q.DominatedCount + 1;
            elseif ~p_valid && q_valid
                % 如果q有效而p无效，则q支配p
                q.DominationSet = [q.DominationSet i];
                p.DominatedCount = p.DominatedCount + 1;
            end
            
            pop(i) = p;
            pop(j) = q;
        end
        
        if pop(i).DominatedCount == 0
            F{1} = [F{1} i];
            pop(i).Rank = 1;
        end
    end
    
    % 如果第一个前沿为空，至少添加一个个体
    if isempty(F{1})
        % 找出有效的个体
        valid_indices = [];
        for i = 1:nPop
            if ~any(isnan(pop(i).Cost) | isinf(pop(i).Cost))
                valid_indices = [valid_indices i];
            end
        end
        
        % 如果有有效个体，选择第一个作为非支配解
        if ~isempty(valid_indices)
            F{1} = [F{1} valid_indices(1)];
        else
            % 如果没有有效个体，选择第一个个体
            F{1} = 1;
        end
        
        for i = F{1}
            pop(i).Rank = 1;
        end
    end
    
    % 生成其他前沿
    k = 1;
    while true
        Q = [];
        
        for i = F{k}
            p = pop(i);
            
            for j = p.DominationSet
                q = pop(j);
                
                q.DominatedCount = q.DominatedCount - 1;
                
                if q.DominatedCount == 0
                    Q = [Q j];
                    q.Rank = k + 1;
                end
                
                pop(j) = q;
            end
        end
        
        if isempty(Q)
            break;
        end
        
        F{k+1} = Q;
        k = k + 1;
    end
    
    % 确保所有个体都被分配到某个前沿
    assigned = [];
    for k = 1:length(F)
        assigned = [assigned F{k}];
    end
    
    unassigned = setdiff(1:nPop, assigned);
    if ~isempty(unassigned)
        % 将未分配的个体添加到最后一个前沿
        F{end} = [F{end} unassigned];
        
        % 设置这些个体的排名
        for i = unassigned
            pop(i).Rank = length(F);
        end
    end
end

function [pop, d, rho] = AssociateToReferencePoint(pop, params)
% 将解关联到参考点

    % 提取归一化的目标函数值
    costs = reshape([pop.NormalizedCost], [], length(pop))';
    
    % 计算每个解到每个参考点的距离
    d = zeros(numel(pop), params.nZr);
    for i = 1:numel(pop)
        for j = 1:params.nZr
            % 计算向量到参考线的垂直距离
            ref = params.Zr(:, j)';
            
            % 避免无效值
            if any(isnan(costs(i, :)) | isinf(costs(i, :)))
                d(i, j) = inf;
                continue;
            end
            
            % 计算余弦相似度
            cosine = dot(costs(i, :), ref) / (norm(costs(i, :)) * norm(ref) + eps);
            cosine = max(-1, min(1, cosine)); % 确保在[-1, 1]范围内
            
            % 计算向量到参考线的距离
            d(i, j) = norm(costs(i, :)) * sqrt(1 - cosine^2);
        end
        
        % 找到最近的参考点
        [~, idx] = min(d(i, :));
        pop(i).AssociatedRef = idx;
        pop(i).DistanceToAssociatedRef = d(i, idx);
    end
    
    % 计算每个参考点关联的解的数量
    rho = zeros(1, params.nZr);
    for i = 1:numel(pop)
        if isfield(pop(i), 'Rank') && pop(i).Rank == 1
            rho(pop(i).AssociatedRef) = rho(pop(i).AssociatedRef) + 1;
        end
    end
end

function [pop, F, params] = SortAndSelectPopulation(pop, params)
% 排序和选择种群

    % 归一化种群
    [pop, params] = NormalizePopulation(pop, params);
    
    % 非支配排序
    [pop, F] = NonDominatedSorting(pop);
    
    % 关联解到参考点
    [pop, d, rho] = AssociateToReferencePoint(pop, params);
    
    % 选择种群
    nPop = params.nPop;
    
    % 计算前沿中个体的总数
    nF1 = numel(F{1});
    
    % 如果第一前沿的个体数量小于等于种群大小，保留所有第一前沿的个体
    if nF1 <= nPop
        newpop = pop(F{1});
        
        % 计算剩余需要选择的个体数量
        nRemain = nPop - nF1;
        
        % 如果需要更多个体，从其他前沿中选择
        k = 2;
        while nRemain > 0 && k <= numel(F)
            nFk = numel(F{k});
            
            % 如果当前前沿的个体数量小于等于剩余需要选择的个体数量，保留所有当前前沿的个体
            if nFk <= nRemain
                newpop = [newpop; pop(F{k})];
                nRemain = nRemain - nFk;
                k = k + 1;
            else
                % 否则，需要从当前前沿中选择部分个体
                % 使用LastSelection函数选择个体
                pop_Fk = pop(F{k});
                [pop_Fk, d_Fk, rho_Fk] = AssociateToReferencePoint(pop_Fk, params);
                
                % 创建临时前沿
                temp_F = cell(1, 1);
                temp_F{1} = 1:nFk;
                
                % 选择个体
                selected = false(nFk, 1);
                while sum(selected) < nRemain
                    % 找到关联解最少的参考点
                    min_rho_idx = find(rho_Fk == min(rho_Fk));
                    
                    % 如果有多个参考点关联解数量相同，随机选择一个
                    j = min_rho_idx(randi(length(min_rho_idx)));
                    
                    % 找到关联到该参考点的解
                    I = [];
                    for i = 1:nFk
                        if ~selected(i) && pop_Fk(i).AssociatedRef == j
                            I = [I i];
                        end
                    end
                    
                    % 如果没有解关联到该参考点，将其从考虑中排除
                    if isempty(I)
                        rho_Fk(j) = inf;
                        continue;
                    end
                    
                    % 如果只有一个解关联到该参考点，选择它
                    if length(I) == 1
                        selected(I) = true;
                    else
                        % 否则，选择距离最近的解
                        [~, min_idx] = min(d_Fk(I));
                        selected(I(min_idx)) = true;
                    end
                    
                    % 更新参考点的关联解数量
                    rho_Fk(j) = rho_Fk(j) + 1;
                end
                
                % 添加选择的个体
                newpop = [newpop; pop_Fk(selected)];
                break;
            end
        end
        
        % 如果仍然需要更多个体，随机选择
        if nRemain > 0
            % 随机选择剩余的个体
            remaining_indices = setdiff(1:length(pop), [F{1:k-1}]);
            if length(remaining_indices) >= nRemain
                selected_indices = remaining_indices(randperm(length(remaining_indices), nRemain));
                newpop = [newpop; pop(selected_indices)];
            else
                % 如果剩余的个体不够，复制一些已有的个体
                newpop = [newpop; pop(remaining_indices)];
                nRemain = nRemain - length(remaining_indices);
                
                % 从第一前沿中复制个体
                if nF1 > 0
                    dup_indices = F{1}(randi(nF1, nRemain, 1));
                    newpop = [newpop; pop(dup_indices)];
                end
            end
        end
        
        % 更新种群
        pop = newpop;
        
        % 重新进行非支配排序
        [pop, F] = NonDominatedSorting(pop);
    else
        % 如果第一前沿的个体数量大于种群大小，需要选择部分个体
        % 使用LastSelection函数选择个体
        F{1} = F{1}(1:min(nPop, length(F{1})));
        
        % 删除其他前沿
        F = F(1);
        
        % 更新种群
        pop = pop(F{1});
    end
end

function p = TournamentSelection(pop)
% 锦标赛选择

    n = numel(pop);
    
    % 随机选择两个个体
    i1 = randi(n);
    i2 = randi(n);
    
    % 比较它们的排名
    if pop(i1).Rank < pop(i2).Rank
        p = pop(i1);
    elseif pop(i2).Rank < pop(i1).Rank
        p = pop(i2);
    else
        % 如果排名相同，随机选择一个
        if rand < 0.5
            p = pop(i1);
        else
            p = pop(i2);
        end
    end
end

function [y1, y2] = Crossover(x1, x2, pc, lb, ub)
% SBX交叉

    n = length(x1);
    y1 = x1;
    y2 = x2;
    
    if rand <= pc
        eta_c = 15;  % 交叉分布指数
        
        for j = 1:n
            if rand <= 0.5
                if abs(x1(j) - x2(j)) > 1e-10
                    if x1(j) < x2(j)
                        xl = x1(j);
                        xu = x2(j);
                    else
                        xl = x2(j);
                        xu = x1(j);
                    end
                    
                    beta = 1 + 2*(xl-lb(min(j,length(lb))))/(xu-xl);
                    alpha = 2 - beta^(-eta_c-1);
                    
                    if rand <= 1/alpha
                        beta_q = (rand*alpha)^(1/(eta_c+1));
                    else
                        beta_q = (1/(2-rand*alpha))^(1/(eta_c+1));
                    end
                    
                    c1 = 0.5*((xl+xu) - beta_q*(xu-xl));
                    
                    beta = 1 + 2*(ub(min(j,length(ub)))-xu)/(xu-xl);
                    alpha = 2 - beta^(-eta_c-1);
                    
                    if rand <= 1/alpha
                        beta_q = (rand*alpha)^(1/(eta_c+1));
                    else
                        beta_q = (1/(2-rand*alpha))^(1/(eta_c+1));
                    end
                    
                    c2 = 0.5*((xl+xu) + beta_q*(xu-xl));
                    
                    c1 = max(lb(min(j,length(lb))), min(ub(min(j,length(ub))), c1));
                    c2 = max(lb(min(j,length(lb))), min(ub(min(j,length(ub))), c2));
                    
                    if rand <= 0.5
                        y1(j) = c1;
                        y2(j) = c2;
                    else
                        y1(j) = c2;
                        y2(j) = c1;
                    end
                end
            end
        end
    end
end

function y = Mutate(x, pm, lb, ub)
% 多项式变异

    n = length(x);
    y = x;
    
    eta_m = 20;  % 变异分布指数
    
    for j = 1:n
        if rand <= pm
            delta1 = (y(j) - lb(min(j,length(lb)))) / (ub(min(j,length(ub))) - lb(min(j,length(lb))));
            delta2 = (ub(min(j,length(ub))) - y(j)) / (ub(min(j,length(ub))) - lb(min(j,length(lb))));
            
            r = rand();
            mut_pow = 1/(eta_m + 1);
            
            if r <= 0.5
                xy = 1 - delta1;
                val = 2*r + (1-2*r)*(xy^(eta_m+1));
                delta_q = val^mut_pow - 1;
            else
                xy = 1 - delta2;
                val = 2*(1-r) + 2*(r-0.5)*(xy^(eta_m+1));
                delta_q = 1 - val^mut_pow;
            end
            
            y(j) = y(j) + delta_q * (ub(min(j,length(ub))) - lb(min(j,length(lb))));
            y(j) = max(lb(min(j,length(lb))), min(ub(min(j,length(ub))), y(j)));
        end
    end
end

function result = Dominates(x, y)
% 判断x是否支配y

    % 检查输入是否为空
    if isempty(x) || isempty(y)
        result = false;
        return;
    end
    
    % 检查维度是否匹配
    if length(x) ~= length(y)
        result = false;
        return;
    end
    
    % 检查是否有无效值
    if any(isnan(x)) || any(isnan(y)) || any(isinf(x)) || any(isinf(y))
        result = false;
        return;
    end
    
    % 标准支配关系检查
    result = all(x <= y) && any(x < y);
end

function pop = LastSelection(pop, F, params, nRemain)
% 最后的选择

    % 提取最后一个前沿
    Fl = F{end};
    
    % 如果最后一个前沿为空或不需要选择，直接返回
    if isempty(Fl) || nRemain >= length(Fl)
        return;
    end
    
    % 计算每个解到其关联参考点的距离
    d = zeros(length(Fl), 1);
    for i = 1:length(Fl)
        d(i) = pop(Fl(i)).DistanceToAssociatedRef;
    end
    
    % 计算每个参考点关联的解的数量
    rho = zeros(1, params.nZr);
    for i = 1:length(pop)
        if isfield(pop(i), 'Rank') && pop(i).Rank < length(F)
            ref = pop(i).AssociatedRef;
            if ~isnan(ref) && ref <= params.nZr
                rho(ref) = rho(ref) + 1;
            end
        end
    end
    
    % 选择解
    selected = false(length(Fl), 1);
    while sum(selected) < nRemain
        % 找到关联解最少的参考点
        min_rho = min(rho(rho >= 0));
        min_rho_idx = find(rho == min_rho);
        
        % 如果有多个参考点关联解数量相同，随机选择一个
        j = min_rho_idx(randi(length(min_rho_idx)));
        
        % 找到关联到该参考点的解
        I = [];
        for i = 1:length(Fl)
            if ~selected(i) && isfield(pop(Fl(i)), 'AssociatedRef') && pop(Fl(i)).AssociatedRef == j
                I = [I i];
            end
        end
        
        % 如果没有解关联到该参考点，将其从考虑中排除
        if isempty(I)
            rho(j) = inf;
            continue;
        end
        
        % 如果只有一个解关联到该参考点，选择它
        if length(I) == 1
            selected(I) = true;
        else
            % 否则，选择距离最近的解
            [~, min_idx] = min(d(I));
            selected(I(min_idx)) = true;
        end
        
        % 更新参考点的关联解数量
        rho(j) = rho(j) + 1;
    end
    
    % 移除未选择的解
    not_selected = find(~selected);
    F{end}(not_selected) = [];
end 