function [varargout] = PlotRadarMetrics(all_results, alg_names, save_path)
% PLOTRADARMETRICS 绘制算法多指标雷达图
% 根据原有Metrics文件夹的评估方法生成雷达图
%
% 输入:
%   all_results - 包含各算法结果的cell数组，每个元素是一个N×3的矩阵
%   alg_names - 算法名称数组
%   save_path - 可选，保存图像的路径
%
% 输出:
%   varargout{1} - 可选，返回图窗句柄

% 设置默认字体为宋体
set(0, 'DefaultAxesFontName', 'SimSun');
set(0, 'DefaultTextFontName', 'SimSun');
set(0, 'DefaultUicontrolFontName', 'SimSun');

% 如果没有提供保存路径，设置默认值
if nargin < 3
    save_path = '';
end

% 使用EvaluateAlgorithms函数计算性能指标
fprintf('正在计算算法性能指标用于雷达图...\n');
metrics = EvaluateAlgorithms(all_results, alg_names);

% 确保算法名称匹配
if isfield(metrics, 'AlgorithmNames')
    % 使用metrics中的算法名称
    alg_names = metrics.AlgorithmNames;
end

% 获取有效的算法数量
n_algs = length(alg_names);

% 提取我们需要的六个指标
indicator_names = {'GD', 'IGD', 'Spread', 'MS', 'HV', 'Time'};
n_indicators = length(indicator_names);

% 创建数据矩阵
data = zeros(n_algs, n_indicators);

% 填充数据矩阵
for i = 1:n_algs
    for j = 1:n_indicators
        field_name = indicator_names{j};
        if isfield(metrics, field_name) && i <= length(metrics.(field_name))
            data(i, j) = metrics.(field_name)(i);
        end
    end
end

% 对每个指标进行归一化处理
norm_data = zeros(size(data));
for j = 1:n_indicators
    % 获取当前指标的数据
    indicator_data = data(:, j);
    
    % 跳过全零的指标
    if all(indicator_data == 0)
        continue;
    end
    
    % 确定是否需要反转（对于GD、IGD、Spread和Time，越小越好）
    invert = ismember(indicator_names{j}, {'GD', 'IGD', 'Spread', 'Time'});
    
    % 归一化处理
    if invert
        % 对于越小越好的指标，反转后归一化
        max_val = max(indicator_data);
        if max_val > 0
            norm_data(:, j) = 1 - (indicator_data / max_val);
        end
    else
        % 对于越大越好的指标，直接归一化
        max_val = max(indicator_data);
        if max_val > 0
            norm_data(:, j) = indicator_data / max_val;
        end
    end
end

% 创建图窗
fig = figure('Position', [100, 100, 1000, 800], 'Name', '算法性能雷达图', 'NumberTitle', 'off');

% 设置鲜艳的颜色方案 - 与前沿图保持一致
colors = [
    1.0000, 0.0000, 0.0000;  % 红色 - NSGA-II
    0.0000, 0.0000, 1.0000;  % 蓝色 - NSGA-III
    0.0000, 0.7000, 0.0000;  % 鲜绿色 - SPEA2
    0.8000, 0.0000, 0.8000;  % 亮紫色 - MOEA-D
    0.0000, 0.8000, 0.8000;  % 青绿色 - MOEA-D-DE
    1.0000, 0.6000, 0.0000;  % 橙色 - MOEA-D-M2M
    0.5000, 0.0000, 1.0000;  % 紫色 - MOPSO
    1.0000, 1.0000, 0.0000;  % 鲜黄色 - MOGWO
    1.0000, 0.0000, 0.5000;  % 品红色 - MOWOA
];

% 确保颜色数量足够
if n_algs > size(colors, 1)
    colors = [colors; jet(n_algs - size(colors, 1))];
end

% 计算雷达图的角度
theta = linspace(0, 2*pi, n_indicators+1);
theta = theta(1:end-1); % 移除最后一个重复的点

% 设置3×3子图布局
rows = 3;
cols = 3;

% 添加总标题，减小字体大小
sgtitle('多目标优化算法性能雷达图对比', 'FontSize', 14, 'FontWeight', 'bold', 'FontName', 'SimSun');

% 绘制每个算法的雷达图到独立子图
for i = 1:min(n_algs, 9) % 最多显示9个算法
    % 创建子图
    subplot(rows, cols, i);
    
    % 初始化子图设置
    hold on;
    axis equal;
    axis off;
    
    % 绘制参考圆
    for r = 0.2:0.2:1
        circle_x = r * cos(linspace(0, 2*pi, 100));
        circle_y = r * sin(linspace(0, 2*pi, 100));
        plot(circle_x, circle_y, 'Color', [0.7 0.7 0.7], 'LineStyle', ':');
        
        % 添加刻度标签
        if r == 0.5
            text(0, r+0.05, sprintf('%.1f', r), 'HorizontalAlignment', 'center', 'FontSize', 7, 'Color', [0.5 0.5 0.5], 'FontName', 'SimSun');
        end
    end
    
    % 绘制径向线
    for j = 1:n_indicators
        line([0, 1.1*cos(theta(j))], [0, 1.1*sin(theta(j))], 'Color', [0.7 0.7 0.7], 'LineStyle', '-');
    end
    
    % 添加指标标签
    for j = 1:n_indicators
        % 计算标签位置
        label_x = 1.2 * cos(theta(j));
        label_y = 1.2 * sin(theta(j));
        
        % 调整水平对齐方式
        if abs(label_x) < 0.1
            h_align = 'center';
        elseif label_x > 0
            h_align = 'left';
        else
            h_align = 'right';
        end
        
        % 调整垂直对齐方式
        if abs(label_y) < 0.1
            v_align = 'middle';
        elseif label_y > 0
            v_align = 'bottom';
        else
            v_align = 'top';
        end
        
        % 添加标签
        text(label_x, label_y, indicator_names{j}, ...
            'HorizontalAlignment', h_align, ...
            'VerticalAlignment', v_align, ...
            'FontSize', 8, ...
            'FontWeight', 'bold', ...
            'FontName', 'SimSun');
    end
    
    % 闭合多边形的数据
    values = [norm_data(i, :), norm_data(i, 1)];
    angles = [theta, theta(1)];
    
    % 转换为笛卡尔坐标
    x = values .* cos(angles);
    y = values .* sin(angles);
    
    % 绘制多边形
    patch(x, y, colors(i,:), 'FaceAlpha', 0.3, 'EdgeColor', colors(i,:), 'LineWidth', 2);
    
    % 添加算法名称作为子图标题
    title(alg_names{i}, 'FontSize', 11, 'Color', [0 0 0], 'FontWeight', 'bold', 'FontName', 'SimSun');
    
    % 设置轴范围
    axis([-1.2 1.2 -1.2 1.2]);
end

% 自适应调整子图布局，根据窗口大小计算位置
set(fig, 'Units', 'normalized');
for i = 1:min(n_algs, 9)
    % 计算行列位置
    row = ceil(i/cols);
    col = mod(i-1, cols) + 1;
    
    % 设置子图间距
    h_margin = 0.07; % 水平边距
    v_margin = 0.07; % 垂直边距
    h_spacing = (1 - 2*h_margin) / cols; % 水平间距
    v_spacing = (1 - 2*v_margin) / rows; % 垂直间距
    
    % 计算子图位置
    x = h_margin + (col-1) * h_spacing;
    y = 1 - v_margin - row * v_spacing;
    width = h_spacing * 0.85; % 宽度略小于间距
    height = v_spacing * 0.85; % 高度略小于间距
    
    % 设置子图位置
    pos = [x, y, width, height];
    set(subplot(rows, cols, i), 'Position', pos);
end

% 如果提供了保存路径，则保存图像
if nargin >= 3 && ~isempty(save_path)
    try
        % 使用print函数保存，更适合批处理模式
        [path_dir, name, ~] = fileparts(save_path);
        if ~exist(path_dir, 'dir')
            mkdir(path_dir);
        end

        % 保存为PNG格式
        print(fig, save_path, '-dpng', '-r300');
        fprintf('雷达图已保存至: %s\n', save_path);
    catch e
        fprintf('保存雷达图失败: %s\n', e.message);
        % 尝试使用saveas作为备选方案
        try
            saveas(fig, save_path);
            fprintf('雷达图已保存至: %s (使用备选方案)\n', save_path);
        catch e2
            fprintf('备选保存方案也失败: %s\n', e2.message);
        end
    end
end

% 返回图窗句柄，方便调用者进一步处理
if nargout > 0
    varargout{1} = fig;
end
end
