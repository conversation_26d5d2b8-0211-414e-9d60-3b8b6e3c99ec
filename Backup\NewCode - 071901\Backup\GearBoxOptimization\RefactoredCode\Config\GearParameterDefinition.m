function param_def = GearParameterDefinition()
% GearParameterDefinition 齿轮参数定义系统
% 严格按照原有代码的参数范围和功能定义
% 保持与原有GearOptimizationMain.m中的参数范围完全一致
%
% 输出:
%   param_def - 参数定义结构体，包含所有参数的定义和关系

%% 1. 优化变量定义（25个完整变量，按原有代码顺序）
% 根据GearOptimizationMain.m第377行注释的完整变量列表
param_def.optimization_variables = {
    % 基本几何参数
    'm1',                    % x(1)  - 一级模数 (mm)
    'z1',                    % x(2)  - 一级小齿轮齿数
    'z2',                    % x(3)  - 一级大齿轮齿数
    'mn2',                   % x(4)  - 二级法向模数 (mm)
    'zs2',                   % x(5)  - 二级太阳轮齿数
    'zp2',                   % x(6)  - 二级行星轮齿数
    'k_h2',                  % x(7)  - 二级齿宽系数
    'mn3',                   % x(8)  - 三级法向模数 (mm)
    'zs3',                   % x(9)  - 三级太阳轮齿数
    'zp3',                   % x(10) - 三级行星轮齿数
    'k_h3',                  % x(11) - 三级齿宽系数
    'planets_count_2',       % x(12) - 二级行星轮数量
    'planets_count_3',       % x(13) - 三级行星轮数量
    'pressure_angle',        % x(14) - 一二级压力角
    'helix_angle_1',         % x(15) - 一级螺旋角 (度)
    'helix_angle_2',         % x(16) - 二级螺旋角 (度)
    'helix_angle_3',         % x(17) - 三级螺旋角 (度)
    'x1',                    % x(18) - 一级小齿轮变位系数
    'x2',                    % x(19) - 一级大齿轮变位系数
    'xs2',                   % x(20) - 二级太阳轮变位系数
    'xp2',                   % x(21) - 二级行星轮变位系数
    'xs3',                   % x(22) - 三级太阳轮变位系数
    'xp3',                   % x(23) - 三级行星轮变位系数
    'pressure_angle_3_choice', % x(24) - 三级压力角选择
    'k_h1'                   % x(25) - 一级齿宽系数
};

%% 2. 固定一级参数模式的变量定义（18个变量）
param_def.fixed_first_stage_variables = {
    'mn2',                   % x(1)  - 二级法向模数
    'zs2',                   % x(2)  - 二级太阳轮齿数
    'zp2',                   % x(3)  - 二级行星轮齿数
    'k_h2',                  % x(4)  - 二级齿宽系数
    'mn3',                   % x(5)  - 三级法向模数
    'zs3',                   % x(6)  - 三级太阳轮齿数
    'zp3',                   % x(7)  - 三级行星轮齿数
    'k_h3',                  % x(8)  - 三级齿宽系数
    'planets_count_2',       % x(9)  - 二级行星轮数量
    'planets_count_3',       % x(10) - 三级行星轮数量
    'pressure_angle',        % x(11) - 压力角
    'helix_angle_2',         % x(12) - 二级螺旋角
    'helix_angle_3',         % x(13) - 三级螺旋角
    'xs2',                   % x(14) - 二级太阳轮变位系数
    'xp2',                   % x(15) - 二级行星轮变位系数
    'xs3',                   % x(16) - 三级太阳轮变位系数
    'xp3',                   % x(17) - 三级行星轮变位系数
    'pressure_angle_3_choice' % x(18) - 三级压力角选择
};

%% 3. 完整表格参数列表（71个参数，按原有顺序）
param_def.table_columns = {
    % 一级参数 (16个)
    'm1', 'z1', 'z2', 'k_h1', 'x1', 'x2', 'x_sum1', 'beta1', 'alpha1', 'a1', 'i1', 
    'SH1', 'SF1', 'SF2', 'M1', 'M2',
    
    % 二级参数 (25个)
    'mn2', 'zs2', 'zp2', 'zr2', 'n2', 'k_h2', 'xs2', 'xp2', 'xr2', 'x_sum2', 
    'beta2', 'alpha2', 'a2', 'i2', 'SHsps2', 'SHspp2', 'SFsps2', 'SFspp2', 
    'SHprr2', 'SHprp2', 'SFprr2', 'SFprp2', 'Ms2', 'Mp2', 'Mr2',
    
    % 三级参数 (25个)
    'mn3', 'zs3', 'zp3', 'zr3', 'n3', 'k_h3', 'xs3', 'xp3', 'xr3', 'x_sum3', 
    'beta3', 'alpha3', 'a3', 'i3', 'SHsps3', 'SHspp3', 'SFsps3', 'SFspp3', 
    'SHprr3', 'SHprp3', 'SFprr3', 'SFprp3', 'Ms3', 'Mp3', 'Mr3',
    
    % 汇总指标 (5个)
    'TotalMass', 'SH', 'SF', 'TotalRatio', 'Error'
};

%% 4. 加载配置文件
% 加载约束范围配置
constraint_config = ConstraintRanges();
param_def.constraints = constraint_config;

% 加载系统默认参数配置
system_config = SystemDefaults();
param_def.system_defaults = system_config.operating_conditions;
param_def.system_defaults.contact_safety_factor = system_config.safety_factors.contact_safety_factor;
param_def.system_defaults.bending_safety_factor = system_config.safety_factors.bending_safety_factor;
param_def.system_defaults.center_distance = system_config.geometric_constraints.center_distance;
param_def.system_defaults.quality_grade = system_config.quality.grade;
param_def.system_defaults.pressure_angle = system_config.calculation.pressure_angle;

% 加载材料属性配置
material_config = MaterialProperties();
param_def.materials = material_config.assignment;

%% 7. 算法列表
param_def.algorithms = {'NSGA-II', 'NSGA-III', 'SPEA2', 'MOEA/D', 'MOEA/D-DE', 'MOEA/D-M2M', 'MOPSO', 'MOGWO', 'MOWOA'};

%% 8. 离散变量定义（从约束配置中获取）
param_def.discrete_variables = constraint_config.discrete_variables;

%% 9. 参数映射关系
param_def.parameter_mapping = struct();
% 优化变量到表格参数的映射
param_def.parameter_mapping.opt_to_table = containers.Map();
% 一级参数映射
param_def.parameter_mapping.opt_to_table('m1') = 'm1';
param_def.parameter_mapping.opt_to_table('z1') = 'z1';
param_def.parameter_mapping.opt_to_table('z2') = 'z2';
param_def.parameter_mapping.opt_to_table('k_h1') = 'k_h1';
param_def.parameter_mapping.opt_to_table('x1') = 'x1';
param_def.parameter_mapping.opt_to_table('x2') = 'x2';
param_def.parameter_mapping.opt_to_table('helix_angle_1') = 'beta1';
param_def.parameter_mapping.opt_to_table('pressure_angle') = 'alpha1';

% 二级参数映射
param_def.parameter_mapping.opt_to_table('mn2') = 'mn2';
param_def.parameter_mapping.opt_to_table('zs2') = 'zs2';
param_def.parameter_mapping.opt_to_table('zp2') = 'zp2';
param_def.parameter_mapping.opt_to_table('k_h2') = 'k_h2';
param_def.parameter_mapping.opt_to_table('xs2') = 'xs2';
param_def.parameter_mapping.opt_to_table('xp2') = 'xp2';
param_def.parameter_mapping.opt_to_table('helix_angle_2') = 'beta2';
param_def.parameter_mapping.opt_to_table('planets_count_2') = 'n2';

% 三级参数映射
param_def.parameter_mapping.opt_to_table('mn3') = 'mn3';
param_def.parameter_mapping.opt_to_table('zs3') = 'zs3';
param_def.parameter_mapping.opt_to_table('zp3') = 'zp3';
param_def.parameter_mapping.opt_to_table('k_h3') = 'k_h3';
param_def.parameter_mapping.opt_to_table('xs3') = 'xs3';
param_def.parameter_mapping.opt_to_table('xp3') = 'xp3';
param_def.parameter_mapping.opt_to_table('helix_angle_3') = 'beta3';
param_def.parameter_mapping.opt_to_table('planets_count_3') = 'n3';

%% 10. 计算属性定义
param_def.calculated_properties = {
    % 一级计算属性
    'x_sum1',       % 一级综合变位系数 = x1 + x2
    'alpha1',       % 一级压力角 (度)
    'a1',           % 一级中心距 (mm)
    'i1',           % 一级传动比 = z2/z1
    
    % 二级计算属性
    'zr2',          % 二级内齿圈齿数 = zs2 + 2*zp2
    'xr2',          % 二级内齿圈变位系数
    'x_sum2',       % 二级综合变位系数 = xs2 + xp2 + xr2
    'alpha2',       % 二级压力角 (度)
    'a2',           % 二级中心距 (mm)
    'i2',           % 二级传动比 = 1 + zr2/zs2
    
    % 三级计算属性
    'zr3',          % 三级内齿圈齿数 = zs3 + 2*zp3
    'xr3',          % 三级内齿圈变位系数
    'x_sum3',       % 三级综合变位系数 = xs3 + xp3 + xr3
    'alpha3',       % 三级压力角 (度)
    'a3',           % 三级中心距 (mm)
    'i3'            % 三级传动比 = 1 + zr3/zs3
};

%% 11. 性能属性定义
param_def.performance_properties = {
    % 一级性能属性
    'SH1', 'SF1', 'SF2', 'M1', 'M2',
    
    % 二级性能属性
    'SHsps2', 'SHspp2', 'SFsps2', 'SFspp2', 'SHprr2', 'SHprp2', 'SFprr2', 'SFprp2',
    'Ms2', 'Mp2', 'Mr2',
    
    % 三级性能属性
    'SHsps3', 'SHspp3', 'SFsps3', 'SFspp3', 'SHprr3', 'SHprp3', 'SFprr3', 'SFprp3',
    'Ms3', 'Mp3', 'Mr3'
};

%% 12. 汇总指标定义
param_def.summary_indicators = {
    'TotalMass',    % 总质量 (kg)
    'SH',           % 最小接触安全系数
    'SF',           % 最小弯曲安全系数
    'TotalRatio',   % 总传动比
    'Error'         % 传动比误差百分比
};

%% 13. 兼容性函数（与原有MaterialManager接口兼容）
material_config = MaterialProperties();
param_def.getMaterial = @(material_name) material_config.getMaterialByName(material_name);
param_def.getStandardGearMaterials = @() material_config.gear_materials;
param_def.material_properties = material_config;

end
