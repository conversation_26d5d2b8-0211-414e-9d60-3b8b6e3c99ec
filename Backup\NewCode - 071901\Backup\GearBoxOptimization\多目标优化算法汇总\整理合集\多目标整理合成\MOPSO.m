function [REP_pos, REP_pos_fit] = MOPSO(CostFunction, nVar, VarMin, VarMax, MaxIt, nPop)
% MOPSO: 多目标粒子群优化算法 (Multi-objective Particle Swarm Optimization)
%
% 输入参数:
%   CostFunction: 目标函数句柄
%   nVar: 决策变量数量
%   VarMin, VarMax: 决策变量的上下界
%   MaxIt: 最大迭代次数
%   nPop: 粒子数量
%
% 输出参数:
%   REP_pos: 非支配解的决策变量
%   REP_pos_fit: 非支配解的目标函数值
%
% 参考文献:
%   Coello, C. A. C., Pulido, G. T., & Lechuga, M. S. (2004). 
%   "Handling multiple objectives with particle swarm optimization." 
%   IEEE Transactions on evolutionary computation, 8(3), 256-279.

    % 默认参数值
    if nargin < 6
        nPop = 100;
    end
    if nargin < 5
        MaxIt = 100;
    end
    if nargin < 4
        VarMax = 1;
    end
    if nargin < 3
        VarMin = 0;
    end
    if nargin < 2
        nVar = 5;
    end
    
    % 变量区间向量化
    if isscalar(VarMin)
        VarMin = VarMin * ones(nVar, 1);
    end
    if isscalar(VarMax)
        VarMax = VarMax * ones(nVar, 1);
    end
    
    % 算法参数
    W = 0.4;             % 惯性权重
    C1 = 1.5;            % 个体学习因子
    C2 = 1.5;            % 社会学习因子
    nGrid = 10;          % 每个维度上的网格数量
    alpha = 0.1;         % 网格膨胀因子
    beta = 4;            % 领导者选择压力参数
    gamma = 2;           % 存档删除压力参数
    mu = 0.1;            % 变异率
    MaxVel = 0.2;        % 最大速度（占搜索空间的百分比）
    nRep = 100;          % 存档大小
    
    % 初始化粒子群
    empty_particle.Position = [];
    empty_particle.Velocity = [];
    empty_particle.Cost = [];
    empty_particle.Best.Position = [];
    empty_particle.Best.Cost = [];
    empty_particle.IsDominated = [];
    empty_particle.GridIndex = [];
    empty_particle.GridSubIndex = [];
    
    % 粒子群
    particle = repmat(empty_particle, nPop, 1);
    
    % 全局最佳解存档
    REP = CreateEmptyRepo(nRep);
    REP.pos = [];
    REP.pos_fit = [];
    REP.member_count = 0;
    
    % 初始化粒子位置和速度
    for i = 1:nPop
        % 随机初始化位置
        particle(i).Position = unifrnd(VarMin', VarMax', [1, nVar]);
        
        % 初始化速度为0
        particle(i).Velocity = zeros(1, nVar);
        
        % 评估位置的目标函数值
        particle(i).Cost = CostFunction(particle(i).Position);
        
        % 初始化个体最佳位置
        particle(i).Best.Position = particle(i).Position;
        particle(i).Best.Cost = particle(i).Cost;
    end
    
    % 确定支配关系
    particle = DetermineDomination(particle);
    
    % 初始化存档
    for i = 1:nPop
        if ~particle(i).IsDominated
            if REP.member_count < nRep
                REP.member_count = REP.member_count + 1;
                REP.pos(REP.member_count, :) = particle(i).Position;
                REP.pos_fit(REP.member_count, :) = particle(i).Cost;
            end
        end
    end
    
    % 创建超立方体
    REP = CreateHypercubes(REP, nGrid, alpha);
    
    % 计算网格索引
    for i = 1:REP.member_count
        [REP.GridIndex(i), REP.GridSubIndex(i, :)] = GetGridIndex(REP, REP.pos_fit(i, :));
    end
    
    % 主循环
    for it = 1:MaxIt
        % 更新W值（线性减小）
        w = W - (it-1) * (W/MaxIt);
        
        for i = 1:nPop
            % 选择领导者（从存档中选择一个解作为全局引导）
            leader_index = SelectLeader(REP, beta);
            leader = REP.pos(leader_index, :);
            
            % 更新速度
            particle(i).Velocity = w * particle(i).Velocity ...
                + C1 * rand(1, nVar) .* (particle(i).Best.Position - particle(i).Position) ...
                + C2 * rand(1, nVar) .* (leader - particle(i).Position);
            
            % 限制速度
            particle(i).Velocity = max(particle(i).Velocity, -MaxVel*abs(VarMax-VarMin)');
            particle(i).Velocity = min(particle(i).Velocity, MaxVel*abs(VarMax-VarMin)');
            
            % 更新位置
            particle(i).Position = particle(i).Position + particle(i).Velocity;
            
            % 变异操作
            if rand < mu
                j = randi([1, nVar]);
                particle(i).Position(j) = unifrnd(VarMin(j), VarMax(j));
            end
            
            % 边界处理
            particle(i).Position = max(particle(i).Position, VarMin');
            particle(i).Position = min(particle(i).Position, VarMax');
            
            % 评估新位置
            particle(i).Cost = CostFunction(particle(i).Position);
            
            % 更新个体最佳位置
            if dominates(particle(i).Cost, particle(i).Best.Cost)
                particle(i).Best.Position = particle(i).Position;
                particle(i).Best.Cost = particle(i).Cost;
            elseif ~dominates(particle(i).Best.Cost, particle(i).Cost)
                if rand < 0.5
                    particle(i).Best.Position = particle(i).Position;
                    particle(i).Best.Cost = particle(i).Cost;
                end
            end
        end
        
        % 确定支配关系
        particle = DetermineDomination(particle);
        
        % 更新存档
        REP = UpdateRepository(REP, particle, nGrid, alpha);
        
        % 如果存档太大，删除一些解
        if REP.member_count > nRep
            REP = DeleteFromRepository(REP, REP.member_count - nRep, gamma);
        end
    end
    
    % 返回存档中的非支配解
    REP_pos = REP.pos(1:REP.member_count, :);
    REP_pos_fit = REP.pos_fit(1:REP.member_count, :);
end

% 创建空的存档
function REP = CreateEmptyRepo(maxSize)
    REP.pos = zeros(maxSize, 1);
    REP.pos_fit = zeros(maxSize, 1);
    REP.GridIndex = zeros(maxSize, 1);
    REP.GridSubIndex = zeros(maxSize, 1);
    REP.member_count = 0;
end

% 确定支配关系
function particles = DetermineDomination(particles)
    nPop = numel(particles);
    
    for i = 1:nPop
        particles(i).IsDominated = false;
    end
    
    for i = 1:nPop-1
        for j = i+1:nPop
            if dominates(particles(i).Cost, particles(j).Cost)
                particles(j).IsDominated = true;
            elseif dominates(particles(j).Cost, particles(i).Cost)
                particles(i).IsDominated = true;
            end
        end
    end
end

% 支配关系检查
function dom = dominates(x, y)
    % x支配y，如果x的所有目标都不劣于y，且至少有一个目标严格优于y
    dom = all(x <= y) && any(x < y);
end

% 创建超立方体
function REP = CreateHypercubes(REP, nGrid, alpha)
    if REP.member_count == 0
        return;
    end
    
    nObj = size(REP.pos_fit, 2);
    
    % 计算每个维度的最小和最大值
    min_costs = min(REP.pos_fit(1:REP.member_count, :), [], 1);
    max_costs = max(REP.pos_fit(1:REP.member_count, :), [], 1);
    
    % 防止min和max相同导致网格宽度为0
    diff_costs = max_costs - min_costs;
    diff_costs(diff_costs == 0) = 0.1 * max_costs(diff_costs == 0);
    
    % 膨胀网格，确保边界点不会落在边界上
    REP.lower = min_costs - alpha * diff_costs;
    REP.upper = max_costs + alpha * diff_costs;
    
    % 保存网格参数
    REP.nGrid = nGrid;
    REP.nObj = nObj;
    
    % 计算每个维度上的网格大小
    REP.GridSize = (REP.upper - REP.lower) / nGrid;
end

% 获取解的网格索引
function [GridIndex, GridSubIndex] = GetGridIndex(REP, cost)
    nObj = size(cost, 2);
    
    % 计算每个维度上的子索引
    GridSubIndex = zeros(1, nObj);
    for j = 1:nObj
        GridSubIndex(j) = ceil((cost(j) - REP.lower(j)) / REP.GridSize(j));
        
        if GridSubIndex(j) < 1
            GridSubIndex(j) = 1;
        end
        
        if GridSubIndex(j) > REP.nGrid
            GridSubIndex(j) = REP.nGrid;
        end
    end
    
    % 计算总的网格索引
    GridIndex = 0;
    for j = 1:nObj
        GridIndex = GridIndex + GridSubIndex(j) * REP.nGrid^(j-1);
    end
end

% 更新存档
function REP = UpdateRepository(REP, particles, nGrid, alpha)
    for i = 1:numel(particles)
        if ~particles(i).IsDominated
            new_pos = particles(i).Position;
            new_cost = particles(i).Cost;
            
            add_to_rep = true;
            
            % 检查新解是否被存档中的解支配
            j = 1;
            while j <= REP.member_count && add_to_rep
                if dominates(REP.pos_fit(j, :), new_cost)
                    add_to_rep = false;
                elseif dominates(new_cost, REP.pos_fit(j, :))
                    % 如果新解支配存档中的解，移除被支配的解
                    REP.pos(j:REP.member_count-1, :) = REP.pos(j+1:REP.member_count, :);
                    REP.pos_fit(j:REP.member_count-1, :) = REP.pos_fit(j+1:REP.member_count, :);
                    REP.GridIndex(j:REP.member_count-1) = REP.GridIndex(j+1:REP.member_count);
                    REP.GridSubIndex(j:REP.member_count-1, :) = REP.GridSubIndex(j+1:REP.member_count, :);
                    REP.member_count = REP.member_count - 1;
                    j = j - 1;
                end
                j = j + 1;
            end
            
            % 添加新解到存档
            if add_to_rep
                REP.member_count = REP.member_count + 1;
                REP.pos(REP.member_count, :) = new_pos;
                REP.pos_fit(REP.member_count, :) = new_cost;
                
                % 更新超立方体
                REP = CreateHypercubes(REP, nGrid, alpha);
                
                % 更新所有解的网格索引
                for j = 1:REP.member_count
                    [REP.GridIndex(j), REP.GridSubIndex(j, :)] = GetGridIndex(REP, REP.pos_fit(j, :));
                end
            end
        end
    end
end

% 从存档中选择领导者
function leader_index = SelectLeader(REP, beta)
    % 获取所有网格索引
    GridIndices = REP.GridIndex(1:REP.member_count);
    
    % 获取每个网格中的粒子数
    unique_cells = unique(GridIndices);
    N = numel(unique_cells);
    
    % 计算每个网格的拥挤度
    cell_count = zeros(1, N);
    for k = 1:N
        cell_count(k) = sum(GridIndices == unique_cells(k));
    end
    
    % 计算选择概率（拥挤度越低，概率越高）
    P = (1./cell_count).^beta;
    P = P / sum(P);
    
    % 轮盘赌选择一个网格
    selected_cell_index = RouletteWheelSelection(P);
    selected_cell = unique_cells(selected_cell_index);
    
    % 从选中的网格中随机选择一个粒子
    selected_indices = find(GridIndices == selected_cell);
    h = randi(numel(selected_indices));
    leader_index = selected_indices(h);
end

% 从存档中删除多余的解
function REP = DeleteFromRepository(REP, extra, gamma)
    % 获取所有网格索引
    GridIndices = REP.GridIndex(1:REP.member_count);
    
    % 获取每个网格中的粒子数
    unique_cells = unique(GridIndices);
    N = numel(unique_cells);
    
    % 计算每个网格的拥挤度
    cell_count = zeros(1, N);
    for k = 1:N
        cell_count(k) = sum(GridIndices == unique_cells(k));
    end
    
    % 计算选择概率（拥挤度越高，概率越高）
    P = cell_count.^gamma;
    P = P / sum(P);
    
    % 选择要删除的解
    for e = 1:extra
        % 选择一个网格
        selected_cell_index = RouletteWheelSelection(P);
        selected_cell = unique_cells(selected_cell_index);
        
        % 从选中的网格中随机选择一个粒子删除
        selected_indices = find(GridIndices == selected_cell);
        h = randi(numel(selected_indices));
        selected_index = selected_indices(h);
        
        % 删除选中的粒子
        REP.pos(selected_index:REP.member_count-1, :) = REP.pos(selected_index+1:REP.member_count, :);
        REP.pos_fit(selected_index:REP.member_count-1, :) = REP.pos_fit(selected_index+1:REP.member_count, :);
        REP.GridIndex(selected_index:REP.member_count-1) = REP.GridIndex(selected_index+1:REP.member_count);
        REP.GridSubIndex(selected_index:REP.member_count-1, :) = REP.GridSubIndex(selected_index+1:REP.member_count, :);
        REP.member_count = REP.member_count - 1;
        
        % 更新GridIndices
        GridIndices = REP.GridIndex(1:REP.member_count);
        
        % 更新拥挤度
        cell_count(selected_cell_index) = sum(GridIndices == selected_cell);
        
        % 更新选择概率
        P = cell_count.^gamma;
        P = P / sum(P);
        
        % 如果网格为空，删除该网格
        if cell_count(selected_cell_index) == 0
            unique_cells(selected_cell_index) = [];
            cell_count(selected_cell_index) = [];
            P(selected_cell_index) = [];
            N = N - 1;
        end
    end
end

% 轮盘赌选择
function index = RouletteWheelSelection(P)
    if isempty(P) || sum(P) == 0
        index = 1;
        return;
    end
    
    r = rand;
    c = cumsum(P);
    index = find(r <= c, 1, 'first');
end 