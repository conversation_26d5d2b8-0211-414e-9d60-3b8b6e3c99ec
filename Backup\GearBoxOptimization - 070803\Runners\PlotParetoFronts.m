function main_fig = PlotParetoFronts()
    % 从Results文件夹加载数据
    fprintf('正在加载Pareto前沿数据...\n');
    
    % 确保Results文件夹存在
    if ~exist('Results', 'dir')
        mkdir('Results');
    end

    % 路径
    result_folder = 'Results';
    
    % 尝试加载所有结果文件 - 包含所有9种算法
    algNames = {'NSGA-II', 'NSGA-III', 'SPEA2', 'MOEA-D', 'MOEA-D-DE', 'MOEA-D-M2M', 'MOPSO', 'MOGWO', 'MOWOA'};
    all_results = cell(1, length(algNames));
    
    % 逐个加载结果文件
    for i = 1:length(algNames)
        file_path = fullfile(result_folder, ['pareto_solutions_', strrep(algNames{i}, '-', '_'), '.mat']);
        
        if exist(file_path, 'file')
            data = load(file_path);
            if isfield(data, 'pareto_solutions')
                all_results{i} = data.pareto_solutions;
            else
                fprintf('警告: %s中没有找到pareto_solutions字段\n', file_path);
                all_results{i} = [];
            end
        else
            fprintf('警告: 找不到文件 %s\n', file_path);
            all_results{i} = [];
        end
    end
    
    % 提取所有目标函数值用于绘图范围
    f1_all = [];  % 质量
    f2_all = [];  % 弯曲安全系数
    f3_all = [];  % 接触安全系数
    
    for i = 1:length(all_results)
        if ~isempty(all_results{i})
            f1_all = [f1_all; all_results{i}(:, 1)];
            f2_all = [f2_all; -all_results{i}(:, 2)];
            f3_all = [f3_all; -all_results{i}(:, 3)];
        end
    end
    
    % 创建一个宽大的figure窗口，包含三个子图
    main_fig = figure('Position', [100, 100, 1600, 600], 'Color', 'white');
    
    % 设置图形的打印分辨率为超高清
    set(main_fig, 'PaperUnits', 'inches');
    set(main_fig, 'PaperPositionMode', 'manual');
    set(main_fig, 'PaperPosition', [0 0 16 6]);
    set(main_fig, 'PaperSize', [16 6]);
    set(main_fig, 'Renderer', 'painters');
    
    % 绘制三个子图：三维Pareto前沿、质量vs弯曲安全系数、质量vs接触安全系数
    % 子图1：三维Pareto前沿
    subplot(1, 3, 1);
    Plot3DParetoFront(all_results, algNames, f1_all, f2_all, f3_all);
    
    % 子图2：质量vs弯曲安全系数
    subplot(1, 3, 2);
    PlotMassVsBendingSafety(all_results, algNames, f1_all, f2_all);
    
    % 子图3：质量vs接触安全系数
    subplot(1, 3, 3);
    PlotMassVsContactSafety(all_results, algNames, f1_all, f3_all);
    
    % 调整子图间距
    set(main_fig, 'Units', 'Normalized');
    p = get(main_fig, 'Position');
    set(main_fig, 'Position', [p(1), p(2), p(3), p(4)]);
    
    % 保存整个figure
    try
        % 保存组合图形
        saveas(main_fig, fullfile(result_folder, 'Pareto前沿图组合.fig'));
        print(main_fig, fullfile(result_folder, 'Pareto前沿图组合'), '-dpng', '-r600');
        print(main_fig, fullfile(result_folder, 'Pareto前沿图组合'), '-depsc');
        fprintf('Pareto前沿图组合已保存到Results文件夹\n');
    catch e
        if ~isempty(e.identifier)
            warning(e.identifier, '保存Pareto前沿图组合时出错: %s', e.message);
        else
            warning('MATLAB:PlotParetoFronts:SaveError', '保存Pareto前沿图组合时出错: %s', e.message);
        end
    end
end 