function Score = Spread(PopObj)
% Spread - 分布均匀度指标
% 根据Deb等人的标准定义：Δ = (df + dl + Σ|di - d̄|) / (df + dl + (N-1)d̄)
%
% 输入:
%   PopObj - 解集矩阵，每行代表一个解
%
% 输出:
%   Score - Spread值，越小越好（0表示完美均匀分布）
%
% 公式说明:
% - df, dl: 边界解到极值点的距离
% - di: 相邻解之间的距离
% - d̄: 平均距离
% - N: 解的数量
%
% 参考文献:
% De<PERSON>, <PERSON><PERSON>, <PERSON>ratap, A., A<PERSON>wal, S., & Meyarivan, T. (2002). A fast and
% elitist multiobjective genetic algorithm: NSGA-II.

if size(PopObj, 1) < 2
    Score = 0;
    return;
end

[N, M] = size(PopObj);

% 1. 找到当前解集的极值点（用作参考）
extreme_points = zeros(M, M);
for i = 1:M
    [~, idx] = min(PopObj(:, i));
    extreme_points(i, :) = PopObj(idx, :);
end

% 2. 对解集按第一个目标排序（用于计算相邻距离）
sorted_pop = sortrows(PopObj, 1);

% 3. 计算相邻解之间的距离
distances = zeros(N-1, 1);
for i = 1:N-1
    distances(i) = norm(sorted_pop(i+1, :) - sorted_pop(i, :));
end

% 4. 计算平均距离
mean_distance = mean(distances);

% 5. 计算边界距离
% df: 第一个解到对应极值点的距离
df = norm(sorted_pop(1, :) - extreme_points(1, :));

% dl: 最后一个解到对应极值点的距离
dl = norm(sorted_pop(end, :) - extreme_points(end, :));

% 6. 计算Spread指标
numerator = df + dl + sum(abs(distances - mean_distance));
denominator = df + dl + (N-1) * mean_distance;

if denominator == 0
    Score = 0;
else
    Score = numerator / denominator;
end
end