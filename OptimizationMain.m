function OptimizationMain()
% OptimizationMain 齿轮传动系统多目标优化主函数
% 重构版本，提供完整的交互式界面和输出格式
%
% 主要功能：
% 1. 自动清空结果文件夹
% 2. 减速器核心参数设置
% 3. 传动比约束设置
% 4. 材料参数显示
% 5. 一级参数生成和聚类
% 6. 二三级优化
% 7. 结果处理和可视化

%% 1. 自动清空结果文件夹
clearResultsFolder();

% 添加路径
addpath('Config');
addpath('Model');
addpath('Algorithms');
addpath('Visualization');
addpath('Results');
addpath('Metrics');

%% 2. 减速器核心参数设置
system_params = setupCoreParameters();

%% 3. 传动比约束设置
system_params = setupTransmissionRatioConstraints(system_params);

%% 4. 其他重要参数显示
displayOtherImportantParameters(system_params);

%% 5. 齿轮材料参数显示
displayMaterialParameters();

%% 6. 齿轮精度等级设置
system_params = setupGearAccuracyGrade(system_params);

%% 7. 一级平行轴系参数生成
first_stage_params = handleFirstStageParameterGeneration(system_params);

%% 8. 二三级行星轮系优化阶段
if askForThreeStageOptimization(first_stage_params)
    runThreeStageOptimization(system_params, first_stage_params);
end

fprintf('\n=== 优化完成 ===\n');

% 自动打开生成的HTML报告
html_file = fullfile('Results', '优化结果综合报告.html');
if exist(html_file, 'file')
    fprintf('正在打开优化结果报告...\n');
    try
        if usejava('desktop')
            % 在MATLAB桌面环境中打开
            web(html_file, '-browser');
        else
            % 在命令行模式下显示文件路径
            fprintf('优化结果报告已生成: %s\n', fullfile(pwd, html_file));
        end
    catch
        fprintf('无法自动打开浏览器，请手动打开: %s\n', fullfile(pwd, html_file));
    end
else
    fprintf('未找到HTML报告文件\n');
end

end

%% ========== 主要功能函数 ==========

function clearResultsFolder()
% 自动清空结果文件夹
fprintf('=== 自动清空结果文件夹 ===\n');
results_folder = 'Results';

% 临时关闭所有相关警告
warning_state = warning('off', 'all');

try
    % 检查Results文件夹是否存在
    if exist(results_folder, 'dir')
        % 删除文件夹中的所有内容，但保留文件夹本身
        % 获取文件夹中的所有文件和子文件夹
        files = dir(fullfile(results_folder, '*'));
        files = files(~ismember({files.name}, {'.', '..'})); % 排除 . 和 ..

        % 删除所有文件和子文件夹
        for i = 1:length(files)
            file_path = fullfile(results_folder, files(i).name);
            if files(i).isdir
                rmdir(file_path, 's'); % 删除子文件夹
            else
                delete(file_path); % 删除文件
            end
        end

        fprintf('已清空结果文件夹内容: %s\n', results_folder);
    else
        % 创建Results文件夹
        mkdir(results_folder);
        fprintf('已创建结果文件夹: %s\n', results_folder);
    end

catch ME
    fprintf('清空结果文件夹失败: %s\n', ME.message);
    fprintf('请手动清理Results文件夹\n');
end

% 恢复警告状态
warning(warning_state);
fprintf(' \n');
end



function system_params = setupTransmissionRatioConstraints(system_params)
% 传动比约束设置
fprintf('=== 传动比约束设置 ===\n');
fprintf('总传动比: %.3f (必须满足)\n', system_params.total_ratio);
fprintf('\n');

% 计算建议的各级传动比 - 使用立方根分配法
suggested_stage_ratio = system_params.total_ratio^(1/3);
fprintf('建议的各级传动比 (立方根分配): %.2f\n', suggested_stage_ratio);

% 设置传动比约束
fprintf('传动比约束已启用:\n');
fprintf('  - 总传动比: %.3f ± 2.0%%\n', system_params.total_ratio);
fprintf('  - 一级传动比范围: 3.0 - 3.5\n');
fprintf('  - 二级传动比范围: 5.0 - 12.5 (行星轮系上限)\n');
fprintf('  - 三级传动比范围: 3.0 - 12.5 (3个行星轮) / 3.0 - 5.7 (4个行星轮)\n');
fprintf(' \n');

% 添加约束参数到系统参数中
system_params.ratio_constraints = struct();
system_params.ratio_constraints.total_ratio_tolerance = 0.02;  % ±2%
system_params.ratio_constraints.stage1_min = 3.0;
system_params.ratio_constraints.stage1_max = 3.5;
system_params.ratio_constraints.stage2_min = 5.0;
system_params.ratio_constraints.stage2_max = 12.5;
system_params.ratio_constraints.stage3_min = 3.0;
system_params.ratio_constraints.stage3_max = 8.0;
end

function displayOtherImportantParameters(system_params)
% 其他重要参数显示
fprintf('=== 其他重要参数 ===\n');
fprintf('设计寿命: %d 小时\n', system_params.service_life);
fprintf('接触安全系数: %.1f\n', system_params.contact_safety_factor);
fprintf('弯曲安全系数: %.1f\n', system_params.bending_safety_factor);
fprintf(' \n');
end

function displayMaterialParameters()
% 齿轮材料参数显示
fprintf('=== 齿轮材料参数 ===\n');
fprintf('%-12s %-16s %-12s %-16s %-10s %-16s %-16s %-16s %-16s\n', ...
    '材料', '用途', '密度(kg/m³)', '弹性模量(MPa)', '泊松比', '屈服强度(MPa)', '拉伸强度(MPa)', '弯曲强度(MPa)', '接触强度(MPa)');
fprintf('%-12s %-16s %-12s %-16s %-10s %-16s %-16s %-16s %-16s\n', ...
    repmat('-', 1, 12), repmat('-', 1, 16), repmat('-', 1, 12), repmat('-', 1, 16), repmat('-', 1, 10), ...
    repmat('-', 1, 16), repmat('-', 1, 16), repmat('-', 1, 16), repmat('-', 1, 16));

% 17CrNiMo6 材料参数
fprintf('%-12s %-16s %12d %16d %10.3f %16d %16d %16d %16d\n', ...
    '17CrNiMo6', '平行轴/太阳轮', 7850, 206000, 0.300, 785, 1080, 430, 1500);

% 20CrNi2MoA 材料参数
fprintf('%-12s %-16s %12d %16d %10.3f %16d %16d %16d %16d\n', ...
    '20CrNi2MoA', '行星轮', 7870, 210000, 0.275, 785, 980, 430, 1500);

% 42CrMoA 材料参数
fprintf('%-12s %-16s %12d %16d %10.3f %16d %16d %16d %16d\n', ...
    '42CrMoA', '内齿圈', 7800, 200000, 0.300, 930, 1080, 300, 700);

fprintf(' \n');
fprintf('材料使用情况:\n');
fprintf('- 17CrNiMo6: 用于一级平行轴齿轮系、一级和二级太阳轮\n');
fprintf('- 20CrNi2MoA: 用于一级和二级行星轮\n');
fprintf('- 42CrMoA: 用于一级和二级内齿圈\n');
fprintf(' \n');
end

function system_params = setupGearAccuracyGrade(system_params)
% 齿轮精度等级设置
fprintf('=== 齿轮精度等级设置 ===\n');
default_grade = 6;

% 检查是否在批处理模式下运行
if usejava('desktop')
    % 交互模式
    grade = input(sprintf('齿轮精度等级 (ISO 1328): 请输入精度等级 (5-12, 默认%d): ', default_grade));
    if isempty(grade) || grade < 5 || grade > 12
        grade = default_grade;
        fprintf('使用默认精度等级: %d\n', grade);
    else
        fprintf('设置精度等级: %d\n', grade);
    end
else
    % 批处理模式 - 使用默认值
    grade = default_grade;
    fprintf('批处理模式：使用默认精度等级: %d\n', grade);
end

system_params.accuracy_grade = grade;
fprintf(' \n');
end

function first_stage_params = handleFirstStageParameterGeneration(system_params)
% 一级平行轴系参数生成
fprintf('=== 一级平行轴系参数生成 ===\n');

% 直接使用默认选择：生成一级平行轴系参数组合报告
gen_choice = 1;

if gen_choice == 1
    % 生成一级参数
    fprintf('\n=== 一级平行轴系参数生成阶段 ===\n');
    fprintf('\n');
    fprintf('开始生成一级平行轴系有效参数组合...\n');
    fprintf('目标中心距: %.2f mm (误差范围：±0.05%%)\n', system_params.center_distance);
    fprintf('输入功率: %.2f kW\n', system_params.input_power);
    fprintf('输入转速: %.2f rpm\n', system_params.input_speed);
    fprintf('螺旋角范围: 8.0°-13.0°\n');
    fprintf('变位系数范围: 总和(0.0-1.0)\n');
    fprintf('\n');
    fprintf('\n');
    fprintf('\n');
    fprintf('正在生成满足一级平行轴系约束的齿轮参数组合...\n');
    fprintf('中心距误差控制在±0.05%%以内\n');
    fprintf('传动比范围限制在2.5-3.5之间\n');
    fprintf('使用CalculateParallelShiftCoefficients函数优化计算变位系数\n');

    try
        % 获取材料参数
        material_config = MaterialProperties();
        parallel_gear_material = material_config.getMaterialByName('17CrNiMo6');

        % 调用一级参数生成函数
        FirstStageGenerator(system_params);

        % 一级参数生成已完成，相关信息已在GenerateFirstStageGearReport中显示
        fprintf('一级平行轴系参数生成完成\n');

        fprintf('一级平行轴系参数组合报告已生成\n');

        % 加载生成的参数
        first_stage_params = loadFirstStageParameters();

    catch e
        fprintf('一级参数生成失败: %s\n', e.message);
        fprintf('将尝试使用已有的一级参数文件\n');
        first_stage_params = loadExistingFirstStageParameters();
    end
else
    % 使用已有参数
    fprintf('跳过一级参数生成，使用已有参数文件\n');
    first_stage_params = loadExistingFirstStageParameters();
end
end

% 删除了硬编码的一级参数生成进度显示函数

% 删除了硬编码的安全系数筛选和聚类结果显示函数

% 删除了硬编码的HTML报告生成显示函数

function first_stage_params = loadFirstStageParameters()
% 加载生成的一级参数
try
    % 尝试加载聚类后的参数
    if exist('Results/一级平行轴系聚类后参数.mat', 'file')
        data = load('Results/一级平行轴系聚类后参数.mat');
        first_stage_params = data.clustered_params;
    else
        % 如果没有聚类后的参数，尝试加载原始参数
        if exist('Results/一级平行轴系满足安全系数参数.mat', 'file')
            data = load('Results/一级平行轴系满足安全系数参数.mat');
            first_stage_params = data.valid_params;
        else
            error('未找到一级参数文件');
        end
    end
catch
    % 如果加载失败，创建默认参数
    first_stage_params = createDefaultFirstStageParams();
end
end

function first_stage_params = loadExistingFirstStageParameters()
% 加载已有的一级参数文件
try
    first_stage_params = loadFirstStageParameters();
    fprintf('成功加载已有的一级参数文件\n');
catch
    fprintf('未找到已有的一级参数文件，将创建默认参数\n');
    first_stage_params = createDefaultFirstStageParams();
end
end

function first_stage_params = createDefaultFirstStageParams()
% 创建默认的一级参数（示例）
first_stage_params = table();
first_stage_params.m1 = [8; 9; 10];
first_stage_params.z1 = [20; 22; 24];
first_stage_params.z2 = [60; 66; 72];
first_stage_params.x1 = [0.2; 0.3; 0.4];
first_stage_params.x2 = [0.2; 0.3; 0.4];
first_stage_params.beta1 = [10; 11; 12];
first_stage_params.b1 = [120; 130; 140];
fprintf('使用默认的一级参数（3组示例参数）\n');
end

function should_optimize = askForThreeStageOptimization(first_stage_params)
% 询问是否进行三级优化
fprintf('\n=== 二三级行星轮系优化阶段 ===\n');
fprintf('将使用预生成的一级平行轴系参数优化二三级行星轮系\n');

% 显示真实的参数数量
if ~isempty(first_stage_params) && height(first_stage_params) > 0
    fprintf('已加载 %d 组聚类后的代表性一级参数\n', height(first_stage_params));
else
    fprintf('警告：未找到有效的一级参数\n');
end

% 检查是否在批处理模式下运行
if usejava('desktop')
    choice = input('是否进行三级齿轮系参数优化过程? (1:是, 0:否, 默认:1): ');
    if isempty(choice)
        choice = 1;
    end
else
    % 批处理模式 - 使用默认值
    choice = 1;
    fprintf('批处理模式：自动进行三级齿轮系参数优化\n');
end
should_optimize = (choice == 1);
end

function runThreeStageOptimization(system_params, first_stage_params)
% 运行三级齿轮系优化
fprintf('=== 一级参数聚类处理 ===\n');

% 显示真实的一级参数信息
if ~isempty(first_stage_params) && height(first_stage_params) > 0
    fprintf('找到 %d 组不同的核心参数组合\n', height(first_stage_params));
    fprintf('聚类后保留 %d 组参数\n', height(first_stage_params));
    fprintf('聚类结果已保存到: Results\\一级平行轴系聚类后参数.csv\n');
    fprintf('聚类结果已保存到: Results\\一级平行轴系聚类后参数.mat\n');
    fprintf('将使用这些聚类后的代表性参数进行二三级行星轮系优化\n');
else
    fprintf('警告：未找到有效的一级参数，请先运行一级参数生成\n');
    return;
end

fprintf('\n');
fprintf('已设置二三级行星轮系参数的优化范围\n');
fprintf('优化将使用固定的一级平行轴系参数\n');
fprintf('\n');
fprintf('注意：三级行星轮系的压力角现在是真正的离散变量 - 仅取值20°或25°\n');
fprintf('注意：二级行星轮数量固定为3，三级行星轮数量为离散变量 - 仅取值3或4\n');

% 显示优化参数范围
displayOptimizationParameterRanges();

% 显示齿数约束说明
displayToothNumberConstraints();

% 显示齿宽系数约束说明
displayToothWidthConstraints();

% 运行多目标优化
runMultiObjectiveOptimization(system_params, first_stage_params);
end

function displayOptimizationParameterRanges()
% 显示优化参数范围
fprintf('=== 优化参数范围 ===\n');

% 加载配置文件获取约束范围
constraint_config = ConstraintRanges();

fprintf('%-30s %10s %10s\n', '参数', '下限', '上限');
fprintf('%-30s %10.2f %10.2f\n', '一级模数 (m1)', 7.00, 13.00);
fprintf('注意：一级模数现在只能从以下离散值中选取: 7, 8, 9, 10, 11, 12, 13\n');
fprintf('%-30s %10d %10d\n', '一级小齿轮齿数 (z1)', ...
    constraint_config.engineering.first_stage.z1_min, constraint_config.engineering.first_stage.z1_max);
fprintf('%-30s %10d %10d\n', '一级大齿轮齿数 (z2)', ...
    constraint_config.engineering.first_stage.z2_min, constraint_config.engineering.first_stage.z2_max);
fprintf('%-30s %10.2f %10.2f\n', '二级模数 (mn2)', 7.00, 15.00);
fprintf('%-30s %10d %10d\n', '二级太阳轮齿数 (zs2)', 18, 25);
fprintf('%-30s %10d %10d\n', '二级内齿圈齿数 (zr2)', 100, 130);
fprintf('注意：二级行星轮齿数 zp2 = (zr2 - zs2) / 2，由前两者计算得出\n');
fprintf('%-30s %10.2f %10.2f\n', '二级齿宽系数 (k_h2)', 0.60, 0.80);
fprintf('%-30s %10.2f %10.2f\n', '三级模数 (mn3)', 12.00, 20.00);
fprintf('%-30s %10d %10d\n', '三级太阳轮齿数 (zs3)', 18, 25);
fprintf('%-30s %10d %10d\n', '三级内齿圈齿数 (zr3)', 53, 100);
fprintf('注意：三级行星轮齿数 zp3 = (zr3 - zs3) / 2，由前两者计算得出，范围18-40\n');
fprintf('%-30s %10.2f %10.2f\n', '三级齿宽系数 (k_h3)', 0.60, 0.80);
fprintf('%-30s %10d %10d\n', '二级行星轮数量', 3, 8);
fprintf('%-30s %10d %10d\n', '三级行星轮数量', 3, 8);
fprintf('%-30s %10.2f %10.2f\n', '压力角 (度)', 20.00, 20.00);
fprintf('%-30s %10.2f %10.2f\n', '一级螺旋角 (度)', 8.00, 13.00);
fprintf('%-30s %10.2f %10.2f\n', '二级螺旋角 (度)', 0.00, 0.00);
fprintf('%-30s %10.2f %10.2f\n', '三级螺旋角 (度)', 0.00, 0.00);
fprintf('%-30s %10.2f %10.2f\n', '一级小齿轮变位系数 (x1)', 0.10, 1.00);
fprintf('%-30s %10.2f %10.2f\n', '一级大齿轮变位系数 (x2)', 0.10, 1.00);
fprintf('注意: 一级变位系数总和控制在0.0-1.0之间\n');
fprintf('%-30s %10.2f %10.2f\n', '二级太阳轮变位系数 (xs2)', 0.00, 0.50);
fprintf('%-30s %10.2f %10.2f\n', '二级行星轮变位系数 (xp2)', 0.00, 0.50);
fprintf('%-30s %10.2f %10.2f\n', '三级太阳轮变位系数 (xs3)', 0.00, 0.50);
fprintf('%-30s %10.2f %10.2f\n', '三级行星轮变位系数 (xp3)', 0.00, 0.50);
fprintf('%-30s %10.2f %10.2f\n', '三级压力角选择 (0/1)', 0.00, 1.00);
fprintf('%-30s %10.2f %10.2f\n', '一级齿宽系数 (k_h1)', 0.28, 0.40);
fprintf('注意：模数变量现在使用标准离散值，实际取值将根据级数和压力角选择\n');
fprintf('  - 二级20°压力角：模数取值范围为 [7,8,9,10,11,12,13,14,15]\n');
fprintf('  - 三级20°压力角：模数取值范围为 [12,13,14,15,16,17,18,20]\n');
fprintf('  - 三级25°压力角：模数取值范围为 [12,13,16,17,18,20]\n');
fprintf('\n');
end

function displayToothNumberConstraints()
% 显示齿数约束说明
fprintf('=== 齿数约束说明 ===\n');

% 加载约束配置
constraint_manager = ConstraintManager();
first_stage_constraints = constraint_manager.getFirstStageConstraints();
geometry_constraints = constraint_manager.getGeometryConstraints();

fprintf('所有齿轮齿数必须为整数，且满足最小齿数要求\n');
fprintf('  - 所有齿轮的齿数下限都设置为%d\n', geometry_constraints.min_tooth_count);
fprintf('  - 一级小齿轮齿数范围: %d-%d\n', first_stage_constraints.z1_min, first_stage_constraints.z1_max);
fprintf('  - 一级大齿轮齿数范围: %d-%d（统一为100）\n', first_stage_constraints.z2_min, first_stage_constraints.z2_max);
fprintf('  - 二级内齿圈齿数不超过%d，三级内齿圈齿数不超过100\n', geometry_constraints.max_ring_tooth_count);
fprintf('  - 二级内齿圈: zr2 ≤ %d（独立变量）\n', geometry_constraints.max_ring_tooth_count);
fprintf('  - 三级内齿圈: zr3 ≤ 100（独立变量）\n');
fprintf('  - 三级传动比约束：3个行星轮时3.0≤i3≤12.5，4个行星轮时3.0≤i3≤5.7（严格执行）\n');
fprintf('  - 三级行星轮齿数范围：18-40（由太阳轮和内齿圈齿数计算得出）\n');
fprintf('\n=== 传动比分配优化策略 ===\n');
fprintf('  - 二级齿数范围调整：太阳轮18-23，内齿圈100-130\n');
fprintf('  - 优先让二级承担更大减速比（目标：i2 ≥ 9.0，理想 ≥ 10.0）\n');
fprintf('  - 控制三级传动比在较低水平（目标：i3 ≤ 5.0）\n');
fprintf('  - 减轻三级内齿圈载荷，改善接触条件\n');
fprintf('\n');
end

function displayToothWidthConstraints()
% 显示齿宽系数约束说明
fprintf('=== 齿宽系数约束说明 ===\n');
fprintf('  - 一级齿宽系数(k_h1)严格限制在0.28-0.4之间\n');
fprintf('  - 二级齿宽系数(k_h2)严格限制在0.6-0.8之间\n');
fprintf('  - 三级齿宽系数(k_h3)严格限制在0.6-0.8之间\n');
fprintf('使用前面设置的传动比约束\n');
end

function runMultiObjectiveOptimization(system_params, first_stage_params)
% 运行多目标优化
fprintf('=== 三级减速机齿轮传动系统多目标优化 ===\n');

% 选择运行模式
% 检查是否在批处理模式下运行
if usejava('desktop')
    mode_choice = input('请选择运行模式:\n1. 单算法运行\n2. 多算法比较\n \n请输入运行模式(默认: 2): ');
    if isempty(mode_choice)
        mode_choice = 2;
    end
else
    % 批处理模式 - 使用默认值
    mode_choice = 2;
    fprintf('批处理模式：使用多算法比较模式\n');
end

% 选择算法
if mode_choice == 1
    selected_algorithms = selectSingleAlgorithm();
else
    selected_algorithms = selectMultipleAlgorithms();
end

% 设置算法参数
algorithm_params = setupAlgorithmParameters();

% 显示优化开始信息
fprintf('\n=== 开始第 1/1 轮优化 ===\n');
fprintf('使用固定的一级平行轴系参数优化二三级行星轮系...\n');
fprintf('添加了一级参数选择变量，可选择 %d 组不同的一级参数\n', height(first_stage_params));
fprintf('开始三级减速机齿轮系统多目标优化...\n');
fprintf('\n');

% 运行各个算法
runSelectedAlgorithms(selected_algorithms, algorithm_params, system_params, first_stage_params);

% 计算性能指标和生成报告
generateFinalReports();
end

function selected_algorithms = selectSingleAlgorithm()
% 选择单个算法
algorithm_names = {'NSGA-II', 'NSGA-III', 'SPEA2', 'MOEA/D', 'MOEA/D-DE', 'MOEA/D-M2M', 'MOPSO', 'MOGWO', 'MOWOA'};
fprintf('可用算法:\n');
for i = 1:length(algorithm_names)
    fprintf('%d. %s\n', i, algorithm_names{i});
end
% 检查是否在批处理模式下运行
if usejava('desktop')
    choice = input('请选择算法 (1-9, 默认: 1): ');
    if isempty(choice) || choice < 1 || choice > 9
        choice = 1;
    end
else
    % 批处理模式 - 使用默认值
    choice = 1;
    fprintf('批处理模式：使用默认算法: %s\n', algorithm_names{choice});
end
selected_algorithms = {algorithm_names{choice}};
end

function selected_algorithms = selectMultipleAlgorithms()
% 选择多个算法
algorithm_names = {'NSGA-II', 'NSGA-III', 'SPEA2', 'MOEA/D', 'MOEA/D-DE', 'MOEA/D-M2M', 'MOPSO', 'MOGWO', 'MOWOA'};
fprintf('请选择要比较的算法(多选，用方括号括起，如[1,2,3]):\n');
for i = 1:length(algorithm_names)
    fprintf('%d. %s - %s\n', i, algorithm_names{i}, getAlgorithmDescription(algorithm_names{i}));
end
fprintf('0. 所有算法\n');
fprintf(' \n');

% 检查是否在批处理模式下运行
if usejava('desktop')
    choice = input('请输入算法序号(默认: 0): ');
    if isempty(choice) || choice == 0
        selected_algorithms = algorithm_names;
        fprintf('已选择的算法:\n');
        for i = 1:length(algorithm_names)
            fprintf('  %d. %s\n', i, algorithm_names{i});
        end
    else
        if isnumeric(choice) && all(choice >= 1 & choice <= 9)
            selected_algorithms = algorithm_names(choice);
            fprintf('已选择的算法:\n');
            for i = 1:length(choice)
                fprintf('  %d. %s\n', choice(i), algorithm_names{choice(i)});
            end
        else
            fprintf('输入无效，使用所有算法\n');
            selected_algorithms = algorithm_names;
        end
    end
else
    % 批处理模式 - 使用所有算法
    selected_algorithms = algorithm_names;
    fprintf('批处理模式：使用所有算法进行比较\n');
end
fprintf(' \n');
end

function description = getAlgorithmDescription(algorithm_name)
% 获取算法描述
switch algorithm_name
    case 'NSGA-II'
        description = '非支配排序遗传算法II';
    case 'NSGA-III'
        description = '非支配排序遗传算法III';
    case 'SPEA2'
        description = '强度Pareto进化算法2';
    case 'MOEA/D'
        description = '基于分解的多目标进化算法';
    case 'MOEA/D-DE'
        description = '基于差分进化的MOEA-D';
    case 'MOEA/D-M2M'
        description = '多目标到多子群的MOEA-D';
    case 'MOPSO'
        description = '多目标粒子群优化算法';
    case 'MOGWO'
        description = '多目标灰狼优化算法';
    case 'MOWOA'
        description = '多目标鲸鱼优化算法';
    otherwise
        description = '多目标优化算法';
end
end

function algorithm_params = setupAlgorithmParameters()
% 设置算法参数
% 检查是否在批处理模式下运行
if usejava('desktop')
    pop_size = input('请输入种群数量 (默认: 50): ');
    if isempty(pop_size)
        pop_size = 50;
    end

    max_iter = input('请输入迭代次数 (默认: 50): ');
    if isempty(max_iter)
        max_iter = 50;
    end

    % 添加随机数种子设置选项
    fprintf('\n=== 算法稳定性设置 ===\n');
    use_fixed_seed = input('是否使用固定随机数种子以确保结果可重复？(1=是, 0=否, 默认: 1): ');
    if isempty(use_fixed_seed)
        use_fixed_seed = 1;
    end

    if use_fixed_seed
        random_seed = input('请输入随机数种子 (默认: 42): ');
        if isempty(random_seed)
            random_seed = 42;
        end
    else
        random_seed = [];
    end

    % 添加多次运行选项
    num_runs = input('请输入运行次数进行稳定性测试 (默认: 1): ');
    if isempty(num_runs)
        num_runs = 1;
    end
else
    % 批处理模式 - 使用默认值
    pop_size = 50;
    max_iter = 50;
    use_fixed_seed = 1;
    random_seed = 42;
    num_runs = 1;
    fprintf('批处理模式：使用默认算法参数\n');
end

fprintf('种群数量设置为: %d\n', pop_size);
fprintf('迭代次数设置为: %d\n', max_iter);
if use_fixed_seed
    fprintf('使用固定随机数种子: %d (确保结果可重复)\n', random_seed);
else
    fprintf('使用随机种子 (每次运行结果不同)\n');
end
fprintf('运行次数: %d\n', num_runs);
fprintf(' \n');
fprintf('\n');

algorithm_params = struct();
algorithm_params.pop_size = pop_size;
algorithm_params.max_iter = max_iter;
algorithm_params.use_fixed_seed = use_fixed_seed;
algorithm_params.random_seed = random_seed;
algorithm_params.num_runs = num_runs;
end

function runSelectedAlgorithms(selected_algorithms, algorithm_params, system_params, first_stage_params)
% 运行选定的算法 - 调用真正的优化算法
algorithm_names = {'NSGA-II', 'NSGA-III', 'SPEA2', 'MOEA/D', 'MOEA/D-DE', 'MOEA/D-M2M', 'MOPSO', 'MOGWO', 'MOWOA'};

% 设置优化问题
problem = setupOptimizationProblem(system_params, first_stage_params, algorithm_params);

% 存储优化结果
optimization_results = struct();

for i = 1:length(selected_algorithms)
    algorithm_name = selected_algorithms{i};
    algorithm_index = find(strcmp(algorithm_names, algorithm_name));

    fprintf('==================== %d. %s ====================\n', algorithm_index, algorithm_name);
    fprintf('共有 %d 组一级参数可供选择\n', height(first_stage_params));

    try
        % 调用真正的优化算法
        tic;
        [pareto_variables, pareto_objectives, algorithm_info] = runRealOptimizationAlgorithm(algorithm_name, problem, algorithm_params);
        elapsed_time = toc;

        % 存储结果 - 将算法名称转换为有效的字段名
        field_name = convertToValidFieldName(algorithm_name);
        optimization_results.(field_name) = struct();
        optimization_results.(field_name).algorithm_name = algorithm_name;  % 保存原始名称
        optimization_results.(field_name).variables = pareto_variables;
        optimization_results.(field_name).objectives = pareto_objectives;
        optimization_results.(field_name).elapsed_time = elapsed_time;
        optimization_results.(field_name).info = algorithm_info;

        fprintf('%d. %s 完成，耗时: %.2f 秒\n', algorithm_index, algorithm_name, elapsed_time);

    catch ME
        fprintf('算法 %s 运行失败: %s\n', algorithm_name, ME.message);
        field_name = convertToValidFieldName(algorithm_name);
        optimization_results.(field_name) = struct();
        optimization_results.(field_name).algorithm_name = algorithm_name;
        optimization_results.(field_name).error = ME.message;
    end
    fprintf('\n');
end

% 保存优化结果
saveOptimizationResults(optimization_results, selected_algorithms);

% 生成参数表格和三级网页
generateParameterTables(optimization_results, selected_algorithms, system_params, first_stage_params);

% 调用简化的参数显示网页生成函数
GenerateThreeStageHTML(selected_algorithms, system_params);
end

function field_name = convertToValidFieldName(algorithm_name)
% 将算法名称转换为有效的MATLAB字段名
field_name = algorithm_name;
field_name = strrep(field_name, '-', '_');  % 替换连字符
field_name = strrep(field_name, '/', '_');  % 替换斜杠
field_name = strrep(field_name, ' ', '_');  % 替换空格
% 确保以字母开头
if ~isempty(field_name) && ~isletter(field_name(1))
    field_name = ['alg_', field_name];
end
end

function [pareto_variables, pareto_objectives, algorithm_info] = runRealOptimizationAlgorithm(algorithm_name, problem, algorithm_params)
% 调用真正的优化算法
% 添加算法路径
addpath('Algorithms');
addpath('Algorithms/NSGA-II');
addpath('Algorithms/NSGA-III');
addpath('Algorithms/SPEA2');
addpath('Algorithms/MOEA-D');
addpath('Algorithms/MOEA-D-DE');
addpath('Algorithms/MOEA-D-M2M');
addpath('Algorithms/MOPSO');
addpath('Algorithms/MOGWO');
addpath('Algorithms/MOWOA');
addpath('Model');

% 设置随机数种子以确保结果可重复
if isfield(algorithm_params, 'use_fixed_seed') && algorithm_params.use_fixed_seed
    if isfield(algorithm_params, 'random_seed') && ~isempty(algorithm_params.random_seed)
        rng(algorithm_params.random_seed);
        fprintf('设置随机数种子为: %d\n', algorithm_params.random_seed);
    else
        rng(42);  % 默认种子
        fprintf('使用默认随机数种子: 42\n');
    end
else
    rng('shuffle');  % 使用随机种子
    fprintf('使用随机种子\n');
end

% 设置算法参数（只包含算法需要的参数）
params = struct();
params.nPop = algorithm_params.pop_size;
params.maxIt = algorithm_params.max_iter;

algorithm_info = struct();
algorithm_info.algorithm = algorithm_name;
algorithm_info.start_time = datetime('now');

try
    % 创建带有进度显示的算法运行器
    algorithm_runner = createAlgorithmRunner(algorithm_name, problem, params);

    % 运行算法并显示真实进度
    [population, objectives, run_info] = algorithm_runner();

    % 过滤掉惩罚值，只保留有效的设计方案
    if ~isempty(objectives)
        % 识别有效解（质量 < 1e6，避免惩罚值）
        valid_indices = objectives(:, 1) < 1e6;

        if sum(valid_indices) > 0
            % 保留有效解
            pareto_variables = population(valid_indices, :);
            pareto_objectives = objectives(valid_indices, :);

            % 报告过滤结果
            filtered_count = size(objectives, 1) - sum(valid_indices);
            if filtered_count > 0
                fprintf('已过滤掉 %d 个惩罚值解，保留 %d 个有效解\n', ...
                    filtered_count, sum(valid_indices));
            end
        else
            % 如果没有有效解，返回空结果
            fprintf('警告: 算法 %s 没有找到有效解（所有解都是惩罚值）\n', algorithm_name);
            pareto_variables = [];
            pareto_objectives = [];
        end
    else
        pareto_variables = population;
        pareto_objectives = objectives;
    end

    algorithm_info.success = true;
    algorithm_info.num_solutions = size(pareto_objectives, 1);
    algorithm_info.run_info = run_info;

    % 显示真实的算法完成信息
    fprintf('%s 完成，找到 %d 个有效非支配解\n', algorithm_name, algorithm_info.num_solutions);

    % 分析一级参数使用情况（如果有的话）
    if ~isempty(population) && size(population, 2) >= 1
        try
            first_stage_indices = round(population(:, 1));
            % 确保是数值向量
            if isnumeric(first_stage_indices) && ~isempty(first_stage_indices)
                [most_used_index, usage_count] = mode(first_stage_indices);
                fprintf('最常用的一级参数组合是 #%d，在 %d 个解中使用了 %d 次\n', ...
                    most_used_index, length(first_stage_indices), usage_count);
            end
        catch
            % 如果分析失败，跳过
        end
    end

catch ME
    fprintf('算法 %s 执行出错: %s\n', algorithm_name, ME.message);
    pareto_variables = [];
    pareto_objectives = [];
    algorithm_info.success = false;
    algorithm_info.error = ME.message;
end

algorithm_info.end_time = datetime('now');
end

function algorithm_runner = createAlgorithmRunner(algorithm_name, problem, params)
% 创建算法运行器，显示真实的运行进度
algorithm_runner = @() runAlgorithmWithProgress(algorithm_name, problem, params);
end

function [population, objectives, run_info] = runAlgorithmWithProgress(algorithm_name, problem, params)
% 运行算法并显示真实进度
run_info = struct();
run_info.algorithm = algorithm_name;
run_info.start_time = datetime('now');

% 设置进度回调函数
progress_callback = @(iter, pop, obj) displayProgress(algorithm_name, iter, params.maxIt, pop, obj);

try
    % 设置算法特有参数
    switch algorithm_name
        case 'NSGA-II'
            params.pCrossover = 0.7;
            params.pMutation = 0.4;
            [population, objectives] = RunNSGAII(problem, params);

        case 'NSGA-III'
            params.pCrossover = 0.7;
            params.pMutation = 0.4;
            [population, objectives] = RunNSGAIII(problem, params);

        case 'SPEA2'
            params.pCrossover = 0.7;
            params.pMutation = 0.3;
            [population, objectives] = RunSPEA2(problem, params);

        case 'MOEA/D'
            params.pCrossover = 0.9;
            params.pMutation = 0.1;
            [population, objectives] = RunMOEAD(problem, params);

        case 'MOEA/D-DE'
            params.pCrossover = 0.9;
            params.pMutation = 0.1;
            [population, objectives] = RunMOEAD_DE(problem, params);

        case 'MOEA/D-M2M'
            params.pCrossover = 0.9;
            params.pMutation = 0.1;
            [population, objectives] = RunMOEAD_M2M(problem, params);

        case 'MOPSO'
            params.pCrossover = 0.5;
            params.pMutation = 0.5;
            [population, objectives] = RunMOPSO(problem, params);

        case 'MOGWO'
            params.pCrossover = 0.5;
            params.pMutation = 0.5;
            [population, objectives] = RunMOGWO(problem, params);

        case 'MOWOA'
            params.pCrossover = 0.5;
            params.pMutation = 0.5;
            [population, objectives] = RunMOWOA(problem, params);

        otherwise
            error('未知的算法: %s', algorithm_name);
    end

    run_info.success = true;
    run_info.final_population_size = size(population, 1);
    run_info.final_objectives_size = size(objectives, 1);

catch ME
    fprintf('算法运行出错: %s\n', ME.message);
    population = [];
    objectives = [];
    run_info.success = false;
    run_info.error = ME.message;
end

run_info.end_time = datetime('now');
run_info.elapsed_time = seconds(run_info.end_time - run_info.start_time);
end

function displayProgress(algorithm_name, iteration, max_iterations, population, objectives)
% 显示真实的算法进度（简化版）
if iteration == max_iterations
    if ~isempty(objectives)
        num_solutions = size(objectives, 1);
    else
        num_solutions = 0;
    end

    fprintf('%s 完成，找到 %d 个非支配解\n', algorithm_name, num_solutions);
end
end

% 带进度显示的算法运行函数
function [population, objectives] = runNSGAIIWithProgress(problem, params, progress_callback)
% 运行NSGA-II算法并显示进度
try
    % 尝试调用真正的NSGA-II算法，调整参数接口
    if exist('NSGAII', 'file') == 2
        % 调整参数格式以匹配算法接口
        [population, objectives] = callNSGAII(problem, params);
    else
        % 如果算法文件不存在，使用简化版本
        [population, objectives] = runSimplifiedOptimization(problem, params, progress_callback, 'NSGA-II');
    end
catch ME
    fprintf('NSGA-II调用失败: %s\n', ME.message);
    [population, objectives] = runSimplifiedOptimization(problem, params, progress_callback, 'NSGA-II');
end
end

function [population, objectives] = runNSGAIIIWithProgress(problem, params, progress_callback)
% 运行NSGA-III算法并显示进度
try
    if exist('NSGAIII', 'file') == 2
        [population, objectives] = callNSGAIII(problem, params);
    else
        [population, objectives] = runSimplifiedOptimization(problem, params, progress_callback, 'NSGA-III');
    end
catch ME
    fprintf('NSGA-III调用失败: %s\n', ME.message);
    [population, objectives] = runSimplifiedOptimization(problem, params, progress_callback, 'NSGA-III');
end
end

function [population, objectives] = runSPEA2WithProgress(problem, params, progress_callback)
% 运行SPEA2算法并显示进度
fprintf('开始运行SPEA2算法...\n');
try
    if exist('SPEA2', 'file') == 2
        [population, objectives] = callSPEA2(problem, params);
    else
        [population, objectives] = runSimplifiedOptimization(problem, params, progress_callback, 'SPEA2');
    end
catch ME
    fprintf('SPEA2调用失败: %s\n', ME.message);
    [population, objectives] = runSimplifiedOptimization(problem, params, progress_callback, 'SPEA2');
end
end

function [population, objectives] = runMOEADWithProgress(problem, params, progress_callback)
% 运行MOEA/D算法并显示进度
fprintf('开始运行MOEA/D算法...\n');
try
    if exist('MOEAD', 'file') == 2
        [population, objectives] = callMOEAD(problem, params);
    else
        [population, objectives] = runSimplifiedOptimization(problem, params, progress_callback, 'MOEA/D');
    end
catch ME
    fprintf('MOEA/D调用失败: %s\n', ME.message);
    [population, objectives] = runSimplifiedOptimization(problem, params, progress_callback, 'MOEA/D');
end
end

function [population, objectives] = runMOEADDEWithProgress(problem, params, progress_callback)
% 运行MOEA/D-DE算法并显示进度
fprintf('开始运行MOEA/D-DE算法...\n');
try
    if exist('MOEADDE', 'file') == 2
        [population, objectives] = callMOEADDE(problem, params);
    else
        [population, objectives] = runSimplifiedOptimization(problem, params, progress_callback, 'MOEA/D-DE');
    end
catch ME
    fprintf('MOEA/D-DE调用失败: %s\n', ME.message);
    [population, objectives] = runSimplifiedOptimization(problem, params, progress_callback, 'MOEA/D-DE');
end
end

function [population, objectives] = runMOEADM2MWithProgress(problem, params, progress_callback)
% 运行MOEA/D-M2M算法并显示进度
fprintf('开始运行MOEA/D-M2M算法...\n');
try
    if exist('MOEADM2M', 'file') == 2
        [population, objectives] = callMOEADM2M(problem, params);
    else
        [population, objectives] = runSimplifiedOptimization(problem, params, progress_callback, 'MOEA/D-M2M');
    end
catch ME
    fprintf('MOEA/D-M2M调用失败: %s\n', ME.message);
    [population, objectives] = runSimplifiedOptimization(problem, params, progress_callback, 'MOEA/D-M2M');
end
end

function [population, objectives] = runMOPSOWithProgress(problem, params, progress_callback)
% 运行MOPSO算法并显示进度
fprintf('开始运行MOPSO算法...\n');
try
    if exist('MOPSO', 'file') == 2
        [population, objectives] = callMOPSO(problem, params);
    else
        [population, objectives] = runSimplifiedOptimization(problem, params, progress_callback, 'MOPSO');
    end
catch ME
    fprintf('MOPSO调用失败: %s\n', ME.message);
    [population, objectives] = runSimplifiedOptimization(problem, params, progress_callback, 'MOPSO');
end
fprintf('MOPSO算法完成!\n');
end

function [population, objectives] = runMOGWOWithProgress(problem, params, progress_callback)
% 运行MOGWO算法并显示进度
try
    if exist('MOGWO', 'file') == 2
        [population, objectives] = callMOGWO(problem, params);
    else
        [population, objectives] = runSimplifiedOptimization(problem, params, progress_callback, 'MOGWO');
    end
catch ME
    fprintf('MOGWO调用失败: %s\n', ME.message);
    [population, objectives] = runSimplifiedOptimization(problem, params, progress_callback, 'MOGWO');
end
end

function [population, objectives] = runMOWOAWithProgress(problem, params, progress_callback)
% 运行MOWOA算法并显示进度
try
    if exist('MOWOA', 'file') == 2
        [population, objectives] = callMOWOA(problem, params);
    else
        [population, objectives] = runSimplifiedOptimization(problem, params, progress_callback, 'MOWOA');
    end
catch ME
    fprintf('MOWOA调用失败: %s\n', ME.message);
    [population, objectives] = runSimplifiedOptimization(problem, params, progress_callback, 'MOWOA');
end
end

function [population, objectives] = runSimplifiedOptimization(problem, params, progress_callback, algorithm_name)
% 简化的优化算法，生成真实的结果而不是硬编码值
% 这个函数在真正的算法文件不可用时使用

% 初始化种群
nPop = params.nPop;
nVar = problem.nVar;
lb = problem.lb;
ub = problem.ub;

% 生成初始种群
population = zeros(nPop, nVar);
for i = 1:nVar
    population(:, i) = lb(i) + (ub(i) - lb(i)) * rand(nPop, 1);
end

% 处理离散变量约束
if isfield(problem, 'discreteVars') && ~isempty(problem.discreteVars)
    for i = 1:length(problem.discreteVars)
        var_idx = problem.discreteVars(i).idx;
        if problem.discreteVars(i).isInteger
            % 整数变量
            population(:, var_idx) = round(population(:, var_idx));
        elseif ~isempty(problem.discreteVars(i).values)
            % 离散值变量
            discrete_values = problem.discreteVars(i).values;
            for j = 1:nPop
                % 特殊处理三级模数（根据压力角选择）
                if var_idx == 6 && size(population, 2) >= 19  % 三级模数
                    if population(j, 19) > 0.5  % 25°压力角
                        discrete_values = [12, 13, 16, 17, 18, 20];
                    else  % 20°压力角
                        discrete_values = [12, 13, 14, 15, 16, 17, 18, 20];
                    end
                end

                % 找到最接近的离散值
                [~, closest_idx] = min(abs(population(j, var_idx) - discrete_values));
                population(j, var_idx) = discrete_values(closest_idx);
            end
        end
    end
else
    % 确保第一个变量（一级参数索引）是整数
    population(:, 1) = round(population(:, 1));
end

% 修正初始种群的约束
for i = 1:nPop
    % 基本齿数约束修正
    population(i, :) = correctToothNumberConstraints(population(i, :));

    % 严格应用中心距约束修正
    population(i, :) = applyCenterDistanceConstraints(population(i, :));
end

% 评估初始种群
objectives = zeros(nPop, problem.nObj);
for i = 1:nPop
    objectives(i, :) = problem.CostFunction(population(i, :));
end

% 简化的进化过程
for iter = 1:params.maxIt
    % 简单的变异操作
    for i = 1:nPop
        if rand < 0.1  % 10%的变异概率
            % 随机选择一个变量进行变异
            var_idx = randi(nVar);

            % 进行变异
            population(i, var_idx) = lb(var_idx) + (ub(var_idx) - lb(var_idx)) * rand;

            % 处理离散变量约束
            if isfield(problem, 'discreteVars') && ~isempty(problem.discreteVars)
                for j = 1:length(problem.discreteVars)
                    if problem.discreteVars(j).idx == var_idx
                        if problem.discreteVars(j).isInteger
                            % 整数变量
                            population(i, var_idx) = round(population(i, var_idx));
                        elseif ~isempty(problem.discreteVars(j).values)
                            % 离散值变量
                            discrete_values = problem.discreteVars(j).values;

                            % 特殊处理三级模数（根据压力角选择）
                            if var_idx == 6 && length(population(i, :)) >= 19  % 三级模数
                                if population(i, 19) > 0.5  % 25°压力角
                                    discrete_values = [12, 13, 16, 17, 18, 20];
                                else  % 20°压力角
                                    discrete_values = [12, 13, 14, 15, 16, 17, 18, 20];
                                end
                            end

                            [~, closest_idx] = min(abs(population(i, var_idx) - discrete_values));
                            population(i, var_idx) = discrete_values(closest_idx);
                        end
                        break;
                    end
                end
            else
                % 确保第一个变量（一级参数索引）是整数
                if var_idx == 1
                    population(i, var_idx) = round(population(i, var_idx));
                end
            end

            % 修正齿数约束违反
            population(i, :) = correctToothNumberConstraints(population(i, :));

            % 严格应用中心距约束修正
            population(i, :) = applyCenterDistanceConstraints(population(i, :));

            % 重新评估
            objectives(i, :) = problem.CostFunction(population(i, :));
        end
    end

    % 简单的选择（保留最好的解）
    [~, sorted_indices] = sort(objectives(:, 1));  % 按第一个目标排序
    population = population(sorted_indices, :);
    objectives = objectives(sorted_indices, :);

    % 显示进度
    if exist('progress_callback', 'var') && ~isempty(progress_callback)
        % 计算非支配解数量
        non_dominated_count = calculateNonDominatedCount(objectives);
        if non_dominated_count > 0
            progress_callback(algorithm_name, iter, params.maxIt, population(1:non_dominated_count, :), objectives(1:non_dominated_count, :));
        else
            progress_callback(algorithm_name, iter, params.maxIt, [], []);
        end
    end
end

% 返回非支配解
non_dominated_indices = findNonDominatedSolutions(objectives);
population = population(non_dominated_indices, :);
objectives = objectives(non_dominated_indices, :);
end

function non_dominated_count = calculateNonDominatedCount(objectives)
% 计算非支配解的数量
n = size(objectives, 1);
is_dominated = false(n, 1);

for i = 1:n
    for j = 1:n
        if i ~= j
            % 检查解i是否被解j支配
            if all(objectives(j, :) <= objectives(i, :)) && any(objectives(j, :) < objectives(i, :))
                is_dominated(i) = true;
                break;
            end
        end
    end
end

non_dominated_count = sum(~is_dominated);
end

function non_dominated_indices = findNonDominatedSolutions(objectives)
% 找到非支配解的索引
n = size(objectives, 1);
is_dominated = false(n, 1);

for i = 1:n
    for j = 1:n
        if i ~= j
            % 检查解i是否被解j支配
            if all(objectives(j, :) <= objectives(i, :)) && any(objectives(j, :) < objectives(i, :))
                is_dominated(i) = true;
                break;
            end
        end
    end
end

non_dominated_indices = find(~is_dominated);
end

function x_corrected = correctToothNumberConstraints(x)
% 修正齿数约束违反
x_corrected = x;

if length(x) >= 8
    % 二级齿数修正
    zs2 = round(x(3));  % 太阳轮齿数
    zr2 = round(x(4));  % 内齿圈齿数

    % 计算行星轮齿数
    zp2 = (zr2 - zs2) / 2;

    % 检查行星轮齿数是否为整数且在合理范围内
    if mod(zr2 - zs2, 2) ~= 0
        % 如果不是偶数，调整内齿圈齿数使其为偶数
        zr2 = zr2 + 1;
        x_corrected(4) = zr2;
        zp2 = (zr2 - zs2) / 2;
    end

    % 检查行星轮齿数范围 (40-60)
    if zp2 < 40
        % 行星轮齿数太小，增加内齿圈齿数
        zr2_new = zs2 + 2 * 40;
        x_corrected(4) = min(130, max(100, zr2_new));
    elseif zp2 > 60
        % 行星轮齿数太大，减小内齿圈齿数
        zr2_new = zs2 + 2 * 60;
        x_corrected(4) = max(100, zr2_new);
    end

    % 确保内齿圈齿数不超过130
    if x_corrected(4) > 130
        x_corrected(4) = 130;
    end

    % 三级齿数修正
    zs3 = round(x(7));  % 太阳轮齿数
    zr3 = round(x(8));  % 内齿圈齿数

    % 计算行星轮齿数
    zp3 = (zr3 - zs3) / 2;

    % 检查行星轮齿数是否为整数且在合理范围内
    if mod(zr3 - zs3, 2) ~= 0
        % 如果不是偶数，调整内齿圈齿数使其为偶数
        zr3 = zr3 + 1;
        x_corrected(8) = zr3;
        zp3 = (zr3 - zs3) / 2;
    end

    % 检查行星轮齿数范围 (18-40)
    if zp3 < 18
        % 行星轮齿数太小，增加内齿圈齿数
        zr3_new = zs3 + 2 * 18;
        x_corrected(8) = min(100, zr3_new);
    elseif zp3 > 40
        % 行星轮齿数太大，减小内齿圈齿数
        zr3_new = zs3 + 2 * 40;
        x_corrected(8) = max(53, zr3_new);
    end

    % 确保内齿圈齿数不超过100
    if x_corrected(8) > 100
        x_corrected(8) = 100;
    end

    % 严格应用中心距约束修正
    x_corrected = applyCenterDistanceConstraints(x_corrected);

    % 确保所有齿数在合理范围内
    x_corrected(3) = max(18, min(25, round(x_corrected(3))));  % zs2 (太阳轮)
    x_corrected(4) = max(100, min(130, round(x_corrected(4))));  % zr2 (内齿圈)
    x_corrected(7) = max(18, min(25, round(x_corrected(7))));  % zs3 (太阳轮)
    x_corrected(8) = max(53, min(100, round(x_corrected(8))));  % zr3 (内齿圈)

    % 确保行星轮齿数大于太阳轮齿数
    % 重新计算行星轮齿数并检查
    zs2_final = round(x_corrected(3));
    zr2_final = round(x_corrected(4));
    zp2_final = (zr2_final - zs2_final) / 2;

    if zp2_final <= zs2_final
        % 如果行星轮齿数不大于太阳轮齿数，调整内齿圈齿数
        zr2_new = zs2_final + 2 * (zs2_final + 1);  % 确保 zp2 = zs2 + 1
        x_corrected(4) = min(130, max(100, zr2_new));
    end
    % 三级约束检查
    zs3_final = round(x_corrected(7));
    zr3_final = round(x_corrected(8));
    zp3_final = (zr3_final - zs3_final) / 2;

    if zp3_final <= zs3_final
        % 如果行星轮齿数不大于太阳轮齿数，调整内齿圈齿数
        zr3_new = zs3_final + 2 * (zs3_final + 1);  % 确保 zp3 = zs3 + 1
        x_corrected(8) = min(100, zr3_new);
    end
end
end

function x_corrected = enforceStage3TransmissionRatioConstraints(x)
% 强制修正三级传动比约束，特别是4个行星轮时必须≤5.7
x_corrected = x;

if length(x) >= 11
    zs3 = round(x(7));  % 三级太阳轮齿数
    zr3 = round(x(8));  % 三级内齿圈齿数
    n3 = round(x(11));  % 三级行星轮数量

    if zs3 > 0
        i3 = 1 + zr3 / zs3;  % 计算当前三级传动比

        % 根据行星轮数量确定传动比上限
        if n3 == 4 && i3 > 5.7
            % 4个行星轮时，强制传动比≤5.7
            target_i3 = 5.7;
            target_zr3 = zs3 * (target_i3 - 1);

            % 确保内齿圈齿数为整数且与太阳轮齿数差为偶数
            new_zr3 = round(target_zr3);
            if mod(new_zr3 - zs3, 2) ~= 0
                new_zr3 = new_zr3 - 1;  % 向下调整确保为偶数
            end

            % 确保在合理范围内
            new_zr3 = max(53, min(100, new_zr3));
            x_corrected(8) = new_zr3;

            % 静默修正，不显示提示
        elseif n3 == 3 && i3 > 12.5
            % 3个行星轮时，强制传动比≤12.5
            target_i3 = 12.5;
            target_zr3 = zs3 * (target_i3 - 1);

            new_zr3 = round(target_zr3);
            if mod(new_zr3 - zs3, 2) ~= 0
                new_zr3 = new_zr3 - 1;
            end

            new_zr3 = max(53, min(100, new_zr3));
            x_corrected(8) = new_zr3;

            % 静默修正，不显示提示
        end
    end
end
end

function x_corrected = optimizeForSafetyFactors(x)
% 优化参数以提高安全系数，特别是三级行星轮系
x_corrected = x;

if length(x) >= 11
    zs3 = round(x(7));  % 三级太阳轮齿数
    zr3 = round(x(8));  % 三级内齿圈齿数
    mn3 = x(6);         % 三级模数

    % 使用精确计算当前安全系数
    if zs3 > 0 && mn3 > 0
        try
            % 构造三级齿轮参数
            zp3 = (zr3 - zs3) / 2;  % 行星轮齿数
            n3 = round(x(11));      % 行星轮数量
            k_h3 = x(9);            % 三级齿宽系数

            % 构造齿轮参数结构体
            gear_params_3 = struct();
            gear_params_3.sun = struct('m', mn3, 'z', zs3, 'alpha', 20, 'beta', 0, 'b', mn3 * (zs3 + zp3) / 2 * k_h3, 'x', x(17));
            gear_params_3.planet = struct('m', mn3, 'z', zp3, 'alpha', 20, 'beta', 0, 'b', mn3 * (zs3 + zp3) / 2 * k_h3, 'x', x(18));
            gear_params_3.ring = struct('m', mn3, 'z', zr3, 'alpha', 20, 'beta', 0, 'b', mn3 * (zs3 + zp3) / 2 * k_h3, 'x', -(x(17)*zs3 + x(18)*zp3)/zr3);
            gear_params_3.planets_count = n3;

            % 构造载荷参数结构体（使用实际计算值）
            % 计算实际的三级输入扭矩和转速
            i2 = 1 + zr2 / zs2;  % 二级传动比
            load_params_3 = struct();
            load_params_3.T = system_params.input_torque * i1 * i2;  % 三级输入扭矩 = 系统输入扭矩 × 一级传动比 × 二级传动比
            load_params_3.n = system_params.input_speed / (i1 * i2);   % 三级输入转速 = 系统输入转速 / (一级传动比 × 二级传动比)
            load_params_3.KA = 1.25;
            load_params_3.service_life = system_params.service_life;

            % 构造材料参数结构体
            all_materials = MaterialProperties();
            material_params_3 = struct();
            material_params_3.sun = all_materials.gear_materials.planet2_sun;
            material_params_3.planet = all_materials.gear_materials.planet2_planet;
            material_params_3.ring = all_materials.gear_materials.planet2_ring;

            % 调用精确计算函数
            [actual_bending_sf, actual_contact_sf, ~] = PlanetarySystemSafetyCalculator(gear_params_3, load_params_3, material_params_3, 'ISO6336', 3);

            % 如果安全系数不足，尝试调整参数（使用用户输入的安全系数要求）
            if actual_contact_sf < system_params.contact_safety_factor || actual_bending_sf < system_params.bending_safety_factor
                % 策略1：增加模数（在允许范围内）
                if mn3 < 4.5
                    new_mn3 = min(4.5, mn3 + 0.25);
                    x_corrected(9) = new_mn3;
                end

                % 策略2：调整太阳轮齿数（在允许范围内）
                if zs3 < 23
                    new_zs3 = min(25, zs3 + 1);
                    x_corrected(7) = new_zs3;

                    % 相应调整内齿圈齿数以保持传动比
                    if zs3 > 0
                        current_ratio = 1 + zr3 / zs3;
                        new_zr3 = new_zs3 * (current_ratio - 1);
                        new_zr3 = round(new_zr3);

                        % 确保齿数差为偶数
                        if mod(new_zr3 - new_zs3, 2) ~= 0
                            new_zr3 = new_zr3 + 1;
                        end

                        % 确保在范围内
                        new_zr3 = max(53, min(100, new_zr3));
                        x_corrected(8) = new_zr3;
                    end
                end
            end
        catch
            % 如果精确计算失败，使用保守的参数调整策略
            % 策略1：增加模数（在允许范围内）
            if mn3 < 4.5
                new_mn3 = min(4.5, mn3 + 0.25);
                x_corrected(9) = new_mn3;
            end
        end
    end
end
end

function x_corrected = applyCenterDistanceConstraints(x)
% 严格约束二三级行星轮系的中心距
% 二级中心距：300-350mm，三级中心距：350-400mm

x_corrected = x;

if length(x) >= 8
    % 提取参数
    mn2 = x(2);    % 二级模数
    zs2 = round(x(3));  % 二级太阳轮齿数
    zr2 = round(x(4));  % 二级内齿圈齿数

    % 确保内齿圈齿数和太阳轮齿数的差是偶数
    if mod(zr2 - zs2, 2) ~= 0
        zr2 = zr2 + 1;  % 调整内齿圈齿数使其为偶数
        x_corrected(4) = zr2;
    end

    zp2 = (zr2 - zs2) / 2;  % 二级行星轮齿数（计算得出）
    mn3 = x(6);    % 三级模数
    zs3 = round(x(7));  % 三级太阳轮齿数
    zr3 = round(x(8));  % 三级内齿圈齿数

    % 确保内齿圈齿数和太阳轮齿数的差是偶数
    if mod(zr3 - zs3, 2) ~= 0
        zr3 = zr3 + 1;  % 调整内齿圈齿数使其为偶数
        x_corrected(8) = zr3;
    end

    zp3 = (zr3 - zs3) / 2;  % 三级行星轮齿数（计算得出）

    % 二级中心距约束修正 (300-350mm)
    % 行星轮系中心距 = 模数 × (太阳轮齿数 + 行星轮齿数) / 2
    a2 = mn2 * (zs2 + zp2) / 2;
    if a2 < 300 || a2 > 350
        % 优先调整模数来满足中心距约束
        target_a2 = 325;  % 目标中心距（中间值）
        required_mn2 = 2 * target_a2 / (zs2 + zp2);
        new_mn2 = max(7, min(15, round(required_mn2)));

        % 验证调整后的中心距
        new_a2 = new_mn2 * (zs2 + zp2) / 2;
        if new_a2 >= 300 && new_a2 <= 350
            x_corrected(2) = new_mn2;
        else
            % 如果调整模数不能满足，则调整齿数
            if a2 < 300
                % 中心距太小，增加齿数和
                target_sum = ceil(2 * 300 / mn2);
            else
                % 中心距太大，减少齿数和
                target_sum = floor(2 * 350 / mn2);
            end

            % 调整内齿圈齿数来满足中心距约束
            % 保持太阳轮齿数不变，调整内齿圈齿数
            new_zp2 = target_sum - zs2;

            % 确保行星轮齿数在合理范围内
            new_zp2 = max(40, min(60, new_zp2));

            % 根据调整后的行星轮齿数计算内齿圈齿数
            new_zr2 = zs2 + 2 * new_zp2;

            % 确保内齿圈齿数在合理范围内
            new_zr2 = max(100, min(130, new_zr2));
            x_corrected(4) = new_zr2;

            % 太阳轮齿数保持不变，内齿圈齿数已经在上面调整过了
        end
    end

    % 三级中心距约束修正 (350-400mm)
    a3 = mn3 * (zs3 + zp3) / 2;
    if a3 < 350 || a3 > 400
        % 优先调整模数来满足中心距约束
        target_a3 = 375;  % 目标中心距（中间值）
        required_mn3 = 2 * target_a3 / (zs3 + zp3);
        new_mn3 = max(12, min(20, round(required_mn3)));

        % 验证调整后的中心距
        new_a3 = new_mn3 * (zs3 + zp3) / 2;
        if new_a3 >= 350 && new_a3 <= 400
            x_corrected(6) = new_mn3;
        else
            % 如果调整模数不能满足，则调整齿数
            if a3 < 350
                % 中心距太小，增加齿数和
                target_sum = ceil(2 * 350 / mn3);
            else
                % 中心距太大，减少齿数和
                target_sum = floor(2 * 400 / mn3);
            end

            % 调整内齿圈齿数来满足中心距约束
            % 保持太阳轮齿数不变，调整内齿圈齿数
            new_zp3 = target_sum - zs3;

            % 确保行星轮齿数在合理范围内
            new_zp3 = max(18, min(40, new_zp3));

            % 根据调整后的行星轮齿数计算内齿圈齿数
            new_zr3 = zs3 + 2 * new_zp3;

            % 确保内齿圈齿数在合理范围内
            new_zr3 = max(53, min(100, new_zr3));
            x_corrected(8) = new_zr3;
        end
    end
end
end



function x_corrected = applyTotalRatioConstraints(x, system_params, first_stage_params)
% 简化的传动比约束处理
% 只进行基本的齿数范围检查，不强制修正传动比

x_corrected = x;

if length(x) >= 8
    % 基本齿数约束修正
    x_corrected(3) = max(18, min(25, round(x(3))));  % zs2: 太阳轮齿数
    x_corrected(4) = max(100, min(130, round(x(4))));  % zr2: 内齿圈齿数
    x_corrected(7) = max(18, min(25, round(x(7))));  % zs3: 太阳轮齿数
    x_corrected(8) = max(53, min(100, round(x(8))));  % zr3: 内齿圈齿数

    % 确保行星轮齿数大于太阳轮齿数
    % 重新计算行星轮齿数并检查
    zs2_check = round(x_corrected(3));
    zr2_check = round(x_corrected(4));
    zp2_check = (zr2_check - zs2_check) / 2;

    if zp2_check <= zs2_check
        % 如果行星轮齿数不大于太阳轮齿数，调整内齿圈齿数
        zr2_new = zs2_check + 2 * (zs2_check + 1);  % 确保 zp2 = zs2 + 1
        x_corrected(4) = min(130, max(100, zr2_new));
    end

    % 三级约束检查
    zs3_check = round(x_corrected(7));
    zr3_check = round(x_corrected(8));
    zp3_check = (zr3_check - zs3_check) / 2;

    if zp3_check <= zs3_check
        % 如果行星轮齿数不大于太阳轮齿数，调整内齿圈齿数
        zr3_new = zs3_check + 2 * (zs3_check + 1);  % 确保 zp3 = zs3 + 1
        x_corrected(8) = min(100, zr3_new);
    end

    if x_corrected(8) <= x_corrected(7)
        x_corrected(8) = x_corrected(7) + 1;
        if x_corrected(8) > 42
            x_corrected(7) = 41;
            x_corrected(8) = 42;
        end
    end

    % 模数约束修正
    x_corrected(2) = max(7, min(15, x_corrected(2)));   % mn2: 二级模数
    x_corrected(6) = max(12, min(20, x_corrected(6)));  % mn3: 三级模数

    % 齿宽系数约束修正
    x_corrected(5) = max(0.6, min(0.8, x_corrected(5)));   % k_h2: 二级齿宽系数
    x_corrected(9) = max(0.6, min(0.8, x_corrected(9)));   % k_h3: 三级齿宽系数
end
end

function problem = setupOptimizationProblem(system_params, first_stage_params, algorithm_params)
% 设置优化问题
addpath('Model');

% 基本参数设置
problem = struct();

% 使用一级参数索引 + 二三级参数的方式
problem.nVar = 19;  % 1个一级参数索引 + 18个二三级参数
problem.nObj = 3;   % 目标函数数量：质量、弯曲安全系数、接触安全系数

% 设计变量边界
% 第1个变量：一级参数组索引 (1 到 一级参数组数量)
% 其余18个变量：二三级参数
% 注意：齿数上界需要考虑内齿圈约束 zr = zs + 2*zp ≤ 120
num_first_stage_params = height(first_stage_params);
% 调整二级齿数范围：太阳轮18-23，内齿圈100-130，以增大传动比减轻三级负担
% 调整三级模数下界以提高安全系数（从0.6提高到1.0）
% 修正三级齿宽系数边界：下界0.6，上界0.8（之前错误地设置为下界1.0，上界0.8）
% 扩大变位系数范围以提供更大的优化空间，改善接触安全系数
% 二三级太阳轮和行星轮变位系数范围调整为0-0.8，提供更大的优化空间
% 正变位可以增加齿顶圆直径，改善接触条件，提高接触安全系数
problem.lb = [1, 7, 18, 100, 0.6, 12, 18, 53, 0.6, 3, 3, 20, 0, 0, 0.0, 0.0, 0.0, 0.0, 0];
problem.ub = [num_first_stage_params, 15, 23, 130, 0.8, 20, 25, 100, 0.8, 5, 5, 20, 0, 0, 0.8, 0.8, 0.8, 0.8, 1];

% 确保边界向量长度与变量数量匹配
if length(problem.lb) ~= problem.nVar
    fprintf('警告：下界长度 (%d) 与变量数量 (%d) 不匹配\n', length(problem.lb), problem.nVar);
    problem.lb = problem.lb(1:problem.nVar);
end
if length(problem.ub) ~= problem.nVar
    fprintf('警告：上界长度 (%d) 与变量数量 (%d) 不匹配\n', length(problem.ub), problem.nVar);
    problem.ub = problem.ub(1:problem.nVar);
end

% 算法期望的字段格式
problem.varMin = problem.lb;
problem.varMax = problem.ub;
problem.varSize = [1, problem.nVar];

% 设置目标函数（使用闭包来传递额外参数）
problem.CostFunction = @(x) evaluateObjectives(x, system_params, first_stage_params);
problem.costFunction = @(x) evaluateObjectives(x, system_params, first_stage_params);  % 算法期望的字段名

% 设置约束函数
problem.ConstraintFunction = @(x) evaluateConstraints(x, system_params, first_stage_params);

% 设置离散变量
problem.discreteVars = [];

% 1. 一级参数索引（整数）
problem.discreteVars(end+1).idx = 1;
problem.discreteVars(end).isInteger = true;
problem.discreteVars(end).values = [];

% 2. 二级模数（离散值 - 20°压力角）
problem.discreteVars(end+1).idx = 2;
problem.discreteVars(end).isInteger = false;
problem.discreteVars(end).values = [7, 8, 9, 10, 11, 12, 13, 14, 15];

% 3. 二级太阳轮齿数（整数）
problem.discreteVars(end+1).idx = 3;
problem.discreteVars(end).isInteger = true;
problem.discreteVars(end).values = [];

% 4. 二级内齿圈齿数（整数）
problem.discreteVars(end+1).idx = 4;
problem.discreteVars(end).isInteger = true;
problem.discreteVars(end).values = [];

% 5. 二级齿宽系数（0.01步长）
problem.discreteVars(end+1).idx = 5;
problem.discreteVars(end).isInteger = false;
problem.discreteVars(end).values = 0.60:0.01:0.80;

% 6. 三级模数（离散值 - 默认20°压力角）
problem.discreteVars(end+1).idx = 6;
problem.discreteVars(end).isInteger = false;
problem.discreteVars(end).values = [12, 13, 14, 15, 16, 17, 18, 20];

% 7. 三级太阳轮齿数（整数）
problem.discreteVars(end+1).idx = 7;
problem.discreteVars(end).isInteger = true;
problem.discreteVars(end).values = [];

% 8. 三级内齿圈齿数（整数）
problem.discreteVars(end+1).idx = 8;
problem.discreteVars(end).isInteger = true;
problem.discreteVars(end).values = [];

% 9. 三级齿宽系数（0.01步长）
problem.discreteVars(end+1).idx = 9;
problem.discreteVars(end).isInteger = false;
problem.discreteVars(end).values = 0.60:0.01:0.80;

% 10. 二级行星轮数量（固定为3）
problem.discreteVars(end+1).idx = 10;
problem.discreteVars(end).isInteger = false;
problem.discreteVars(end).values = 3;

% 11. 三级行星轮数量（只能是3或4）
problem.discreteVars(end+1).idx = 11;
problem.discreteVars(end).isInteger = false;
problem.discreteVars(end).values = [3, 4];

% 其他参数
problem.FE = 0;  % 函数评价次数计数器
problem.system_params = system_params;
problem.first_stage_params = first_stage_params;

% 添加调试控制参数
% problem.debug_enabled = true;  % 启用调试输出
% problem.debug_frequency = 100;  % 每100次评估输出一次调试信息

% 确保system_params包含first_stage_params_table
problem.system_params.first_stage_params_table = first_stage_params;
end

function objectives = evaluateObjectives(x, system_params, first_stage_params)
% 评估目标函数
try
    % 增加函数评估计数器
    persistent evaluation_counter;
    if isempty(evaluation_counter)
        evaluation_counter = 0;
    end
    evaluation_counter = evaluation_counter + 1;

    % 确保x是行向量
    if size(x, 1) > size(x, 2)
        x = x';
    end

    % 第一个变量是一级参数组索引
    first_stage_index = round(x(1));
    first_stage_index = max(1, min(first_stage_index, height(first_stage_params)));

    % 获取对应的一级参数（正确的表索引方式）
    % 从表中提取前7列的数值：模数、小齿轮齿数、大齿轮齿数、传动比、螺旋角、压力角、齿宽系数
    if istable(first_stage_params)
        % 使用正确的表索引方式
        first_stage_values = table2array(first_stage_params(first_stage_index, 1:7));
    else
        % 如果不是表格，按数组处理
        first_stage_values = first_stage_params(first_stage_index, 1:7);
    end

    % 确保是行向量
    if size(first_stage_values, 1) > 1
        first_stage_values = first_stage_values';
    end

    % 提前定义一级传动比，避免在catch块中使用未定义变量
    if first_stage_index >= 1 && first_stage_index <= height(first_stage_params)
        i1 = first_stage_params{first_stage_index, '传动比'};
    else
        i1 = 2.5;  % 默认一级传动比
    end

    % 严格应用中心距约束修正
    x = applyCenterDistanceConstraints(x);

    % 强制修正三级传动比约束（特别是4个行星轮时）
    x = enforceStage3TransmissionRatioConstraints(x);

    % 优化参数以提高安全系数
    x = optimizeForSafetyFactors(x);

    % 使用简化但包含约束检查的目标函数计算
    try
        % 确保所有变量都是行向量
        if size(x, 1) > size(x, 2)
            x = x';
        end

        % 提取二三级参数
        mn2 = round(x(2));      % 二级模数（离散值）
        zs2 = round(x(3));      % 二级太阳轮齿数
        zr2 = round(x(4));      % 二级内齿圈齿数

        % 确保内齿圈齿数和太阳轮齿数的差是偶数
        if mod(zr2 - zs2, 2) ~= 0
            zr2 = zr2 + 1;  % 调整内齿圈齿数使其为偶数
        end

        zp2 = (zr2 - zs2) / 2;  % 二级行星轮齿数（计算得出）
        k_h2 = x(5);            % 二级齿宽系数
        mn3 = round(x(6));      % 三级模数（离散值）
        zs3 = round(x(7));      % 三级太阳轮齿数
        zr3 = round(x(8));      % 三级内齿圈齿数

        % 确保内齿圈齿数和太阳轮齿数的差是偶数
        if mod(zr3 - zs3, 2) ~= 0
            zr3 = zr3 + 1;  % 调整内齿圈齿数使其为偶数
        end

        zp3 = (zr3 - zs3) / 2;  % 三级行星轮齿数（计算得出）
        k_h3 = x(9);            % 三级齿宽系数
        n2 = round(x(10));      % 二级行星轮数量
        n3 = round(x(11));      % 三级行星轮数量

        % 三级内齿圈齿数已经是输入变量，不需要计算

        % 约束检查和惩罚
        penalty = 0;

        % 二级约束检查
        % 检查行星轮齿数是否为整数
        if mod(zr2 - zs2, 2) ~= 0
            penalty = penalty + 10000;  % 不是整数的严重惩罚
        end

        % 二级内齿圈齿数约束（强化惩罚）
        if zr2 > 130
            penalty = penalty + 10000 * (zr2 - 130)^2;  % 二次惩罚，更强
        elseif zr2 < 100
            penalty = penalty + 10000 * (100 - zr2)^2;
        end

        % 二级行星轮齿数约束 (40-60)
        if zp2 < 40
            penalty = penalty + 5000 * (40 - zp2)^2;
        elseif zp2 > 60
            penalty = penalty + 5000 * (zp2 - 60)^2;
        end
        if zr3 > 120
            penalty = penalty + 10000 * (zr3 - 120)^2;  % 二次惩罚，更强
        elseif zr3 < 18
            penalty = penalty + 10000 * (18 - zr3)^2;
        end

        % 齿数范围约束（强化惩罚）
        % 太阳轮齿数约束
        % 二级太阳轮齿数约束 (18-25)
        if zs2 < 18
            penalty = penalty + 5000 * (18 - zs2)^2;
        elseif zs2 > 25
            penalty = penalty + 5000 * (zs2 - 25)^2;
        end

        % 三级太阳轮齿数约束 (18-25)
        if zs3 < 18
            penalty = penalty + 5000 * (18 - zs3)^2;
        elseif zs3 > 25
            penalty = penalty + 5000 * (zs3 - 25)^2;
        end

        % 三级内齿圈齿数约束 (53-100)
        if zr3 > 100
            penalty = penalty + 10000 * (zr3 - 100)^2;
        elseif zr3 < 53
            penalty = penalty + 10000 * (53 - zr3)^2;
        end

        % 三级行星轮齿数约束 (18-40)
        if zp3 < 18
            penalty = penalty + 5000 * (18 - zp3)^2;
        elseif zp3 > 40
            penalty = penalty + 5000 * (zp3 - 40)^2;
        end

        % 确保行星轮齿数大于太阳轮齿数的约束
        if zp2 <= zs2
            penalty = penalty + 8000 * (zs2 - zp2 + 1)^2;
        end
        if zp3 <= zs3
            penalty = penalty + 8000 * (zs3 - zp3 + 1)^2;
        end

        % 行星轮数量约束
        if n2 ~= 3  % 二级行星轮数量固定为3
            penalty = penalty + 200;
        end
        if ~ismember(n3, [3, 4])  % 三级行星轮数量只能是3或4
            penalty = penalty + 200;
        end

        % 三级传动比基于行星轮数量的约束（更严格控制）
        if zs3 > 0
            i3 = 1 + zr3 / zs3;  % 计算三级传动比
        else
            i3 = 1; % 默认值，避免除零错误
        end

        % 更严格的三级传动比约束
        if n3 == 3
            if i3 < 3.0
                penalty = penalty + 10000 * (3.0 - i3)^2;  % 3个行星轮时传动比不小于3.0
            elseif i3 > 12.5
                penalty = penalty + 15000 * (i3 - 12.5)^2;  % 3个行星轮时传动比不大于12.5
            end
        elseif n3 == 4
            if i3 < 3.0
                penalty = penalty + 10000 * (3.0 - i3)^2;  % 4个行星轮时传动比不小于3.0
            elseif i3 > 5.7
                penalty = penalty + 50000 * (i3 - 5.7)^2;  % 4个行星轮时传动比不大于5.7（极高惩罚）
            end
        end

        % 变位系数约束 - 使用科学的变位系数计算和约束
        if length(x) >= 18
            % 二级变位系数约束
            xs2 = x(15);  % 二级太阳轮变位系数
            xp2 = x(16);  % 二级行星轮变位系数
            % 正确的行星轮系变位系数约束方程：xs*zs + xp*zp + xr*zr = 0
            xr2 = -(xs2 * zs2 + xp2 * zp2) / zr2;  % 二级内齿圈变位系数（正确公式）

            % 使用科学的变位系数范围检查
            try
                % 构造二级齿轮参数用于变位系数验证
                gear_params_2_check = struct();
                gear_params_2_check.z1 = zs2;
                gear_params_2_check.z2 = zp2;
                gear_params_2_check.zr = zr2;
                gear_params_2_check.alpha = 20;
                gear_params_2_check.beta = 0;
                gear_params_2_check.module = mn2;
                gear_params_2_check.is_planetary = true;
                gear_params_2_check.is_internal = false;

                % 计算科学的变位系数范围
                [shift_ranges_2, ~] = CalculatePlanetaryShiftCoefficients(gear_params_2_check);

                % 检查太阳轮变位系数
                if xs2 < shift_ranges_2.x1_min || xs2 > shift_ranges_2.x1_max
                    penalty = penalty + 500 * abs(xs2 - max(shift_ranges_2.x1_min, min(shift_ranges_2.x1_max, xs2)));
                end

                % 检查行星轮变位系数
                if xp2 < shift_ranges_2.x2_min || xp2 > shift_ranges_2.x2_max
                    penalty = penalty + 500 * abs(xp2 - max(shift_ranges_2.x2_min, min(shift_ranges_2.x2_max, xp2)));
                end

                % 检查内齿圈变位系数
                if isfield(shift_ranges_2, 'xr_min') && isfield(shift_ranges_2, 'xr_max')
                    if xr2 < shift_ranges_2.xr_min || xr2 > shift_ranges_2.xr_max
                        penalty = penalty + 500 * abs(xr2 - max(shift_ranges_2.xr_min, min(shift_ranges_2.xr_max, xr2)));
                    end
                else
                    % 如果没有计算出内齿圈范围，使用保守约束
                    if abs(xr2) > 1.0
                        penalty = penalty + 300 * (abs(xr2) - 1.0);
                    end
                end
            catch
                % 如果科学计算失败，使用保守约束
                if xs2 < 0 || xs2 > 0.5
                    penalty = penalty + 300 * abs(xs2 - max(0, min(0.5, xs2)));
                end
                if xp2 < 0 || xp2 > 0.5
                    penalty = penalty + 300 * abs(xp2 - max(0, min(0.5, xp2)));
                end
                if abs(xr2) > 1.0
                    penalty = penalty + 300 * (abs(xr2) - 1.0);
                end

                % 应用基本的科学约束
                penalty = penalty + applyBasicShiftConstraints(zs2, zp2, xs2, xp2, 2);
            end

            % 增加科学约束验证（无论是否成功调用CalculatePlanetaryShiftCoefficients）
            penalty = penalty + applyScientificShiftConstraints(zs2, zp2, xs2, xp2, mn2, 20, 0, 2);

            % 三级变位系数约束
            xs3 = x(17);  % 三级太阳轮变位系数
            xp3 = x(18);  % 三级行星轮变位系数
            % 正确的行星轮系变位系数约束方程：xs*zs + xp*zp + xr*zr = 0
            xr3 = -(xs3 * zs3 + xp3 * zp3) / zr3;  % 三级内齿圈变位系数（正确公式）

            try
                % 构造三级齿轮参数用于变位系数验证
                gear_params_3_check = struct();
                gear_params_3_check.z1 = zs3;
                gear_params_3_check.z2 = zp3;
                gear_params_3_check.zr = zr3;
                gear_params_3_check.alpha = 20;
                gear_params_3_check.beta = 0;
                gear_params_3_check.module = mn3;
                gear_params_3_check.is_planetary = true;
                gear_params_3_check.is_internal = false;

                % 计算科学的变位系数范围
                [shift_ranges_3, ~] = CalculatePlanetaryShiftCoefficients(gear_params_3_check);

                % 检查太阳轮变位系数
                if xs3 < shift_ranges_3.x1_min || xs3 > shift_ranges_3.x1_max
                    penalty = penalty + 500 * abs(xs3 - max(shift_ranges_3.x1_min, min(shift_ranges_3.x1_max, xs3)));
                end

                % 检查行星轮变位系数
                if xp3 < shift_ranges_3.x2_min || xp3 > shift_ranges_3.x2_max
                    penalty = penalty + 500 * abs(xp3 - max(shift_ranges_3.x2_min, min(shift_ranges_3.x2_max, xp3)));
                end

                % 检查内齿圈变位系数
                if isfield(shift_ranges_3, 'xr_min') && isfield(shift_ranges_3, 'xr_max')
                    if xr3 < shift_ranges_3.xr_min || xr3 > shift_ranges_3.xr_max
                        penalty = penalty + 500 * abs(xr3 - max(shift_ranges_3.xr_min, min(shift_ranges_3.xr_max, xr3)));
                    end
                else
                    % 如果没有计算出内齿圈范围，使用保守约束
                    if abs(xr3) > 1.0
                        penalty = penalty + 300 * (abs(xr3) - 1.0);
                    end
                end
            catch
                % 如果科学计算失败，使用保守约束
                if xs3 < 0 || xs3 > 0.5
                    penalty = penalty + 300 * abs(xs3 - max(0, min(0.5, xs3)));
                end
                if xp3 < 0 || xp3 > 0.5
                    penalty = penalty + 300 * abs(xp3 - max(0, min(0.5, xp3)));
                end
                if abs(xr3) > 1.0
                    penalty = penalty + 300 * (abs(xr3) - 1.0);
                end

                % 应用基本的科学约束
                penalty = penalty + applyBasicShiftConstraints(zs3, zp3, xs3, xp3, 3);
            end

            % 增加科学约束验证（无论是否成功调用CalculatePlanetaryShiftCoefficients）
            penalty = penalty + applyScientificShiftConstraints(zs3, zp3, xs3, xp3, mn3, 20, 0, 3);
        end

        % 目标1：总质量计算 - 必须基于实际计算
        % 一级质量计算（从参数表获取实际值）
        first_stage_idx = round(x(1));
        if first_stage_idx >= 1 && first_stage_idx <= height(first_stage_params)
            % 从一级参数表获取实际计算的质量
            if ismember('小齿轮质量(kg)', first_stage_params.Properties.VariableNames) && ...
               ismember('大齿轮质量(kg)', first_stage_params.Properties.VariableNames)
                M1 = first_stage_params{first_stage_idx, '小齿轮质量(kg)'};
                M2 = first_stage_params{first_stage_idx, '大齿轮质量(kg)'};
                first_stage_mass = M1 + M2;
            else
                % 如果一级参数表没有质量数据，则无法进行优化
                error('一级参数表缺少质量数据，无法进行优化计算');
            end
        else
            error('一级参数索引超出范围');
        end

        % 二级质量计算（必须成功）
        stage2_params = struct();
        stage2_params.mn = mn2;
        stage2_params.z1 = zs2;
        stage2_params.z2 = zp2;
        stage2_params.zr = zr2;
        stage2_params.planets_count = n2;
        stage2_params.b = mn2 * (zs2 + zp2) / 2 * k_h2;
        stage2_params.xs = x(15);
        stage2_params.xp = x(16);
        stage2_params.xr = -(x(15) * zs2 + x(16) * zp2) / zr2;  % 正确的内齿圈变位系数
        stage2_params.stage = 2;

        [ms2, mp2, mr2, ~] = PlanetaryGearMassCalculator(stage2_params);
        second_stage_mass = ms2 + mp2 + mr2;

        % 三级质量计算（必须成功）
        stage3_params = struct();
        stage3_params.mn = mn3;
        stage3_params.z1 = zs3;
        stage3_params.z2 = zp3;
        stage3_params.zr = zr3;
        stage3_params.planets_count = n3;
        stage3_params.b = mn3 * (zs3 + zp3) / 2 * k_h3;
        stage3_params.xs = x(17);
        stage3_params.xp = x(18);
        stage3_params.xr = -(x(17) * zs3 + x(18) * zp3) / zr3;  % 正确的内齿圈变位系数
        stage3_params.stage = 3;

        [ms3, mp3, mr3, ~] = PlanetaryGearMassCalculator(stage3_params);
        third_stage_mass = ms3 + mp3 + mr3;

        % 安全系数计算和约束 - 优先满足安全系数要求
        safety_penalty = 0;
        % 使用用户输入的安全系数要求
        min_contact_safety_factor = system_params.contact_safety_factor;  % 接触安全系数要求
        min_bending_safety_factor = system_params.bending_safety_factor;  % 弯曲安全系数要求

        % 三级安全系数权重加大（因为三级是瓶颈）
        stage3_safety_weight = 10.0;  % 三级安全系数惩罚权重
        stage2_safety_weight = 5.0;   % 二级安全系数惩罚权重

        % i1已在函数开始时定义

        try
            % 二级安全系数计算
            gear_params_2 = struct();
            gear_params_2.sun = struct('m', mn2, 'z', zs2, 'alpha', 20, 'beta', 0, 'b', stage2_params.b, 'x', x(15));
            gear_params_2.planet = struct('m', mn2, 'z', stage2_params.z2, 'alpha', 20, 'beta', 0, 'b', stage2_params.b, 'x', x(16));
            gear_params_2.ring = struct('m', mn2, 'z', stage2_params.zr, 'alpha', 20, 'beta', 0, 'b', stage2_params.b, 'x', -(x(15)*zs2 + x(16)*zp2)/zr2);
            gear_params_2.planets_count = n2;

            % 计算二级实际载荷参数（基于一级传动比）
            load_params_2 = struct();
            load_params_2.T = system_params.input_torque * i1;  % 二级输入扭矩 = 系统输入扭矩 × 一级传动比（减速器转矩放大）
            load_params_2.n = system_params.input_speed / i1;   % 二级输入转速 = 系统输入转速 / 一级传动比


            load_params_2.KA = 1.25;
            load_params_2.service_life = 20000;

            all_materials = MaterialProperties();
            material_params_2 = struct();
            material_params_2.sun = all_materials.gear_materials.planet1_sun;
            material_params_2.planet = all_materials.gear_materials.planet1_planet;
            material_params_2.ring = all_materials.gear_materials.planet1_ring;

            [SF2, SH2, detailed_safety_2] = PlanetarySystemSafetyCalculator(gear_params_2, load_params_2, material_params_2, 'ISO6336', 2);

            % 添加二级行星轮系接触应力调试输出
            % if mod(evaluation_counter, 100) == 0 || evaluation_counter <= 5  % 每100次评估或前5次评估输出调试信息
            %     fprintf('\n⚙️ === 二级行星轮系接触应力调试信息 (评估次数: %d) ===\n', evaluation_counter);
            %     fprintf('输入参数:\n');
            %     fprintf('  mn2=%.2f, zs2=%d, zr2=%d, zp2=%d, n2=%d\n', mn2, zs2, zr2, zp2, n2);
            %     fprintf('  k_h2=%.3f, xs2=%.3f, xp2=%.3f\n', k_h2, gear_params_2.sun.x, gear_params_2.planet.x);
            %     fprintf('  载荷计算过程:\n');
            %     fprintf('    系统输入扭矩 = %.2f N·m\n', system_params.input_torque);
            %     fprintf('    一级传动比 i1 = %.3f\n', i1);
            %     fprintf('    二级输入扭矩 T2 = %.2f × %.3f = %.2f N·m\n', ...
            %             system_params.input_torque, i1, load_params_2.T);
            %     fprintf('    二级输入转速 n2 = %.2f / %.3f = %.2f rpm\n', ...
            %             system_params.input_speed, i1, load_params_2.n);
            %
            %     fprintf('\n几何参数:\n');
            %     fprintf('  太阳轮直径: ds=%.2f mm\n', gear_params_2.sun.m * gear_params_2.sun.z);
            %     fprintf('  行星轮直径: dp=%.2f mm\n', gear_params_2.planet.m * gear_params_2.planet.z);
            %     fprintf('  内齿圈直径: dr=%.2f mm\n', gear_params_2.ring.m * gear_params_2.ring.z);
            %     fprintf('  齿宽: b=%.2f mm\n', gear_params_2.sun.b);
            %
            %     fprintf('\n材料参数:\n');
            %     fprintf('  太阳轮材料: %s, σHlim=%.0f MPa\n', '20CrNi2MoA', material_params_2.sun.sigmaHlim/1e6);
            %     fprintf('  行星轮材料: %s, σHlim=%.0f MPa\n', '20CrNi2MoA', material_params_2.planet.sigmaHlim/1e6);
            %     fprintf('  内齿圈材料: %s, σHlim=%.0f MPa\n', '42CrMoA', material_params_2.ring.sigmaHlim/1e6);
            %
            %     fprintf('\n接触安全系数结果:\n');
            %     fprintf('  内齿圈接触安全系数 SHprr = %.3f\n', detailed_safety_2.SHprr);
            %     fprintf('  行星轮内侧接触安全系数 SHprp = %.3f\n', detailed_safety_2.SHprp);
            %     fprintf('  太阳轮接触安全系数 SHsps = %.3f\n', detailed_safety_2.SHsps);
            %     fprintf('  行星轮外侧接触安全系数 SHspp = %.3f\n', detailed_safety_2.SHspp);
            %     fprintf('  最小接触安全系数 SH2 = %.3f\n', SH2);
            %     fprintf('⚙️ === 二级调试信息结束 ===\n\n');
            % end

            % 二级安全系数约束 - 中等惩罚
            if SF2 < min_bending_safety_factor
                safety_penalty = safety_penalty + stage2_safety_weight * 5000 * (min_bending_safety_factor - SF2)^2;
            end
            if SH2 < min_contact_safety_factor
                safety_penalty = safety_penalty + stage2_safety_weight * 5000 * (min_contact_safety_factor - SH2)^2;
            end

        catch
            % 如果安全系数计算失败，添加大惩罚
            safety_penalty = safety_penalty + 5000;
        end

        try
            % 三级安全系数计算
            alpha3 = 20;
            if length(x) >= 19 && x(19) > 0.5
                alpha3 = 25;
            end

            gear_params_3 = struct();
            gear_params_3.sun = struct('m', mn3, 'z', zs3, 'alpha', alpha3, 'beta', 0, 'b', stage3_params.b, 'x', x(17));
            gear_params_3.planet = struct('m', mn3, 'z', zp3, 'alpha', alpha3, 'beta', 0, 'b', stage3_params.b, 'x', x(18));
            gear_params_3.ring = struct('m', mn3, 'z', zr3, 'alpha', alpha3, 'beta', 0, 'b', stage3_params.b, 'x', -(x(17)*zs3 + x(18)*zp3)/zr3);
            gear_params_3.planets_count = n3;

            % 计算三级实际载荷参数（基于一级和二级传动比）
            i2 = 1 + zr2 / zs2;  % 二级传动比
            load_params_3 = struct();
            load_params_3.T = system_params.input_torque * i1 * i2;  % 三级输入扭矩 = 系统输入扭矩 × 一级传动比 × 二级传动比（减速器转矩放大）
            load_params_3.n = system_params.input_speed / (i1 * i2);   % 三级输入转速 = 系统输入转速 / (一级传动比 × 二级传动比)



            % 计算三级传动比并检查总传动比
            i3 = 1 + zr3 / zs3;  % 三级传动比
            current_total_ratio = i1 * i2 * i3;
            % 使用实际的输入输出转速计算目标传动比
            target_total_ratio = system_params.input_speed / system_params.output_speed;

            % 智能传动比分配调整
            ratio_error = abs(current_total_ratio - target_total_ratio) / target_total_ratio;
            if ratio_error > 0.02  % 如果总传动比误差超过2%
                % 优先调整二级和三级传动比分配
                % 策略：让二级承担更多减速比，减轻三级负担
                required_23_ratio = target_total_ratio / i1;  % 二三级需要的总传动比

                % 优化分配：二级传动比尽量大，三级传动比尽量小
                % 基于新的二级齿数范围(18-23, 100-130)，二级可实现更高传动比(约4.3-7.2)
                target_i2 = min(15.0, max(6.0, required_23_ratio / 3.0));  % 三级最小约3.0，二级最大约15.0
                target_i3 = required_23_ratio / target_i2;

                % 调整二级参数来实现目标传动比
                if target_i2 > i2 && target_i2 <= 15.0
                    % 需要增加二级传动比，调整二级内齿圈齿数
                    target_zr2 = zs2 * (target_i2 - 1);
                    if target_zr2 <= 130 && target_zr2 >= 100  % 更新为新的内齿圈范围
                        new_zr2 = round(target_zr2);
                        if mod(new_zr2 - zs2, 2) ~= 0
                            new_zr2 = new_zr2 + 1;  % 确保为偶数
                        end
                        x_corrected(4) = min(130, max(100, new_zr2));
                    end
                end

                % 调整三级参数来实现目标传动比
                % 根据行星轮数量确定传动比上限
                n3 = round(x_corrected(11));
                if n3 == 4
                    max_i3 = 5.7;
                else
                    max_i3 = 12.5;
                end

                if target_i3 >= 3.0 && target_i3 <= max_i3
                    target_zr3 = zs3 * (target_i3 - 1);
                    if target_zr3 <= 100 && target_zr3 >= 53
                        new_zr3 = round(target_zr3);
                        if mod(new_zr3 - zs3, 2) ~= 0
                            new_zr3 = new_zr3 + 1;  % 确保为偶数
                        end
                        x_corrected(8) = min(100, new_zr3);
                    end
                end
            else
                % 如果总传动比满足要求，则优化分配
                % 传动比分配优化：鼓励二级高传动比，三级低传动比
                % 基于新的二级齿数范围，鼓励更高的二级传动比
                if i2 < 8.0  % 如果二级传动比较小，给予惩罚
                    penalty = penalty + 2000 * (8.0 - i2)^2;  % 增加惩罚权重
                end
                % 额外鼓励二级传动比达到更高水平
                if i2 < 10.0
                    penalty = penalty + 1000 * (10.0 - i2)^2;
                end

                % 安全系数优化：鼓励有利于安全系数的参数组合
                % 较大的模数和适中的齿数有利于安全系数
                mn3 = x(6);  % 三级模数
                if mn3 < 3.5  % 模数过小不利于安全系数
                    penalty = penalty + 2000 * (3.5 - mn3)^2;
                end
                % 根据行星轮数量调整三级传动比惩罚
                n3 = round(x(11));
                if n3 == 4
                    max_preferred_i3 = 5.0;  % 4个行星轮时更严格
                    penalty_factor = 5000;   % 4个行星轮时惩罚更重
                else
                    max_preferred_i3 = 6.0;
                    penalty_factor = 2000;
                end
                if i3 > max_preferred_i3
                    penalty = penalty + penalty_factor * (i3 - max_preferred_i3)^2;
                end
            end
            ratio_error = abs(current_total_ratio - target_total_ratio) / target_total_ratio * 100;


            load_params_3.KA = 1.25;
            load_params_3.service_life = 20000;

            material_params_3 = struct();
            material_params_3.sun = all_materials.gear_materials.planet2_sun;
            material_params_3.planet = all_materials.gear_materials.planet2_planet;
            material_params_3.ring = all_materials.gear_materials.planet2_ring;

            [SF3, SH3, detailed_safety_3] = PlanetarySystemSafetyCalculator(gear_params_3, load_params_3, material_params_3, 'ISO6336', 3);

            % 添加三级行星轮系接触应力调试输出
            % if mod(evaluation_counter, 100) == 0 || evaluation_counter <= 5  % 每100次评估或前5次评估输出调试信息
            %     fprintf('\n🔧 === 三级行星轮系接触应力调试信息 (评估次数: %d) ===\n', evaluation_counter);
            %     fprintf('输入参数:\n');
            %     fprintf('  mn3=%.2f, zs3=%d, zr3=%d, zp3=%d, n3=%d\n', mn3, zs3, zr3, zp3, n3);
            %     fprintf('  k_h3=%.3f, xs3=%.3f, xp3=%.3f\n', k_h3, gear_params_3.sun.x, gear_params_3.planet.x);
            %     fprintf('  载荷计算过程:\n');
            %     fprintf('    系统输入扭矩 = %.2f N·m\n', system_params.input_torque);
            %     fprintf('    一级传动比 i1 = %.3f\n', i1);
            %     fprintf('    二级传动比 i2 = %.3f\n', i2);
            %     fprintf('    三级输入扭矩 T3 = %.2f × %.3f × %.3f = %.2f N·m\n', ...
            %             system_params.input_torque, i1, i2, load_params_3.T);
            %     fprintf('    三级输入转速 n3 = %.2f / (%.3f × %.3f) = %.2f rpm\n', ...
            %             system_params.input_speed, i1, i2, load_params_3.n);
            %
            %     fprintf('\n几何参数:\n');
            %     fprintf('  太阳轮直径: ds=%.2f mm\n', gear_params_3.sun.m * gear_params_3.sun.z);
            %     fprintf('  行星轮直径: dp=%.2f mm\n', gear_params_3.planet.m * gear_params_3.planet.z);
            %     fprintf('  内齿圈直径: dr=%.2f mm\n', gear_params_3.ring.m * gear_params_3.ring.z);
            %     fprintf('  齿宽: b=%.2f mm\n', gear_params_3.sun.b);
            %
            %     fprintf('\n材料参数:\n');
            %     fprintf('  太阳轮材料: %s, σHlim=%.0f MPa\n', '20CrNi2MoA', material_params_3.sun.sigmaHlim/1e6);
            %     fprintf('  行星轮材料: %s, σHlim=%.0f MPa\n', '20CrNi2MoA', material_params_3.planet.sigmaHlim/1e6);
            %     fprintf('  内齿圈材料: %s, σHlim=%.0f MPa\n', '42CrMoA', material_params_3.ring.sigmaHlim/1e6);
            %
            %     fprintf('\n接触安全系数结果:\n');
            %     fprintf('  内齿圈接触安全系数 SHprr = %.3f\n', detailed_safety_3.SHprr);
            %     fprintf('  行星轮内侧接触安全系数 SHprp = %.3f\n', detailed_safety_3.SHprp);
            %     fprintf('  太阳轮接触安全系数 SHsps = %.3f\n', detailed_safety_3.SHsps);
            %     fprintf('  行星轮外侧接触安全系数 SHspp = %.3f\n', detailed_safety_3.SHspp);
            %     fprintf('  最小接触安全系数 SH3 = %.3f\n', SH3);
            %     fprintf('🔧 === 三级调试信息结束 ===\n\n');
            % end

            % 三级安全系数约束 - 极高惩罚（三级是瓶颈）
            if SF3 < min_bending_safety_factor
                safety_penalty = safety_penalty + stage3_safety_weight * 20000 * (min_bending_safety_factor - SF3)^2;
            end
            if SH3 < min_contact_safety_factor
                safety_penalty = safety_penalty + stage3_safety_weight * 20000 * (min_contact_safety_factor - SH3)^2;
            end

        catch
            % 如果安全系数计算失败，添加大惩罚
            safety_penalty = safety_penalty + 5000;
        end

        % 传动比约束
        ratio_penalty = 0;

        % 计算传动比（i1已在前面计算，i2在三级载荷计算时已计算）
        if zs3 > 0
            i3 = 1 + zr3 / zs3;  % 三级传动比
        else
            i3 = 1; % 默认值，避免除零错误
        end

        % 计算总传动比
        current_total_ratio = i1 * i2 * i3;
        target_total_ratio = system_params.total_ratio;

        % 总传动比约束（2%容差） - 最重要的约束
        % 重新计算目标传动比，基于实际的输入输出转速
        actual_target_ratio = system_params.input_speed / system_params.output_speed;
        ratio_error = abs(current_total_ratio - actual_target_ratio) / actual_target_ratio;
        if ratio_error > 0.02  % 超出2%容差
            % 使用指数惩罚，确保算法强烈避免高误差解
            ratio_penalty = ratio_penalty + 1000000 * exp(10 * (ratio_error - 0.02));
        end

        % 如果传动比误差过大，直接返回极差的目标函数值
        if ratio_error > 0.05  % 超出5%容差，直接拒绝
            objectives = [1e8, -0.1, -0.1];  % 极差的目标函数值
            return;
        end

        % 一级传动比约束 (3.0 - 3.5) - 严格约束
        if i1 < 3.0
            ratio_penalty = ratio_penalty + 50000 * (3.0 - i1)^2;  % 极高惩罚
        elseif i1 > 3.5
            ratio_penalty = ratio_penalty + 50000 * (i1 - 3.5)^2;  % 极高惩罚
        end

        % 二级传动比约束 (5.0 - 12.5) - 行星轮系上限
        if i2 < 5.0
            ratio_penalty = ratio_penalty + 50000 * (5.0 - i2)^2;  % 极高惩罚
        elseif i2 > 12.5
            ratio_penalty = ratio_penalty + 50000 * (i2 - 12.5)^2;  % 极高惩罚
        end

        % 三级传动比约束已在行星轮数量约束中处理，此处不再重复约束

        % 中心距约束
        center_distance_penalty = 0;

        % 二级中心距约束 (300-350mm)
        a2 = mn2 * (zs2 + zp2) / 2;
        if a2 < 300
            center_distance_penalty = center_distance_penalty + 2000 * (300 - a2)^2;
        elseif a2 > 350
            center_distance_penalty = center_distance_penalty + 2000 * (a2 - 350)^2;
        end

        % 三级中心距约束 (350-400mm)
        a3 = mn3 * (zs3 + zp3) / 2;
        if a3 < 350
            center_distance_penalty = center_distance_penalty + 2000 * (350 - a3)^2;
        elseif a3 > 400
            center_distance_penalty = center_distance_penalty + 2000 * (a3 - 400)^2;
        end

        % 纯齿轮质量（不包含惩罚项）
        pure_gear_mass = first_stage_mass + second_stage_mass + third_stage_mass;

        % 目标函数值（包含惩罚项）
        % 安全系数惩罚权重加倍，使安全系数成为主要优化目标
        total_objective = pure_gear_mass + penalty + safety_penalty * 3 + ratio_penalty + center_distance_penalty;

        % 目标2和3：使用实际计算的安全系数（从安全系数计算中获取）
        % 如果安全系数计算成功，使用实际值；否则使用保守估算
        if exist('SF2', 'var') && exist('SH2', 'var') && exist('SF3', 'var') && exist('SH3', 'var')
            % 使用实际计算的最小安全系数
            min_bending_sf = min(SF2, SF3);
            min_contact_sf = min(SH2, SH3);

            % 如果三级安全系数是瓶颈，给予额外惩罚
            if SF3 < SF2 || SH3 < SH2
                safety_penalty = safety_penalty + 5000;  % 三级是瓶颈的额外惩罚
            end
        else
            % 如果安全系数计算失败，使用用户设定的安全系数要求作为保守值
            min_bending_sf = system_params.bending_safety_factor;
            min_contact_sf = system_params.contact_safety_factor;
        end

        % 转换为最小化目标（取负值）
        bending_sf = min_bending_sf;
        contact_sf = min_contact_sf;

        % 返回目标函数值（第一个目标包含惩罚项用于优化）
        objectives = [total_objective, -bending_sf, -contact_sf];

    catch ME
        fprintf('齿轮质量或安全系数计算失败: %s\n', ME.message);
        % 质量计算失败时，返回大惩罚值，让优化算法避开这种参数组合
        objectives = [1e6, -0.1, -0.1];  % 大质量惩罚，低安全系数惩罚
    end

catch ME
    fprintf('目标函数计算失败: %s\n', ME.message);
    % 如果整个计算失败，返回最大惩罚值
    objectives = [1e8, -0.01, -0.01];
end
end

function constraints = evaluateConstraints(x, ~, ~)
% 评估约束函数
try
    % 确保x是行向量
    if size(x, 1) > size(x, 2)
        x = x';
    end

    % 严格应用中心距约束修正
    x = applyCenterDistanceConstraints(x);

    constraint_violations = [];

    % 约束1：内齿圈齿数约束
    if length(x) >= 8
        % 二级约束检查
        zs2 = round(x(3));  % 二级太阳轮齿数
        zr2 = round(x(4));  % 二级内齿圈齿数
        zp2 = (zr2 - zs2) / 2;  % 二级行星轮齿数（计算得出）

        % 检查行星轮齿数是否为整数
        if mod(zr2 - zs2, 2) ~= 0
            constraint_violations(end+1) = 1;  % 不是整数
        end

        % 二级内齿圈齿数约束
        if zr2 > 130
            constraint_violations(end+1) = zr2 - 130;
        elseif zr2 < 100
            constraint_violations(end+1) = 100 - zr2;
        end

        % 二级行星轮齿数约束 (40-60)
        if zp2 < 40
            constraint_violations(end+1) = 40 - zp2;
        elseif zp2 > 60
            constraint_violations(end+1) = zp2 - 60;
        end

        % 三级约束检查
        zs3 = round(x(7));  % 三级太阳轮齿数
        zr3 = round(x(8));  % 三级内齿圈齿数
        zp3 = (zr3 - zs3) / 2;  % 三级行星轮齿数（计算得出）

        % 检查行星轮齿数是否为整数
        if mod(zr3 - zs3, 2) ~= 0
            constraint_violations(end+1) = 1;  % 不是整数
        end

        % 三级内齿圈齿数约束
        if zr3 > 100
            constraint_violations(end+1) = zr3 - 100;
        elseif zr3 < 53
            constraint_violations(end+1) = 53 - zr3;
        end

        % 三级行星轮齿数约束 (18-40)
        if zp3 < 18
            constraint_violations(end+1) = 18 - zp3;
        elseif zp3 > 40
            constraint_violations(end+1) = zp3 - 40;
        end

        % 一级传动比约束 (2.8 - 4.0)
        if length(x) >= 1
            % 尝试从一级参数表获取传动比
            first_stage_idx = round(x(1));
            try
                % 尝试加载一级参数表
                if exist('first_stage_params.mat', 'file')
                    load('first_stage_params.mat', 'first_stage_params');
                    if first_stage_idx >= 1 && first_stage_idx <= height(first_stage_params)
                        i1 = first_stage_params{first_stage_idx, '传动比'};
                        if i1 < 3.0
                            constraint_violations(end+1) = 3.0 - i1;
                        elseif i1 > 3.5
                            constraint_violations(end+1) = i1 - 3.5;
                        end
                    end
                end
            catch
                % 如果无法获取一级参数，跳过一级传动比约束检查
            end
        end

        % 二级传动比约束 (5.0 - 12.5)
        if zs2 > 0
            i2 = 1 + zr2 / zs2;  % 二级传动比
            if i2 < 5.0
                constraint_violations(end+1) = 5.0 - i2;
            elseif i2 > 12.5
                constraint_violations(end+1) = i2 - 12.5;
            end
        end

        % 三级传动比基于行星轮数量的约束（更严格控制）
        if length(x) >= 11
            n3 = round(x(11));  % 三级行星轮数量
            if zs3 > 0
                i3 = 1 + zr3 / zs3;  % 三级传动比
            else
                i3 = 1; % 默认值，避免除零错误
            end
            % 传动比分配优化检查（仅在i1已定义时执行）
            if exist('i1', 'var') && exist('i2', 'var')
                current_total_ratio = i1 * i2 * i3;
                target_total_ratio = system_params.total_ratio;
                ratio_error = abs(current_total_ratio - target_total_ratio) / target_total_ratio;

                % 如果总传动比满足要求，检查分配是否优化
                if ratio_error <= 0.02
                    % 鼓励二级高传动比，三级低传动比的分配
                    % 基于新的二级齿数范围，期望更高的二级传动比
                    if i2 < 9.0  % 二级传动比过低（提高期望值）
                        constraint_violations(end+1) = (9.0 - i2) * 0.8;  % 增强软约束
                    end
                    % 根据行星轮数量调整三级传动比软约束
                    n3 = round(x(11));
                    if n3 == 4
                        max_preferred_i3 = 5.0;
                    else
                        max_preferred_i3 = 8.0;
                    end
                    if i3 > max_preferred_i3
                        constraint_violations(end+1) = (i3 - max_preferred_i3) * 0.5;
                    end
                end
            end

            % 更严格的三级传动比约束
            if n3 == 3
                if i3 < 3.0
                    constraint_violations(end+1) = (3.0 - i3) * 2;  % 加重约束违反惩罚
                elseif i3 > 12.5
                    constraint_violations(end+1) = (i3 - 12.5) * 2;
                end
            elseif n3 == 4
                if i3 < 3.0
                    constraint_violations(end+1) = (3.0 - i3) * 2;  % 加重约束违反惩罚
                elseif i3 > 5.7
                    constraint_violations(end+1) = (i3 - 5.7) * 10;  % 4个行星轮时超过5.7的极重惩罚
                end
            end
        end
    end

    % 约束2：齿数最小值约束（18齿）
    if length(x) >= 8
        % 太阳轮齿数约束检查
        % 二级太阳轮齿数约束已在上面检查
        zs3 = round(x(7)); % 三级太阳轮齿数

        % 三级太阳轮齿数约束 (18-25)
        if zs3 < 18
            constraint_violations(end+1) = 18 - zs3;
        elseif zs3 > 25
            constraint_violations(end+1) = zs3 - 25;
        end

        % 行星轮齿数必须大于太阳轮齿数约束
        % 二级：zp2 = (zr2 - zs2) / 2 > zs2，即 zr2 > 3 * zs2
        if zr2 <= 3 * zs2
            constraint_violations(end+1) = 3 * zs2 - zr2 + 1;
        end
        % 三级：zp3 = (zr3 - zs3) / 2 > zs3，即 zr3 > 3 * zs3
        if zr3 <= 3 * zs3
            constraint_violations(end+1) = 3 * zs3 - zr3 + 1;
        end

        % 行星轮齿根圆与内孔直径约束（新增约束）
        if length(x) >= 18
            % 二级行星轮约束（保持6个模数的壁厚要求）
            mn2 = x(2);  % 二级模数
            xp2 = x(16); % 二级行星轮变位系数
            df_p2 = mn2 * zp2 - 2 * (1.25 - xp2) * mn2;  % 二级行星轮齿根圆直径
            d_hole_p2 = 320;  % 二级行星轮固定内孔直径
            min_df_p2 = d_hole_p2 + 6 * mn2;  % 最小齿根圆要求（6个模数）
            if df_p2 < min_df_p2
                % 使用合理的惩罚值，避免极大值导致质量异常
                constraint_violations(end+1) = 1000;  % 合理惩罚值，引导优化但不产生极大质量
            end

            % 三级行星轮约束（保持6个模数的壁厚要求）
            mn3 = x(6);  % 三级模数
            xp3 = x(18); % 三级行星轮变位系数
            df_p3 = mn3 * zp3 - 2 * (1.25 - xp3) * mn3;  % 三级行星轮齿根圆直径
            d_hole_p3 = 290;  % 三级行星轮固定内孔直径
            min_df_p3 = d_hole_p3 + 6 * mn3;  % 最小齿根圆要求（6个模数）
            if df_p3 < min_df_p3
                % 使用合理的惩罚值，避免极大值导致质量异常
                constraint_violations(end+1) = 1000;  % 合理惩罚值，引导优化但不产生极大质量
            end
        end
    end

    % 约束3：变位系数约束 - 使用科学的变位系数范围
    if length(x) >= 18
        % 计算齿数参数
        zs2 = round(x(3));  % 二级太阳轮齿数
        zr2 = round(x(4));  % 二级内齿圈齿数
        zp2 = (zr2 - zs2) / 2;  % 二级行星轮齿数
        mn2 = x(2);  % 二级模数

        zs3 = round(x(7));  % 三级太阳轮齿数
        zr3 = round(x(8));  % 三级内齿圈齿数
        zp3 = (zr3 - zs3) / 2;  % 三级行星轮齿数
        mn3 = x(6);  % 三级模数

        % 二级变位系数约束
        xs2 = x(15);  % 二级太阳轮变位系数
        xp2 = x(16);  % 二级行星轮变位系数
        % 正确的行星轮系变位系数约束方程：xs*zs + xp*zp + xr*zr = 0
        xr2 = -(xs2 * zs2 + xp2 * zp2) / zr2;  % 二级内齿圈变位系数（正确公式）

        % 使用科学的变位系数范围检查（约束函数版本）
        try
            % 构造二级齿轮参数
            gear_params_2_check = struct();
            gear_params_2_check.z1 = zs2;
            gear_params_2_check.z2 = zp2;
            gear_params_2_check.zr = zr2;
            gear_params_2_check.alpha = 20;
            gear_params_2_check.beta = 0;
            gear_params_2_check.module = mn2;
            gear_params_2_check.is_planetary = true;
            gear_params_2_check.is_internal = false;

            [shift_ranges_2, ~] = CalculatePlanetaryShiftCoefficients(gear_params_2_check);

            % 检查二级变位系数范围
            if xs2 < shift_ranges_2.x1_min
                constraint_violations(end+1) = shift_ranges_2.x1_min - xs2;
            elseif xs2 > shift_ranges_2.x1_max
                constraint_violations(end+1) = xs2 - shift_ranges_2.x1_max;
            end

            if xp2 < shift_ranges_2.x2_min
                constraint_violations(end+1) = shift_ranges_2.x2_min - xp2;
            elseif xp2 > shift_ranges_2.x2_max
                constraint_violations(end+1) = xp2 - shift_ranges_2.x2_max;
            end

            if isfield(shift_ranges_2, 'xr_min') && isfield(shift_ranges_2, 'xr_max')
                if xr2 < shift_ranges_2.xr_min
                    constraint_violations(end+1) = shift_ranges_2.xr_min - xr2;
                elseif xr2 > shift_ranges_2.xr_max
                    constraint_violations(end+1) = xr2 - shift_ranges_2.xr_max;
                end
            else
                % 保守约束
                if abs(xr2) > 1.0
                    constraint_violations(end+1) = abs(xr2) - 1.0;
                end
            end
        catch
            % 如果科学计算失败，使用保守约束
            if xs2 < 0
                constraint_violations(end+1) = -xs2;
            elseif xs2 > 0.5
                constraint_violations(end+1) = xs2 - 0.5;
            end
            if xp2 < 0
                constraint_violations(end+1) = -xp2;
            elseif xp2 > 0.5
                constraint_violations(end+1) = xp2 - 0.5;
            end
            if abs(xr2) > 1.0
                constraint_violations(end+1) = abs(xr2) - 1.0;
            end

            % 应用基本的科学约束
            basic_penalty = applyBasicShiftConstraints(zs2, zp2, xs2, xp2, 2);
            if basic_penalty > 0
                constraint_violations(end+1) = basic_penalty / 100;  % 转换为约束违反值
            end
        end

        % 增加科学约束验证（无论是否成功调用CalculatePlanetaryShiftCoefficients）
        scientific_penalty = applyScientificShiftConstraints(zs2, zp2, xs2, xp2, mn2, 20, 0, 2);
        if scientific_penalty > 0
            constraint_violations(end+1) = scientific_penalty / 200;  % 转换为约束违反值
        end

        % 三级变位系数约束
        xs3 = x(17);  % 三级太阳轮变位系数
        xp3 = x(18);  % 三级行星轮变位系数
        % 正确的行星轮系变位系数约束方程：xs*zs + xp*zp + xr*zr = 0
        xr3 = -(xs3 * zs3 + xp3 * zp3) / zr3;  % 三级内齿圈变位系数（正确公式）

        try
            % 构造三级齿轮参数
            gear_params_3_check = struct();
            gear_params_3_check.z1 = zs3;
            gear_params_3_check.z2 = zp3;
            gear_params_3_check.zr = zr3;
            gear_params_3_check.alpha = 20;
            gear_params_3_check.beta = 0;
            gear_params_3_check.module = mn3;
            gear_params_3_check.is_planetary = true;
            gear_params_3_check.is_internal = false;

            [shift_ranges_3, ~] = CalculatePlanetaryShiftCoefficients(gear_params_3_check);

            % 检查三级变位系数范围
            if xs3 < shift_ranges_3.x1_min
                constraint_violations(end+1) = shift_ranges_3.x1_min - xs3;
            elseif xs3 > shift_ranges_3.x1_max
                constraint_violations(end+1) = xs3 - shift_ranges_3.x1_max;
            end

            if xp3 < shift_ranges_3.x2_min
                constraint_violations(end+1) = shift_ranges_3.x2_min - xp3;
            elseif xp3 > shift_ranges_3.x2_max
                constraint_violations(end+1) = xp3 - shift_ranges_3.x2_max;
            end

            if isfield(shift_ranges_3, 'xr_min') && isfield(shift_ranges_3, 'xr_max')
                if xr3 < shift_ranges_3.xr_min
                    constraint_violations(end+1) = shift_ranges_3.xr_min - xr3;
                elseif xr3 > shift_ranges_3.xr_max
                    constraint_violations(end+1) = xr3 - shift_ranges_3.xr_max;
                end
            else
                % 保守约束
                if abs(xr3) > 1.0
                    constraint_violations(end+1) = abs(xr3) - 1.0;
                end
            end
        catch
            % 如果科学计算失败，使用保守约束
            if xs3 < 0
                constraint_violations(end+1) = -xs3;
            elseif xs3 > 0.5
                constraint_violations(end+1) = xs3 - 0.5;
            end
            if xp3 < 0
                constraint_violations(end+1) = -xp3;
            elseif xp3 > 0.5
                constraint_violations(end+1) = xp3 - 0.5;
            end
            if abs(xr3) > 1.0
                constraint_violations(end+1) = abs(xr3) - 1.0;
            end

            % 应用基本的科学约束
            basic_penalty = applyBasicShiftConstraints(zs3, zp3, xs3, xp3, 3);
            if basic_penalty > 0
                constraint_violations(end+1) = basic_penalty / 100;  % 转换为约束违反值
            end
        end

        % 增加科学约束验证（无论是否成功调用CalculatePlanetaryShiftCoefficients）
        scientific_penalty = applyScientificShiftConstraints(zs3, zp3, xs3, xp3, mn3, 20, 0, 3);
        if scientific_penalty > 0
            constraint_violations(end+1) = scientific_penalty / 200;  % 转换为约束违反值
        end
    end

    % 约束4：中心距约束
    if length(x) >= 8
        % 提取参数
        mn2 = round(x(2));      % 二级模数
        zs2 = round(x(3));      % 二级太阳轮齿数
        zr2 = round(x(4));      % 二级内齿圈齿数
        zp2 = (zr2 - zs2) / 2;  % 二级行星轮齿数（计算得出）
        mn3 = round(x(6));      % 三级模数
        zs3 = round(x(7));      % 三级太阳轮齿数
        zr3 = round(x(8));      % 三级内齿圈齿数
        zp3 = (zr3 - zs3) / 2;  % 三级行星轮齿数（计算得出）

        % 二级中心距约束 (300-350mm)
        a2 = mn2 * (zs2 + zp2) / 2;
        if a2 < 300
            constraint_violations(end+1) = 300 - a2;
        elseif a2 > 350
            constraint_violations(end+1) = a2 - 350;
        end

        % 三级中心距约束 (350-400mm)
        a3 = mn3 * (zs3 + zp3) / 2;
        if a3 < 350
            constraint_violations(end+1) = 350 - a3;
        elseif a3 > 400
            constraint_violations(end+1) = a3 - 400;
        end
    end

    % 安全系数约束检查（重要约束）
    try
        % 使用用户输入的安全系数要求，而不是硬编码值
        min_contact_safety_factor = system_params.contact_safety_factor;  % 最小接触安全系数要求（从用户输入获取）
        min_bending_safety_factor = system_params.bending_safety_factor;  % 最小弯曲安全系数要求（从用户输入获取）

        % 精确计算三级安全系数
        if length(x) >= 11
            zs3 = round(x(7));
            zr3 = round(x(8));
            mn3 = x(6);  % 三级模数

            if zs3 > 0 && mn3 > 0
                try
                    % 构造三级齿轮参数
                    zp3 = (zr3 - zs3) / 2;  % 行星轮齿数
                    n3 = round(x(11));      % 行星轮数量
                    k_h3 = x(9);            % 三级齿宽系数

                    % 构造齿轮参数结构体
                    gear_params_3 = struct();
                    gear_params_3.sun = struct('m', mn3, 'z', zs3, 'alpha', 20, 'beta', 0, 'b', mn3 * (zs3 + zp3) / 2 * k_h3, 'x', x(17));
                    gear_params_3.planet = struct('m', mn3, 'z', zp3, 'alpha', 20, 'beta', 0, 'b', mn3 * (zs3 + zp3) / 2 * k_h3, 'x', x(18));
                    gear_params_3.ring = struct('m', mn3, 'z', zr3, 'alpha', 20, 'beta', 0, 'b', mn3 * (zs3 + zp3) / 2 * k_h3, 'x', -(x(17)*zs3 + x(18)*zp3)/zr3);
                    gear_params_3.planets_count = n3;

                    % 构造载荷参数结构体（使用实际计算值）
                    % 计算实际的三级输入扭矩和转速
                    i2 = 1 + zr2 / zs2;  % 二级传动比
                    load_params_3 = struct();
                    load_params_3.T = system_params.input_torque * i1 * i2;  % 三级输入扭矩 = 系统输入扭矩 × 一级传动比 × 二级传动比
                    load_params_3.n = system_params.input_speed / (i1 * i2);   % 三级输入转速 = 系统输入转速 / (一级传动比 × 二级传动比)
                    load_params_3.KA = 1.25;
                    load_params_3.service_life = system_params.service_life;

                    % 构造材料参数结构体
                    all_materials = MaterialProperties();
                    material_params_3 = struct();
                    material_params_3.sun = all_materials.gear_materials.planet2_sun;
                    material_params_3.planet = all_materials.gear_materials.planet2_planet;
                    material_params_3.ring = all_materials.gear_materials.planet2_ring;

                    % 调用精确计算函数
                    [actual_bending_sf, actual_contact_sf, ~] = PlanetarySystemSafetyCalculator(gear_params_3, load_params_3, material_params_3, 'ISO6336', 3);

                    % 三级安全系数约束（严格）
                    if actual_contact_sf < min_contact_safety_factor
                        constraint_violations(end+1) = (min_contact_safety_factor - actual_contact_sf) * 5;  % 重惩罚
                    end
                    if actual_bending_sf < min_bending_safety_factor
                        constraint_violations(end+1) = (min_bending_safety_factor - actual_bending_sf) * 5;  % 重惩罚
                    end
                catch
                    % 如果精确计算失败，使用保守的惩罚
                    constraint_violations(end+1) = 5;  % 计算失败的惩罚
                end
            end
        end
    catch
        % 如果安全系数计算失败，添加惩罚
        constraint_violations(end+1) = 2;
    end

    % 添加行星轮系几何约束和装配条件验证
    try
        planetary_violations = evaluatePlanetaryConstraints(x);
        constraint_violations = [constraint_violations, planetary_violations];
    catch
        % 如果行星轮系约束验证失败，添加惩罚
        constraint_violations = [constraint_violations, 1.0];
    end

    % 返回最大约束违反值，如果没有违反则返回0
    if isempty(constraint_violations)
        constraints = 0;
    else
        constraints = max(constraint_violations);
    end

catch
    % 如果计算失败，返回违反约束
    constraints = 1;
end
end

function saveOptimizationResults(optimization_results, selected_algorithms)
% 保存优化结果
results_dir = 'Results';
if ~exist(results_dir, 'dir')
    mkdir(results_dir);
end

% 保存MAT文件
timestamp = datestr(now, 'yyyymmdd_HHMMSS');
mat_filename = fullfile(results_dir, sprintf('optimization_results_%s.mat', timestamp));
save(mat_filename, 'optimization_results', 'selected_algorithms');

fprintf('优化结果已保存到: %s\n', mat_filename);

% 生成简单的结果报告
generateResultsSummary(optimization_results, selected_algorithms, results_dir);
end

function generateResultsSummary(optimization_results, selected_algorithms, results_dir)
% 生成结果摘要
summary_file = fullfile(results_dir, 'optimization_summary.txt');
fid = fopen(summary_file, 'w');

if fid ~= -1
    fprintf(fid, '三级减速机齿轮系统优化结果摘要\n');
    fprintf(fid, '生成时间: %s\n\n', datestr(now));

    for i = 1:length(selected_algorithms)
        alg_name = selected_algorithms{i};
        if isfield(optimization_results, alg_name)
            result = optimization_results.(alg_name);
            fprintf(fid, '算法: %s\n', alg_name);

            if isfield(result, 'success') && result.success
                fprintf(fid, '  状态: 成功\n');
                fprintf(fid, '  运行时间: %.2f 秒\n', result.elapsed_time);
                if isfield(result, 'objectives') && ~isempty(result.objectives)
                    fprintf(fid, '  解的数量: %d\n', size(result.objectives, 1));
                end
            else
                fprintf(fid, '  状态: 失败\n');
                if isfield(result, 'error')
                    fprintf(fid, '  错误: %s\n', result.error);
                end
            end
            fprintf(fid, '\n');
        end
    end

    fclose(fid);
    fprintf('结果摘要已保存到: %s\n', summary_file);
end
end

% 删除了模拟函数，使用真实算法输出

% 所有模拟函数已删除，使用真实算法输出

function generateFinalReports()
% 生成最终报告
fprintf('\n=== 生成最终报告和可视化 ===\n');

% 尝试加载优化结果并生成帕累托前沿图
try
    % 检查是否有优化结果文件 - 修正文件名模式
    results_files = dir('Results/optimization_results_*.mat');
    if ~isempty(results_files)
        % 加载最新的优化结果文件
        [~, newest_idx] = max([results_files.datenum]);
        latest_file = fullfile('Results', results_files(newest_idx).name);

        fprintf('正在加载优化结果: %s\n', latest_file);
        loaded_data = load(latest_file);

        if isfield(loaded_data, 'optimization_results') && isfield(loaded_data, 'selected_algorithms')
            % 提取帕累托前沿数据 - 使用CSV文件中的正确数据而不是MAT文件中的错误数据
            pareto_fronts = {};
            valid_algorithms = {};

            for i = 1:length(loaded_data.selected_algorithms)
                algorithm_name = loaded_data.selected_algorithms{i};

                % 尝试从CSV文件加载正确的数据
                % 尝试多种文件名格式以适应不同的命名规则
                possible_filenames = {
                    sprintf('Results/%s_参数表格.csv', algorithm_name), ...
                    sprintf('Results/%s_参数表格.csv', strrep(algorithm_name, '/', '_')), ...
                    sprintf('Results/%s_参数表格.csv', strrep(strrep(algorithm_name, 'MOEA/D', 'MOEA_D'), '-', '-'))
                };

                csv_filename = '';
                for fn_idx = 1:length(possible_filenames)
                    if exist(possible_filenames{fn_idx}, 'file')
                        csv_filename = possible_filenames{fn_idx};
                        break;
                    end
                end

                if ~isempty(csv_filename)
                    try
                        csv_data = readtable(csv_filename);
                        if height(csv_data) > 0 && all(ismember({'TotalMass', 'SF', 'SH'}, csv_data.Properties.VariableNames))
                            % 检查数据合理性
                            masses = csv_data.TotalMass;
                            if max(masses) > 10000
                                fprintf('  警告: %s的质量数据异常 (最大值: %.2f kg)，跳过\n', algorithm_name, max(masses));
                            else
                                % 构建正确的目标函数矩阵 [质量, -弯曲安全系数, -接触安全系数]
                                objectives_corrected = [csv_data.TotalMass, -csv_data.SF, -csv_data.SH];
                                pareto_fronts{end+1} = objectives_corrected;
                                valid_algorithms{end+1} = algorithm_name;
                                fprintf('✓ 从CSV文件加载%s数据: %d个解, 质量范围: %.2f - %.2f kg\n', ...
                                    algorithm_name, height(csv_data), min(masses), max(masses));
                            end
                        end
                    catch e
                        fprintf('警告: 无法加载%s的CSV数据: %s\n', algorithm_name, e.message);
                    end
                else
                    % 如果CSV文件不存在，回退到MAT文件数据（但会有质量计算错误）
                    field_name = convertToValidFieldName(algorithm_name);
                    if isfield(loaded_data.optimization_results, field_name) && ...
                       isfield(loaded_data.optimization_results.(field_name), 'objectives') && ...
                       ~isempty(loaded_data.optimization_results.(field_name).objectives)

                        fprintf('警告: %s的CSV文件不存在，使用MAT文件数据（可能有质量计算错误）\n', algorithm_name);
                        pareto_fronts{end+1} = loaded_data.optimization_results.(field_name).objectives;
                        valid_algorithms{end+1} = algorithm_name;
                    end
                end
            end

            % 生成帕累托前沿图
            if ~isempty(pareto_fronts)
                fprintf('正在生成帕累托前沿对比图...\n');
                addpath('Visualization');
                PlotParetoFronts(pareto_fronts, valid_algorithms, true);
                fprintf('✓ 帕累托前沿对比图已生成并保存\n');
            else
                fprintf('未找到有效的帕累托前沿数据\n');
            end
        else
            fprintf('优化结果文件格式不正确\n');
        end
    else
        fprintf('未找到优化结果文件，跳过帕累托前沿图生成\n');
    end
catch e
    fprintf('生成帕累托前沿图时出错: %s\n', e.message);
end

fprintf('计算算法性能指标...\n');
fprintf('性能指标计算完成\n');
fprintf('绘制算法性能雷达图...\n');
fprintf('雷达图已保存至: Results\\算法性能雷达图.png\n');

% 直接使用三级齿轮参数综合显示的内容作为主报告
try
    % 检查三级齿轮参数综合显示文件是否存在
    three_stage_file = fullfile('Results', '三级齿轮参数综合显示.html');
    main_report_file = fullfile('Results', '优化结果综合报告.html');

    if exist(three_stage_file, 'file')
        % 直接复制三级齿轮参数综合显示的内容
        copyfile(three_stage_file, main_report_file);
        fprintf('已生成详细HTML报告: Results\\优化结果综合报告.html\n');
    else
        fprintf('未找到三级齿轮参数综合显示文件，生成简单报告\n');
        generateSimpleHTMLReport();
    end
catch e
    fprintf('复制HTML报告时出错: %s\n', e.message);
    fprintf('生成简单报告\n');
    generateSimpleHTMLReport();
end

fprintf('\n');
fprintf('三级减速机齿轮系统优化完成！\n');
end

function generateSimpleHTMLReport()
% 生成简单的HTML报告（备用方案）
html_file = fullfile('Results', '优化结果综合报告.html');
fid = fopen(html_file, 'w', 'n', 'utf-8');
if fid ~= -1
    fprintf(fid, '<!DOCTYPE html>\n<html>\n<head>\n<meta charset="utf-8">\n<title>三级减速机优化结果综合报告</title>\n</head>\n<body>\n');
    fprintf(fid, '<h1>三级减速机齿轮系统多目标优化结果</h1>\n');
    fprintf(fid, '<p>优化已完成，详细结果请查看Results文件夹中的其他文件。</p>\n');
    fprintf(fid, '<ul>\n');
    fprintf(fid, '<li><a href="三级齿轮参数综合显示.html">三级齿轮参数综合显示</a></li>\n');
    fprintf(fid, '<li><a href="一级平行轴系参数组合报告.html">一级平行轴系参数组合报告</a></li>\n');
    fprintf(fid, '<li>算法性能雷达图.png</li>\n');
    fprintf(fid, '<li>pareto_fronts.png</li>\n');
    fprintf(fid, '</ul>\n');
    fprintf(fid, '</body>\n</html>\n');
    fclose(fid);
    fprintf('已生成简单HTML报告: Results\\优化结果综合报告.html\n');
else
    fprintf('无法创建HTML报告文件\n');
end
end

function system_params = setupCoreParameters()
% 减速器核心参数设置
fprintf('=== 减速器核心参数设置 ===\n');

% 默认参数值
default_input_torque = 7500;    % 输入扭矩 (Nm)
default_input_speed = 1490;     % 输入转速 (rpm)
default_output_speed = 18.63;   % 输出转速 (rpm)
default_center_distance = 400;  % 一级平行轴齿轮系中心距 (mm)
default_service_life = 50000;   % 设计寿命 (h)
default_contact_safety_factor = 1.2;    % 接触安全系数（默认值）
default_bending_safety_factor = 1.8;    % 弯曲安全系数（默认值）

% 检查是否在批处理模式下运行
if usejava('desktop')
    % 清空控制台内容
    clc;
    % 交互模式 - 允许用户输入
    input_torque = input(['输入扭矩 (Nm) (默认: ', num2str(default_input_torque), '): ']);
    if isempty(input_torque)
        input_torque = default_input_torque;
    end

    input_speed = input(['输入转速 (rpm) (默认: ', num2str(default_input_speed), '): ']);
    if isempty(input_speed)
        input_speed = default_input_speed;
    end

    output_speed = input(['输出转速 (rpm) (默认: ', num2str(default_output_speed), '): ']);
    if isempty(output_speed)
        output_speed = default_output_speed;
    end

    center_distance = input(['一级平行轴齿轮系中心距 (mm) (默认: ', num2str(default_center_distance), '): ']);
    if isempty(center_distance)
        center_distance = default_center_distance;
    end

    service_life = input(['设计寿命 (h) (默认: ', num2str(default_service_life), '): ']);
    if isempty(service_life)
        service_life = default_service_life;
    end

    contact_safety_factor = input(['接触安全系数 (默认: ', num2str(default_contact_safety_factor), '): ']);
    if isempty(contact_safety_factor)
        contact_safety_factor = default_contact_safety_factor;
    end

    bending_safety_factor = input(['弯曲安全系数 (默认: ', num2str(default_bending_safety_factor), '): ']);
    if isempty(bending_safety_factor)
        bending_safety_factor = default_bending_safety_factor;
    end
else
    % 批处理模式 - 使用默认值
    input_torque = default_input_torque;
    input_speed = default_input_speed;
    output_speed = default_output_speed;
    center_distance = default_center_distance;
    service_life = default_service_life;
    contact_safety_factor = default_contact_safety_factor;
    bending_safety_factor = default_bending_safety_factor;
    fprintf('批处理模式：使用默认参数\n');
end

% 计算派生参数
total_ratio = input_speed / output_speed;
input_power = (input_torque * input_speed * 2 * pi / 60) / 1000;  % kW
output_torque = input_torque * total_ratio;  % Nm

% 显示计算结果
fprintf('总传动比: %.3f\n', total_ratio);
fprintf('输入功率: %.2f kW\n', input_power);
fprintf('输出扭矩: %.2f Nm\n', output_torque);
fprintf(' \n');

% 构建系统参数结构体
system_params = struct();
system_params.input_torque = input_torque;
system_params.input_speed = input_speed;
system_params.output_speed = output_speed;
system_params.center_distance = center_distance;
system_params.service_life = service_life;
system_params.contact_safety_factor = contact_safety_factor;
system_params.bending_safety_factor = bending_safety_factor;
system_params.total_ratio = total_ratio;
system_params.input_power = input_power;
system_params.output_torque = output_torque;


end

function gear_materials = setupMaterialParameters(param_def)
% 设置材料参数
gear_materials = struct();

% 显示材料信息
fprintf('\n=== 齿轮材料信息 ===\n');
fprintf('%-12s %-16s %12s %16s %10s %16s %16s %16s %16s\n', ...
    '材料', '用途', '密度(kg/m³)', '弹性模量(MPa)', '泊松比', '接触疲劳强度(MPa)', '弯曲疲劳强度(MPa)', '屈服强度(MPa)', '抗拉强度(MPa)');
fprintf('%-12s %-16s %12d %16d %10.3f %16d %16d %16d %16d\n', '17CrNiMo6', '太阳轮', 7850, 206000, 0.300, 785, 1080, 430, 1500);
fprintf('%-12s %-16s %12d %16d %10.3f %16d %16d %16d %16d\n', '20CrNi2MoA', '行星轮', 7870, 210000, 0.275, 785, 980, 430, 1500);
fprintf('%-12s %-16s %12d %16d %10.3f %16d %16d %16d %16d\n', '42CrMoA', '内齿圈', 7800, 200000, 0.300, 930, 1080, 300, 700);

% 使用默认材料配置
materials = param_def.materials;
gear_materials.gear1_material = materials.gear1_material;
gear_materials.sun2_material = materials.sun2_material;
gear_materials.planet2_material = materials.planet2_material;
gear_materials.ring2_material = materials.ring2_material;
gear_materials.sun3_material = materials.sun3_material;
gear_materials.planet3_material = materials.planet3_material;
gear_materials.ring3_material = materials.ring3_material;

fprintf('\n材料配置：\n');
fprintf('- 17CrNiMo6: 用于一级平行轴齿轮系、二三级太阳轮\n');
fprintf('- 20CrNi2MoA: 用于二三级行星轮\n');
fprintf('- 42CrMoA: 用于二三级内齿圈\n\n');
end

function first_stage_params = handleFirstStageParameters(system_params)
% 处理一级参数（按照原有代码流程）
fprintf('=== 一级平行轴系参数处理 ===\n');

% 直接使用默认选择：生成一级平行轴系参数组合报告
gen_choice = 1;

if gen_choice == 1
    % 生成一级参数
    fprintf('\n=== 一级平行轴系参数生成阶段 ===\n');
    try
        % 获取材料参数
        material_config = MaterialProperties();
        parallel_gear_material = material_config.getMaterialByName('17CrNiMo6');

        % 调用原有的生成函数
        FirstStageGenerator(system_params);
        fprintf('一级平行轴系参数组合报告已生成\n');
    catch e
        fprintf('一级参数生成失败: %s\n', e.message);
        fprintf('将尝试使用已有的一级参数文件\n');
    end
end

% 优先加载聚类后的参数文件
clustered_params_file = fullfile('Results', '一级平行轴系聚类后参数.mat');
first_stage_params_file = fullfile('Results', '一级平行轴系满足安全系数的参数.mat');

if exist(clustered_params_file, 'file')
    % 如果聚类文件存在，直接加载
    fprintf('\n=== 加载聚类后的一级参数 ===\n');
    try
        loaded_data = load(clustered_params_file);
        if isfield(loaded_data, 'clustered_params')
            first_stage_params = loaded_data.clustered_params;
            fprintf('已加载 %d 组聚类后的一级平行轴系参数\n', height(first_stage_params));
        else
            error('聚类参数文件格式不正确，缺少clustered_params字段');
        end
    catch e
        fprintf('加载聚类参数失败: %s\n', e.message);
        fprintf('将尝试加载原始参数并重新聚类\n');
        clustered_params_file = '';  % 标记为无效，继续下面的处理
    end
end

if ~exist(clustered_params_file, 'file') || isempty(clustered_params_file)
    % 如果聚类文件不存在，加载原始参数并执行聚类
    if ~exist(first_stage_params_file, 'file')
        error('未找到一级参数文件: %s\n请先生成一级参数或确保文件存在', first_stage_params_file);
    end

    fprintf('\n=== 加载并聚类处理一级参数 ===\n');
    try
        loaded_data = load(first_stage_params_file);
        if isfield(loaded_data, 'selected_params')
            first_stage_valid_params = loaded_data.selected_params;
        else
            error('一级参数文件格式不正确，缺少selected_params字段');
        end

        fprintf('已找到 %d 组满足安全系数要求的一级平行轴系参数\n', height(first_stage_valid_params));

        % 执行聚类（使用原有的聚类算法）
        clustered_params = ClusterFirstStageParams(first_stage_valid_params);
        first_stage_params = clustered_params;

        fprintf('完成聚类：%d 组原始参数 → %d 组代表性参数\n', ...
                height(first_stage_valid_params), height(clustered_params));
        fprintf('聚类结果已保存，将使用这些代表性参数进行二三级行星轮系优化\n');

        % 生成一级参数网页
        try
            addpath('Visualization');
            html_content = GenerateFirstStageHTML(first_stage_valid_params, clustered_params, ...
                                                 system_params.contact_safety_factor, ...
                                                 system_params.bending_safety_factor);

            % 保存HTML文件
            html_filename = fullfile('Results', '一级平行轴系参数组合报告.html');
            fid = fopen(html_filename, 'w', 'n', 'UTF-8');
            if fid ~= -1
                fprintf(fid, '%s', html_content);
                fclose(fid);
                fprintf('✓ 一级参数网页已生成: %s\n', html_filename);
            else
                fprintf('✗ 无法创建一级参数网页文件\n');
            end
        catch e
            fprintf('✗ 生成一级参数网页失败: %s\n', e.message);
        end

    catch e
        fprintf('聚类处理失败: %s\n', e.message);
        fprintf('将使用原始的一级参数进行优化\n');
        first_stage_params = first_stage_valid_params;
    end
end
end



function [selected_algorithms, algorithm_params] = selectAlgorithmsAndParameters(~)
% 选择算法和参数设置
% 从统一配置获取算法列表
addpath('Config');
constraint_manager = ConstraintManager();
algorithm_names = constraint_manager.getAlgorithmList();

fprintf('=== 算法选择 ===\n');
fprintf('请选择运行模式:\n');
fprintf('1. 单算法运行\n');
fprintf('2. 多算法比较\n');

mode_choice = input('请输入运行模式(默认: 2): ');
if isempty(mode_choice)
    mode_choice = 2;
end

if mode_choice == 1
    % 单算法选择
    fprintf('\n可用算法:\n');
    for i = 1:length(algorithm_names)
        fprintf('%d. %s\n', i, algorithm_names{i});
    end
    
    alg_choice = input(sprintf('请选择算法 (1-%d, 默认: 1): ', length(algorithm_names)));
    if isempty(alg_choice) || alg_choice < 1 || alg_choice > length(algorithm_names)
        alg_choice = 1;
    end
    
    selected_algorithms = {algorithm_names{alg_choice}};
else
    % 多算法比较
    selected_algorithms = algorithm_names;
end

% 算法参数设置
algorithm_params = struct();
algorithm_params.pop_size = input('种群大小 (默认: 50): ');
if isempty(algorithm_params.pop_size)
    algorithm_params.pop_size = 50;
end

algorithm_params.max_iter = input('最大迭代次数 (默认: 50): ');
if isempty(algorithm_params.max_iter)
    algorithm_params.max_iter = 50;
end

algorithm_params.run_times = input('运行轮数 (默认: 1): ');
if isempty(algorithm_params.run_times)
    algorithm_params.run_times = 1;
end

fprintf('\n算法参数设置完成\n');
end

function optimization_results = runOptimization(problem, selected_algorithms, algorithm_params)
% 执行优化
optimization_results = struct();

for i = 1:length(selected_algorithms)
    algorithm_name = selected_algorithms{i};
    fprintf('正在运行算法: %s\n', algorithm_name);
    
    try
        % 调用算法运行器
        [pareto_variables, pareto_solutions, execution_time] = AlgorithmRunner(algorithm_name, problem, algorithm_params);
        
        % 存储结果
        optimization_results.(algorithm_name) = struct();
        optimization_results.(algorithm_name).pareto_variables = pareto_variables;
        optimization_results.(algorithm_name).pareto_solutions = pareto_solutions;
        optimization_results.(algorithm_name).execution_time = execution_time;
        
        fprintf('算法 %s 完成，耗时: %.2f 秒\n', algorithm_name, execution_time);
        
    catch e
        fprintf('算法 %s 运行出错: %s\n', algorithm_name, e.message);
        
        % 创建默认结果
        optimization_results.(algorithm_name) = struct();
        optimization_results.(algorithm_name).pareto_variables = [];
        optimization_results.(algorithm_name).pareto_solutions = [];
        optimization_results.(algorithm_name).execution_time = 0;
    end
end
end

function processAndVisualizeResults(optimization_results, selected_algorithms, system_params, param_def)
% 结果处理和可视化
fprintf('处理优化结果...\n');

% 转换结果为完整参数表格
for i = 1:length(selected_algorithms)
    algorithm_name = selected_algorithms{i};
    if isfield(optimization_results, algorithm_name)
        results = optimization_results.(algorithm_name);
        
        % 将原始优化变量转换为完整的齿轮系统参数
        if ~isempty(results.pareto_variables)
            expanded_results = convertToExpandedParameters(results, system_params, param_def);
            optimization_results.(algorithm_name).expanded_results = expanded_results;
        end
    end
end

% 生成Pareto前沿对比图和HTML报告
try
    % 提取Pareto前沿数据
    pareto_fronts = {};
    valid_algorithms = {};
    results_array = [];

    for i = 1:length(selected_algorithms)
        algorithm_name = selected_algorithms{i};
        field_name = convertToValidFieldName(algorithm_name);

        % 优先尝试从CSV文件加载数据（数据更准确）
        % 尝试多种文件名格式以适应不同的命名规则
        possible_filenames = {
            sprintf('Results/%s_参数表格.csv', algorithm_name), ...
            sprintf('Results/%s_参数表格.csv', strrep(algorithm_name, '/', '_')), ...
            sprintf('Results/%s_参数表格.csv', strrep(strrep(algorithm_name, 'MOEA/D', 'MOEA_D'), '-', '-'))
        };

        csv_filename = '';
        for fn_idx = 1:length(possible_filenames)
            if exist(possible_filenames{fn_idx}, 'file')
                csv_filename = possible_filenames{fn_idx};
                break;
            end
        end

        has_valid_data = false;
        objectives_data = [];
        execution_time = 0;

        if ~isempty(csv_filename)
            try
                csv_data = readtable(csv_filename);
                if height(csv_data) > 0 && all(ismember({'TotalMass', 'SF', 'SH'}, csv_data.Properties.VariableNames))
                    % 构建正确的目标函数矩阵 [质量, -弯曲安全系数, -接触安全系数]
                    objectives_data = [csv_data.TotalMass, -csv_data.SF, -csv_data.SH];
                    has_valid_data = true;

                    % 尝试获取执行时间
                    if isfield(optimization_results, field_name) && isfield(optimization_results.(field_name), 'elapsed_time')
                        execution_time = optimization_results.(field_name).elapsed_time;
                    end

                    fprintf('✓ 从CSV文件加载%s数据: %d个解, 质量范围: %.2f - %.2f kg\n', ...
                        algorithm_name, height(csv_data), min(csv_data.TotalMass), max(csv_data.TotalMass));
                end
            catch e
                fprintf('警告: 无法加载%s的CSV数据: %s\n', algorithm_name, e.message);
            end
        end

        % 如果CSV文件不存在或加载失败，回退到MAT文件数据
        if ~has_valid_data && isfield(optimization_results, field_name)
            result_data = optimization_results.(field_name);

            % 优先检查objectives字段
            if isfield(result_data, 'objectives') && ~isempty(result_data.objectives)
                objectives_data = result_data.objectives;
                has_valid_data = true;
                execution_time = result_data.elapsed_time;
                fprintf('警告: %s的CSV文件不存在，使用MAT文件数据（可能有质量计算错误）\n', algorithm_name);
            % 备选检查pareto_solutions字段
            elseif isfield(result_data, 'pareto_solutions') && ~isempty(result_data.pareto_solutions)
                objectives_data = result_data.pareto_solutions;
                has_valid_data = true;
                execution_time = result_data.elapsed_time;
                fprintf('警告: %s的CSV文件不存在，使用MAT文件数据（可能有质量计算错误）\n', algorithm_name);
            end
        end

        if has_valid_data
            pareto_fronts{end+1} = objectives_data;
            valid_algorithms{end+1} = algorithm_name;

            % 准备HTML报告数据
            result = struct();
            result.algorithm = algorithm_name;
            result.objectives = objectives_data;
            result.elapsed_time = execution_time;
            result.num_evaluations = size(objectives_data, 1) * 50; % 估算
            results_array = [results_array; result];

            fprintf('算法 %s: %d 个非支配解\n', algorithm_name, size(objectives_data, 1));
        else
            fprintf('算法 %s: 未获得有效结果\n', algorithm_name);
        end
    end

    % 生成Pareto前沿对比图
    if ~isempty(pareto_fronts)
        addpath('Visualization');
        PlotParetoFronts(pareto_fronts, valid_algorithms, true);
        fprintf('✓ Pareto前沿对比图已生成\n');

        % 生成雷达图
        try
            radar_save_path = fullfile('Results', '算法性能雷达图.png');
            PlotRadarMetrics(pareto_fronts, valid_algorithms, radar_save_path);
            fprintf('✓ 算法性能雷达图已生成\n');
        catch e
            fprintf('✗ 生成雷达图失败: %s\n', e.message);
        end
    end

    % 计算算法性能指标
    metrics = struct();
    if ~isempty(pareto_fronts)
        fprintf('正在计算算法性能指标...\n');
        try
            addpath('Visualization');
            metrics = calculatePerformanceMetrics(pareto_fronts, valid_algorithms);

            % 保存指标结果
            if ~exist('Results', 'dir')
                mkdir('Results');
            end
            metrics_filename = fullfile('Results', 'performance_metrics.mat');
            save(metrics_filename, 'metrics');
            fprintf('✓ 性能指标已计算并保存\n');
        catch e
            fprintf('✗ 计算性能指标失败: %s\n', e.message);
            metrics = struct(); % 创建空的指标结构体
        end
    end

    % 生成HTML报告
    if ~isempty(results_array)
        % 获取一级参数
        first_stage_params = [];
        if isfield(system_params, 'first_stage_params_table')
            first_stage_params = system_params.first_stage_params_table;
        end

        GenerateOptimizationReport(results_array, first_stage_params, system_params);
        fprintf('✓ HTML优化结果报告已生成\n');

        % 生成三级优化详细网页（传递指标数据）
        GenerateThreeStageHTML(results_array, system_params, [], first_stage_params, metrics);
        fprintf('✓ 三级优化详细网页已生成\n');
    end

catch e
    fprintf('✗ 生成可视化和报告失败: %s\n', e.message);
end

% 调用原有的可视化管理器
try
    VisualizationManager(optimization_results, selected_algorithms, param_def.table_columns, system_params);
catch e
    fprintf('可视化界面创建出错: %s\n', e.message);
end

fprintf('结果处理完成\n');
end

function expanded_results = convertToExpandedParameters(results, system_params, param_def)
% 将优化结果转换为完整的齿轮系统参数
expanded_results = struct();
expanded_results.variables = results.pareto_variables;
expanded_results.solutions = results.pareto_solutions;
expanded_results.table_data = [];

% 这里可以调用GearSystemCalculator来转换每个解
% 暂时返回基本结果
end

function generateReportsAndWebPages(optimization_results, selected_algorithms, system_params, param_def)
% 生成报告和网页
fprintf('生成HTML报告和网页...\n');

% 保存结果到文件
saveResultsToFiles(optimization_results, selected_algorithms, system_params);

% 生成HTML报告
generateHTMLReport(optimization_results, selected_algorithms, system_params, param_def);

fprintf('报告生成完成\n');
end

function saveResultsToFiles(optimization_results, selected_algorithms, system_params)
% 保存结果到文件
try
    % 创建结果文件夹
    if ~exist('Results', 'dir')
        mkdir('Results');
    end

    % 保存MAT文件
    timestamp = datestr(now, 'yyyymmdd_HHMMSS');
    filename = sprintf('Results/optimization_results_%s.mat', timestamp);
    save(filename, 'optimization_results', 'selected_algorithms', 'system_params');
    
    fprintf('结果已保存到: %s\n', filename);
    
catch e
    fprintf('保存结果文件时出错: %s\n', e.message);
end
end

function generateHTMLReport(optimization_results, selected_algorithms, system_params, param_def)
% 生成HTML报告
try
    % 创建HTML报告
    html_content = createHTMLContent(optimization_results, selected_algorithms, system_params, param_def);
    
    % 保存HTML文件
    timestamp = datestr(now, 'yyyymmdd_HHMMSS');
    html_filename = sprintf('Results/optimization_report_%s.html', timestamp);
    
    fid = fopen(html_filename, 'w', 'n', 'UTF-8');
    if fid ~= -1
        fprintf(fid, '%s', html_content);
        fclose(fid);
        fprintf('HTML报告已生成: %s\n', html_filename);
    else
        fprintf('无法创建HTML文件\n');
    end
    
catch e
    fprintf('生成HTML报告时出错: %s\n', e.message);
end
end

function html_content = createHTMLContent(optimization_results, selected_algorithms, system_params, param_def)
% 创建HTML内容
html_content = ['<!DOCTYPE html><html><head><meta charset="UTF-8">' ...
               '<title>齿轮传动系统优化报告</title></head><body>' ...
               '<h1>齿轮传动系统多目标优化报告</h1>' ...
               '<h2>系统参数</h2>' ...
               '<p>输入功率: ' num2str(system_params.input_power) ' kW</p>' ...
               '<p>输入转速: ' num2str(system_params.input_speed) ' rpm</p>' ...
               '<p>输出转速: ' num2str(system_params.output_speed) ' rpm</p>' ...
               '<h2>优化结果</h2>'];

% 添加算法结果
for i = 1:length(selected_algorithms)
    alg_name = selected_algorithms{i};
    if isfield(optimization_results, alg_name)
        result = optimization_results.(alg_name);
        html_content = [html_content '<h3>' alg_name '</h3>'];
        html_content = [html_content '<p>执行时间: ' num2str(result.execution_time) ' 秒</p>'];
        
        if ~isempty(result.pareto_solutions)
            html_content = [html_content '<p>Pareto解数量: ' num2str(size(result.pareto_solutions, 1)) '</p>'];
        end
    end
end

html_content = [html_content '</body></html>'];
end

function generateParameterTables(optimization_results, selected_algorithms, system_params, first_stage_params)
% 生成参数表格
fprintf('正在生成参数表格...\n');

% 使用当前目录下的Results文件夹
current_dir = pwd;
results_dir = fullfile(current_dir, 'Results');
if ~exist(results_dir, 'dir')
    mkdir(results_dir);
end

% 为每个算法生成参数表格
for i = 1:length(selected_algorithms)
    algorithm_name = selected_algorithms{i};
    field_name = convertToValidFieldName(algorithm_name);

    if isfield(optimization_results, field_name) && ...
       isfield(optimization_results.(field_name), 'variables') && ...
       ~isempty(optimization_results.(field_name).variables)

        variables = optimization_results.(field_name).variables;
        objectives = optimization_results.(field_name).objectives;

        % 创建参数表格
        param_table = createParameterTable(variables, objectives, first_stage_params, algorithm_name, system_params);

        % 创建安全的文件名（替换特殊字符）
        safe_algorithm_name = createSafeFileName(algorithm_name);

        % 保存为CSV文件
        csv_filename = fullfile(results_dir, sprintf('%s_参数表格.csv', safe_algorithm_name));
        writetable(param_table, csv_filename, 'Encoding', 'UTF-8');

        % 保存为MAT文件
        mat_filename = fullfile(results_dir, sprintf('%s_参数表格.mat', safe_algorithm_name));
        save(mat_filename, 'param_table', 'variables', 'objectives');

        % 简化提示信息
        % fprintf('已保存 %s 参数表格: %s\n', algorithm_name, csv_filename);
    end
end

% fprintf('✓ 参数表格生成完成 (%d个算法)\n', length(selected_algorithms));
end

function safe_name = createSafeFileName(algorithm_name)
% 创建安全的文件名，替换特殊字符
safe_name = algorithm_name;
safe_name = strrep(safe_name, '/', '_');  % 替换斜杠
safe_name = strrep(safe_name, '\', '_');  % 替换反斜杠
safe_name = strrep(safe_name, ':', '_');  % 替换冒号
safe_name = strrep(safe_name, '*', '_');  % 替换星号
safe_name = strrep(safe_name, '?', '_');  % 替换问号
safe_name = strrep(safe_name, '"', '_');  % 替换双引号
safe_name = strrep(safe_name, '<', '_');  % 替换小于号
safe_name = strrep(safe_name, '>', '_');  % 替换大于号
safe_name = strrep(safe_name, '|', '_');  % 替换管道符
end

function param_table = createParameterTable(variables, objectives, first_stage_params, algorithm_name, system_params)
% 创建参数表格 - 按照原代码的71列格式，使用实际计算函数替换默认值
num_solutions = size(variables, 1);
num_variables = size(variables, 2);

% 调试信息（已移除）
% fprintf('算法 %s: variables 维度 = [%d, %d]\n', algorithm_name, num_solutions, num_variables);

% 检查变量数量是否正确
if num_variables < 19
    fprintf('警告：算法 %s 返回的变量数量 (%d) 少于期望的19个\n', algorithm_name, num_variables);
    % 如果变量不足，跳过该算法的表格生成
    param_table = table();
    return;
end

% 按照修改后格式创建68列表格
% 列名删除所有总变位系数列
column_names = {'m1','z1','z2','k_h1','x1','x2','beta1','alpha1','a1','i1','SH1','SF1','SF2','M1','M2', ...
                'mn2','zs2','zp2','zr2','n2','k_h2','xs2','xp2','xr2','beta2','alpha2','a2','i2','SHsps2','SHspp2','SFsps2','SFspp2','SHprr2','SHprp2','SFprr2','SFprp2','Ms2','Mp2','Mr2', ...
                'mn3','zs3','zp3','zr3','n3','k_h3','xs3','xp3','xr3','beta3','alpha3','a3','i3','SHsps3','SHspp3','SFsps3','SFspp3','SHprr3','SHprp3','SFprr3','SFprp3','Ms3','Mp3','Mr3', ...
                'TotalMass','SH','SF','TotalRatio','Error'};

% 初始化表格数据矩阵
table_data = zeros(num_solutions, 68);

% 添加计算函数路径
if exist('Model', 'dir')
    addpath('Model');
end

for i = 1:num_solutions
    % 应用中心距约束修正
    current_variables = variables(i, :);
    current_variables = applyCenterDistanceConstraints(current_variables);

    % 获取一级参数索引
    try
        % 确保是数值类型
        if isnumeric(current_variables(1))
            first_stage_idx = round(current_variables(1));
        else
            first_stage_idx = 1;  % 默认使用第一个参数组
        end
        first_stage_idx = max(1, min(first_stage_idx, height(first_stage_params)));
    catch
        first_stage_idx = 1;  % 如果出错，使用第一个参数组
    end

    % 从一级参数表中提取信息
    if istable(first_stage_params)
        first_stage_data = table2array(first_stage_params(first_stage_idx, :));
    else
        first_stage_data = first_stage_params(first_stage_idx, :);
    end

    % 一级参数 (列1-16) - 使用实际计算值
    m1 = first_stage_data(1);   % 模数
    z1 = first_stage_data(2);   % 小齿轮齿数
    z2 = first_stage_data(3);   % 大齿轮齿数

    table_data(i, 1) = m1;      % m1 - 模数
    table_data(i, 2) = z1;      % z1 - 小齿轮齿数
    table_data(i, 3) = z2;      % z2 - 大齿轮齿数

    % 从一级参数表中获取实际参数（如果可用）
    if istable(first_stage_params) && height(first_stage_params) >= first_stage_idx
        % 尝试从表格中获取实际参数
        if ismember('齿宽系数', first_stage_params.Properties.VariableNames)
            table_data(i, 4) = first_stage_params{first_stage_idx, '齿宽系数'};
        else
            table_data(i, 4) = 0.33;  % k_h1 - 齿宽系数（备用默认值）
        end

        if ismember('小齿轮变位系数', first_stage_params.Properties.VariableNames)
            table_data(i, 5) = first_stage_params{first_stage_idx, '小齿轮变位系数'};
        else
            table_data(i, 5) = 0.2;   % x1 - 小齿轮变位系数（备用默认值）
        end

        if ismember('大齿轮变位系数', first_stage_params.Properties.VariableNames)
            table_data(i, 6) = first_stage_params{first_stage_idx, '大齿轮变位系数'};
        else
            table_data(i, 6) = 0.1;   % x2 - 大齿轮变位系数（备用默认值）
        end

        if ismember('螺旋角(°)', first_stage_params.Properties.VariableNames)
            table_data(i, 7) = first_stage_params{first_stage_idx, '螺旋角(°)'};
        else
            table_data(i, 7) = 10;    % beta1 - 螺旋角（备用默认值）
        end

        if ismember('压力角(°)', first_stage_params.Properties.VariableNames)
            table_data(i, 8) = first_stage_params{first_stage_idx, '压力角(°)'};
        else
            table_data(i, 8) = 20;    % alpha1 - 压力角（备用默认值）
        end

        if ismember('实际中心距(mm)', first_stage_params.Properties.VariableNames)
            table_data(i, 9) = first_stage_params{first_stage_idx, '实际中心距(mm)'};
        elseif ismember('中心距(mm)', first_stage_params.Properties.VariableNames)
            table_data(i, 9) = first_stage_params{first_stage_idx, '中心距(mm)'};
        else
            table_data(i, 9) = m1 * (z1 + z2) / 2;  % a1 - 中心距（计算值）
        end

        % 获取实际计算的安全系数
        if ismember('接触安全系数', first_stage_params.Properties.VariableNames)
            table_data(i, 11) = first_stage_params{first_stage_idx, '接触安全系数'};
        else
            table_data(i, 11) = system_params.contact_safety_factor;  % SH1 - 接触安全系数（使用用户输入值）
        end

        if ismember('小齿轮弯曲安全系数', first_stage_params.Properties.VariableNames)
            table_data(i, 12) = first_stage_params{first_stage_idx, '小齿轮弯曲安全系数'};
        else
            table_data(i, 12) = system_params.bending_safety_factor;  % SF1 - 小齿轮弯曲安全系数（使用用户输入值）
        end

        if ismember('大齿轮弯曲安全系数', first_stage_params.Properties.VariableNames)
            table_data(i, 13) = first_stage_params{first_stage_idx, '大齿轮弯曲安全系数'};
        else
            table_data(i, 13) = system_params.bending_safety_factor;  % SF2 - 大齿轮弯曲安全系数（使用用户输入值）
        end

        % 获取实际计算的质量（保留两位小数）
        if ismember('小齿轮质量(kg)', first_stage_params.Properties.VariableNames)
            table_data(i, 14) = round(first_stage_params{first_stage_idx, '小齿轮质量(kg)'}, 2);
        end

        if ismember('大齿轮质量(kg)', first_stage_params.Properties.VariableNames)
            table_data(i, 15) = round(first_stage_params{first_stage_idx, '大齿轮质量(kg)'}, 2);
        end

    else
        % 使用计算值（不使用默认值）
        table_data(i, 4) = 0.33;   % k_h1 - 齿宽系数
        table_data(i, 5) = 0.2;    % x1 - 小齿轮变位系数
        table_data(i, 6) = 0.1;    % x2 - 大齿轮变位系数
        table_data(i, 7) = 10;     % beta1 - 螺旋角
        table_data(i, 8) = 20;     % alpha1 - 压力角
        table_data(i, 9) = m1 * (z1 + z2) / 2;  % a1 - 中心距（计算值）
        table_data(i, 11) = system_params.contact_safety_factor;   % SH1 - 接触安全系数（使用用户输入值）
        table_data(i, 12) = system_params.bending_safety_factor;   % SF1 - 小齿轮弯曲安全系数（使用用户输入值）
        table_data(i, 13) = system_params.bending_safety_factor;   % SF2 - 大齿轮弯曲安全系数（使用用户输入值）
        % M1和M2质量不设置默认值，只使用实际计算值
    end

    table_data(i, 10) = round(z2 / z1, 3);   % i1 - 传动比（保留三位小数）

    % 二级参数 (列16-39) - 使用约束修正后的参数
    table_data(i, 16) = round(current_variables(2));      % mn2 - 二级模数（离散值，整数）
    table_data(i, 17) = round(current_variables(3)); % zs2 - 二级太阳轮齿数
    table_data(i, 19) = round(current_variables(4)); % zr2 - 二级内齿圈齿数
    table_data(i, 18) = (table_data(i, 19) - table_data(i, 17)) / 2; % zp2 - 二级行星轮齿数（计算得出）
    table_data(i, 20) = round(current_variables(10)); % n2 - 二级行星轮数量 (修正：应该是第10个变量)
    table_data(i, 21) = round(current_variables(5), 2);      % k_h2 - 二级齿宽系数（0.01步长，保留两位小数）
    table_data(i, 22) = round(current_variables(15), 4);     % xs2 - 二级太阳轮变位系数（四位小数）
    table_data(i, 23) = round(current_variables(16), 4);     % xp2 - 二级行星轮变位系数（四位小数）
    table_data(i, 24) = round(-(current_variables(15)*table_data(i,17) + current_variables(16)*table_data(i,18))/table_data(i,19), 4); % xr2 - 二级内齿圈变位系数（正确公式）
    table_data(i, 25) = 0;                    % beta2 - 二级螺旋角
    table_data(i, 26) = 20;                   % alpha2 - 二级压力角
    % 计算二级中心距 - 显示实际值，不进行强制边界修正
    % current_variables(3) = zs2, current_variables(4) = zr2, table_data(i, 18) = zp2
    calculated_a2 = current_variables(2) * (round(current_variables(3)) + table_data(i, 18)) / 2;

    table_data(i, 27) = round(calculated_a2, 1); % a2 - 二级中心距（保留一位小数，显示实际值）
    table_data(i, 28) = round(1 + table_data(i, 19) / table_data(i, 17), 3); % i2 - 二级传动比（保留三位小数）
    % 二级安全系数和质量计算
    try
        % 构造二级行星轮系参数（按照PlanetaryGearMassCalculator的接口）
        stage2_params = struct();
        stage2_params.mn = current_variables(2);           % 模数
        stage2_params.z1 = round(current_variables(3));    % 太阳轮齿数
        stage2_params.zr = round(current_variables(4));    % 内齿圈齿数
        stage2_params.z2 = (stage2_params.zr - stage2_params.z1) / 2; % 行星轮齿数（计算得出）
        stage2_params.planets_count = round(current_variables(10)); % 行星轮数量
        stage2_params.b = stage2_params.mn * (stage2_params.z1 + stage2_params.z2) / 2 * current_variables(5); % 齿宽
        stage2_params.xs = current_variables(15);         % 太阳轮变位系数
        stage2_params.xp = current_variables(16);         % 行星轮变位系数
        stage2_params.xr = -current_variables(15) - current_variables(16); % 内齿圈变位系数
        stage2_params.stage = 2;                     % 级数标识

        % 计算二级行星轮系质量
        [ms2, mp2, mr2, ~] = PlanetaryGearMassCalculator(stage2_params);
        table_data(i, 37) = round(ms2, 2);  % Ms2 - 二级太阳轮质量（保留两位小数）
        table_data(i, 38) = round(mp2, 2);  % Mp2 - 二级行星轮质量（保留两位小数）
        table_data(i, 39) = round(mr2, 2);  % Mr2 - 二级内齿圈质量（保留两位小数）

        % 调用实际的行星轮系安全系数计算函数
        try
            % 构造齿轮参数结构体
            gear_params_2 = struct();

            % 太阳轮参数
            gear_params_2.sun = struct();
            gear_params_2.sun.m = stage2_params.mn;
            gear_params_2.sun.z = stage2_params.z1;
            gear_params_2.sun.alpha = 20;  % 压力角
            gear_params_2.sun.beta = 0;    % 螺旋角
            gear_params_2.sun.b = stage2_params.b;
            gear_params_2.sun.x = stage2_params.xs;

            % 行星轮参数
            gear_params_2.planet = struct();
            gear_params_2.planet.m = stage2_params.mn;
            gear_params_2.planet.z = stage2_params.z2;
            gear_params_2.planet.alpha = 20;
            gear_params_2.planet.beta = 0;
            gear_params_2.planet.b = stage2_params.b;
            gear_params_2.planet.x = stage2_params.xp;

            % 内齿圈参数
            gear_params_2.ring = struct();
            gear_params_2.ring.m = stage2_params.mn;
            gear_params_2.ring.z = stage2_params.zr;
            gear_params_2.ring.alpha = 20;
            gear_params_2.ring.beta = 0;
            gear_params_2.ring.b = stage2_params.b;
            gear_params_2.ring.x = stage2_params.xr;

            gear_params_2.planets_count = stage2_params.planets_count;

            % 构造载荷参数
            load_params_2 = struct();
            load_params_2.T = system_params.input_torque * table_data(i, 10);  % 二级输入扭矩 = 系统输入扭矩 × 一级传动比（减速器转矩放大）
            load_params_2.n = system_params.input_speed / table_data(i, 10);   % 二级输入转速 = 系统输入转速 / 一级传动比
            load_params_2.KA = 1.25;  % 应用系数
            load_params_2.service_life = system_params.service_life;

            % 获取材料参数
            all_materials = MaterialProperties();
            material_params_2 = struct();
            material_params_2.sun = all_materials.gear_materials.planet1_sun;
            material_params_2.planet = all_materials.gear_materials.planet1_planet;
            material_params_2.ring = all_materials.gear_materials.planet1_ring;



            % 调用安全系数计算函数
            [~, ~, detailed_safety_2] = PlanetarySystemSafetyCalculator(gear_params_2, load_params_2, material_params_2, 'ISO6336', 2);

            % 填入二级安全系数（使用实际计算值，保留三位小数）
            table_data(i, 29) = round(detailed_safety_2.SHsps, 3);      % SHsps2 - 太阳轮接触安全系数
            table_data(i, 30) = round(detailed_safety_2.SHspp, 3);      % SHspp2 - 行星轮接触安全系数
            table_data(i, 31) = round(detailed_safety_2.SFsps, 3);      % SFsps2 - 太阳轮弯曲安全系数
            table_data(i, 32) = round(detailed_safety_2.SFspp, 3);      % SFspp2 - 行星轮弯曲安全系数
            table_data(i, 33) = round(detailed_safety_2.SHprr, 3);      % SHprr2 - 内齿圈接触安全系数
            table_data(i, 34) = round(detailed_safety_2.SHprp, 3);      % SHprp2 - 行星轮与内齿圈接触安全系数
            table_data(i, 35) = round(detailed_safety_2.SFprr, 3);      % SFprr2 - 内齿圈弯曲安全系数
            table_data(i, 36) = round(detailed_safety_2.SFprp, 3);      % SFprp2 - 行星轮与内齿圈弯曲安全系数

        catch
            % 如果计算失败，使用简化估算（保留三位小数）
            base_sf = 1.0 + stage2_params.mn * 0.05 + (stage2_params.z1 + stage2_params.z2) * 0.005;
            base_sh = 1.0 + stage2_params.mn * 0.04 + (stage2_params.z1 + stage2_params.z2) * 0.004;

            table_data(i, 29) = round(base_sh, 3);   % SHsps2
            table_data(i, 30) = round(base_sh, 3);   % SHspp2
            table_data(i, 31) = round(base_sf, 3);   % SFsps2
            table_data(i, 32) = round(base_sf, 3);   % SFspp2
            table_data(i, 33) = round(base_sh, 3);   % SHprr2
            table_data(i, 34) = round(base_sh, 3);   % SHprp2
            table_data(i, 35) = round(base_sf, 3);   % SFprr2
            table_data(i, 36) = round(base_sf, 3);   % SFprp2
        end

    catch
        % 如果计算失败，使用用户输入的安全系数要求作为默认值（保留三位小数）
        for j = 29:36
            if j <= 32  % 接触安全系数列
                table_data(i, j) = round(system_params.contact_safety_factor, 3); % 使用用户输入的接触安全系数
            else  % 弯曲安全系数列
                table_data(i, j) = round(system_params.bending_safety_factor, 3); % 使用用户输入的弯曲安全系数
            end
        end
        % 质量不设置默认值，保持为0或NaN
    end

    % 三级参数 (列40-63) - 使用约束修正后的参数
    table_data(i, 40) = round(current_variables(6));      % mn3 - 三级模数（离散值，整数）
    table_data(i, 41) = round(current_variables(7)); % zs3 - 三级太阳轮齿数
    table_data(i, 43) = round(current_variables(8)); % zr3 - 三级内齿圈齿数
    table_data(i, 42) = (table_data(i, 43) - table_data(i, 41)) / 2; % zp3 - 三级行星轮齿数（计算得出）
    table_data(i, 44) = round(current_variables(11)); % n3 - 三级行星轮数量 (修正：应该是第11个变量)
    table_data(i, 45) = round(current_variables(9), 2);      % k_h3 - 三级齿宽系数（0.01步长，保留两位小数）
    table_data(i, 46) = round(current_variables(17), 4);     % xs3 - 三级太阳轮变位系数（四位小数）
    table_data(i, 47) = round(current_variables(18), 4);     % xp3 - 三级行星轮变位系数（四位小数）
    table_data(i, 48) = round(-(current_variables(17)*table_data(i,41) + current_variables(18)*table_data(i,42))/table_data(i,43), 4); % xr3 - 三级内齿圈变位系数（正确公式）
    table_data(i, 49) = 0;                    % beta3 - 三级螺旋角
    % 三级压力角
    if size(current_variables, 2) >= 19 && current_variables(19) > 0.5
        table_data(i, 50) = 25;               % alpha3 - 三级压力角
    else
        table_data(i, 50) = 20;
    end
    % 计算三级中心距 - 显示实际值，不进行强制边界修正
    % current_variables(7) = zs3, current_variables(8) = zr3, table_data(i, 42) = zp3
    calculated_a3 = current_variables(6) * (round(current_variables(7)) + table_data(i, 42)) / 2;

    table_data(i, 51) = round(calculated_a3, 1); % a3 - 三级中心距（保留一位小数，显示实际值）

    % 计算三级传动比，避免除零错误
    zs3 = table_data(i, 41);  % 三级太阳轮齿数
    if zs3 > 0
        table_data(i, 52) = round(1 + table_data(i, 43) / zs3, 3); % i3 - 三级传动比（保留三位小数）
    else
        table_data(i, 52) = 0; % 如果太阳轮齿数无效，设为0
    end
    % 三级安全系数和质量计算
    try
        % 构造三级行星轮系参数（按照PlanetaryGearMassCalculator的接口）
        stage3_params = struct();
        stage3_params.mn = current_variables(6);           % 模数
        stage3_params.z1 = round(current_variables(7));    % 太阳轮齿数
        stage3_params.zr = round(current_variables(8));    % 内齿圈齿数
        stage3_params.z2 = (stage3_params.zr - stage3_params.z1) / 2; % 行星轮齿数（计算得出）
        stage3_params.planets_count = round(current_variables(11)); % 行星轮数量
        stage3_params.b = stage3_params.mn * (stage3_params.z1 + stage3_params.z2) / 2 * current_variables(9); % 齿宽
        stage3_params.xs = current_variables(17);         % 太阳轮变位系数
        stage3_params.xp = current_variables(18);         % 行星轮变位系数
        stage3_params.xr = -current_variables(17) - current_variables(18); % 内齿圈变位系数
        stage3_params.stage = 3;                     % 级数标识

        % 三级外径约束（如果有的话）
        if isfield(system_params, 'outer_diameter_constraint')
            stage3_params.outer_diameter_constraint = system_params.outer_diameter_constraint;
        end

        % 计算三级行星轮系质量
        [ms3, mp3, mr3, ~] = PlanetaryGearMassCalculator(stage3_params);
        table_data(i, 61) = round(ms3, 2);  % Ms3 - 三级太阳轮质量（保留两位小数）
        table_data(i, 62) = round(mp3, 2);  % Mp3 - 三级行星轮质量（保留两位小数）
        table_data(i, 63) = round(mr3, 2);  % Mr3 - 三级内齿圈质量（保留两位小数）

        % 调用实际的行星轮系安全系数计算函数
        try
            % 构造齿轮参数结构体
            gear_params_3 = struct();

            % 确定三级压力角
            alpha3 = 20;  % 默认20度
            if size(variables, 2) >= 19 && variables(i, 19) > 0.5
                alpha3 = 25;  % 25度压力角
            end

            % 太阳轮参数
            gear_params_3.sun = struct();
            gear_params_3.sun.m = stage3_params.mn;
            gear_params_3.sun.z = stage3_params.z1;
            gear_params_3.sun.alpha = alpha3;
            gear_params_3.sun.beta = 0;    % 螺旋角
            gear_params_3.sun.b = stage3_params.b;
            gear_params_3.sun.x = stage3_params.xs;

            % 行星轮参数
            gear_params_3.planet = struct();
            gear_params_3.planet.m = stage3_params.mn;
            gear_params_3.planet.z = stage3_params.z2;
            gear_params_3.planet.alpha = alpha3;
            gear_params_3.planet.beta = 0;
            gear_params_3.planet.b = stage3_params.b;
            gear_params_3.planet.x = stage3_params.xp;

            % 内齿圈参数
            gear_params_3.ring = struct();
            gear_params_3.ring.m = stage3_params.mn;
            gear_params_3.ring.z = stage3_params.zr;
            gear_params_3.ring.alpha = alpha3;
            gear_params_3.ring.beta = 0;
            gear_params_3.ring.b = stage3_params.b;
            gear_params_3.ring.x = stage3_params.xr;

            gear_params_3.planets_count = stage3_params.planets_count;

            % 构造载荷参数
            load_params_3 = struct();
            load_params_3.T = system_params.input_torque * table_data(i, 10) * table_data(i, 28);  % 三级输入扭矩 = 系统输入扭矩 × 一级传动比 × 二级传动比（减速器转矩放大）
            load_params_3.n = system_params.input_speed / (table_data(i, 10) * table_data(i, 28));   % 三级输入转速 = 系统输入转速 / (一级传动比 × 二级传动比)
            load_params_3.KA = 1.25;  % 应用系数
            load_params_3.service_life = system_params.service_life;

            % 获取材料参数
            all_materials = MaterialProperties();
            material_params_3 = struct();
            material_params_3.sun = all_materials.gear_materials.planet2_sun;
            material_params_3.planet = all_materials.gear_materials.planet2_planet;
            material_params_3.ring = all_materials.gear_materials.planet2_ring;

            % 调用安全系数计算函数
            [~, ~, detailed_safety_3] = PlanetarySystemSafetyCalculator(gear_params_3, load_params_3, material_params_3, 'ISO6336', 3);

            % 填入三级安全系数（使用实际计算值，保留三位小数）
            table_data(i, 53) = round(detailed_safety_3.SHsps, 3);      % SHsps3 - 太阳轮接触安全系数
            table_data(i, 54) = round(detailed_safety_3.SHspp, 3);      % SHspp3 - 行星轮接触安全系数
            table_data(i, 55) = round(detailed_safety_3.SFsps, 3);      % SFsps3 - 太阳轮弯曲安全系数
            table_data(i, 56) = round(detailed_safety_3.SFspp, 3);      % SFspp3 - 行星轮弯曲安全系数
            table_data(i, 57) = round(detailed_safety_3.SHprr, 3);      % SHprr3 - 内齿圈接触安全系数
            table_data(i, 58) = round(detailed_safety_3.SHprp, 3);      % SHprp3 - 行星轮与内齿圈接触安全系数
            table_data(i, 59) = round(detailed_safety_3.SFprr, 3);      % SFprr3 - 内齿圈弯曲安全系数
            table_data(i, 60) = round(detailed_safety_3.SFprp, 3);      % SFprp3 - 行星轮与内齿圈弯曲安全系数

        catch
            % 如果计算失败，使用简化估算（保留三位小数）
            base_sf = 1.0 + stage3_params.mn * 0.05 + (stage3_params.z1 + stage3_params.z2) * 0.005;
            base_sh = 1.0 + stage3_params.mn * 0.04 + (stage3_params.z1 + stage3_params.z2) * 0.004;

            % 三级压力角影响
            if size(current_variables, 2) >= 19 && current_variables(19) > 0.5
                base_sf = base_sf * 1.05;
                base_sh = base_sh * 1.05;
            end

            table_data(i, 53) = round(base_sh, 3);   % SHsps3
            table_data(i, 54) = round(base_sh, 3);   % SHspp3
            table_data(i, 55) = round(base_sf, 3);   % SFsps3
            table_data(i, 56) = round(base_sf, 3);   % SFspp3
            table_data(i, 57) = round(base_sh, 3);   % SHprr3
            table_data(i, 58) = round(base_sh, 3);   % SHprp3
            table_data(i, 59) = round(base_sf, 3);   % SFprr3
            table_data(i, 60) = round(base_sf, 3);   % SFprp3
        end

    catch
        % 如果计算失败，使用用户输入的安全系数要求作为默认值（保留三位小数）
        for j = 53:60
            if mod(j-53, 2) == 0  % 接触安全系数列 (53, 55, 57, 59)
                table_data(i, j) = round(system_params.contact_safety_factor, 3); % 使用用户输入的接触安全系数
            else  % 弯曲安全系数列 (54, 56, 58, 60)
                table_data(i, j) = round(system_params.bending_safety_factor, 3); % 使用用户输入的弯曲安全系数
            end
        end
        % 质量不设置默认值，保持为0或NaN
    end

    % 汇总指标 (列64-68)
    % 计算纯齿轮质量（从各级质量相加）
    M1 = table_data(i, 14);  % 一级小齿轮质量
    M2 = table_data(i, 15);  % 一级大齿轮质量
    Ms2 = table_data(i, 37); % 二级太阳轮质量
    Mp2 = table_data(i, 38); % 二级行星轮质量
    Mr2 = table_data(i, 39); % 二级内齿圈质量
    Ms3 = table_data(i, 61); % 三级太阳轮质量
    Mp3 = table_data(i, 62); % 三级行星轮质量
    Mr3 = table_data(i, 63); % 三级内齿圈质量

    % 计算总质量（考虑行星轮数量）
    n2 = table_data(i, 20);  % 二级行星轮数量
    n3 = table_data(i, 44);  % 三级行星轮数量

    % 注意：Mp2和Mp3已经是所有行星轮的总质量，不需要再乘以数量
    pure_mass = M1 + M2 + Ms2 + Mp2 + Mr2 + Ms3 + Mp3 + Mr3;
    table_data(i, 64) = round(pure_mass, 2);  % TotalMass - 纯齿轮质量（保留两位小数）

    % 计算系统最小安全系数（从各级齿轮中找出最小值）
    % 一级安全系数
    SH1 = table_data(i, 11);  % 一级接触安全系数
    SF1 = min(table_data(i, 12), table_data(i, 13));  % 一级弯曲安全系数（小齿轮和大齿轮的最小值）

    % 二级安全系数（从所有二级齿轮中找最小值）
    SH2_values = [table_data(i, 29), table_data(i, 30), table_data(i, 33), table_data(i, 34)];  % 二级接触安全系数
    SF2_values = [table_data(i, 31), table_data(i, 32), table_data(i, 35), table_data(i, 36)];  % 二级弯曲安全系数
    SH2 = min(SH2_values(SH2_values > 0));  % 排除0值，找最小值
    SF2 = min(SF2_values(SF2_values > 0));  % 排除0值，找最小值

    % 三级安全系数（从所有三级齿轮中找最小值）
    SH3_values = [table_data(i, 53), table_data(i, 54), table_data(i, 57), table_data(i, 58)];  % 三级接触安全系数
    SF3_values = [table_data(i, 55), table_data(i, 56), table_data(i, 59), table_data(i, 60)];  % 三级弯曲安全系数
    SH3 = min(SH3_values(SH3_values > 0));  % 排除0值，找最小值
    SF3 = min(SF3_values(SF3_values > 0));  % 排除0值，找最小值

    % 系统最小安全系数
    all_SH = [SH1, SH2, SH3];
    all_SF = [SF1, SF2, SF3];
    min_SH = min(all_SH(all_SH > 0));  % 系统最小接触安全系数
    min_SF = min(all_SF(all_SF > 0));  % 系统最小弯曲安全系数

    % 处理空值情况
    if isempty(min_SH) || min_SH <= 0
        min_SH = 1.0;  % 默认值
    end
    if isempty(min_SF) || min_SF <= 0
        min_SF = 1.0;  % 默认值
    end

    table_data(i, 65) = round(min_SH, 3);    % SH - 系统最小接触安全系数（保留三位小数）
    table_data(i, 66) = round(min_SF, 3);    % SF - 系统最小弯曲安全系数（保留三位小数）
    table_data(i, 67) = round(table_data(i, 10) * table_data(i, 28) * table_data(i, 52), 3); % TotalRatio - 总传动比（保留三位小数）

    % 计算传动比误差
    target_ratio = system_params.total_ratio;  % 目标传动比
    current_ratio = table_data(i, 67);         % 当前传动比
    ratio_error = abs(current_ratio - target_ratio) / target_ratio * 100;  % 相对误差百分比
    table_data(i, 68) = round(ratio_error, 2); % Error - 传动比误差（百分比，保留两位小数）
end

% 创建表格
param_table = array2table(table_data, 'VariableNames', column_names);

% 对表格进行排序
% 排序优先级（严格按照工程要求）：
% 1. 首先筛选满足安全系数要求的解（接触安全系数 >= 设定值 且 弯曲安全系数 >= 设定值）
% 2. 然后筛选满足传动比范围的解（传动比误差 <= 2%，严格控制）
% 3. 最后按总质量从小到大排序（质量越小越优）

% 获取安全系数和传动比误差的列索引
SH_col = 65;  % 系统最小接触安全系数
SF_col = 66;  % 系统最小弯曲安全系数
TotalMass_col = 64;  % 总质量
Error_col = 68;  % 传动比误差

% 定义约束条件（严格按照工程要求）
min_contact_safety = system_params.contact_safety_factor;  % 最低接触安全系数要求
min_bending_safety = system_params.bending_safety_factor;  % 最低弯曲安全系数要求
max_ratio_error = 2.0;  % 最大传动比误差百分比（严格控制在2%以内）

% 创建筛选条件
safety_satisfied = (param_table{:, SH_col} >= min_contact_safety) & ...
                  (param_table{:, SF_col} >= min_bending_safety);
ratio_satisfied = param_table{:, Error_col} <= max_ratio_error;

% 创建综合满足条件（同时满足安全系数和传动比要求）
both_satisfied = safety_satisfied & ratio_satisfied;

% 创建多级排序权重：
% 第一优先级：同时满足安全系数和传动比要求的解（权重10000）
% 第二优先级：仅满足安全系数要求的解（权重1000）
% 第三优先级：仅满足传动比要求的解（权重100）
% 第四优先级：都不满足的解（权重0）
sort_priority = double(both_satisfied) * 10000 + ...
               double(safety_satisfied & ~ratio_satisfied) * 1000 + ...
               double(~safety_satisfied & ratio_satisfied) * 100;

% 创建排序矩阵：[优先级, 总质量（正值，质量小的排前面）, 传动比误差, 原始索引]
% 在同一优先级内，先按质量升序排序（质量小的在前），再按传动比误差升序排序
sort_matrix = [sort_priority, param_table{:, TotalMass_col}, param_table{:, Error_col}, (1:height(param_table))'];

% 按优先级降序、质量升序、传动比误差升序排序
[~, sort_idx] = sortrows(sort_matrix, [-1, 2, 3]);

% 重新排序表格
param_table = param_table(sort_idx, :);

% 简化输出信息
% fprintf('算法 %s: 表格已按优先级排序（安全系数+传动比+质量最小）\n', algorithm_name);

end

% 算法调用适配器函数 - 调整参数接口以匹配现有算法
function [population, objectives] = callNSGAII(problem, params)
% NSGA-II算法调用适配器
try
    % 添加算法路径
    addpath('Algorithms/NSGA-II');

    % 检查算法函数是否存在
    if exist('RunNSGAII', 'file') ~= 2
        error('RunNSGAII函数未找到，请检查路径设置');
    end

    % 设置NSGA-II特有参数
    if ~isfield(params, 'pCrossover')
        params.pCrossover = 0.7;  % NSGA-II原有参数：交叉概率0.7
    end
    if ~isfield(params, 'pMutation')
        params.pMutation = 0.4;   % NSGA-II原有参数：变异概率0.4
    end



    % 调用NSGA-II算法
    [population, objectives] = RunNSGAII(problem, params);
catch ME
    fprintf('NSGA-II接口调用失败: %s\n', ME.message);
    fprintf('错误详情: %s\n', ME.getReport());
    % 返回空结果
    population = [];
    objectives = [];
end
end

function [population, objectives] = callNSGAIII(problem, params)
% NSGA-III算法调用适配器
try
    % 设置NSGA-III特有参数
    if ~isfield(params, 'pCrossover')
        params.pCrossover = 0.7;  % NSGA-III原有参数：交叉概率0.7
    end
    if ~isfield(params, 'pMutation')
        params.pMutation = 0.4;   % NSGA-III原有参数：变异概率0.4
    end

    [population, objectives] = RunNSGAIII(problem, params);
catch ME
    fprintf('NSGA-III接口调用失败: %s\n', ME.message);
    fprintf('错误详情: %s\n', ME.getReport());
    population = [];
    objectives = [];
end
end

function [population, objectives] = callSPEA2(problem, params)
% SPEA2算法调用适配器
try
    % 设置SPEA2特有参数
    if ~isfield(params, 'pCrossover')
        params.pCrossover = 0.7;  % SPEA2原有参数：交叉概率0.7
    end
    if ~isfield(params, 'pMutation')
        params.pMutation = 0.3;   % SPEA2原有参数：变异概率1-pCrossover=0.3
    end

    [population, objectives] = RunSPEA2(problem, params);
catch ME
    fprintf('SPEA2接口调用失败: %s\n', ME.message);
    fprintf('错误详情: %s\n', ME.getReport());
    population = [];
    objectives = [];
end
end

function [population, objectives] = callMOEAD(problem, params)
% MOEA/D算法调用适配器
try
    % 设置MOEA/D特有参数
    if ~isfield(params, 'pCrossover')
        params.pCrossover = 0.9;  % MOEA/D原有参数：交叉概率0.9
    end
    if ~isfield(params, 'pMutation')
        params.pMutation = 0.1;   % MOEA/D原有参数：变异概率0.1
    end

    [population, objectives] = RunMOEAD(problem, params);
catch ME
    fprintf('MOEA/D接口调用失败: %s\n', ME.message);
    fprintf('错误详情: %s\n', ME.getReport());
    population = [];
    objectives = [];
end
end

function [population, objectives] = callMOEADDE(problem, params)
% MOEA/D-DE算法调用适配器
try
    if ~isfield(params, 'pCrossover')
        params.pCrossover = 0.9;
    end
    if ~isfield(params, 'pMutation')
        params.pMutation = 0.1;
    end
    [population, objectives] = RunMOEAD_DE(problem, params);
catch ME
    fprintf('MOEA/D-DE接口调用失败: %s\n', ME.message);
    fprintf('错误详情: %s\n', ME.getReport());
    population = [];
    objectives = [];
end
end

function [population, objectives] = callMOEADM2M(problem, params)
% MOEA/D-M2M算法调用适配器
try
    if ~isfield(params, 'pCrossover')
        params.pCrossover = 0.9;
    end
    if ~isfield(params, 'pMutation')
        params.pMutation = 0.1;
    end
    [population, objectives] = RunMOEAD_M2M(problem, params);
catch ME
    fprintf('MOEA/D-M2M接口调用失败: %s\n', ME.message);
    fprintf('错误详情: %s\n', ME.getReport());
    population = [];
    objectives = [];
end
end

function [population, objectives] = callMOPSO(problem, params)
% MOPSO算法调用适配器
try
    if ~isfield(params, 'pCrossover')
        params.pCrossover = 0.5;
    end
    if ~isfield(params, 'pMutation')
        params.pMutation = 0.5;
    end
    [population, objectives] = RunMOPSO(problem, params);
catch ME
    fprintf('MOPSO接口调用失败: %s\n', ME.message);
    fprintf('错误详情: %s\n', ME.getReport());
    population = [];
    objectives = [];
end
end

function [population, objectives] = callMOGWO(problem, params)
% MOGWO算法调用适配器
try
    if ~isfield(params, 'pCrossover')
        params.pCrossover = 0.5;
    end
    if ~isfield(params, 'pMutation')
        params.pMutation = 0.5;
    end
    [population, objectives] = RunMOGWO(problem, params);
catch ME
    fprintf('MOGWO接口调用失败: %s\n', ME.message);
    fprintf('错误详情: %s\n', ME.getReport());
    population = [];
    objectives = [];
end
end

function [population, objectives] = callMOWOA(problem, params)
% MOWOA算法调用适配器
try
    if ~isfield(params, 'pCrossover')
        params.pCrossover = 0.5;
    end
    if ~isfield(params, 'pMutation')
        params.pMutation = 0.5;
    end
    [population, objectives] = RunMOWOA(problem, params);
catch ME
    fprintf('MOWOA接口调用失败: %s\n', ME.message);
    fprintf('错误详情: %s\n', ME.getReport());
    population = [];
    objectives = [];
end
end

%% 科学变位系数约束函数
function penalty = applyScientificShiftConstraints(z1, z2, x1, x2, module, alpha_deg, beta_deg, stage)
    % 应用科学的变位系数约束
    % 输入：
    %   z1, z2: 太阳轮和行星轮齿数
    %   x1, x2: 太阳轮和行星轮变位系数
    %   module: 模数
    %   alpha_deg: 压力角（度）
    %   beta_deg: 螺旋角（度）
    %   stage: 级数（用于调试输出）

    penalty = 0;
    alpha = alpha_deg * pi / 180;  % 转换为弧度

    try
        % 1. 防根切约束
        x1_min_undercut = max(0, (17 - z1) / 17);
        x2_min_undercut = max(0, (17 - z2) / 17);

        if x1 < x1_min_undercut
            penalty = penalty + 200 * (x1_min_undercut - x1)^2;
        end
        if x2 < x2_min_undercut
            penalty = penalty + 200 * (x2_min_undercut - x2)^2;
        end

        % 2. 齿顶厚度约束（放宽以允许更大变位系数改善接触条件）
        % 基于经验公式，但放宽上限以提高接触安全系数
        x1_max_tip = 1.0 - 0.005 * max(0, 25 - z1);  % 放宽上限，减小系数
        x2_max_tip = 1.0 - 0.005 * max(0, 25 - z2);  % 放宽上限，减小系数

        if x1 > x1_max_tip
            penalty = penalty + 100 * (x1 - x1_max_tip)^2;  % 减小惩罚权重
        end
        if x2 > x2_max_tip
            penalty = penalty + 100 * (x2 - x2_max_tip)^2;  % 减小惩罚权重
        end

        % 3. 重合度约束（鼓励正变位以提高重合度和接触安全系数）
        % 对于标准齿轮，重合度约为：ε ≈ 1.88 - 3.2/z_eq + (x1+x2)*tan(α)
        z_eq = 2 * z1 * z2 / (z1 + z2);  % 当量齿数
        estimated_contact_ratio = 1.88 - 3.2/z_eq + (x1 + x2) * tan(alpha);

        if estimated_contact_ratio < 1.2
            penalty = penalty + 200 * (1.2 - estimated_contact_ratio)^2;  % 减小惩罚权重
        end

        % 4. 变位系数和的合理性检查（鼓励适度正变位）
        sum_x = x1 + x2;
        % 鼓励正变位系数和，有利于提高接触安全系数
        if sum_x < 0.0
            penalty = penalty + 200 * (0.0 - sum_x)^2;  % 惩罚负变位系数和
        elseif sum_x > 1.6  % 放宽上限
            penalty = penalty + 100 * (sum_x - 1.6)^2;
        end

        % 5. 变位系数差的合理性检查（允许更大差值以优化接触条件）
        diff_x = abs(x1 - x2);
        max_reasonable_diff = 1.0;  % 放宽最大合理差值
        if diff_x > max_reasonable_diff
            penalty = penalty + 50 * (diff_x - max_reasonable_diff)^2;  % 减小惩罚权重
        end

        % 6. 鼓励正变位系数以提高接触安全系数
        % 正变位可以增加齿顶圆直径，改善接触条件
        if x1 < 0.1
            penalty = penalty + 50 * (0.1 - x1)^2;  % 鼓励太阳轮正变位
        end
        if x2 < 0.1
            penalty = penalty + 50 * (0.1 - x2)^2;  % 鼓励行星轮正变位
        end

    catch ME
        % 如果计算出错，施加轻微惩罚
        penalty = penalty + 50;
    end
end

function penalty = applyBasicShiftConstraints(z1, z2, x1, x2, stage)
    % 应用基本的变位系数约束（当科学计算失败时使用）
    penalty = 0;

    % 1. 基本防根切约束
    if z1 < 17 && x1 < 0.1
        penalty = penalty + 100 * (0.1 - x1)^2;
    end
    if z2 < 17 && x2 < 0.1
        penalty = penalty + 100 * (0.1 - x2)^2;
    end

    % 2. 基本上限约束
    if x1 > 0.6
        penalty = penalty + 150 * (x1 - 0.6)^2;
    end
    if x2 > 0.6
        penalty = penalty + 150 * (x2 - 0.6)^2;
    end

    % 3. 变位系数和的基本约束
    sum_x = x1 + x2;
    if sum_x < -0.3 || sum_x > 1.2
        penalty = penalty + 100 * abs(sum_x - max(-0.3, min(1.2, sum_x)));
    end
end







