# Copy algorithm files
$sourceDir = "C:\Users\<USER>\Desktop\多目标优化算法汇总\多目标整理合成"
$algorithmFiles = @("NSGA2.m", "NSGA3.m", "MODA.m", "MODE.m", "MOEAD.m", "MOGOA.m", "MOGWO.m", "MOMVO.m", "MOPSO.m", "MSSA.m", "NSDBO.m", "PESA2.m", "SPEA2.m", "NSWOA.m")

foreach ($file in $algorithmFiles) {
    $sourcePath = Join-Path $sourceDir $file
    $destPath = Join-Path $sourceDir "Algorithms"
    if (Test-Path $sourcePath) {
        Copy-Item $sourcePath $destPath
        Write-Host "Copied $file to Algorithms folder"
    } else {
        Write-Host "File not found: $file"
    }
}

# Copy evaluation metrics
$metricsSource = Join-Path $sourceDir "EvaluationMetrics.m"
$metricsDest = Join-Path $sourceDir "Metrics"
if (Test-Path $metricsSource) {
    Copy-Item $metricsSource $metricsDest
    Write-Host "Copied EvaluationMetrics.m to Metrics folder"
}

# Also copy individual metric files from the metrics folder
$metricsDir = "C:\Users\<USER>\Desktop\多目标优化算法汇总\多目标优化的评价指标"
$metricFiles = @("GD.m", "IGD.m", "Spacing.m", "Spread.m", "HV.m")

foreach ($file in $metricFiles) {
    $sourcePath = Join-Path $metricsDir $file
    $destPath = Join-Path $sourceDir "Metrics"
    if (Test-Path $sourcePath) {
        Copy-Item $sourcePath $destPath
        Write-Host "Copied $file to Metrics folder"
    } else {
        Write-Host "File not found: $file"
    }
}

# Copy test function files
$testFuncSource = Join-Path $sourceDir "TestProblems.m"
$testFuncDest = Join-Path $sourceDir "TestFunctions"
if (Test-Path $testFuncSource) {
    Copy-Item $testFuncSource $testFuncDest
    Write-Host "Copied TestProblems.m to TestFunctions folder"
} 