function constraint_manager = ConstraintManager()
% ConstraintManager 简化的约束管理器
% 只提供重构后代码实际使用的接口
%
% 输出:
%   constraint_manager - 约束管理器结构体，包含获取约束的方法

% 加载配置文件
constraints = ConstraintRanges();

% 创建约束管理器结构体
constraint_manager = struct();

%% 实际使用的方法
constraint_manager.getFirstStageConstraints = @() getFirstStageConstraints(constraints);
constraint_manager.getGeometryConstraints = @() getGeometryConstraints(constraints);
constraint_manager.getAlgorithmList = @() getAlgorithmList(constraints);
constraint_manager.getSystemDefaults = @() getSystemDefaults(constraints);

end

%% ========== 约束获取函数 ==========

function first_stage = getFirstStageConstraints(constraints)
% 获取一级齿轮约束
first_stage = constraints.display_ranges.first_stage;
end

function geometry = getGeometryConstraints(constraints)
% 获取几何约束
geometry = constraints.engineering.geometry;
end

function algorithm_list = getAlgorithmList(constraints)
% 获取算法列表
algorithm_list = constraints.algorithms;
end

function system_defaults = getSystemDefaults(constraints)
% 获取系统默认参数
system_defaults = constraints.system_defaults;
end
