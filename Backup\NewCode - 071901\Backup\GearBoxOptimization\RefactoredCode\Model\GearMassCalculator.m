function [mass1, mass2, total_mass] = GearMassCalculator(gear_params, gear_type)
% GearMassCalculator 齿轮质量计算器接口函数
% 严格按照原有GearOptObjectives.m中的质量计算方法，完全保持一致
%
% 输入参数:
%   gear_params - 齿轮几何参数结构体
%   gear_type - 齿轮类型 ('parallel' 或 'planetary')
%
% 输出:
%   mass1 - 第一个齿轮质量 (kg)
%   mass2 - 第二个齿轮质量 (kg) 
%   total_mass - 总质量 (kg)
%
% 注意：此函数作为接口，实际计算由以下独立函数完成：
%   - ParallelGearMassCalculator.m - 平行轴齿轮质量计算
%   - PlanetaryGearMassCalculator.m - 行星轮系质量计算

if nargin < 2
    gear_type = 'parallel';
end

switch gear_type
    case 'parallel'
        % 平行轴齿轮质量计算（调用独立函数）
        [mass1, mass2, total_mass] = ParallelGearMassCalculator(gear_params);
        
    case 'planetary'
        % 行星齿轮质量计算（调用独立函数）
        % 注意：行星轮系返回4个值，这里适配为3个值的接口
        [mass_sun, mass_planet, mass_ring, total_mass_all] = PlanetaryGearMassCalculator(gear_params);
        
        % 适配输出格式
        mass1 = mass_sun;           % 太阳轮质量
        mass2 = mass_planet;        % 行星轮总质量
        total_mass = mass_sun + mass_planet;  % 不包括内齿圈（通常固定）
        
    otherwise
        error('未知的齿轮类型: %s', gear_type);
end

end
