%% PopObj：算法求得的pareto解集
%% PF：真实的解集
% 收敛性指标：Hypervolume则是用于测量算法生成的解集合能够覆盖的真实前沿解的体积大小的指标。目标是越大越好，即算法生成的解集合应该尽可能覆盖更多的真实前沿解。
function [Score,PopObj] = HV(PopObj,PF)
% Hypervolume
[N,M]  = size(PopObj);
fmin   = min(min(PopObj,[],1),zeros(1,M));
fmax   = max(PF,[],1);
PopObj = (PopObj-repmat(fmin,N,1))./repmat((fmax-fmin)*1.1,N,1);
PopObj(any(PopObj>1,2),:) = [];
% The reference point is set to (1,1,...)
RefPoint = ones(1,M);
if isempty(PopObj)
    Score = 0;
elseif M < 4
    % Calculate the exact HV value
    pl = sortrows(PopObj);
    S  = {1,pl};
    for k = 1 : M-1
        S_ = {};
        for i = 1 : size(S,1)
            Stemp = Slice(cell2mat(S(i,2)),k,RefPoint);
            for j = 1 : size(Stemp,1)
                temp(1) = {cell2mat(Stemp(j,1))*cell2mat(S(i,1))};
                temp(2) = Stemp(j,2);
                S_      = Add(temp,S_);
            end
        end
        S = S_;
    end
    Score = 0;
    for i = 1 : size(S,1)
        Score = Score + cell2mat(S(i,1));
    end
else
    % Use Monte Carlo method to calculate the HV value
    SampleNum = 1000000;
    MaxValue  = RefPoint;
    MinValue  = min(PopObj,[],1);
    Samples   = repmat(MinValue,SampleNum,1) + repmat(MaxValue-MinValue,SampleNum,1).*rand(SampleNum,M);
    for i = 1 : size(PopObj,1)
        domi = true(size(Samples,1),1);
        for j = 1 : M
            domi = domi & PopObj(i,j) <= Samples(:,j);
        end
        Samples(domi,:) = [];
    end
    Score = prod(MaxValue-MinValue)*(1-size(Samples,1)/SampleNum);
end
end

function S = Slice(pl,k,RefPoint)
p  = Head(pl);
pl = Tail(pl);
ql = [];
S  = {};
while ~isempty(pl)
    ql  = Insert(p,k+1,ql);
    p_  = Head(pl);
    cell2mat(p_(k)) = cell2mat(p(k));
    pl  = Tail(pl);
    p   = p_;
end
ql = Insert(p,k+1,ql);
S  = Slice_rec(ql,k,RefPoint,S);
end

function S = Slice_rec(ql,k,RefPoint,S)
if length(ql) == 0
    return;
elseif length(ql) == 1
    S = Add({RefPoint(k)-cell2mat(ql{1}(k)),ql{1}},S);
    return;
else
    i = 1;
    while i < length(ql)
        Stemp = Slice_rec(ql(i+1:end),k,RefPoint,{});
        for j = 1:size(Stemp,1)
            temp(1) = {cell2mat(Stemp(j,1))*(cell2mat(ql{i}(k))-cell2mat(ql{i+1}(k)))};
            temp(2) = Stemp(j,2);
            S       = Add(temp,S);
        end
        i = i+1;
    end
end
end

function ql = Insert(p,k,ql)
flag1 = 0;
flag2 = 0;
ql_   = {};
while ~isempty(ql) && cell2mat(Head(ql)(k)) > cell2mat(p(k))
    ql_ = [ql_,ql(1)];
    ql  = Tail(ql);
end
ql_ = [ql_,p];
ql  = [ql_,ql];

i = 1;
while i <= length(ql)-1
    if cell2mat(ql{i}(k)) >= cell2mat(ql{i+1}(k))
        ql(i) = [];
        i = i-1;
    end
    i = i+1;
end
end

function p = Head(pl)
if isempty(pl)
    p = [];
else
    p = pl(1);
end
end

function ql = Tail(pl)
if length(pl) < 2
    ql = [];
else
    ql = pl(2:end);
end
end

function S = Add(temp,S)
n = size(S,1);
m = 0;
for k = 1:n
    if isequal(temp{2},S{k,2})
        S{k,1} = cell2mat(S(k,1)) + cell2mat(temp(1));
        m = 1;
        break;
    end
end
if m == 0
    S(n+1,:) = temp;
end
end
