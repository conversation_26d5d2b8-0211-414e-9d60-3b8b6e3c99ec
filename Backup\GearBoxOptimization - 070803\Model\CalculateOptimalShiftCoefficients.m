function [shift_ranges, optimal_shifts] = CalculateOptimalShiftCoefficients(gear_params)
% CalculateOptimalShiftCoefficients 计算齿轮变位系数的科学约束范围和最优值
%
% 输入参数:
%   gear_params - 结构体，包含以下字段:
%     - z1, z2: 平行轴齿轮的齿数 (对行星轮系，z1=zs, z2=zp)
%     - alpha: 压力角 (度)
%     - beta: 螺旋角 (度)
%     - center_distance: 中心距 (mm) (可选)
%     - module: 模数 (mm)
%     - is_planetary: 是否为行星轮系
%     - is_internal: 是否为内啮合 (行星轮-内齿圈啮合)
%     - zr: 内齿圈齿数 (仅当is_planetary=true时需要)
%
% 输出参数:
%   shift_ranges - 结构体，包含变位系数的合理范围:
%     - x1_min, x1_max: 第一个齿轮 (小齿轮/太阳轮) 的变位系数范围
%     - x2_min, x2_max: 第二个齿轮 (大齿轮/行星轮) 的变位系数范围
%     - xr_min, xr_max: 内齿圈的变位系数范围 (仅当is_planetary=true时)
%   optimal_shifts - 结构体，包含最优变位系数:
%     - x1_opt: 第一个齿轮的最优变位系数
%     - x2_opt: 第二个齿轮的最优变位系数
%     - xr_opt: 内齿圈的最优变位系数 (仅当is_planetary=true时)

% 参数提取
z1 = gear_params.z1;
z2 = gear_params.z2;
alpha_deg = gear_params.alpha;
beta_deg = gear_params.beta;
module = gear_params.module;
is_planetary = false;
is_internal = false;
zr = 0;

if isfield(gear_params, 'is_planetary')
    is_planetary = gear_params.is_planetary;
end

if isfield(gear_params, 'is_internal')
    is_internal = gear_params.is_internal;
end

if isfield(gear_params, 'zr') && is_planetary
    zr = gear_params.zr;
end

% 转换角度为弧度
alpha = alpha_deg * pi/180;
beta = beta_deg * pi/180;

% 计算啮合角(考虑螺旋角)
alpha_t = atan(tan(alpha)/cos(beta));

% 初始化输出结构体
shift_ranges = struct('x1_min', 0, 'x1_max', 0, 'x2_min', 0, 'x2_max', 0);
optimal_shifts = struct('x1_opt', 0, 'x2_opt', 0);

if is_planetary
    shift_ranges.xr_min = 0;
    shift_ranges.xr_max = 0;
    optimal_shifts.xr_opt = 0;
end

% 1. 计算防止根切的最小变位系数 - 使用更精确的公式
% 标准公式：对于z < 17(当压力角α=20°)
min_teeth_no_undercut = 2 / (sin(alpha)^2);

% 使用公式(13)和(14)，更精确的防根切条件
if ~is_internal
    % 外啮合
    if z1 >= z2
        x1_min_undercut = max(0, (17 - z2) / 17); % 基于小齿数齿轮
        x2_min_undercut = max(0, (17 - z2) / 17);
    else
        x1_min_undercut = max(0, (17 - z1) / 17);
        x2_min_undercut = max(0, (17 - z1) / 17);
    end
else
    % 内啮合 (z1=zp, z2=zr)
    % 行星轮变位系数下限
    x1_min_undercut = max(0, (17 - z1) / 17);
    % 内齿圈变位系数上限关系
    xr_max_interference = -x1_min_undercut * z1 / z2;
    shift_ranges.xr_max = xr_max_interference;
end

% 2. 计算基于齿顶厚度约束的最大变位系数
% 公式(15)的实现：保证齿顶厚度sa > 0.4m
% 设定标准参数
ha_star = 1.0;  % 标准齿顶高系数
sa_min = 0.4;   % 最小齿顶厚度系数(相对于模数)

% 基于公式(15)的齿顶厚度计算
function x_max = calc_max_x_for_tip_thickness(z, alpha)
    % 精确计算齿顶变位系数上限
    fun = @(x) calc_tip_thickness(z, x, alpha) - 0.4;
    x_max = fzero(fun, 1);
    return;
end

function sa = calc_tip_thickness(z, x, alpha)
    % 计算齿顶厚度
    inv_alpha = tan(alpha) - alpha;
    alpha_a = acos(cos(alpha) * z / (z + 2));
    inv_alpha_a = tan(alpha_a) - alpha_a;
    sa = module * (pi/(2*z) + 2*x*tan(alpha)/z - (inv_alpha_a - inv_alpha));
    return;
end

% 计算各齿轮的最大变位系数
x1_max_tip = calc_max_x_for_tip_thickness(z1, alpha);
x2_max_tip = calc_max_x_for_tip_thickness(z2, alpha);

% 3. 计算中心距修改系数
center_distance_factor = 0;
if isfield(gear_params, 'center_distance') && gear_params.center_distance > 0
    normal_center_distance = module * (z1 + z2) / (2 * cos(beta));
    center_distance_factor = (gear_params.center_distance - normal_center_distance) / (module * cos(beta));
end

% 4. 基于中心距因子的变位系数和
% 根据公式(10)(11)计算无侧隙啮合的变位系数关系
inv_alpha = tan(alpha) - alpha;

% 计算工作中心距下的啮合角alpha_w
if is_internal
    center_sum = (z2 - z1)/2;  % 内啮合中心距
else
    center_sum = (z1 + z2)/2;  % 外啮合中心距
end
center_sum_modified = center_sum + center_distance_factor;

% 计算工作压力角
alpha_w = acos(center_sum * cos(alpha) / center_sum_modified);
inv_alpha_w = tan(alpha_w) - alpha_w;

% 变位系数和
if is_internal
    sum_x = (z2 - z1) * (inv_alpha_w - inv_alpha) / (2 * tan(alpha));
else
    sum_x = (z1 + z2) * (inv_alpha_w - inv_alpha) / (2 * tan(alpha));
end

% 5. 计算最优变位系数 - 使用Maag公式实现滑动率平衡

% 外啮合情况 - 根据滑动磨损均衡原则(Maag公式)
if ~is_internal
    u = z2 / z1; % 传动比
    x1_opt_sliding = sum_x * (u - 1) / (u + 1);
    x2_opt_sliding = sum_x - x1_opt_sliding;
else
    % 内啮合情况 - 滑动率平衡
    u = -z2 / z1; % 内啮合传动比(负值)
    x1_opt_sliding = sum_x * (u + 1) / (u - 1);
    x2_opt_sliding = -x1_opt_sliding * z1 / z2;
end

% 6. 计算重合度并检查是否满足要求(ε ≥ 1.2)
function epsilon = calculate_contact_ratio(z1, z2, x1, x2, alpha, module, is_internal)
    % 计算齿轮副重合度
    % 参数计算
    ha = module;  % 标准齿顶高
    
    if ~is_internal
        % 外啮合重合度计算
        a = module * (z1 + z2) / 2;  % 标准中心距
        rb1 = (z1 * module * cos(alpha)) / 2;  % 基圆半径1
        rb2 = (z2 * module * cos(alpha)) / 2;  % 基圆半径2
        ra1 = (z1 * module) / 2 + ha + x1 * module;  % 齿顶圆半径1
        ra2 = (z2 * module) / 2 + ha + x2 * module;  % 齿顶圆半径2
        
        % 计算啮合极限点
        rho_a1 = sqrt(ra1^2 - rb1^2);
        rho_a2 = sqrt(ra2^2 - rb2^2);
        rho_p = a * sin(alpha);  % 节圆啮合点与中心连线的垂距
        
        % 重合度
        p_b = pi * module * cos(alpha);  % 基节圆弧长
        epsilon = (rho_a1 + rho_a2 - rho_p * 2) / p_b;
    else
        % 内啮合重合度计算
        a = module * (z2 - z1) / 2;  % 内啮合中心距
        rb1 = (z1 * module * cos(alpha)) / 2;  % 小轮基圆半径
        rb2 = (z2 * module * cos(alpha)) / 2;  % 内齿圈基圆半径
        ra1 = (z1 * module) / 2 + ha + x1 * module;  % 小轮齿顶圆半径
        ra2 = (z2 * module) / 2 - ha - x2 * module;  % 内齿圈齿根圆半径
        
        % 计算啮合极限点
        rho_a1 = sqrt(ra1^2 - rb1^2);
        rho_a2 = sqrt(rb2^2 - ra2^2);  % 内啮合特殊处理
        rho_p = a * sin(alpha);  % 节圆啮合点与中心连线的垂距
        
        % 重合度
        p_b = pi * module * cos(alpha);  % 基节圆弧长
        epsilon = (rho_a1 + rho_a2 - rho_p * 2) / p_b;
    end
end

% 检查变位系数的重合度约束
function [x1_min_for_eps, x2_min_for_eps] = get_min_shifts_for_contact_ratio(z1, z2, alpha, module, is_internal)
    % 查找满足重合度≥1.2的最小变位系数
    min_eps = 1.2;
    
    % 使用简化方法近似计算
    if ~is_internal
        % 外啮合 - 简化估计
        base_eps = (sqrt((z1+2)^2-z1^2*cos(alpha)^2) + sqrt((z2+2)^2-z2^2*cos(alpha)^2) - 2*(z1+z2)*sin(alpha))/(2*pi*cos(alpha));
        
        if base_eps < min_eps
            % 需要通过变位来增加重合度
            needed_increase = (min_eps - base_eps) * pi * module * cos(alpha);
            x1_min_for_eps = needed_increase / (2 * module);
            x2_min_for_eps = needed_increase / (2 * module);
        else
            x1_min_for_eps = 0;
            x2_min_for_eps = 0;
        end
    else
        % 内啮合 - 简化估计
        base_eps = (sqrt((z1+2)^2-z1^2*cos(alpha)^2) + sqrt(z2^2*cos(alpha)^2-(z2-2.5)^2) - (z2-z1)*sin(alpha))/(2*pi*cos(alpha));
        
        if base_eps < min_eps
            needed_increase = (min_eps - base_eps) * pi * module * cos(alpha);
            x1_min_for_eps = needed_increase / (2 * module);
            x2_min_for_eps = -needed_increase * z1 / (2 * module * z2);  % 内啮合特殊处理
        else
            x1_min_for_eps = 0;
            x2_min_for_eps = 0;
        end
    end
end

% 获取满足重合度约束的最小变位系数
[x1_min_eps, x2_min_eps] = get_min_shifts_for_contact_ratio(z1, z2, alpha, module, is_internal);

% 7. 啮合干涉检查 - 实现公式(17)(18)(19)
function [x1_min_interf, x2_max_interf] = check_interference(z1, z2, alpha, is_internal)
    % 检查啮合干涉并返回相应约束
    if ~is_internal
        % 外啮合 - 公式(17)(18)
        if z1 <= z2
            % 小齿轮齿根不干涉约束
            ha_star = 1.0;
            x1_min_interf = -ha_star + z1*sin(alpha)^2/4;
            
            % 大齿轮齿根不干涉约束
            x2_max_interf = ha_star + z1*sin(2*alpha)/(4*z2);
        else
            % 交换齿轮角色
            ha_star = 1.0;
            x2_min_interf = -ha_star + z2*sin(alpha)^2/4;
            x1_max_interf = ha_star + z2*sin(2*alpha)/(4*z1);
            
            x1_min_interf = x2_min_interf;
            x2_max_interf = x1_max_interf;
        end
    else
        % 内啮合 - 公式(19)
        ha_star = 1.0;
        % 简化内啮合干涉检查
        x1_min_interf = -ha_star + z1*sin(alpha)^2/4;
        x2_max_interf = -x1_min_interf * z1 / z2;
    end
end

% 获取干涉约束的变位系数范围
[x1_min_interf, x2_max_interf] = check_interference(z1, z2, alpha, is_internal);

% 8. 综合所有约束确定最终的变位系数范围
% 考虑所有约束，取最严格的值
x1_min = max([x1_min_undercut, x1_min_eps, x1_min_interf, -0.5]);
x1_max = min([x1_max_tip, 1.0]);

if is_internal
    x2_min = -0.5;
    x2_max = min(x2_max_interf, 0.5);
else
    x2_min = max([x2_min_undercut, x2_min_eps, -0.5]);
    x2_max = min([x2_max_tip, x2_max_interf, 1.0]);
end

% 9. 考虑变位系数和的约束
if ~is_internal
    sum_x_min = 0.6; % 变位系数和下限固定为0.6
    sum_x_max = 1.2; % 变位系数和上限固定为1.2
    
    % 调整变位系数范围以满足变位系数和的约束
    if x1_min + x2_max < sum_x_min
        x1_min = sum_x_min - x2_max;
    end
    if x1_max + x2_min < sum_x_min
        x2_min = sum_x_min - x1_max;
    end
    if x1_min + x2_min > sum_x_max
        if x1_min > x2_min
            x1_min = sum_x_max - x2_min;
        else
            x2_min = sum_x_max - x1_min;
        end
    end
    if x1_max + x2_max > sum_x_max
        if x1_max > x2_max
            x1_max = sum_x_max - x2_max;
        else
            x2_max = sum_x_max - x1_max;
        end
    end
end

% 10. 行星轮系特殊处理
if is_planetary
    % 对于行星轮系，需要同时满足两对啮合的约束，并控制变位系数和在0.6-1.2范围内
    if ~is_internal
        % 太阳轮-行星轮啮合
        shift_ranges.x1_min = max(0.1, x1_min); % 太阳轮变位通常为正
        shift_ranges.x1_max = min(0.8, x1_max);
        shift_ranges.x2_min = max(0.1, x2_min); % 行星轮变位通常为正
        shift_ranges.x2_max = min(0.6, x2_max);
        
        % 确保太阳轮和行星轮的变位系数和在0.6-1.2范围内
        if shift_ranges.x1_min + shift_ranges.x2_min < 0.6
            if shift_ranges.x1_max + shift_ranges.x2_min >= 0.6
                shift_ranges.x1_min = max(shift_ranges.x1_min, 0.6 - shift_ranges.x2_max);
            elseif shift_ranges.x1_min + shift_ranges.x2_max >= 0.6
                shift_ranges.x2_min = max(shift_ranges.x2_min, 0.6 - shift_ranges.x1_max);
            else
                % 如果无法满足下限约束，则尽量接近0.6
                shift_ranges.x1_min = shift_ranges.x1_max;
                shift_ranges.x2_min = shift_ranges.x2_max;
            end
        end
        
        if shift_ranges.x1_max + shift_ranges.x2_max > 1.2
            if shift_ranges.x1_min + shift_ranges.x2_max <= 1.2
                shift_ranges.x1_max = min(shift_ranges.x1_max, 1.2 - shift_ranges.x2_min);
            elseif shift_ranges.x1_max + shift_ranges.x2_min <= 1.2
                shift_ranges.x2_max = min(shift_ranges.x2_max, 1.2 - shift_ranges.x1_min);
            else
                % 如果无法满足上限约束，则尽量接近1.2
                shift_ranges.x1_max = shift_ranges.x1_min;
                shift_ranges.x2_max = shift_ranges.x2_min;
            end
        end
        
        % 最优变位系数
        optimal_shifts.x1_opt = min(max(x1_opt_sliding, shift_ranges.x1_min), shift_ranges.x1_max);
        optimal_shifts.x2_opt = min(max(x2_opt_sliding, shift_ranges.x2_min), shift_ranges.x2_max);
    else
        % 行星轮-内齿圈啮合
        shift_ranges.x2_min = max(0.1, x2_min); % 行星轮变位通常为正
        shift_ranges.x2_max = min(0.6, x2_max);
        
        % 计算内齿圈的变位范围
        if isfield(shift_ranges, 'xr_min') && ~isfield(shift_ranges, 'xr_max')
            shift_ranges.xr_min = -shift_ranges.x2_max * z1 / z2;
        end
        
        % 最优变位系数
        optimal_shifts.x2_opt = min(max(x1_opt_sliding, shift_ranges.x2_min), shift_ranges.x2_max);
        optimal_shifts.xr_opt = min(max(x2_opt_sliding, shift_ranges.xr_min), shift_ranges.xr_max);
    end
else
    % 普通平行轴齿轮啮合
    shift_ranges.x1_min = x1_min;
    shift_ranges.x1_max = x1_max;
    shift_ranges.x2_min = x2_min;
    shift_ranges.x2_max = x2_max;
    
    % 最优变位系数
    optimal_shifts.x1_opt = min(max(x1_opt_sliding, shift_ranges.x1_min), shift_ranges.x1_max);
    optimal_shifts.x2_opt = min(max(x2_opt_sliding, shift_ranges.x2_min), shift_ranges.x2_max);
    
    % 调整以确保变位系数和严格控制在0.6-1.2之间
    if ~is_internal
        current_sum = optimal_shifts.x1_opt + optimal_shifts.x2_opt;
        if current_sum < 0.6
            % 总和小于0.6时，按比例增加变位系数
            ratio = optimal_shifts.x1_opt / (optimal_shifts.x1_opt + optimal_shifts.x2_opt + 1e-10);
            excess = 0.6 - current_sum;
            optimal_shifts.x1_opt = min(optimal_shifts.x1_opt + excess * ratio, shift_ranges.x1_max);
            optimal_shifts.x2_opt = min(optimal_shifts.x2_opt + excess * (1-ratio), shift_ranges.x2_max);
            
            % 再次检查是否达到0.6
            if (optimal_shifts.x1_opt + optimal_shifts.x2_opt) < 0.6
                % 如果仍未达到，将剩余差值均匀分配
                remaining = 0.6 - (optimal_shifts.x1_opt + optimal_shifts.x2_opt);
                optimal_shifts.x1_opt = min(optimal_shifts.x1_opt + remaining/2, shift_ranges.x1_max);
                optimal_shifts.x2_opt = min(optimal_shifts.x2_opt + remaining/2, shift_ranges.x2_max);
            end
        elseif current_sum > 1.2
            % 总和大于1.2时，按比例减少变位系数
            ratio = optimal_shifts.x1_opt / (optimal_shifts.x1_opt + optimal_shifts.x2_opt);
            excess = current_sum - 1.2;
            optimal_shifts.x1_opt = max(optimal_shifts.x1_opt - excess * ratio, shift_ranges.x1_min);
            optimal_shifts.x2_opt = max(optimal_shifts.x2_opt - excess * (1-ratio), shift_ranges.x2_min);
            
            % 再次检查是否降至1.2
            if (optimal_shifts.x1_opt + optimal_shifts.x2_opt) > 1.2
                % 如果仍超过，将剩余差值均匀减少
                remaining = (optimal_shifts.x1_opt + optimal_shifts.x2_opt) - 1.2;
                optimal_shifts.x1_opt = max(optimal_shifts.x1_opt - remaining/2, shift_ranges.x1_min);
                optimal_shifts.x2_opt = max(optimal_shifts.x2_opt - remaining/2, shift_ranges.x2_min);
            end
        end
    end
end

% 11. 综合处理行星轮系
if is_planetary && zr > 0
    % 由于行星轮同时与太阳轮和内齿圈啮合，需要综合优化
    % 此处简化处理：以太阳轮-行星轮啮合为主，然后计算对应的内齿圈变位
    % 实际工程中可能需要更复杂的迭代优化
    if ~is_internal
        % 确保太阳轮和行星轮的变位系数和在0.6-1.2范围内
        sum_x = optimal_shifts.x1_opt + optimal_shifts.x2_opt;
        if sum_x < 0.6
            % 如果总和小于0.6，按比例增加
            ratio = optimal_shifts.x1_opt / (sum_x + 1e-10);
            excess = 0.6 - sum_x;
            optimal_shifts.x1_opt = optimal_shifts.x1_opt + excess * ratio;
            optimal_shifts.x2_opt = optimal_shifts.x2_opt + excess * (1-ratio);
        elseif sum_x > 1.2
            % 如果总和大于1.2，按比例减少
            ratio = optimal_shifts.x1_opt / sum_x;
            excess = sum_x - 1.2;
            optimal_shifts.x1_opt = optimal_shifts.x1_opt - excess * ratio;
            optimal_shifts.x2_opt = optimal_shifts.x2_opt - excess * (1-ratio);
        end
        
        % 行星轮系综合优化
        xr_calculated = -(optimal_shifts.x1_opt * z1 + optimal_shifts.x2_opt * z2) / zr;
        
        % 检查计算的内齿圈变位是否在允许范围内
        if isfield(shift_ranges, 'xr_min') && isfield(shift_ranges, 'xr_max')
            if xr_calculated < shift_ranges.xr_min || xr_calculated > shift_ranges.xr_max
                % 如果不在范围内，需要调整变位系数
                % 简化处理：取中间值
                optimal_shifts.x1_opt = (shift_ranges.x1_min + shift_ranges.x1_max) / 2;
                optimal_shifts.x2_opt = (shift_ranges.x2_min + shift_ranges.x2_max) / 2;
                optimal_shifts.xr_opt = -(optimal_shifts.x1_opt * z1 + optimal_shifts.x2_opt * z2) / zr;
            else
                optimal_shifts.xr_opt = xr_calculated;
            end
        else
            optimal_shifts.xr_opt = xr_calculated;
        end
        
        % 最后检查重合度
        eps_sp = calculate_contact_ratio(z1, z2, optimal_shifts.x1_opt, optimal_shifts.x2_opt, alpha, module, false);
        eps_rp = calculate_contact_ratio(z2, zr, optimal_shifts.x2_opt, optimal_shifts.xr_opt, alpha, module, true);
        
        if eps_sp < 1.2 || eps_rp < 1.2
            % 如果重合度不满足要求，尝试调整变位系数
            % 这里只是简化处理，实际可能需要更复杂的算法
            adjustment = 0.05;
            while (eps_sp < 1.2 || eps_rp < 1.2) && adjustment < 0.3
                % 尝试适度增加变位系数
                test_x1 = optimal_shifts.x1_opt + adjustment;
                test_x2 = optimal_shifts.x2_opt + adjustment;
                test_xr = -(test_x1 * z1 + test_x2 * z2) / zr;
                
                % 计算新的重合度
                new_eps_sp = calculate_contact_ratio(z1, z2, test_x1, test_x2, alpha, module, false);
                new_eps_rp = calculate_contact_ratio(z2, zr, test_x2, test_xr, alpha, module, true);
                
                % 如果改善了重合度，采用新值
                if new_eps_sp > eps_sp && new_eps_rp > eps_rp
                    optimal_shifts.x1_opt = test_x1;
                    optimal_shifts.x2_opt = test_x2;
                    optimal_shifts.xr_opt = test_xr;
                    eps_sp = new_eps_sp;
                    eps_rp = new_eps_rp;
                    
                    % 如果已经满足要求，结束调整
                    if eps_sp >= 1.2 && eps_rp >= 1.2
                        break;
                    end
                end
                adjustment = adjustment + 0.05;
            end
        end
    end
end
end 